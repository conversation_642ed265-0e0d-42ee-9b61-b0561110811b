[{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\hooks\\useInactivityTimer.ts": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\services\\authService.ts": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\conversacionesService.ts": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\questionService.ts": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\dashboardService.ts": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\services\\documentosService.ts": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx": "82", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx": "83", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx": "84", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx": "85", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx": "86", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx": "87", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx": "88", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx": "89", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx": "90", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx": "91", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx": "92", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx": "93", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx": "94", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts": "95", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardGenerator.ts": "96", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardsService.ts": "97", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx": "98", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx": "99", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx": "100", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx": "101", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts": "102", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts": "103", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts": "104", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts": "105", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx": "106", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx": "107", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx": "108", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx": "109", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\BackgroundTasksPanel.tsx": "110", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx": "111", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx": "112", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx": "113", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx": "114", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx": "115", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx": "116", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx": "117", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx": "118", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx": "119", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx": "120", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx": "121", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx": "122", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts": "123", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts": "124", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx": "125", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx": "126", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx": "127", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx": "128", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx": "129", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.blank.test.tsx": "130", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.test.tsx": "131", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx": "132", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testGenerator.ts": "133", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testsService.ts": "134", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts": "135", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts": "136", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts": "137", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts": "138", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts": "139", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts": "140", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts": "141", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts": "142", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts": "143", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts": "144", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts": "145", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts": "146", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts": "147", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts": "148", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts": "149", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts": "150", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts": "151", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts": "152", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\__tests__\\geminiClient.test.ts": "153", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini.ts": "154", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts": "155", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts": "156", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts": "157", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts": "158", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts": "159", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts": "160", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts": "161", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts": "162", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts": "163", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts": "164", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts": "165", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts": "166", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts": "167", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts": "168", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts": "169", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts": "170", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts": "171", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts": "172", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts": "173", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts": "174", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts": "175", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts": "176", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts": "177", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts": "178", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts": "179", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts": "180", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts": "181", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts": "182", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts": "183", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts": "184", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts": "185", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts": "186", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase.ts": "187", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts": "188", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\planLimits.ts": "189", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts": "190", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts": "191", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts": "192", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts": "193", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts": "194", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts": "195", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx": "196", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts": "197", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\limitHandler.test.ts": "198", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\permissionService.test.ts": "199", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\planValidation.test.ts": "200", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx": "201", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx": "202", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\hooks\\usePlanCalendario.ts": "203", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts": "204", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts": "205", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts": "206"}, {"size": 8681, "mtime": 1750549865174, "results": "207", "hashOfConfig": "208"}, {"size": 8713, "mtime": 1749926277358, "results": "209", "hashOfConfig": "208"}, {"size": 5845, "mtime": 1750309142357, "results": "210", "hashOfConfig": "208"}, {"size": 3612, "mtime": 1750086603738, "results": "211", "hashOfConfig": "208"}, {"size": 6683, "mtime": 1750085064464, "results": "212", "hashOfConfig": "208"}, {"size": 5926, "mtime": 1750549829701, "results": "213", "hashOfConfig": "208"}, {"size": 8784, "mtime": 1750086580286, "results": "214", "hashOfConfig": "208"}, {"size": 8491, "mtime": 1750079882056, "results": "215", "hashOfConfig": "208"}, {"size": 7052, "mtime": 1750082610808, "results": "216", "hashOfConfig": "208"}, {"size": 1301, "mtime": 1750287727080, "results": "217", "hashOfConfig": "208"}, {"size": 5091, "mtime": 1750698172366, "results": "218", "hashOfConfig": "208"}, {"size": 4017, "mtime": 1750546019180, "results": "219", "hashOfConfig": "208"}, {"size": 2270, "mtime": 1750549762427, "results": "220", "hashOfConfig": "208"}, {"size": 9194, "mtime": 1750020486326, "results": "221", "hashOfConfig": "208"}, {"size": 11683, "mtime": 1749644921713, "results": "222", "hashOfConfig": "208"}, {"size": 271, "mtime": 1750308563029, "results": "223", "hashOfConfig": "208"}, {"size": 5932, "mtime": 1750106816513, "results": "224", "hashOfConfig": "208"}, {"size": 3019, "mtime": 1750549747348, "results": "225", "hashOfConfig": "208"}, {"size": 4781, "mtime": 1750547906770, "results": "226", "hashOfConfig": "208"}, {"size": 4139, "mtime": 1749829295626, "results": "227", "hashOfConfig": "208"}, {"size": 3765, "mtime": 1750309415770, "results": "228", "hashOfConfig": "208"}, {"size": 3987, "mtime": 1750107085364, "results": "229", "hashOfConfig": "208"}, {"size": 2245, "mtime": 1750086615997, "results": "230", "hashOfConfig": "208"}, {"size": 1120, "mtime": 1750110995546, "results": "231", "hashOfConfig": "208"}, {"size": 5431, "mtime": 1750283688975, "results": "232", "hashOfConfig": "208"}, {"size": 2214, "mtime": 1749910333290, "results": "233", "hashOfConfig": "208"}, {"size": 2110, "mtime": 1749913156452, "results": "234", "hashOfConfig": "208"}, {"size": 3432, "mtime": 1749830150148, "results": "235", "hashOfConfig": "208"}, {"size": 29219, "mtime": 1750697271674, "results": "236", "hashOfConfig": "208"}, {"size": 12130, "mtime": 1750519961058, "results": "237", "hashOfConfig": "208"}, {"size": 5086, "mtime": 1750254562388, "results": "238", "hashOfConfig": "208"}, {"size": 5649, "mtime": 1750544723119, "results": "239", "hashOfConfig": "208"}, {"size": 13484, "mtime": 1750528698459, "results": "240", "hashOfConfig": "208"}, {"size": 8147, "mtime": 1750075142671, "results": "241", "hashOfConfig": "208"}, {"size": 6679, "mtime": 1750020180985, "results": "242", "hashOfConfig": "208"}, {"size": 595, "mtime": 1748788866729, "results": "243", "hashOfConfig": "208"}, {"size": 6311, "mtime": 1748376641348, "results": "244", "hashOfConfig": "208"}, {"size": 11122, "mtime": 1749284756641, "results": "245", "hashOfConfig": "208"}, {"size": 9894, "mtime": 1750549596184, "results": "246", "hashOfConfig": "208"}, {"size": 9527, "mtime": 1750698572231, "results": "247", "hashOfConfig": "208"}, {"size": 16407, "mtime": 1750025885793, "results": "248", "hashOfConfig": "208"}, {"size": 7697, "mtime": 1750086750423, "results": "249", "hashOfConfig": "208"}, {"size": 5926, "mtime": 1750690443715, "results": "250", "hashOfConfig": "208"}, {"size": 12825, "mtime": 1750074337834, "results": "251", "hashOfConfig": "208"}, {"size": 8472, "mtime": 1749855884749, "results": "252", "hashOfConfig": "208"}, {"size": 8352, "mtime": 1749830601307, "results": "253", "hashOfConfig": "208"}, {"size": 2673, "mtime": 1749908591667, "results": "254", "hashOfConfig": "208"}, {"size": 6340, "mtime": 1750073060045, "results": "255", "hashOfConfig": "208"}, {"size": 7534, "mtime": 1750075103015, "results": "256", "hashOfConfig": "208"}, {"size": 9495, "mtime": 1750073106534, "results": "257", "hashOfConfig": "208"}, {"size": 8278, "mtime": 1750073147880, "results": "258", "hashOfConfig": "208"}, {"size": 6047, "mtime": 1749826972699, "results": "259", "hashOfConfig": "208"}, {"size": 2643, "mtime": 1749364222822, "results": "260", "hashOfConfig": "208"}, {"size": 3239, "mtime": 1750692677577, "results": "261", "hashOfConfig": "208"}, {"size": 3469, "mtime": 1750693988715, "results": "262", "hashOfConfig": "208"}, {"size": 4720, "mtime": 1750692751611, "results": "263", "hashOfConfig": "208"}, {"size": 8346, "mtime": 1750692897611, "results": "264", "hashOfConfig": "208"}, {"size": 5161, "mtime": 1750073161083, "results": "265", "hashOfConfig": "208"}, {"size": 2566, "mtime": 1750715304859, "results": "266", "hashOfConfig": "208"}, {"size": 56653, "mtime": 1750714886122, "results": "267", "hashOfConfig": "208"}, {"size": 9777, "mtime": 1750546783460, "results": "268", "hashOfConfig": "208"}, {"size": 4662, "mtime": 1750696077841, "results": "269", "hashOfConfig": "208"}, {"size": 2386, "mtime": 1748898729938, "results": "270", "hashOfConfig": "208"}, {"size": 3162, "mtime": 1748813999472, "results": "271", "hashOfConfig": "208"}, {"size": 1279, "mtime": 1748898005145, "results": "272", "hashOfConfig": "208"}, {"size": 2089, "mtime": 1748784425494, "results": "273", "hashOfConfig": "208"}, {"size": 3631, "mtime": 1748814882360, "results": "274", "hashOfConfig": "208"}, {"size": 6524, "mtime": 1749479516795, "results": "275", "hashOfConfig": "208"}, {"size": 3899, "mtime": 1749655003060, "results": "276", "hashOfConfig": "208"}, {"size": 11437, "mtime": 1748789003548, "results": "277", "hashOfConfig": "208"}, {"size": 21487, "mtime": 1749940310146, "results": "278", "hashOfConfig": "208"}, {"size": 10017, "mtime": 1749655003060, "results": "279", "hashOfConfig": "208"}, {"size": 2310, "mtime": 1749329923920, "results": "280", "hashOfConfig": "208"}, {"size": 14701, "mtime": 1749642914607, "results": "281", "hashOfConfig": "208"}, {"size": 15797, "mtime": 1748789194243, "results": "282", "hashOfConfig": "208"}, {"size": 6662, "mtime": 1749655003060, "results": "283", "hashOfConfig": "208"}, {"size": 11388, "mtime": 1749655003060, "results": "284", "hashOfConfig": "208"}, {"size": 6561, "mtime": 1749655003060, "results": "285", "hashOfConfig": "208"}, {"size": 3186, "mtime": 1750697251442, "results": "286", "hashOfConfig": "208"}, {"size": 10544, "mtime": 1750020029412, "results": "287", "hashOfConfig": "208"}, {"size": 4307, "mtime": 1749655003075, "results": "288", "hashOfConfig": "208"}, {"size": 2048, "mtime": 1749137653402, "results": "289", "hashOfConfig": "208"}, {"size": 4361, "mtime": 1749655003075, "results": "290", "hashOfConfig": "208"}, {"size": 4261, "mtime": 1748777250685, "results": "291", "hashOfConfig": "208"}, {"size": 6739, "mtime": 1749137786700, "results": "292", "hashOfConfig": "208"}, {"size": 8927, "mtime": 1748562315253, "results": "293", "hashOfConfig": "208"}, {"size": 6275, "mtime": 1748381218646, "results": "294", "hashOfConfig": "208"}, {"size": 11220, "mtime": 1748562362912, "results": "295", "hashOfConfig": "208"}, {"size": 20867, "mtime": 1750107553549, "results": "296", "hashOfConfig": "208"}, {"size": 1500, "mtime": 1748376642824, "results": "297", "hashOfConfig": "208"}, {"size": 10380, "mtime": 1748563534405, "results": "298", "hashOfConfig": "208"}, {"size": 4612, "mtime": 1748778081572, "results": "299", "hashOfConfig": "208"}, {"size": 15473, "mtime": 1749642914618, "results": "300", "hashOfConfig": "208"}, {"size": 4918, "mtime": 1748789236100, "results": "301", "hashOfConfig": "208"}, {"size": 1090, "mtime": 1749138136757, "results": "302", "hashOfConfig": "208"}, {"size": 2319, "mtime": 1749329859812, "results": "303", "hashOfConfig": "208"}, {"size": 23641, "mtime": 1749655003075, "results": "304", "hashOfConfig": "208"}, {"size": 13256, "mtime": 1750108436905, "results": "305", "hashOfConfig": "208"}, {"size": 3779, "mtime": 1748557840096, "results": "306", "hashOfConfig": "208"}, {"size": 19442, "mtime": 1750717579792, "results": "307", "hashOfConfig": "208"}, {"size": 16833, "mtime": 1749045203181, "results": "308", "hashOfConfig": "208"}, {"size": 7388, "mtime": 1749162951383, "results": "309", "hashOfConfig": "208"}, {"size": 10468, "mtime": 1748881805956, "results": "310", "hashOfConfig": "208"}, {"size": 20287, "mtime": 1749364863869, "results": "311", "hashOfConfig": "208"}, {"size": 6282, "mtime": 1749163006965, "results": "312", "hashOfConfig": "208"}, {"size": 11016, "mtime": 1750086795224, "results": "313", "hashOfConfig": "208"}, {"size": 16089, "mtime": 1750257232905, "results": "314", "hashOfConfig": "208"}, {"size": 10625, "mtime": 1750086841859, "results": "315", "hashOfConfig": "208"}, {"size": 11515, "mtime": 1750110978404, "results": "316", "hashOfConfig": "208"}, {"size": 7292, "mtime": 1748559970572, "results": "317", "hashOfConfig": "208"}, {"size": 1394, "mtime": 1748815043814, "results": "318", "hashOfConfig": "208"}, {"size": 8618, "mtime": 1749655003075, "results": "319", "hashOfConfig": "208"}, {"size": 5212, "mtime": 1749655003075, "results": "320", "hashOfConfig": "208"}, {"size": 7880, "mtime": 1750697439339, "results": "321", "hashOfConfig": "208"}, {"size": 11098, "mtime": 1750696256433, "results": "322", "hashOfConfig": "208"}, {"size": 21480, "mtime": 1750716185639, "results": "323", "hashOfConfig": "208"}, {"size": 4528, "mtime": 1748810558456, "results": "324", "hashOfConfig": "208"}, {"size": 6444, "mtime": 1748810539098, "results": "325", "hashOfConfig": "208"}, {"size": 6524, "mtime": 1748810280408, "results": "326", "hashOfConfig": "208"}, {"size": 20512, "mtime": 1749162085117, "results": "327", "hashOfConfig": "208"}, {"size": 17180, "mtime": 1748900565639, "results": "328", "hashOfConfig": "208"}, {"size": 9265, "mtime": 1748900735287, "results": "329", "hashOfConfig": "208"}, {"size": 7572, "mtime": 1748898099248, "results": "330", "hashOfConfig": "208"}, {"size": 6378, "mtime": 1748877566234, "results": "331", "hashOfConfig": "208"}, {"size": 4512, "mtime": 1748630532621, "results": "332", "hashOfConfig": "208"}, {"size": 2905, "mtime": 1748630509095, "results": "333", "hashOfConfig": "208"}, {"size": 22852, "mtime": 1750107629810, "results": "334", "hashOfConfig": "208"}, {"size": 9380, "mtime": 1749644921767, "results": "335", "hashOfConfig": "208"}, {"size": 14023, "mtime": 1749309504678, "results": "336", "hashOfConfig": "208"}, {"size": 6931, "mtime": 1749644921770, "results": "337", "hashOfConfig": "208"}, {"size": 6240, "mtime": 1749644921770, "results": "338", "hashOfConfig": "208"}, {"size": 23813, "mtime": 1749644921770, "results": "339", "hashOfConfig": "208"}, {"size": 3017, "mtime": 1749329950394, "results": "340", "hashOfConfig": "208"}, {"size": 10621, "mtime": 1749655003091, "results": "341", "hashOfConfig": "208"}, {"size": 7297, "mtime": 1750696131484, "results": "342", "hashOfConfig": "208"}, {"size": 9266, "mtime": 1750082632460, "results": "343", "hashOfConfig": "208"}, {"size": 3333, "mtime": 1748376643113, "results": "344", "hashOfConfig": "208"}, {"size": 2060, "mtime": 1748814199030, "results": "345", "hashOfConfig": "208"}, {"size": 9266, "mtime": 1750692614862, "results": "346", "hashOfConfig": "208"}, {"size": 2651, "mtime": 1748561592350, "results": "347", "hashOfConfig": "208"}, {"size": 1337, "mtime": 1750107504888, "results": "348", "hashOfConfig": "208"}, {"size": 7023, "mtime": 1749416233417, "results": "349", "hashOfConfig": "208"}, {"size": 7540, "mtime": 1750021679298, "results": "350", "hashOfConfig": "208"}, {"size": 1609, "mtime": 1749332804371, "results": "351", "hashOfConfig": "208"}, {"size": 2407, "mtime": 1749364828132, "results": "352", "hashOfConfig": "208"}, {"size": 1086, "mtime": 1749329759055, "results": "353", "hashOfConfig": "208"}, {"size": 3253, "mtime": 1749655003091, "results": "354", "hashOfConfig": "208"}, {"size": 3527, "mtime": 1749364808623, "results": "355", "hashOfConfig": "208"}, {"size": 2587, "mtime": 1749364782712, "results": "356", "hashOfConfig": "208"}, {"size": 2046, "mtime": 1750695500164, "results": "357", "hashOfConfig": "208"}, {"size": 3077, "mtime": 1749655003091, "results": "358", "hashOfConfig": "208"}, {"size": 3887, "mtime": 1749364846856, "results": "359", "hashOfConfig": "208"}, {"size": 18582, "mtime": 1749644921782, "results": "360", "hashOfConfig": "208"}, {"size": 195, "mtime": 1748376643201, "results": "361", "hashOfConfig": "208"}, {"size": 6123, "mtime": 1749364772281, "results": "362", "hashOfConfig": "208"}, {"size": 8426, "mtime": 1750086480511, "results": "363", "hashOfConfig": "208"}, {"size": 6508, "mtime": 1750086442416, "results": "364", "hashOfConfig": "208"}, {"size": 7674, "mtime": 1750086554604, "results": "365", "hashOfConfig": "208"}, {"size": 8509, "mtime": 1750086519480, "results": "366", "hashOfConfig": "208"}, {"size": 8535, "mtime": 1750086411730, "results": "367", "hashOfConfig": "208"}, {"size": 868, "mtime": 1750086640332, "results": "368", "hashOfConfig": "208"}, {"size": 1707, "mtime": 1750086372857, "results": "369", "hashOfConfig": "208"}, {"size": 13923, "mtime": 1750509873388, "results": "370", "hashOfConfig": "208"}, {"size": 17383, "mtime": 1750698297109, "results": "371", "hashOfConfig": "208"}, {"size": 12474, "mtime": 1750692847133, "results": "372", "hashOfConfig": "208"}, {"size": 11782, "mtime": 1750110965854, "results": "373", "hashOfConfig": "208"}, {"size": 31143, "mtime": 1750549685191, "results": "374", "hashOfConfig": "208"}, {"size": 18327, "mtime": 1750547982192, "results": "375", "hashOfConfig": "208"}, {"size": 426, "mtime": 1749138861932, "results": "376", "hashOfConfig": "208"}, {"size": 4105, "mtime": 1750084350458, "results": "377", "hashOfConfig": "208"}, {"size": 13571, "mtime": 1750550713790, "results": "378", "hashOfConfig": "208"}, {"size": 6717, "mtime": 1749655003091, "results": "379", "hashOfConfig": "208"}, {"size": 622, "mtime": 1750254594447, "results": "380", "hashOfConfig": "208"}, {"size": 9246, "mtime": 1749655003091, "results": "381", "hashOfConfig": "208"}, {"size": 6595, "mtime": 1749655003091, "results": "382", "hashOfConfig": "208"}, {"size": 1310, "mtime": 1748378808764, "results": "383", "hashOfConfig": "208"}, {"size": 4038, "mtime": 1749655003108, "results": "384", "hashOfConfig": "208"}, {"size": 11321, "mtime": 1749655003111, "results": "385", "hashOfConfig": "208"}, {"size": 23405, "mtime": 1749642901184, "results": "386", "hashOfConfig": "208"}, {"size": 313, "mtime": 1749655003114, "results": "387", "hashOfConfig": "208"}, {"size": 6748, "mtime": 1749655430336, "results": "388", "hashOfConfig": "208"}, {"size": 1576, "mtime": 1749479562972, "results": "389", "hashOfConfig": "208"}, {"size": 8151, "mtime": 1750084543676, "results": "390", "hashOfConfig": "208"}, {"size": 9301, "mtime": 1749655003118, "results": "391", "hashOfConfig": "208"}, {"size": 8702, "mtime": 1750692817251, "results": "392", "hashOfConfig": "208"}, {"size": 16305, "mtime": 1750692885638, "results": "393", "hashOfConfig": "208"}, {"size": 201, "mtime": 1749655003091, "results": "394", "hashOfConfig": "208"}, {"size": 11110, "mtime": 1749830202167, "results": "395", "hashOfConfig": "208"}, {"size": 7188, "mtime": 1750078415710, "results": "396", "hashOfConfig": "208"}, {"size": 10079, "mtime": 1750026225814, "results": "397", "hashOfConfig": "208"}, {"size": 7707, "mtime": 1750026605896, "results": "398", "hashOfConfig": "208"}, {"size": 2423, "mtime": 1749655955625, "results": "399", "hashOfConfig": "208"}, {"size": 17811, "mtime": 1750550233852, "results": "400", "hashOfConfig": "208"}, {"size": 13888, "mtime": 1750698368960, "results": "401", "hashOfConfig": "208"}, {"size": 4309, "mtime": 1749504407466, "results": "402", "hashOfConfig": "208"}, {"size": 11509, "mtime": 1749853487198, "results": "403", "hashOfConfig": "208"}, {"size": 12234, "mtime": 1749853536927, "results": "404", "hashOfConfig": "208"}, {"size": 11484, "mtime": 1749853441638, "results": "405", "hashOfConfig": "208"}, {"size": 9864, "mtime": 1749853396810, "results": "406", "hashOfConfig": "208"}, {"size": 8686, "mtime": 1749853357863, "results": "407", "hashOfConfig": "208"}, {"size": 9008, "mtime": 1750717425725, "results": "408", "hashOfConfig": "208"}, {"size": 9245, "mtime": 1750717463555, "results": "409", "hashOfConfig": "208"}, {"size": 7515, "mtime": 1750717378223, "results": "410", "hashOfConfig": "208"}, {"size": 4824, "mtime": 1750717293486, "results": "411", "hashOfConfig": "208"}, {"size": 10624, "mtime": 1750717342383, "results": "412", "hashOfConfig": "208"}, {"size": 7057, "mtime": 1750717263087, "results": "413", "hashOfConfig": "208"}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mbgsrr", {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx", [], ["1032"], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx", ["1033"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx", ["1034"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx", ["1035"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx", ["1036"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\hooks\\useInactivityTimer.ts", ["1037"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\dashboardService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\services\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx", ["1038"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx", ["1039"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx", ["1040"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx", ["1041"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\BackgroundTasksPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx", ["1042"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx", ["1043"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.blank.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx", ["1044"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts", ["1045"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\__tests__\\geminiClient.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\planLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\limitHandler.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\permissionService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\planValidation.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\hooks\\usePlanCalendario.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts", [], [], {"ruleId": "1046", "severity": 1, "message": "1047", "line": 169, "column": 6, "nodeType": "1048", "endLine": 169, "endColumn": 28, "suggestions": "1049", "suppressions": "1050"}, {"ruleId": "1046", "severity": 1, "message": "1051", "line": 107, "column": 6, "nodeType": "1048", "endLine": 107, "endColumn": 20, "suggestions": "1052"}, {"ruleId": "1046", "severity": 1, "message": "1053", "line": 62, "column": 6, "nodeType": "1048", "endLine": 62, "endColumn": 33, "suggestions": "1054"}, {"ruleId": "1046", "severity": 1, "message": "1055", "line": 84, "column": 6, "nodeType": "1048", "endLine": 84, "endColumn": 8, "suggestions": "1056"}, {"ruleId": "1046", "severity": 1, "message": "1057", "line": 45, "column": 6, "nodeType": "1048", "endLine": 45, "endColumn": 50, "suggestions": "1058"}, {"ruleId": "1046", "severity": 1, "message": "1059", "line": 113, "column": 6, "nodeType": "1048", "endLine": 113, "endColumn": 39, "suggestions": "1060"}, {"ruleId": "1046", "severity": 1, "message": "1061", "line": 22, "column": 6, "nodeType": "1048", "endLine": 22, "endColumn": 19, "suggestions": "1062"}, {"ruleId": "1046", "severity": 1, "message": "1063", "line": 34, "column": 6, "nodeType": "1048", "endLine": 34, "endColumn": 17, "suggestions": "1064"}, {"ruleId": "1046", "severity": 1, "message": "1065", "line": 78, "column": 6, "nodeType": "1048", "endLine": 78, "endColumn": 29, "suggestions": "1066"}, {"ruleId": "1046", "severity": 1, "message": "1067", "line": 45, "column": 6, "nodeType": "1048", "endLine": 45, "endColumn": 22, "suggestions": "1068"}, {"ruleId": "1046", "severity": 1, "message": "1069", "line": 127, "column": 6, "nodeType": "1048", "endLine": 127, "endColumn": 14, "suggestions": "1070"}, {"ruleId": "1046", "severity": 1, "message": "1071", "line": 58, "column": 6, "nodeType": "1048", "endLine": 58, "endColumn": 41, "suggestions": "1072"}, {"ruleId": "1046", "severity": 1, "message": "1073", "line": 74, "column": 6, "nodeType": "1048", "endLine": 74, "endColumn": 17, "suggestions": "1074"}, {"ruleId": "1046", "severity": 1, "message": "1075", "line": 142, "column": 6, "nodeType": "1048", "endLine": 142, "endColumn": 39, "suggestions": "1076"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'status'. Either include it or remove the dependency array.", "ArrayExpression", ["1077"], ["1078"], "React Hook useEffect has missing dependencies: 'isPageLoading', 'isSessionReady', and 'linkError'. Either include them or remove the dependency array.", ["1079"], "React Hook useEffect has a missing dependency: 'email'. Either include it or remove the dependency array.", ["1080"], "React Hook useEffect has a missing dependency: 'loadUserProfile'. Either include it or remove the dependency array.", ["1081"], "React Hook useEffect has a missing dependency: 'validateAccess'. Either include it or remove the dependency array.", ["1082"], "React Hook useEffect has a missing dependency: 'events'. Either include it or remove the dependency array.", ["1083"], "React Hook useEffect has a missing dependency: 'cargarEstadisticas'. Either include it or remove the dependency array.", ["1084"], "React Hook useEffect has a missing dependency: 'cargarProgreso'. Either include it or remove the dependency array.", ["1085"], "React Hook useEffect has a missing dependency: 'cargarDatos'. Either include it or remove the dependency array.", ["1086"], "React Hook useEffect has a missing dependency: 'loadNotifications'. Either include it or remove the dependency array.", ["1087"], "React Hook useEffect has a missing dependency: 'runDiagnostics'. Either include it or remove the dependency array.", ["1088"], "React Hook useEffect has a missing dependency: 'preguntas'. Either include it or remove the dependency array.", ["1089"], "React Hook useEffect has a missing dependency: 'testCompletado'. Either include it or remove the dependency array.", ["1090"], "React Hook useCallback has a missing dependency: 'data'. Either include it or remove the dependency array.", ["1091"], {"desc": "1092", "fix": "1093"}, {"kind": "1094", "justification": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1102", "fix": "1103"}, {"desc": "1104", "fix": "1105"}, {"desc": "1106", "fix": "1107"}, {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"desc": "1112", "fix": "1113"}, {"desc": "1114", "fix": "1115"}, {"desc": "1116", "fix": "1117"}, {"desc": "1118", "fix": "1119"}, {"desc": "1120", "fix": "1121"}, "Update the dependencies array to be: [router, searchParams, status]", {"range": "1122", "text": "1123"}, "directive", "", "Update the dependencies array to be: [isPageLoading, isSessionReady, linkError, searchParams]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [sessionId, planId, router, email]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [loadUserProfile]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [requiredFeature, requiredPlan, tokensToUse, validateAccess]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [enabled, resetTimer, clearTimer, events]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [cargarEstadisticas, coleccionId]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [cargarProgreso, temarioId]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [temario.id, isEditing, cargarDatos]", {"range": "1138", "text": "1139"}, "Update the dependencies array to be: [userId, filter, loadNotifications]", {"range": "1140", "text": "1141"}, "Update the dependencies array to be: [isOpen, runDiagnostics]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [preguntaActual, preguntas, respuestasUsuario]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [preguntas, testCompletado]", {"range": "1146", "text": "1147"}, "Update the dependencies array to be: [data]", {"range": "1148", "text": "1149"}, [8806, 8828], "[router, searchParams, status]", [5615, 5629], "[isPage<PERSON>oading, isSessionReady, linkError, searchParams]", [2104, 2131], "[sessionId, planId, router, email]", [3095, 3097], "[loadUserProfile]", [1127, 1171], "[requiredFeature, requiredPlan, tokensToUse, validateAccess]", [3145, 3178], "[enabled, resetTimer, clearTimer, events]", [719, 732], "[cargarEstadisticas, coleccionId]", [1407, 1418], "[cargar<PERSON><PERSON><PERSON><PERSON>, temarioId]", [2229, 2252], "[temario.id, isEditing, cargarDatos]", [1042, 1058], "[userId, filter, loadNotifications]", [4364, 4372], "[isOpen, runDiagnostics]", [2002, 2037], "[preguntaActual, preguntas, respuestasUsuario]", [2858, 2869], "[pregun<PERSON>, testCompletado]", [4018, 4051], "[data]"]
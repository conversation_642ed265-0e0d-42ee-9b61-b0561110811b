"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_features_planificacion_services_planEstudiosService_ts";
exports.ids = ["_rsc_src_features_planificacion_services_planEstudiosService_ts"];
exports.modules = {

/***/ "(rsc)/./src/features/auth/services/authService.ts":
/*!***************************************************!*\
  !*** ./src/features/auth/services/authService.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                // Added session\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    // Added session\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    // Added session\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Changed 'error' to 'e' to avoid conflict with 'error' from signInWithPassword\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            // Added session\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        console.log('🔓 Iniciando proceso de logout...');\n        // Cerrar sesión con scope 'global' para limpiar tanto local como servidor\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut({\n            scope: 'global'\n        });\n        if (error) {\n            console.error('❌ Error en signOut:', error.message);\n            return {\n                error: error.message\n            };\n        }\n        console.log('✅ SignOut exitoso');\n        // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage\n        if (false) {}\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('❌ Error inesperado en logout:', error);\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/auth/services/authService.ts\n");

/***/ }),

/***/ "(rsc)/./src/features/planificacion/services/planEstudiosService.ts":
/*!********************************************************************!*\
  !*** ./src/features/planificacion/services/planEstudiosService.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarPlan: () => (/* binding */ activarPlan),\n/* harmony export */   actualizarNotasPlan: () => (/* binding */ actualizarNotasPlan),\n/* harmony export */   eliminarPlan: () => (/* binding */ eliminarPlan),\n/* harmony export */   guardarPlanEstudios: () => (/* binding */ guardarPlanEstudios),\n/* harmony export */   guardarPlanEstudiosServidor: () => (/* binding */ guardarPlanEstudiosServidor),\n/* harmony export */   guardarProgresoTarea: () => (/* binding */ guardarProgresoTarea),\n/* harmony export */   obtenerEstadisticasProgreso: () => (/* binding */ obtenerEstadisticasProgreso),\n/* harmony export */   obtenerHistorialPlanes: () => (/* binding */ obtenerHistorialPlanes),\n/* harmony export */   obtenerProgresoPlan: () => (/* binding */ obtenerProgresoPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(rsc)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Guarda un plan de estudios generado en la base de datos (versión para cliente)\n */ async function guardarPlanEstudios(temarioId, planData, titulo) {\n    try {\n        // Usar cliente del navegador\n        const { user: clientUser, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!clientUser || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').insert([\n            {\n                user_id: clientUser.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                // El trigger se encargará de desactivar otros planes\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios:', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente:', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Guarda un plan de estudios generado en la base de datos (versión para servidor)\n */ async function guardarPlanEstudiosServidor(temarioId, planData, user, titulo) {\n    try {\n        const { createServerSupabaseClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\"));\n        const supabase = await createServerSupabaseClient();\n        const { data, error } = await supabase.from('planes_estudios').insert([\n            {\n                user_id: user.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                // El trigger se encargará de desactivar otros planes\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios (servidor):', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente (servidor):', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios (servidor):', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los planes de estudios de un temario (historial)\n */ async function obtenerHistorialPlanes(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).order('fecha_generacion', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de planes:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener historial de planes:', error);\n        return [];\n    }\n}\n/**\n * Actualiza las notas de un plan de estudios\n */ async function actualizarNotasPlan(planId, notas) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            notas,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al actualizar notas del plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar notas del plan:', error);\n        return false;\n    }\n}\n/**\n * Marca un plan como activo y desactiva los demás\n */ async function activarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            activo: true,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al activar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al activar plan:', error);\n        return false;\n    }\n}\n/**\n * Elimina un plan de estudios\n */ async function eliminarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').delete().eq('id', planId);\n        if (error) {\n            console.error('Error al eliminar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar plan:', error);\n        return false;\n    }\n}\n/**\n * Guarda el progreso de una tarea del plan\n */ async function guardarProgresoTarea(planId, semanaNúmero, diaNombre, tareaTitulo, tareaTipo, completado, tiempoRealMinutos, notasProgreso, calificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return false;\n        }\n        // Verificar si ya existe un registro de progreso para esta tarea\n        const { data: existente } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('id').eq('plan_id', planId).eq('user_id', user.id).eq('semana_numero', semanaNúmero).eq('dia_nombre', diaNombre).eq('tarea_titulo', tareaTitulo).single();\n        if (existente) {\n            // Actualizar registro existente\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').update({\n                completado,\n                fecha_completado: completado ? new Date().toISOString() : null,\n                tiempo_real_minutos: tiempoRealMinutos,\n                notas_progreso: notasProgreso,\n                calificacion,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', existente.id);\n            if (error) {\n                console.error('Error al actualizar progreso:', error);\n                return false;\n            }\n        } else {\n            // Crear nuevo registro\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').insert([\n                {\n                    plan_id: planId,\n                    user_id: user.id,\n                    semana_numero: semanaNúmero,\n                    dia_nombre: diaNombre,\n                    tarea_titulo: tareaTitulo,\n                    tarea_tipo: tareaTipo,\n                    completado,\n                    fecha_completado: completado ? new Date().toISOString() : null,\n                    tiempo_real_minutos: tiempoRealMinutos,\n                    notas_progreso: notasProgreso,\n                    calificacion\n                }\n            ]);\n            if (error) {\n                console.error('Error al crear progreso:', error);\n                return false;\n            }\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar progreso de tarea:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el progreso de un plan de estudios\n */ async function obtenerProgresoPlan(planId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('*').eq('plan_id', planId).eq('user_id', user.id).order('semana_numero', {\n            ascending: true\n        }).order('creado_en', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener progreso del plan:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener progreso del plan:', error);\n        return [];\n    }\n}\n/**\n * Obtiene estadísticas del progreso del plan\n */ async function obtenerEstadisticasProgreso(planId) {\n    try {\n        const progreso = await obtenerProgresoPlan(planId);\n        const plan = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('plan_data').eq('id', planId).single();\n        if (!plan.data) {\n            return {\n                totalTareas: 0,\n                tareasCompletadas: 0,\n                porcentajeCompletado: 0,\n                tiempoTotalEstimado: 0,\n                tiempoTotalReal: 0,\n                semanasCompletadas: 0,\n                totalSemanas: 0\n            };\n        }\n        const planData = plan.data.plan_data;\n        const totalSemanas = planData.semanas.length;\n        // Calcular total de tareas\n        let totalTareas = 0;\n        planData.semanas.forEach((semana)=>{\n            semana.dias.forEach((dia)=>{\n                totalTareas += dia.tareas.length;\n            });\n        });\n        const tareasCompletadas = progreso.filter((p)=>p.completado).length;\n        const porcentajeCompletado = totalTareas > 0 ? tareasCompletadas / totalTareas * 100 : 0;\n        const tiempoTotalReal = progreso.filter((p)=>p.tiempo_real_minutos).reduce((total, p)=>total + (p.tiempo_real_minutos || 0), 0);\n        // Calcular semanas completadas (todas las tareas de la semana completadas)\n        let semanasCompletadas = 0;\n        planData.semanas.forEach((semana)=>{\n            const tareasSemanaTotales = semana.dias.reduce((total, dia)=>total + dia.tareas.length, 0);\n            const tareasSemanCompletadas = progreso.filter((p)=>p.semana_numero === semana.numero && p.completado).length;\n            if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {\n                semanasCompletadas++;\n            }\n        });\n        return {\n            totalTareas,\n            tareasCompletadas,\n            porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,\n            tiempoTotalEstimado: 0,\n            // Se puede calcular desde el plan\n            tiempoTotalReal,\n            semanasCompletadas,\n            totalSemanas\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas de progreso:', error);\n        return {\n            totalTareas: 0,\n            tareasCompletadas: 0,\n            porcentajeCompletado: 0,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal: 0,\n            semanasCompletadas: 0,\n            totalSemanas: 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/planificacion/services/planEstudiosService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            // Persistir sesión en el navegador\n            autoRefreshToken: true,\n            // Refrescar token automáticamente\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFFbkQ7QUFDTyxTQUFTQyxZQUFZQSxDQUFBLEVBQUc7SUFDN0IsT0FBT0Qsa0VBQW1CLENBQ3hCRSwwQ0FBb0MsRUFDcENBLGtOQUF5QyxFQUN6QztRQUNFSSxJQUFJLEVBQUU7WUFDSkMsY0FBYyxFQUFFLElBQUk7WUFBUTtZQUM1QkMsZ0JBQWdCLEVBQUUsSUFBSTtZQUFNO1lBQzVCQyxrQkFBa0IsRUFBRSxJQUFJLENBQUk7UUFDOUI7SUFDRixDQUNGLENBQUM7QUFDSDtBQUVBO0FBQ08sTUFBTUMsUUFBUSxHQUFHVCxZQUFZLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxzcmNcXGxpYlxcc3VwYWJhc2VcXGNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XG5cbi8vIENsaWVudGUgcGFyYSBlbCBuYXZlZ2Fkb3IgKGNvbXBvbmVudGVzIGRlbCBjbGllbnRlKVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUJyb3dzZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXG4gICAge1xuICAgICAgYXV0aDoge1xuICAgICAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSwgICAgICAgLy8gUGVyc2lzdGlyIHNlc2nDs24gZW4gZWwgbmF2ZWdhZG9yXG4gICAgICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsICAgICAvLyBSZWZyZXNjYXIgdG9rZW4gYXV0b23DoXRpY2FtZW50ZVxuICAgICAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUgICAgLy8gRVNFTkNJQUw6IERldGVjdGFyIHkgcHJvY2VzYXIgdG9rZW5zIGRlIFVSTFxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cblxuLy8gTWFudGVuZXIgY29tcGF0aWJpbGlkYWQgY29uIGPDs2RpZ28gZXhpc3RlbnRlXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlQ2xpZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiYXV0aCIsInBlcnNpc3RTZXNzaW9uIiwiYXV0b1JlZnJlc2hUb2tlbiIsImRldGVjdFNlc3Npb25JblVybCIsInN1cGFiYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ })

};
;
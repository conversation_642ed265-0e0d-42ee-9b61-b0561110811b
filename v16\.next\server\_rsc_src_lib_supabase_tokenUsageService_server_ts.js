"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supabase_tokenUsageService_server_ts";
exports.ids = ["_rsc_src_lib_supabase_tokenUsageService_server_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/supabase/tokenUsageService.server.ts":
/*!******************************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.server.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTokenUsageStats: () => (/* binding */ getTokenUsageStats),\n/* harmony export */   saveTokenUsageServer: () => (/* binding */ saveTokenUsageServer),\n/* harmony export */   updateUserPlanLimits: () => (/* binding */ updateUserPlanLimits)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\n\n\n/**\n * Guarda el uso de tokens en Supabase (versión servidor)\n */ async function saveTokenUsageServer(data) {\n    try {\n        console.log('🔄 saveTokenUsageServer iniciado con data:', data);\n        // Crear cliente de Supabase para el servidor\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        console.log('✅ Cliente Supabase del servidor creado');\n        // Obtener el usuario actual\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');\n        if (userError || !user) {\n            console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);\n            return;\n        }\n        const usageRecord = {\n            user_id: user.id,\n            activity_type: data.activity,\n            model_name: data.model,\n            prompt_tokens: data.usage.promptTokens,\n            completion_tokens: data.usage.completionTokens,\n            total_tokens: data.usage.totalTokens,\n            estimated_cost: data.usage.estimatedCost || 0,\n            usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n        };\n        console.log('📝 Registro a insertar:', usageRecord);\n        const { error } = await supabase.from('user_token_usage').insert([\n            usageRecord\n        ]);\n        if (error) {\n            console.error('❌ Error al guardar uso de tokens:', error);\n            return;\n        }\n        console.log('✅ Registro insertado exitosamente en user_token_usage');\n        // Validar límites antes de actualizar contador\n        const canUpdate = await validateTokenLimits(supabase, user.id, data.usage.totalTokens);\n        if (!canUpdate.allowed) {\n            console.warn('⚠️ Límite de tokens alcanzado:', canUpdate.reason);\n            // Aún así guardamos el registro para auditoría, pero marcamos el exceso\n            return;\n        }\n        // Actualizar contador mensual del usuario\n        await updateMonthlyTokenCount(supabase, user.id, data.usage.totalTokens);\n    } catch (error) {\n        console.error('❌ Error en saveTokenUsageServer:', error);\n    }\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ async function updateMonthlyTokenCount(supabase, userId, tokens) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener o crear perfil del usuario\n        let { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (profileError && profileError.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', profileError);\n            return;\n        }\n        if (!profile) {\n            // Crear perfil nuevo con límites dinámicos\n            const defaultPlan = 'free';\n            const tokenLimit = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(defaultPlan);\n            const { error: insertError } = await supabase.from('user_profiles').insert([\n                {\n                    user_id: userId,\n                    subscription_plan: defaultPlan,\n                    monthly_token_limit: tokenLimit,\n                    current_month_tokens: tokens,\n                    current_month: currentMonth,\n                    payment_verified: false\n                }\n            ]);\n            if (insertError) {\n                console.error('Error al crear perfil:', insertError);\n            } else {\n                console.log('✅ Perfil de usuario creado con límite dinámico:', tokenLimit);\n            }\n        } else {\n            // Actualizar perfil existente\n            const newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n            const { error: updateError } = await supabase.from('user_profiles').update({\n                current_month_tokens: newTokenCount,\n                current_month: currentMonth,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', userId);\n            if (updateError) {\n                console.error('Error al actualizar perfil:', updateError);\n            } else {\n                console.log('✅ Perfil de usuario actualizado');\n            }\n        }\n    } catch (error) {\n        console.error('Error en updateMonthlyTokenCount:', error);\n    }\n}\n/**\n * Valida si el usuario puede usar la cantidad de tokens especificada\n */ async function validateTokenLimits(supabase, userId, tokensToUse) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener perfil del usuario\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, monthly_token_limit, current_month_tokens, current_month, payment_verified').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                allowed: false,\n                reason: 'Perfil de usuario no encontrado'\n            };\n        }\n        // Verificar pago para planes de pago\n        if (profile.subscription_plan !== 'free' && !profile.payment_verified) {\n            return {\n                allowed: false,\n                reason: 'Pago no verificado'\n            };\n        }\n        // Calcular tokens actuales (reset si es nuevo mes)\n        let currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n        // Verificar límite\n        if (currentTokens + tokensToUse > profile.monthly_token_limit) {\n            return {\n                allowed: false,\n                reason: `Límite mensual alcanzado: ${currentTokens + tokensToUse}/${profile.monthly_token_limit}`,\n                currentUsage: {\n                    current: currentTokens,\n                    limit: profile.monthly_token_limit,\n                    requested: tokensToUse,\n                    plan: profile.subscription_plan\n                }\n            };\n        }\n        return {\n            allowed: true,\n            currentUsage: {\n                current: currentTokens,\n                limit: profile.monthly_token_limit,\n                remaining: profile.monthly_token_limit - currentTokens - tokensToUse\n            }\n        };\n    } catch (error) {\n        console.error('Error validating token limits:', error);\n        return {\n            allowed: false,\n            reason: 'Error de validación'\n        };\n    }\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario\n */ async function getTokenUsageStats(userId) {\n    try {\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        const { data: profile, error } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error || !profile) {\n            return null;\n        }\n        // Reset si es nuevo mes\n        let currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n        const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(profile.subscription_plan);\n        const percentage = profile.monthly_token_limit > 0 ? currentTokens / profile.monthly_token_limit * 100 : 0;\n        return {\n            currentMonth: {\n                used: currentTokens,\n                limit: profile.monthly_token_limit,\n                percentage: Math.round(percentage),\n                remaining: profile.monthly_token_limit - currentTokens\n            },\n            plan: {\n                name: planConfig?.name || profile.subscription_plan,\n                features: planConfig?.features || []\n            },\n            paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n        };\n    } catch (error) {\n        console.error('Error getting token usage stats:', error);\n        return null;\n    }\n}\n/**\n * Actualiza los límites de tokens cuando cambia el plan del usuario\n */ async function updateUserPlanLimits(userId, newPlan) {\n    try {\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const newTokenLimit = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(newPlan);\n        const { error } = await supabase.from('user_profiles').update({\n            subscription_plan: newPlan,\n            monthly_token_limit: newTokenLimit,\n            updated_at: new Date().toISOString()\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error updating user plan limits:', error);\n            return false;\n        }\n        console.log(`✅ Plan actualizado para usuario ${userId}: ${newPlan} (${newTokenLimit} tokens)`);\n        return true;\n    } catch (error) {\n        console.error('Error updating user plan limits:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/tokenUsageService.server.ts\n");

/***/ })

};
;
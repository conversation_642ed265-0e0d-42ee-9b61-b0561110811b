"use strict";(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},99637:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>d});var o=t(67061),n=t(79378),s=t(1852),i=t.n(s),p=t(13547),a={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>p[e]);t.d(r,a);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,17560,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],l={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4979,6096,4445],()=>t(99637));module.exports=o})();
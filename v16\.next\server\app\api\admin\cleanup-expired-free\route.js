"use strict";(()=>{var e={};e.id=3673,e.ids=[3673],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65046:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>_});var a={};t.r(a),t.d(a,{GET:()=>m,POST:()=>d});var s=t(12693),n=t(79378),o=t(26833),i=t(32644),c=t(83760),l=t(83256),p=t(28319);let u=["<EMAIL>"];async function d(e){try{console.log("\uD83E\uDDF9 Iniciando limpieza manual de cuentas expiradas");let r=(0,c.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"Usuario no autenticado"},{status:401});if(!t.email||!u.includes(t.email))return console.log(`❌ Acceso denegado para usuario: ${t.email}`),i.NextResponse.json({error:"Acceso denegado. Solo administradores pueden ejecutar esta acci\xf3n."},{status:403});console.log(`👤 Administrador autorizado: ${t.email}`);let{dryRun:s=!1,maxAccounts:n=100}=await e.json().catch(()=>({}));s&&console.log("\uD83D\uDD0D Ejecutando en modo dry-run (solo consulta)");let o=new Date().toISOString(),{data:d,error:m}=await p.E.from("user_profiles").select(`
        user_id, 
        id, 
        plan_expires_at, 
        subscription_plan,
        security_flags,
        created_at
      `).eq("subscription_plan","free").lt("plan_expires_at",o).eq("payment_verified",!0).limit(n);if(m)throw Error(`Error buscando cuentas expiradas: ${m.message}`);if(!d||0===d.length)return console.log("✅ No se encontraron cuentas expiradas"),i.NextResponse.json({success:!0,message:"No hay cuentas expiradas para limpiar",found:0,cleaned:0,errors:[]});console.log(`📊 Encontradas ${d.length} cuentas expiradas`);let x=d.map(e=>({userId:e.user_id,profileId:e.id,expiredAt:e.plan_expires_at,createdAt:e.created_at,daysSinceExpiration:Math.floor((new Date().getTime()-new Date(e.plan_expires_at).getTime())/864e5),securityFlags:e.security_flags}));if(s)return i.NextResponse.json({success:!0,dryRun:!0,message:`Se encontraron ${d.length} cuentas para limpiar`,found:d.length,accounts:x,wouldClean:d.length});let g=await l.FreeAccountService.cleanupExpiredAccounts();try{await p.E.from("feature_access_log").insert({user_id:t.id,feature_name:"admin_cleanup_expired_accounts",access_granted:!0,plan_at_time:"admin",tokens_used:0,metadata:{action:"cleanup_expired_free_accounts",found:d.length,cleaned:g.cleaned,errors_count:g.errors.length,executed_at:new Date().toISOString(),admin_email:t.email}})}catch(e){console.error("⚠️ Error registrando auditor\xeda:",e)}return console.log(`✅ Limpieza completada: ${g.cleaned} cuentas procesadas`),i.NextResponse.json({success:!0,message:"Limpieza completada exitosamente",found:d.length,cleaned:g.cleaned,errors:g.errors,accounts:x,summary:{totalFound:d.length,successfullyCleaned:g.cleaned,errorsCount:g.errors.length,executedBy:t.email,executedAt:new Date().toISOString()}})}catch(e){return console.error("❌ Error cr\xedtico en limpieza de cuentas:",e),i.NextResponse.json({error:"Error interno del servidor",details:void 0},{status:500})}}async function m(e){try{console.log("\uD83D\uDCCA Consultando estad\xedsticas de cuentas gratuitas");let r=(0,c.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t||!t.email||!u.includes(t.email))return i.NextResponse.json({error:"Acceso denegado"},{status:403});let s=new Date().toISOString(),{data:n,error:o}=await p.E.from("user_profiles").select("user_id, plan_expires_at, created_at").eq("subscription_plan","free").eq("payment_verified",!0).gt("plan_expires_at",s),{data:l,error:d}=await p.E.from("user_profiles").select("user_id, plan_expires_at, created_at").eq("subscription_plan","free").eq("payment_verified",!0).lt("plan_expires_at",s),{data:m,error:x}=await p.E.from("user_profiles").select("user_id, plan_expires_at, created_at").eq("subscription_plan","free").eq("payment_verified",!1);if(o||d||x)throw Error("Error obteniendo estad\xedsticas");let g={active:n?.length||0,expired:l?.length||0,disabled:m?.length||0,total:(n?.length||0)+(l?.length||0)+(m?.length||0)},_=new Date;_.setDate(_.getDate()+1);let f=n?.filter(e=>new Date(e.plan_expires_at)<=_)||[];return i.NextResponse.json({success:!0,statistics:g,expiringSoon:{count:f.length,accounts:f.map(e=>({userId:e.user_id,expiresAt:e.plan_expires_at,hoursRemaining:Math.ceil((new Date(e.plan_expires_at).getTime()-new Date().getTime())/36e5)}))},lastUpdated:new Date().toISOString()})}catch(e){return console.error("❌ Error obteniendo estad\xedsticas:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/cleanup-expired-free/route",pathname:"/api/admin/cleanup-expired-free",filename:"route",bundlePath:"app/api/admin/cleanup-expired-free/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:_,serverHooks:f}=x;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:_})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4979,8082,1370,3760,8844],()=>t(65046));module.exports=a})();
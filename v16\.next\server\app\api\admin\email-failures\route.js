"use strict";(()=>{var e={};e.id=5770,e.ids=[5770],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83783:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>I,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>p});var a=t(12693),i=t(79378),o=t(26833),n=t(32644),c=t(83760),d=t(72280);let u=["<EMAIL>"];async function l(e){try{let r=(0,c.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t||!t.email||!u.includes(t.email))return n.NextResponse.json({error:"Acceso denegado. Solo administradores pueden ver estas estad\xedsticas."},{status:403});let{searchParams:a}=new URL(e.url),i=a.get("startDate"),o=a.get("endDate"),l=i||new Date(Date.now()-6048e5).toISOString(),p=o||new Date().toISOString();console.log(`📊 Obteniendo estad\xedsticas de fallos desde ${l} hasta ${p}`);let m=await d.X.getFailureStats(l,p);return n.NextResponse.json({success:!0,data:{period:{startDate:l,endDate:p},failures:m,recommendations:function(e){let r=[];return e.failureRate>10&&r.push("⚠️ Alta tasa de fallos (>10%). Revisar configuraci\xf3n del proveedor de email."),e.errorsByType["Network Error"]>0&&r.push("\uD83C\uDF10 Errores de red detectados. Verificar conectividad y timeouts."),e.errorsByType["Rate Limit"]>0&&r.push("\uD83D\uDEA6 L\xedmites de tasa alcanzados. Considerar espaciar m\xe1s los env\xedos."),e.errorsByType["Authentication Error"]>0&&r.push("\uD83D\uDD11 Errores de autenticaci\xf3n. Verificar API keys del proveedor."),e.errorsByType["Invalid Email"]>0&&r.push("\uD83D\uDCE7 Emails inv\xe1lidos detectados. Implementar validaci\xf3n m\xe1s estricta."),e.errorsByType["Email Bounced"]>0&&r.push("↩️ Emails rebotados. Considerar lista de supresi\xf3n autom\xe1tica."),0===e.totalFailures&&r.push("✅ No se detectaron fallos recientes. Sistema funcionando correctamente."),r}(m)},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error obteniendo estad\xedsticas de fallos:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}async function p(e){try{let r=(0,c.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t||!t.email||!u.includes(t.email))return n.NextResponse.json({error:"Acceso denegado. Solo administradores pueden ejecutar esta acci\xf3n."},{status:403});let a=await e.json().catch(()=>({})),i=a.maxAge||24,o=a.limit||10;console.log(`🔄 Iniciando reintentos de notificaciones fallidas (maxAge: ${i}h, limit: ${o})`);let l=await d.X.retryFailedNotifications(i,o);return n.NextResponse.json({success:!0,message:"Reintentos de notificaciones completados",result:{attempted:l.attempted,successful:l.successful,failed:l.failed,successRate:l.attempted>0?`${Math.round(l.successful/l.attempted*100)}%`:"0%",errors:l.errors},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error en reintentos de notificaciones:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email-failures/route",pathname:"/api/admin/email-failures",filename:"route",bundlePath:"app/api/admin/email-failures/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:I}=m;function g(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4979,8082,1370,3760,8444],()=>t(83783));module.exports=s})();
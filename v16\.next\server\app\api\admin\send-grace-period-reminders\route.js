"use strict";(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},25123:(e,r,o)=>{o.r(r),o.d(r,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>_,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>I});var t={};o.r(t),o.d(t,{GET:()=>g,POST:()=>m});var s=o(12693),i=o(79378),a=o(26833),n=o(32644),c=o(83760),d=o(28319),u=o(72280);let l=["<EMAIL>"],p=process.env.CRON_SECRET;async function m(e){try{console.log("\uD83D\uDD04 Iniciando env\xedo de recordatorios de per\xedodo de gracia");let r=e.headers.get("authorization");if(p&&r!==`Bearer ${p}`){let r=(0,c.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:o},error:t}=await r.auth.getUser();if(t||!o||!o.email||!l.includes(o.email))return console.log(`❌ Acceso denegado para usuario: ${o?.email||"an\xf3nimo"}`),n.NextResponse.json({error:"Acceso denegado. Solo administradores pueden ejecutar esta acci\xf3n."},{status:403});console.log(`👤 Administrador autorizado: ${o.email}`)}else p&&console.log("\uD83E\uDD16 Ejecutado por cron job autorizado");let o=(await e.json().catch(()=>({}))).hoursBeforeExpiration||24,t=new Date;t.setHours(t.getHours()+o),console.log(`📅 Buscando usuarios con per\xedodo de gracia que expire en las pr\xf3ximas ${o} horas`);let{data:s,error:i}=await d.E.from("user_profiles").select("user_id, subscription_plan, plan_expires_at, security_flags").eq("auto_renew",!1).neq("subscription_plan","free").gt("plan_expires_at",new Date().toISOString()).lt("plan_expires_at",t.toISOString()).limit(100);if(i)throw Error(`Error buscando usuarios para recordatorio: ${i.message}`);if(!s||0===s.length)return console.log("✅ No se encontraron usuarios que necesiten recordatorio"),n.NextResponse.json({success:!0,message:"No hay usuarios que necesiten recordatorio en este momento",result:{sent:0,errors:[],hoursBeforeExpiration:o}});let a=s.filter(e=>e.security_flags?.subscription_cancelled===!0);console.log(`📋 Encontrados ${a.length} usuarios en per\xedodo de gracia que necesitan recordatorio`);let m=[],g=0;for(let e of a)try{let{data:r}=await d.E.auth.admin.getUserById(e.user_id);if(!r.user?.email){console.log(`⚠️ Usuario ${e.user_id} no tiene email, omitiendo`);continue}let o=r.user.user_metadata?.name||r.user.email.split("@")[0],t="usuario"===e.subscription_plan?"Usuario":"Pro",{data:s}=await d.E.from("email_notifications").select("sent_at").eq("recipient_email",r.user.email).eq("type","grace_period_ending").gte("sent_at",new Date(Date.now()-432e5).toISOString()).single();if(s){console.log(`⏭️ Recordatorio ya enviado recientemente a ${r.user.email}, omitiendo`);continue}if(await u.X.sendGracePeriodEndingNotification(r.user.email,o,t,e.plan_expires_at,e.user_id))g++,console.log(`✅ Recordatorio enviado a: ${r.user.email}`);else{let e=`Error enviando recordatorio a ${r.user.email}`;console.error(e),m.push(e)}}catch(o){let r=`Error procesando usuario ${e.user_id}: ${o instanceof Error?o.message:"Unknown error"}`;console.error(r),m.push(r)}return console.log(`🎯 Recordatorios completados: ${g} enviados, ${m.length} errores`),n.NextResponse.json({success:!0,message:"Env\xedo de recordatorios completado",result:{sent:g,errors:m,hoursBeforeExpiration:o,usersFound:a.length},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error en env\xedo de recordatorios:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido",timestamp:new Date().toISOString()},{status:500})}}async function g(e){try{let r=(0,c.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:o},error:t}=await r.auth.getUser();if(t||!o||!o.email||!l.includes(o.email))return n.NextResponse.json({error:"Acceso denegado"},{status:403});let{data:s,error:i}=await d.E.from("email_notifications").select("sent_at, recipient_email").eq("type","grace_period_ending").gte("sent_at",new Date(Date.now()-6048e5).toISOString()).order("sent_at",{ascending:!1});if(i)throw Error("Error obteniendo estad\xedsticas de recordatorios");let a=(s||[]).reduce((e,r)=>{let o=r.sent_at.split("T")[0];return e[o]=(e[o]||0)+1,e},{});return n.NextResponse.json({success:!0,statistics:{totalRemindersLast7Days:s?.length||0,remindersByDay:a,lastReminders:(s||[]).slice(0,10).map(e=>({email:e.recipient_email,sentAt:e.sent_at}))},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error obteniendo estad\xedsticas de recordatorios:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/send-grace-period-reminders/route",pathname:"/api/admin/send-grace-period-reminders",filename:"route",bundlePath:"app/api/admin/send-grace-period-reminders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:h,workUnitAsyncStorage:I,serverHooks:_}=x;function f(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:I})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[4979,8082,1370,3760,8444],()=>o(25123));module.exports=t})();
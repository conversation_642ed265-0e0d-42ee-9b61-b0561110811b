"use strict";(()=>{var e={};e.id=5042,e.ids=[5042],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51753:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(12693),n=r(79378),i=r(26833),o=r(32644),u=r(83760),p=r(83256);async function c(e){try{console.log("\uD83D\uDD0D Verificando estado de cuenta gratuita");let t=(0,u.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Usuario no autenticado"},{status:401});console.log("\uD83D\uDC64 Usuario autenticado:",r.id);let a=await p.FreeAccountService.getFreeAccountStatus(r.id);if(!a)return console.log("\uD83D\uDCCA Usuario no tiene cuenta gratuita - devolviendo isFreeAccount: false"),o.NextResponse.json({success:!0,isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan"});console.log("\uD83D\uDCCA Estado de cuenta gratuita obtenido:",{isActive:a.isActive,daysRemaining:a.daysRemaining,expiresAt:a.expiresAt});let n=new Date;if(!a.expiresAt)return console.error("❌ status.expiresAt es null, no se puede calcular la informaci\xf3n adicional."),o.NextResponse.json({error:"Error interno: Faltan datos de expiraci\xf3n de la cuenta gratuita."},{status:500});let i=new Date(a.expiresAt),c=n.getTime()-(i.getTime()-432e6),d=Math.min(100,Math.max(0,c/432e6*100)),l=[],g=[];a.isActive?a.daysRemaining<=1?(l.push({type:"warning",message:`Tu cuenta expira en ${a.hoursRemaining} horas`,action:"upgrade"}),g.push("Considera actualizar tu plan antes de que expire")):a.daysRemaining<=2&&l.push({type:"info",message:`Tu cuenta expira en ${a.daysRemaining} d\xedas`,action:"reminder"}):(l.push({type:"error",message:"Tu cuenta gratuita ha expirado",action:"upgrade"}),g.push("Actualiza a un plan premium para continuar usando OposiAI"));let m=[];return a.usageCount&&a.limits?Object.entries(a.usageCount).forEach(([e,t])=>{let r="number"==typeof t?t:0,s=a.limits[e],n="number"==typeof s&&s>0?s:1,i=r/n*100;i>=100?m.push({feature:e,message:`Has alcanzado el l\xedmite de ${e} (${r}/${n})`,severity:"error"}):i>=80&&m.push({feature:e,message:`Cerca del l\xedmite de ${e} (${r}/${n})`,severity:"warning"})}):console.warn("⚠️ No se pudo generar usageWarnings porque status.usageCount o status.limits no est\xe1n definidos."),m.length>0&&g.push("Considera actualizar tu plan para obtener m\xe1s recursos"),o.NextResponse.json({success:!0,isFreeAccount:!0,status:{isActive:a.isActive,expiresAt:a.expiresAt,daysRemaining:a.daysRemaining,hoursRemaining:a.hoursRemaining,progressPercentage:Math.round(d),usageCount:a.usageCount||{},limits:a.limits||{},usagePercentages:a.usageCount&&a.limits?Object.entries(a.usageCount).reduce((e,[t,r])=>{let s=a.limits[t];return e[t]=Math.round(("number"==typeof r?r:0)/("number"==typeof s&&s>0?s:1)*100),e},{}):{}},alerts:l,usageWarnings:m,recommendations:g,upgradeUrl:"/upgrade-plan"})}catch(e){return console.error("❌ Error verificando estado de cuenta gratuita:",e),o.NextResponse.json({error:"Error interno del servidor",details:void 0},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/free-account-status/route",pathname:"/api/auth/free-account-status",filename:"route",bundlePath:"app/api/auth/free-account-status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=d;function x(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4979,8082,1370,3760,8844],()=>r(51753));module.exports=s})();
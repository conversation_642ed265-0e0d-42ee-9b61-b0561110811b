/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/free-account-status/route";
exports.ids = ["app/api/auth/free-account-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_free_account_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/free-account-status/route.ts */ \"(rsc)/./src/app/api/auth/free-account-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/free-account-status/route\",\n        pathname: \"/api/auth/free-account-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/free-account-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\auth\\\\free-account-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_free_account_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/free-account-status/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/auth/free-account-status/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/freeAccountService */ \"(rsc)/./src/lib/services/freeAccountService.ts\");\n// src/app/api/auth/free-account-status/route.ts\n// Endpoint para verificar estado de cuentas gratuitas\n\n\n\n// Interfaces para tipado\nasync function GET(request) {\n    try {\n        console.log('🔍 Verificando estado de cuenta gratuita');\n        // 1. Crear cliente de Supabase para verificar autenticación\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll () {\n                // No necesitamos setear cookies en este endpoint\n                }\n            }\n        });\n        // 2. Verificar autenticación\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Usuario no autenticado'\n            }, {\n                status: 401\n            });\n        }\n        console.log('👤 Usuario autenticado:', user.id);\n        // 3. Obtener estado de la cuenta gratuita\n        const status = await _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_2__.FreeAccountService.getFreeAccountStatus(user.id);\n        if (!status) {\n            // Usuario no tiene una cuenta gratuita activa o no se encontró perfil de cuenta gratuita\n            console.log('📊 Usuario no tiene cuenta gratuita - devolviendo isFreeAccount: false');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                // La petición se procesó correctamente, el resultado es que no es free\n                isFreeAccount: false,\n                status: null,\n                alerts: [],\n                usageWarnings: [],\n                recommendations: [],\n                upgradeUrl: '/upgrade-plan'\n            });\n        }\n        console.log('📊 Estado de cuenta gratuita obtenido:', {\n            isActive: status.isActive,\n            daysRemaining: status.daysRemaining,\n            expiresAt: status.expiresAt\n        });\n        // 4. Calcular información adicional\n        const now = new Date();\n        // Verificar que expiresAt no sea null\n        if (!status.expiresAt) {\n            console.error(\"❌ status.expiresAt es null, no se puede calcular la información adicional.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error interno: Faltan datos de expiración de la cuenta gratuita.'\n            }, {\n                status: 500\n            });\n        }\n        const expiresAtDate = new Date(status.expiresAt);\n        const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms\n        const timeElapsed = now.getTime() - (expiresAtDate.getTime() - totalDuration);\n        const progressPercentage = Math.min(100, Math.max(0, timeElapsed / totalDuration * 100));\n        // 5. Determinar alertas y recomendaciones\n        const alerts = [];\n        const recommendations = [];\n        if (!status.isActive) {\n            alerts.push({\n                type: 'error',\n                message: 'Tu cuenta gratuita ha expirado',\n                action: 'upgrade'\n            });\n            recommendations.push('Actualiza a un plan premium para continuar usando OposiAI');\n        } else if (status.daysRemaining <= 1) {\n            alerts.push({\n                type: 'warning',\n                message: `Tu cuenta expira en ${status.hoursRemaining} horas`,\n                action: 'upgrade'\n            });\n            recommendations.push('Considera actualizar tu plan antes de que expire');\n        } else if (status.daysRemaining <= 2) {\n            alerts.push({\n                type: 'info',\n                message: `Tu cuenta expira en ${status.daysRemaining} días`,\n                action: 'reminder'\n            });\n        }\n        // Verificar límites de uso\n        const usageWarnings = [];\n        // Asegurar que usageCount y limits existen\n        if (status.usageCount && status.limits) {\n            Object.entries(status.usageCount).forEach(([feature, used])=>{\n                // Asegurar que 'used' es un número\n                const usedAmount = typeof used === 'number' ? used : 0;\n                // Asegurar que limit no es undefined\n                const limit = status.limits[feature];\n                const limitAmount = typeof limit === 'number' && limit > 0 ? limit : 1; // Evitar división por cero\n                const percentage = usedAmount / limitAmount * 100;\n                if (percentage >= 100) {\n                    usageWarnings.push({\n                        feature,\n                        message: `Has alcanzado el límite de ${feature} (${usedAmount}/${limitAmount})`,\n                        severity: 'error'\n                    });\n                } else if (percentage >= 80) {\n                    usageWarnings.push({\n                        feature,\n                        message: `Cerca del límite de ${feature} (${usedAmount}/${limitAmount})`,\n                        severity: 'warning'\n                    });\n                }\n            });\n        } else {\n            console.warn(\"⚠️ No se pudo generar usageWarnings porque status.usageCount o status.limits no están definidos.\");\n        }\n        if (usageWarnings.length > 0) {\n            recommendations.push('Considera actualizar tu plan para obtener más recursos');\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            isFreeAccount: true,\n            status: {\n                isActive: status.isActive,\n                expiresAt: status.expiresAt,\n                daysRemaining: status.daysRemaining,\n                hoursRemaining: status.hoursRemaining,\n                progressPercentage: Math.round(progressPercentage),\n                usageCount: status.usageCount || {},\n                limits: status.limits || {},\n                usagePercentages: status.usageCount && status.limits ? Object.entries(status.usageCount).reduce((acc, [key, value])=>{\n                    const limit = status.limits[key];\n                    // Asegurar que limit no es undefined y no es 0\n                    const limitAmount = typeof limit === 'number' && limit > 0 ? limit : 1; // Evitar división por cero\n                    const usedAmount = typeof value === 'number' ? value : 0;\n                    acc[key] = Math.round(usedAmount / limitAmount * 100);\n                    return acc;\n                }, {}) : {} // Enviar objeto vacío si no hay datos\n            },\n            alerts,\n            usageWarnings,\n            recommendations,\n            upgradeUrl: '/upgrade-plan'\n        });\n    } catch (error) {\n        console.error('❌ Error verificando estado de cuenta gratuita:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor',\n            details:  true ? error instanceof Error ? error.message : 'Error desconocido' : 0\n        }, {\n            status: 500\n        });\n    }\n} // NOTA: El endpoint POST ha sido eliminado porque el incremento de uso\n // ahora se hace automáticamente en el backend (api/ai/route.ts y api/document/upload)\n // después de operaciones exitosas. Ya no es necesario que el frontend\n // haga llamadas POST para incrementar contadores.\n //\n // El endpoint GET sigue siendo necesario para obtener el estado de la cuenta gratuita.\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/free-account-status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/freeAccountService.ts":
/*!************************************************!*\
  !*** ./src/lib/services/freeAccountService.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FreeAccountService: () => (/* binding */ FreeAccountService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/services/freeAccountService.ts\n// Servicio para gestión automatizada de cuentas gratuitas\n\n\n// FreeAccountStatus y otras interfaces/clases no cambian\nclass FreeAccountService {\n    /**\n   * Crear cuenta gratuita automáticamente\n   * Ahora crea el usuario directamente y genera un enlace de tipo 'recovery' para establecer la contraseña.\n   */ static async createFreeAccount(request) {\n        try {\n            console.log('🆓 Iniciando creación de cuenta gratuita (flujo de invitación):', request.email);\n            // 1. Validar que el email no esté ya registrado\n            try {\n                const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                if (existingUser) {\n                    return {\n                        success: false,\n                        error: 'El email ya está registrado en el sistema.'\n                    };\n                }\n            } catch (error) {\n                if (error instanceof Error && error.message.toLowerCase().includes('user not found')) {\n                    console.log('Usuario no existe en Auth, continuando con la invitación.');\n                } else {\n                    console.warn('Advertencia al verificar usuario existente, se continuará con el intento de invitación:', error);\n                }\n            }\n            // 2. Calcular fecha de expiración (5 días desde ahora)\n            const expiresAt = new Date();\n            expiresAt.setDate(expiresAt.getDate() + 5);\n            // 3. Preparar metadatos para el usuario invitado\n            const userDataForCreation = {\n                name: request.name || request.email.split('@')[0],\n                plan: 'free',\n                free_account: true,\n                expires_at: expiresAt.toISOString(),\n                created_via: 'free_invitation_flow',\n                registration_type: 'automatic_free_invitation',\n                requires_password_setup: true\n            };\n            console.log('📧 Invitando nuevo usuario con datos:', {\n                email: request.email,\n                userData: userDataForCreation,\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirm-invitation`\n            });\n            // 4. Invitar al usuario. Esto crea el usuario y envía el email de invitación.\n            const { data: { user: newUser }, error: inviteError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.inviteUserByEmail(request.email, {\n                data: userDataForCreation,\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirm-invitation`\n            });\n            if (inviteError) {\n                console.error('❌ Error invitando al usuario:', inviteError);\n                if (inviteError.message.includes('User already registered')) {\n                    return {\n                        success: false,\n                        error: 'Ya existe una cuenta con este email.'\n                    };\n                }\n                throw new Error(`Error invitando al usuario: ${inviteError.message}`);\n            }\n            if (!newUser) {\n                throw new Error('Usuario no devuelto después de la invitación.');\n            }\n            console.log('✅ Usuario invitado exitosamente a Supabase Auth:', newUser.id);\n            // 5. Crear perfil de usuario y registrar historial de forma atómica\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            if (!planConfig) {\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup\n                throw new Error('Configuración de plan gratuito no encontrada');\n            }\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const profileDataForRPC = {\n                subscription_plan: 'free',\n                monthly_token_limit: (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)('free'),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                stripe_customer_id: null,\n                stripe_subscription_id: null,\n                last_payment_date: null,\n                auto_renew: false,\n                plan_expires_at: expiresAt.toISOString(),\n                plan_features: planConfig.features,\n                security_flags: {\n                    created_via_free_invitation_flow: true,\n                    free_account: true,\n                    expires_at: expiresAt.toISOString(),\n                    activation_date: new Date().toISOString(),\n                    usage_count: {\n                        documents: 0,\n                        tests: 0,\n                        flashcards: 0,\n                        mindMaps: 0,\n                        tokens: 0\n                    }\n                }\n            };\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: newUser.id,\n                p_transaction_id: null,\n                p_profile_data: profileDataForRPC\n            }).single();\n            if (rpcError) {\n                console.error('❌ Error al ejecutar la función RPC create_user_profile_and_history:', rpcError);\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);\n            console.log('🎉 Cuenta gratuita creada exitosamente con flujo de invitación.');\n            return {\n                success: true,\n                userId: newUser.id,\n                profileId: profileId,\n                expiresAt: expiresAt.toISOString()\n            };\n        } catch (error) {\n            console.error('❌ Error crítico en la creación de cuenta gratuita:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido al crear cuenta gratuita'\n            };\n        }\n    }\n    // Las funciones getFreeAccountStatus, incrementUsageCount, canPerformAction, cleanupExpiredAccounts\n    // permanecen igual que antes, ya que su lógica no depende directamente de cómo se creó el usuario,\n    // sino de los datos en user_profiles.\n    /**\n   * Verificar estado de cuenta gratuita\n   */ static async getFreeAccountStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return null;\n            }\n            const now = new Date();\n            const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;\n            if (!expiresAt) {\n                return null;\n            }\n            const isActive = now < expiresAt;\n            const timeDiff = expiresAt.getTime() - now.getTime();\n            const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n            const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));\n            const usageCount = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0\n            };\n            usageCount.tokens = profile.current_month_tokens || 0;\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            const limits = {\n                documents: planConfig?.limits.documents || 1,\n                tests: planConfig?.limits.testsForTrial || 10,\n                flashcards: planConfig?.limits.flashcardsForTrial || 10,\n                mindMaps: planConfig?.limits.mindMapsForTrial || 2,\n                tokens: planConfig?.limits.tokensForTrial || 50000\n            };\n            // Calcular progressPercentage\n            const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms\n            const creationDate = new Date(profile.created_at || Date.now()); // Usar created_at o now si no está\n            const activationDate = profile.security_flags?.activation_date ? new Date(profile.security_flags.activation_date) : creationDate;\n            const timeElapsed = now.getTime() - activationDate.getTime();\n            const progressPercentage = Math.min(100, Math.max(0, timeElapsed / totalDuration * 100));\n            return {\n                isActive,\n                expiresAt: expiresAt.toISOString(),\n                daysRemaining,\n                hoursRemaining,\n                usageCount,\n                limits,\n                // @ts-ignore Asegurar que progressPercentage está en el tipo si es necesario\n                progressPercentage: Math.round(progressPercentage)\n            };\n        } catch (error) {\n            console.error('Error obteniendo estado de cuenta gratuita:', error);\n            return null;\n        }\n    }\n    /**\n   * Incrementar contador de uso\n   */ static async incrementUsageCount(userId, feature, amount = 1) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return false;\n            }\n            const currentUsage = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0\n            };\n            currentUsage[feature] = (currentUsage[feature] || 0) + amount;\n            const updateData = {\n                security_flags: _objectSpread(_objectSpread({}, profile.security_flags), {}, {\n                    usage_count: currentUsage\n                }),\n                updated_at: new Date().toISOString()\n            };\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update(updateData).eq('user_id', userId);\n            return true;\n        } catch (error) {\n            console.error('Error incrementando contador de uso:', error);\n            return false;\n        }\n    }\n    /**\n   * Verificar si se puede realizar una acción\n   */ static async canPerformAction(userId, feature, amount = 1) {\n        try {\n            const status = await this.getFreeAccountStatus(userId);\n            if (!status) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta no encontrada o no es gratuita'\n                };\n            }\n            if (!status.isActive) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta gratuita expirada'\n                };\n            }\n            const currentUsage = status.usageCount[feature] || 0;\n            const limit = status.limits[feature];\n            const remaining = limit - currentUsage;\n            if (currentUsage + amount > limit) {\n                return {\n                    allowed: false,\n                    reason: `Límite de ${feature} alcanzado (${currentUsage}/${limit})`,\n                    remaining: Math.max(0, remaining)\n                };\n            }\n            return {\n                allowed: true,\n                remaining: remaining - amount\n            };\n        } catch (error) {\n            console.error('Error verificando acción:', error);\n            return {\n                allowed: false,\n                reason: 'Error interno'\n            };\n        }\n    }\n    /**\n   * Limpiar cuentas gratuitas expiradas\n   */ static async cleanupExpiredAccounts() {\n        try {\n            console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');\n            const now = new Date().toISOString();\n            const { data: expiredProfiles, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, id').eq('subscription_plan', 'free').lt('plan_expires_at', now)// Añadir condición para asegurar que no se desactiven cuentas ya deshabilitadas\n            .neq('security_flags ->> account_disabled', 'true');\n            if (error) {\n                throw new Error(`Error buscando cuentas expiradas: ${error.message}`);\n            }\n            if (!expiredProfiles || expiredProfiles.length === 0) {\n                console.log('✅ No hay cuentas expiradas para limpiar');\n                return {\n                    cleaned: 0,\n                    errors: []\n                };\n            }\n            console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas para procesar`);\n            const errors = [];\n            let cleaned = 0;\n            for (const profile of expiredProfiles){\n                try {\n                    // Desactivar usuario en auth\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.updateUserById(profile.user_id, {\n                        user_metadata: {\n                            account_disabled: true,\n                            disabled_reason: 'free_account_expired'\n                        }\n                    });\n                    // Marcar perfil como inactivo\n                    // Obtener security_flags existentes para no sobrescribirlas\n                    const { data: currentProfileData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('security_flags').eq('user_id', profile.user_id).single();\n                    const existingFlags = currentProfileData?.security_flags || {};\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: false,\n                        // Marcar pago como no verificado\n                        security_flags: _objectSpread(_objectSpread({}, existingFlags), {}, {\n                            // Mantener flags existentes\n                            account_disabled: true,\n                            disabled_at: new Date().toISOString(),\n                            disabled_reason: 'free_account_expired'\n                        })\n                    }).eq('user_id', profile.user_id);\n                    cleaned++;\n                } catch (cleanupError) {\n                    const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);\n            return {\n                cleaned,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en limpieza de cuentas:', error);\n            return {\n                cleaned: 0,\n                errors: [\n                    error instanceof Error ? error.message : 'Error desconocido'\n                ]\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/freeAccountService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                // Requerido para generateLink\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: _objectSpread(_objectSpread({}, user.user_metadata), {}, {\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                })\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2FkbWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBRW9EO0FBRXBEO0FBQ08sTUFBTUMsYUFBYSxHQUFHRCxtRUFBWSxDQUN2Q0UsMENBQW9DLEVBQ3BDQSxPQUFPLENBQUNDLEdBQUcsQ0FBQ0UseUJBQXlCLEVBQ3JDO0lBQ0VDLElBQUksRUFBRTtRQUNKQyxnQkFBZ0IsRUFBRSxLQUFLO1FBQ3ZCQyxjQUFjLEVBQUU7SUFDbEI7QUFDRixDQUNGLENBQUM7QUFFRDtBQTREQTtBQUNPLE1BQU1DLG9CQUFvQixDQUFDO0lBRWhDO0lBQ0EsYUFBYUMsdUJBQXVCQSxDQUFDQyxXQUF5RCxFQUE4QjtRQUMxSCxNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBQUEsRUFBTyxHQUFHLE1BQU1aLGFBQWEsQ0FDeENhLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUMzQkMsTUFBTSxDQUFDO1lBQUNKLFdBQVc7U0FBQyxDQUFDLENBQ3JCSyxNQUFNLENBQUMsQ0FBQyxDQUNSQyxNQUFNLENBQUMsQ0FBQztRQUVYLElBQUlKLEtBQUssRUFBRTtZQUNUSyxPQUFPLENBQUNMLEtBQUssQ0FBQyxvQ0FBb0MsRUFBRUEsS0FBSyxDQUFDO1lBQzFELE1BQU0sSUFBSU0sS0FBSyxDQUFFLGlDQUFnQ04sS0FBSyxDQUFDTyxPQUFRLEVBQUMsQ0FBQztRQUNuRTtRQUVBLE9BQU9SLElBQUk7SUFDYjtJQUVBO0lBQ0EsYUFBYVMseUJBQXlCQSxDQUFDQyxTQUFpQixFQUFxQztRQUMzRixNQUFNLEVBQUVWLElBQUksRUFBRUMsS0FBQUEsRUFBTyxHQUFHLE1BQU1aLGFBQWEsQ0FDeENhLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUMzQkUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUNYTyxFQUFFLENBQUMsbUJBQW1CLEVBQUVELFNBQVMsQ0FBQyxDQUNsQ0wsTUFBTSxDQUFDLENBQUM7UUFFWCxJQUFJSixLQUFLLElBQUlBLEtBQUssQ0FBQ1csSUFBSSxLQUFLLFVBQVUsRUFBRTtZQUN0Q04sT0FBTyxDQUFDTCxLQUFLLENBQUMsNkJBQTZCLEVBQUVBLEtBQUssQ0FBQztZQUNuRCxNQUFNLElBQUlNLEtBQUssQ0FBRSxnQ0FBK0JOLEtBQUssQ0FBQ08sT0FBUSxFQUFDLENBQUM7UUFDbEU7UUFFQSxPQUFPUixJQUFJO0lBQ2I7SUFFQTtJQUNBLGFBQWFhLHdCQUF3QkEsQ0FBQ0MsS0FBYSxFQUFFQyxRQUFhLEVBQUU7UUFDbEVULE9BQU8sQ0FBQ1UsR0FBRyxDQUFDLCtDQUErQyxFQUFFO1lBQzNERixLQUFLO1lBQ0xDLFFBQVE7WUFDUkUsVUFBVSxFQUFHLEdBQUUzQix1QkFBZ0MsZ0JBQWU7WUFDOUQ2QixTQUFTLEVBQUUsSUFBSUMsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDO1FBQ3BDLENBQUMsQ0FBQztRQUVGLE1BQU0sRUFBRXJCLElBQUksRUFBRUMsS0FBQUEsRUFBTyxHQUFHLE1BQU1aLGFBQWEsQ0FBQ0ssSUFBSSxDQUFDNEIsS0FBSyxDQUFDQyxpQkFBaUIsQ0FDdEVULEtBQUssRUFDTDtZQUNFZCxJQUFJLEVBQUVlLFFBQVE7WUFDZEUsVUFBVSxFQUFHLEdBQUUzQix1QkFBZ0M7UUFDakQsQ0FDRixDQUFDO1FBRURnQixPQUFPLENBQUNVLEdBQUcsQ0FBQyx3Q0FBd0MsRUFBRTtZQUNwRFEsT0FBTyxFQUFFLENBQUMsQ0FBQ3hCLElBQUk7WUFDZnlCLE9BQU8sRUFBRSxDQUFDLENBQUN6QixJQUFJLEVBQUUwQixJQUFJO1lBQ3JCQyxNQUFNLEVBQUUzQixJQUFJLEVBQUUwQixJQUFJLEVBQUVFLEVBQUU7WUFDdEJDLFNBQVMsRUFBRTdCLElBQUksRUFBRTBCLElBQUksRUFBRVosS0FBSztZQUM1QmdCLE9BQU8sRUFBRTlCLElBQUksRUFBRTBCLElBQUksRUFBRUssR0FBRztZQUN4QkMsUUFBUSxFQUFFaEMsSUFBSSxFQUFFMEIsSUFBSSxFQUFFTyxJQUFJO1lBQzFCQyxjQUFjLEVBQUVsQyxJQUFJLEVBQUUwQixJQUFJLEVBQUVTLGtCQUFrQjtZQUM5Q0MsWUFBWSxFQUFFcEMsSUFBSSxFQUFFMEIsSUFBSSxFQUFFVyxhQUFhO1lBQ3ZDQyxXQUFXLEVBQUV0QyxJQUFJLEVBQUUwQixJQUFJLEVBQUVhLFlBQVk7WUFDckN0QyxLQUFLLEVBQUVBLEtBQUssRUFBRU8sT0FBTztZQUNyQmdDLFNBQVMsRUFBRXZDLEtBQUssRUFBRXdDLE1BQU07WUFDeEJDLFNBQVMsRUFBRXpDO1FBQ2IsQ0FBQyxDQUFDO1FBRUYsSUFBSUEsS0FBSyxFQUFFO1lBQ1RLLE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLG9EQUFvRCxFQUFFO2dCQUNsRU8sT0FBTyxFQUFFUCxLQUFLLENBQUNPLE9BQU87Z0JBQ3RCaUMsTUFBTSxFQUFFeEMsS0FBSyxDQUFDd0MsTUFBTTtnQkFDcEJFLE9BQU8sRUFBRTFDO1lBQ1gsQ0FBQyxDQUFDO1lBQ0YsTUFBTSxJQUFJTSxLQUFLLENBQUUscUNBQW9DTixLQUFLLENBQUNPLE9BQVEsRUFBQyxDQUFDO1FBQ3ZFO1FBRUEsT0FBT1IsSUFBSTtJQUNiO0lBRUE7SUFDQSxhQUFhNEMsc0JBQXNCQSxDQUFDOUIsS0FBYSxFQUFFK0IsUUFBZ0IsRUFBRTlCLFFBQWEsRUFBRStCLHFCQUE4QixHQUFHLElBQUksRUFBRTtRQUN6SHhDLE9BQU8sQ0FBQ1UsR0FBRyxDQUFDLGtEQUFrRCxFQUFFO1lBQzlERixLQUFLO1lBQ0xDLFFBQVE7WUFDUitCLHFCQUFxQjtZQUNyQjNCLFNBQVMsRUFBRSxJQUFJQyxJQUFJLENBQUMsQ0FBQyxDQUFDQyxXQUFXLENBQUM7UUFDcEMsQ0FBQyxDQUFDO1FBRUYsTUFBTSxFQUFFckIsSUFBSSxFQUFFQyxLQUFBQSxFQUFPLEdBQUcsTUFBTVosYUFBYSxDQUFDSyxJQUFJLENBQUM0QixLQUFLLENBQUN5QixVQUFVLENBQUM7WUFDaEVqQyxLQUFLO1lBQ0wrQixRQUFRO1lBQ1JSLGFBQWEsRUFBRXRCLFFBQVE7WUFDdkJpQyxhQUFhLEVBQUUsS0FBSyxDQUFDO1FBQ3ZCLENBQUMsQ0FBQztRQUVGMUMsT0FBTyxDQUFDVSxHQUFHLENBQUMsMkNBQTJDLEVBQUU7WUFDdkRRLE9BQU8sRUFBRSxDQUFDLENBQUN4QixJQUFJO1lBQ2Z5QixPQUFPLEVBQUUsQ0FBQyxDQUFDekIsSUFBSSxFQUFFMEIsSUFBSTtZQUNyQkMsTUFBTSxFQUFFM0IsSUFBSSxFQUFFMEIsSUFBSSxFQUFFRSxFQUFFO1lBQ3RCQyxTQUFTLEVBQUU3QixJQUFJLEVBQUUwQixJQUFJLEVBQUVaLEtBQUs7WUFDNUJvQixjQUFjLEVBQUVsQyxJQUFJLEVBQUUwQixJQUFJLEVBQUVTLGtCQUFrQjtZQUM5Q0MsWUFBWSxFQUFFcEMsSUFBSSxFQUFFMEIsSUFBSSxFQUFFVyxhQUFhO1lBQ3ZDcEMsS0FBSyxFQUFFQSxLQUFLLEVBQUVPLE9BQU87WUFDckJnQyxTQUFTLEVBQUV2QyxLQUFLLEVBQUV3QztRQUNwQixDQUFDLENBQUM7UUFFRixJQUFJeEMsS0FBSyxFQUFFO1lBQ1RLLE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLHVEQUF1RCxFQUFFO2dCQUNyRU8sT0FBTyxFQUFFUCxLQUFLLENBQUNPLE9BQU87Z0JBQ3RCaUMsTUFBTSxFQUFFeEMsS0FBSyxDQUFDd0MsTUFBTTtnQkFDcEJFLE9BQU8sRUFBRTFDO1lBQ1gsQ0FBQyxDQUFDO1lBQ0YsT0FBTztnQkFBRUQsSUFBSSxFQUFFLElBQUk7Z0JBQUVDO1lBQU0sQ0FBQztRQUM5QjtRQUVBO1FBQ0EsSUFBSUQsSUFBSSxFQUFFMEIsSUFBSSxJQUFJb0IscUJBQXFCLEVBQUU7WUFDdkN4QyxPQUFPLENBQUNVLEdBQUcsQ0FBQyxzQ0FBc0MsQ0FBQztZQUVuRCxNQUFNLEVBQUVmLEtBQUssRUFBRWdELFVBQUFBLEVBQVksR0FBRyxNQUFNNUQsYUFBYSxDQUFDSyxJQUFJLENBQUM0QixLQUFLLENBQUM0QixZQUFZLENBQUM7Z0JBQ3hFQyxJQUFJLEVBQUUsUUFBUTtnQkFDZHJDLEtBQUssRUFBRUEsS0FBSztnQkFDWitCLFFBQVEsRUFBRUEsUUFBUTtnQkFBRTtnQkFDcEJPLE9BQU8sRUFBRTtvQkFDUG5DLFVBQVUsRUFBRyxHQUFFM0IsdUJBQWlDO2dCQUNsRDtZQUNGLENBQUMsQ0FBQztZQUVGLElBQUkyRCxVQUFVLEVBQUU7Z0JBQ2QzQyxPQUFPLENBQUNMLEtBQUssQ0FBQywwQ0FBMEMsRUFBRWdELFVBQVUsQ0FBQztZQUNyRTtZQUNGLENBQUMsTUFBTTtnQkFDTDNDLE9BQU8sQ0FBQ1UsR0FBRyxDQUFDLDhDQUE4QyxDQUFDO1lBQzdEO1FBQ0YsQ0FBQyxNQUFNLElBQUloQixJQUFJLEVBQUUwQixJQUFJLElBQUksQ0FBQ29CLHFCQUFxQixFQUFFO1lBQy9DeEMsT0FBTyxDQUFDVSxHQUFHLENBQUMsZ0VBQWdFLENBQUM7UUFDL0U7UUFFQSxPQUFPO1lBQUVoQixJQUFJO1lBQUVDLEtBQUssRUFBRTtRQUFLLENBQUM7SUFDOUI7SUFFQTtJQUNBLGFBQWFxRCw0QkFBNEJBLENBQUMzQixNQUFjLEVBQWlEO1FBQ3ZHckIsT0FBTyxDQUFDVSxHQUFHLENBQUMsa0VBQWtFLEVBQUVXLE1BQU0sQ0FBQztRQUV2RixJQUFJO1lBQ0Y7WUFDQSxNQUFNLEVBQUUzQixJQUFJLEVBQUVlLFFBQVEsRUFBRWQsS0FBSyxFQUFFc0QsU0FBQUEsRUFBVyxHQUFHLE1BQU1sRSxhQUFhLENBQUNLLElBQUksQ0FBQzRCLEtBQUssQ0FBQ2tDLFdBQVcsQ0FBQzdCLE1BQU0sQ0FBQztZQUUvRixJQUFJNEIsU0FBUyxJQUFJLENBQUN4QyxRQUFRLEVBQUVXLElBQUksRUFBRTtnQkFDaENwQixPQUFPLENBQUNMLEtBQUssQ0FBQyxxQ0FBcUMsRUFBRXNELFNBQVMsQ0FBQztnQkFDL0QsT0FBTztvQkFBRUUsT0FBTyxFQUFFLEtBQUs7b0JBQUV4RCxLQUFLLEVBQUU7Z0JBQXdCLENBQUM7WUFDM0Q7WUFFQSxNQUFNeUIsSUFBSSxHQUFHWCxRQUFRLENBQUNXLElBQUk7WUFFMUI7WUFDQTtZQUNBLE1BQU0sRUFBRXpCLEtBQUssRUFBRXlELFdBQUFBLEVBQWEsR0FBRyxNQUFNckUsYUFBYSxDQUFDSyxJQUFJLENBQUM0QixLQUFLLENBQUNxQyxjQUFjLENBQzFFakMsSUFBSSxDQUFDRSxFQUFFLEVBQ1A7Z0JBQ0VvQixhQUFhLEVBQUUsSUFBSTtnQkFDbkJYLGFBQWEsRUFBQXVCLGFBQUEsQ0FBQUEsYUFBQSxLQUNSbEMsSUFBSSxDQUFDVyxhQUFhO29CQUNyQndCLGdCQUFnQixFQUFFLElBQUk7b0JBQ3RCQywyQkFBMkIsRUFBRSxJQUFJO29CQUNqQ0MsWUFBWSxFQUFFLElBQUkzQyxJQUFJLENBQUMsQ0FBQyxDQUFDQyxXQUFXLENBQUM7Z0JBQUM7WUFFMUMsQ0FDRixDQUFDO1lBRUQsSUFBSXFDLFdBQVcsRUFBRTtnQkFDZnBELE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLHlDQUF5QyxFQUFFeUQsV0FBVyxDQUFDO2dCQUNyRSxPQUFPO29CQUFFRCxPQUFPLEVBQUUsS0FBSztvQkFBRXhELEtBQUssRUFBRXlELFdBQVcsQ0FBQ2xELE9BQUFBO2dCQUFRLENBQUM7WUFDdkQ7WUFFQUYsT0FBTyxDQUFDVSxHQUFHLENBQUMsK0RBQStELENBQUM7WUFDNUUsT0FBTztnQkFBRXlDLE9BQU8sRUFBRTtZQUFLLENBQUM7UUFDMUIsQ0FBQyxDQUFDLE9BQU94RCxLQUFLLEVBQUU7WUFDZEssT0FBTyxDQUFDTCxLQUFLLENBQUMsd0NBQXdDLEVBQUVBLEtBQUssQ0FBQztZQUM5RCxPQUFPO2dCQUFFd0QsT0FBTyxFQUFFLEtBQUs7Z0JBQUV4RCxLQUFLLEVBQUVBLEtBQUssWUFBWU0sS0FBSyxHQUFHTixLQUFLLENBQUNPLE9BQU8sR0FBRztZQUFvQixDQUFDO1FBQ2hHO0lBQ0Y7SUFFQTtJQUNBLGFBQWFzQyxxQkFBcUJBLENBQUNoQyxLQUFhLEVBQUUrQixRQUFnQixFQUFpRDtRQUNqSHZDLE9BQU8sQ0FBQ1UsR0FBRyxDQUFDLDBEQUEwRCxFQUFFRixLQUFLLENBQUM7UUFFOUUsTUFBTSxFQUFFYixLQUFLLEVBQUVnRCxVQUFBQSxFQUFZLEdBQUcsTUFBTTVELGFBQWEsQ0FBQ0ssSUFBSSxDQUFDNEIsS0FBSyxDQUFDNEIsWUFBWSxDQUFDO1lBQ3hFQyxJQUFJLEVBQUUsUUFBUTtZQUNkckMsS0FBSyxFQUFFQSxLQUFLO1lBQ1orQixRQUFRLEVBQUVBLFFBQVE7WUFDbEJPLE9BQU8sRUFBRTtnQkFDUG5DLFVBQVUsRUFBRyxHQUFFM0IsdUJBQWlDO1lBQ2xEO1FBQ0YsQ0FBQyxDQUFDO1FBRUYsSUFBSTJELFVBQVUsRUFBRTtZQUNkM0MsT0FBTyxDQUFDTCxLQUFLLENBQUMsMENBQTBDLEVBQUVnRCxVQUFVLENBQUM7WUFDckUsT0FBTztnQkFBRVEsT0FBTyxFQUFFLEtBQUs7Z0JBQUV4RCxLQUFLLEVBQUVnRCxVQUFVLENBQUN6QyxPQUFBQTtZQUFRLENBQUM7UUFDdEQsQ0FBQyxNQUFNO1lBQ0xGLE9BQU8sQ0FBQ1UsR0FBRyxDQUFDLDhDQUE4QyxDQUFDO1lBQzNELE9BQU87Z0JBQUV5QyxPQUFPLEVBQUU7WUFBSyxDQUFDO1FBQzFCO0lBQ0Y7SUFFQTtJQUNBLGFBQWFPLGlCQUFpQkEsQ0FBQ0MsT0FBcUMsRUFBZ0M7UUFDbEcsTUFBTSxFQUFFakUsSUFBSSxFQUFFQyxLQUFBQSxFQUFPLEdBQUcsTUFBTVosYUFBYSxDQUN4Q2EsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUNyQkMsTUFBTSxDQUFDO1lBQUM4RCxPQUFPO1NBQUMsQ0FBQyxDQUNqQjdELE1BQU0sQ0FBQyxDQUFDLENBQ1JDLE1BQU0sQ0FBQyxDQUFDO1FBRVgsSUFBSUosS0FBSyxFQUFFO1lBQ1RLLE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLDhCQUE4QixFQUFFQSxLQUFLLENBQUM7WUFDcEQsTUFBTSxJQUFJTSxLQUFLLENBQUUsa0NBQWlDTixLQUFLLENBQUNPLE9BQVEsRUFBQyxDQUFDO1FBQ3BFO1FBRUEsT0FBT1IsSUFBSTtJQUNiO0lBRUE7SUFDQSxhQUFha0UsaUJBQWlCQSxDQUFDRCxPQUFxQyxFQUFnQztRQUNsRyxNQUFNLEVBQUVqRSxJQUFJLEVBQUVDLEtBQUFBLEVBQU8sR0FBRyxNQUFNWixhQUFhLENBQ3hDYSxJQUFJLENBQUMsZUFBZSxDQUFDLENBQ3JCaUUsTUFBTSxDQUFDO1lBQUNGLE9BQU87U0FBQyxFQUFFO1lBQUVHLFVBQVUsRUFBRTtRQUFVLENBQUMsQ0FBQyxDQUM1Q2hFLE1BQU0sQ0FBQyxDQUFDLENBQ1JDLE1BQU0sQ0FBQyxDQUFDO1FBRVgsSUFBSUosS0FBSyxFQUFFO1lBQ1RLLE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLCtCQUErQixFQUFFQSxLQUFLLENBQUM7WUFDckQsTUFBTSxJQUFJTSxLQUFLLENBQUUsa0NBQWlDTixLQUFLLENBQUNPLE9BQVEsRUFBQyxDQUFDO1FBQ3BFO1FBRUEsT0FBT1IsSUFBSTtJQUNiO0lBRUE7SUFDQSxhQUFhcUUsYUFBYUEsQ0FBQ0MsVUFBc0QsRUFBNEI7UUFDM0csTUFBTSxFQUFFdEUsSUFBSSxFQUFFQyxLQUFBQSxFQUFPLEdBQUcsTUFBTVosYUFBYSxDQUN4Q2EsSUFBSSxDQUFDLG1CQUFtQixDQUFDLENBQ3pCQyxNQUFNLENBQUM7WUFBQ21FLFVBQVU7U0FBQyxDQUFDLENBQ3BCbEUsTUFBTSxDQUFDLENBQUMsQ0FDUkMsTUFBTSxDQUFDLENBQUM7UUFFWCxJQUFJSixLQUFLLEVBQUU7WUFDVEssT0FBTyxDQUFDTCxLQUFLLENBQUMsNEJBQTRCLEVBQUVBLEtBQUssQ0FBQztZQUNsRCxNQUFNLElBQUlNLEtBQUssQ0FBRSw4QkFBNkJOLEtBQUssQ0FBQ08sT0FBUSxFQUFDLENBQUM7UUFDaEU7UUFFQSxPQUFPUixJQUFJO0lBQ2I7SUFFQTtJQUNBLGFBQWF1RSxnQkFBZ0JBLENBQUNDLFNBQXNELEVBQTZCO1FBQy9HLE1BQU0sRUFBRXhFLElBQUksRUFBRUMsS0FBQUEsRUFBTyxHQUFHLE1BQU1aLGFBQWEsQ0FDeENhLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUMxQkMsTUFBTSxDQUFDO1lBQUNxRSxTQUFTO1NBQUMsQ0FBQyxDQUNuQnBFLE1BQU0sQ0FBQyxDQUFDLENBQ1JDLE1BQU0sQ0FBQyxDQUFDO1FBRVgsSUFBSUosS0FBSyxFQUFFO1lBQ1RLLE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLCtCQUErQixFQUFFQSxLQUFLLENBQUM7WUFDckQsTUFBTSxJQUFJTSxLQUFLLENBQUUsaUNBQWdDTixLQUFLLENBQUNPLE9BQVEsRUFBQyxDQUFDO1FBQ25FO1FBRUEsT0FBT1IsSUFBSTtJQUNiO0lBRUE7SUFDQSxhQUFheUUsY0FBY0EsQ0FBQzlDLE1BQWMsRUFBdUM7UUFDL0UsTUFBTSxFQUFFM0IsSUFBSSxFQUFFQyxLQUFBQSxFQUFPLEdBQUcsTUFBTVosYUFBYSxDQUN4Q2EsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUNyQkUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUNYTyxFQUFFLENBQUMsU0FBUyxFQUFFZ0IsTUFBTSxDQUFDLENBQ3JCdEIsTUFBTSxDQUFDLENBQUM7UUFFWCxJQUFJSixLQUFLLElBQUlBLEtBQUssQ0FBQ1csSUFBSSxLQUFLLFVBQVUsRUFBRTtZQUN0Q04sT0FBTyxDQUFDTCxLQUFLLENBQUMsOEJBQThCLEVBQUVBLEtBQUssQ0FBQztZQUNwRCxNQUFNLElBQUlNLEtBQUssQ0FBRSxpQ0FBZ0NOLEtBQUssQ0FBQ08sT0FBUSxFQUFDLENBQUM7UUFDbkU7UUFFQSxPQUFPUixJQUFJO0lBQ2I7SUFFQTtJQUNBLGFBQWEwRSx5QkFBeUJBLENBQUNDLGFBQXFCLEVBQUVoRCxNQUFjLEVBQWlCO1FBQzNGLE1BQU0sRUFBRTFCLEtBQUFBLEVBQU8sR0FBRyxNQUFNWixhQUFhLENBQ2xDYSxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FDM0IwRSxNQUFNLENBQUM7WUFDTkMsT0FBTyxFQUFFbEQsTUFBTTtZQUNmbUQsVUFBVSxFQUFFLElBQUkxRCxJQUFJLENBQUMsQ0FBQyxDQUFDQyxXQUFXLENBQUM7UUFDckMsQ0FBQyxDQUFDLENBQ0RWLEVBQUUsQ0FBQyxJQUFJLEVBQUVnRSxhQUFhLENBQUM7UUFFMUIsSUFBSTFFLEtBQUssRUFBRTtZQUNUSyxPQUFPLENBQUNMLEtBQUssQ0FBQywwQ0FBMEMsRUFBRUEsS0FBSyxDQUFDO1lBQ2hFLE1BQU0sSUFBSU0sS0FBSyxDQUFFLGlDQUFnQ04sS0FBSyxDQUFDTyxPQUFRLEVBQUMsQ0FBQztRQUNuRTtJQUNGO0lBRUE7SUFDQSxhQUFhdUUsbUJBQW1CQSxDQUFDSixhQUFxQixFQUFpQjtRQUNyRSxNQUFNLEVBQUUxRSxLQUFBQSxFQUFPLEdBQUcsTUFBTVosYUFBYSxDQUNsQ2EsSUFBSSxDQUFDLHFCQUFxQixDQUFDLENBQzNCMEUsTUFBTSxDQUFDO1lBQUVJLFlBQVksRUFBRSxJQUFJNUQsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDO1FBQUUsQ0FBQyxDQUFDLENBQ2xEVixFQUFFLENBQUMsSUFBSSxFQUFFZ0UsYUFBYSxDQUFDO1FBRTFCLElBQUkxRSxLQUFLLEVBQUU7WUFDVEssT0FBTyxDQUFDTCxLQUFLLENBQUMsK0JBQStCLEVBQUVBLEtBQUssQ0FBQztZQUNyRCxNQUFNLElBQUlNLEtBQUssQ0FBRSxtQ0FBa0NOLEtBQUssQ0FBQ08sT0FBUSxFQUFDLENBQUM7UUFDckU7SUFDRjtJQUVBO0lBQ0EsYUFBYXlFLGlCQUFpQkEsQ0FBQ3RELE1BQWMsRUFBbUI7UUFDOUQsTUFBTSxFQUFFdUQsS0FBSyxFQUFFakYsS0FBQUEsRUFBTyxHQUFHLE1BQU1aLGFBQWEsQ0FDekNhLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FDbEJFLE1BQU0sQ0FBQyxHQUFHLEVBQUU7WUFBRThFLEtBQUssRUFBRSxPQUFPO1lBQUVDLElBQUksRUFBRTtRQUFLLENBQUMsQ0FBQyxDQUMzQ3hFLEVBQUUsQ0FBQyxTQUFTLEVBQUVnQixNQUFNLENBQUM7UUFFeEIsSUFBSTFCLEtBQUssRUFBRTtZQUNUSyxPQUFPLENBQUNMLEtBQUssQ0FBQyxnQ0FBZ0MsRUFBRUEsS0FBSyxDQUFDO1lBQ3RELE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFDWjtRQUVBLE9BQU9pRixLQUFLLElBQUksQ0FBQztJQUNuQjtJQUVBO0lBQ0EsYUFBYUUsY0FBY0EsQ0FBQ3RFLEtBQWEsRUFBOEU7UUFDckgsSUFBSTtZQUNGLE1BQU0sRUFBRWQsSUFBSSxFQUFFLEVBQUVxRixLQUFBQSxFQUFPLEVBQUVwRixLQUFBQSxFQUFPLEdBQUcsTUFBTVosYUFBYSxDQUFDSyxJQUFJLENBQUM0QixLQUFLLENBQUNnRSxTQUFTLENBQUMsQ0FBQztZQUU3RSxJQUFJckYsS0FBSyxFQUFFO2dCQUNUSyxPQUFPLENBQUNMLEtBQUssQ0FBQyw4QkFBOEIsRUFBRUEsS0FBSyxDQUFDO2dCQUNwRCxNQUFNLElBQUlNLEtBQUssQ0FBRSxnQ0FBK0JOLEtBQUssQ0FBQ08sT0FBUSxFQUFDLENBQUM7WUFDbEU7WUFFQSxJQUFJLENBQUM2RSxLQUFLLElBQUlBLEtBQUssQ0FBQ0UsTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDaEMsT0FBTyxJQUFJO1lBQ2I7WUFFQTtZQUNBLE1BQU03RCxJQUFJLEdBQUcyRCxLQUFLLENBQUNHLElBQUksRUFBQ0MsQ0FBQyxHQUFJQSxDQUFDLENBQUMzRSxLQUFLLEtBQUtBLEtBQUssQ0FBQztZQUUvQyxJQUFJLENBQUNZLElBQUksRUFBRTtnQkFDVCxPQUFPLElBQUk7WUFDYjtZQUVBLE9BQU87Z0JBQ0xFLEVBQUUsRUFBRUYsSUFBSSxDQUFDRSxFQUFFO2dCQUNYZCxLQUFLLEVBQUVZLElBQUksQ0FBQ1osS0FBTTtnQkFDbEJxQixrQkFBa0IsRUFBRVQsSUFBSSxDQUFDUyxrQkFBQUE7WUFDM0IsQ0FBQztRQUNILENBQUMsQ0FBQyxPQUFPbEMsS0FBSyxFQUFFO1lBQ2RLLE9BQU8sQ0FBQ0wsS0FBSyxDQUFDLDBCQUEwQixFQUFFQSxLQUFLLENBQUM7WUFDaEQsTUFBTUEsS0FBSztRQUNiO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxzcmNcXGxpYlxcc3VwYWJhc2VcXGFkbWluLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9saWIvc3VwYWJhc2UvYWRtaW4udHNcbi8vIENsaWVudGUgYWRtaW5pc3RyYXRpdm8gZGUgU3VwYWJhc2UgcGFyYSBvcGVyYWNpb25lcyBkZWwgc2Vydmlkb3JcblxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLy8gQ2xpZW50ZSBhZG1pbiBjb24gcHJpdmlsZWdpb3MgZWxldmFkb3NcbmV4cG9ydCBjb25zdCBzdXBhYmFzZUFkbWluID0gY3JlYXRlQ2xpZW50KFxuICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZISxcbiAge1xuICAgIGF1dGg6IHtcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGZhbHNlLFxuICAgICAgcGVyc2lzdFNlc3Npb246IGZhbHNlXG4gICAgfVxuICB9XG4pO1xuXG4vLyBUaXBvcyBwYXJhIGxhcyBudWV2YXMgdGFibGFzXG5leHBvcnQgaW50ZXJmYWNlIFN0cmlwZVRyYW5zYWN0aW9uIHtcbiAgaWQ6IHN0cmluZztcbiAgc3RyaXBlX3Nlc3Npb25faWQ6IHN0cmluZztcbiAgc3RyaXBlX2N1c3RvbWVyX2lkPzogc3RyaW5nO1xuICB1c2VyX2VtYWlsOiBzdHJpbmc7XG4gIHVzZXJfbmFtZT86IHN0cmluZztcbiAgcGxhbl9pZDogc3RyaW5nO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgY3VycmVuY3k6IHN0cmluZztcbiAgcGF5bWVudF9zdGF0dXM6IHN0cmluZztcbiAgc3Vic2NyaXB0aW9uX2lkPzogc3RyaW5nO1xuICB1c2VyX2lkPzogc3RyaW5nO1xuICBtZXRhZGF0YT86IGFueTtcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICBhY3RpdmF0ZWRfYXQ/OiBzdHJpbmc7XG4gIGV4cGlyZXNfYXQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlclBsYW5IaXN0b3J5IHtcbiAgaWQ6IHN0cmluZztcbiAgdXNlcl9pZDogc3RyaW5nO1xuICBvbGRfcGxhbj86IHN0cmluZztcbiAgbmV3X3BsYW46IHN0cmluZztcbiAgY2hhbmdlZF9ieTogJ3N5c3RlbScgfCAnYWRtaW4nIHwgJ3VzZXInO1xuICByZWFzb24/OiBzdHJpbmc7XG4gIHRyYW5zYWN0aW9uX2lkPzogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRmVhdHVyZUFjY2Vzc0xvZyB7XG4gIGlkOiBzdHJpbmc7XG4gIHVzZXJfaWQ6IHN0cmluZztcbiAgZmVhdHVyZV9uYW1lOiBzdHJpbmc7XG4gIGFjY2Vzc19ncmFudGVkOiBib29sZWFuO1xuICBwbGFuX2F0X3RpbWU6IHN0cmluZztcbiAgdG9rZW5zX3VzZWQ6IG51bWJlcjtcbiAgZGVuaWFsX3JlYXNvbj86IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEV4dGVuZGVkVXNlclByb2ZpbGUge1xuICBpZDogc3RyaW5nO1xuICB1c2VyX2lkOiBzdHJpbmc7XG4gIHN1YnNjcmlwdGlvbl9wbGFuOiAnZnJlZScgfCAndXN1YXJpbycgfCAncHJvJztcbiAgbW9udGhseV90b2tlbl9saW1pdDogbnVtYmVyO1xuICBjdXJyZW50X21vbnRoX3Rva2VuczogbnVtYmVyO1xuICBjdXJyZW50X21vbnRoOiBzdHJpbmc7XG4gIHBheW1lbnRfdmVyaWZpZWQ6IGJvb2xlYW47XG4gIHN0cmlwZV9jdXN0b21lcl9pZD86IHN0cmluZztcbiAgc3RyaXBlX3N1YnNjcmlwdGlvbl9pZD86IHN0cmluZzsgLy8gSUQgZGUgbGEgc3VzY3JpcGNpw7NuIGFjdGl2YSBlbiBTdHJpcGVcbiAgcGxhbl9leHBpcmVzX2F0Pzogc3RyaW5nO1xuICBsYXN0X3BheW1lbnRfZGF0ZT86IHN0cmluZztcbiAgYXV0b19yZW5ldzogYm9vbGVhbjtcbiAgcGxhbl9mZWF0dXJlczogYW55O1xuICBzZWN1cml0eV9mbGFnczogYW55O1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbn1cblxuLy8gRnVuY2lvbmVzIGRlIHV0aWxpZGFkIHBhcmEgb3BlcmFjaW9uZXMgYWRtaW5pc3RyYXRpdmFzXG5leHBvcnQgY2xhc3MgU3VwYWJhc2VBZG1pblNlcnZpY2Uge1xuICBcbiAgLy8gQ3JlYXIgdHJhbnNhY2Npw7NuIGRlIFN0cmlwZVxuICBzdGF0aWMgYXN5bmMgY3JlYXRlU3RyaXBlVHJhbnNhY3Rpb24odHJhbnNhY3Rpb246IE9taXQ8U3RyaXBlVHJhbnNhY3Rpb24sICdpZCcgfCAnY3JlYXRlZF9hdCc+KTogUHJvbWlzZTxTdHJpcGVUcmFuc2FjdGlvbj4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgIC5mcm9tKCdzdHJpcGVfdHJhbnNhY3Rpb25zJylcbiAgICAgIC5pbnNlcnQoW3RyYW5zYWN0aW9uXSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpO1xuICAgIFxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgc3RyaXBlIHRyYW5zYWN0aW9uOicsIGVycm9yKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSB0cmFuc2FjdGlvbjogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gZGF0YTtcbiAgfVxuICBcbiAgLy8gT2J0ZW5lciB0cmFuc2FjY2nDs24gcG9yIHNlc3Npb24gSURcbiAgc3RhdGljIGFzeW5jIGdldFRyYW5zYWN0aW9uQnlTZXNzaW9uSWQoc2Vzc2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPFN0cmlwZVRyYW5zYWN0aW9uIHwgbnVsbD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgIC5mcm9tKCdzdHJpcGVfdHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCdzdHJpcGVfc2Vzc2lvbl9pZCcsIHNlc3Npb25JZClcbiAgICAgIC5zaW5nbGUoKTtcbiAgICBcbiAgICBpZiAoZXJyb3IgJiYgZXJyb3IuY29kZSAhPT0gJ1BHUlNUMTE2Jykge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdHJhbnNhY3Rpb246JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggdHJhbnNhY3Rpb246ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGRhdGE7XG4gIH1cbiAgXG4gIC8vIENyZWFyIHVzdWFyaW8gY29uIGludml0YWNpw7NuXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVVc2VyV2l0aEludml0YXRpb24oZW1haWw6IHN0cmluZywgdXNlckRhdGE6IGFueSkge1xuICAgIGNvbnNvbGUubG9nKCfwn5SEIFtTVVBBQkFTRV9BRE1JTl0gQ3JlYXRpbmcgdXNlciBpbnZpdGF0aW9uOicsIHtcbiAgICAgIGVtYWlsLFxuICAgICAgdXNlckRhdGEsXG4gICAgICByZWRpcmVjdFRvOiBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVVJMfS9hdXRoL2NhbGxiYWNrYCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfSk7XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4uaW52aXRlVXNlckJ5RW1haWwoXG4gICAgICBlbWFpbCxcbiAgICAgIHtcbiAgICAgICAgZGF0YTogdXNlckRhdGEsXG4gICAgICAgIHJlZGlyZWN0VG86IGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkx9L2F1dGgvY2FsbGJhY2tgXG4gICAgICB9XG4gICAgKTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5OKIFtTVVBBQkFTRV9BRE1JTl0gSW52aXRhdGlvbiByZXN1bHQ6Jywge1xuICAgICAgaGFzRGF0YTogISFkYXRhLFxuICAgICAgaGFzVXNlcjogISFkYXRhPy51c2VyLFxuICAgICAgdXNlcklkOiBkYXRhPy51c2VyPy5pZCxcbiAgICAgIHVzZXJFbWFpbDogZGF0YT8udXNlcj8uZW1haWwsXG4gICAgICB1c2VyQXVkOiBkYXRhPy51c2VyPy5hdWQsXG4gICAgICB1c2VyUm9sZTogZGF0YT8udXNlcj8ucm9sZSxcbiAgICAgIGVtYWlsQ29uZmlybWVkOiBkYXRhPy51c2VyPy5lbWFpbF9jb25maXJtZWRfYXQsXG4gICAgICB1c2VyTWV0YWRhdGE6IGRhdGE/LnVzZXI/LnVzZXJfbWV0YWRhdGEsXG4gICAgICBhcHBNZXRhZGF0YTogZGF0YT8udXNlcj8uYXBwX21ldGFkYXRhLFxuICAgICAgZXJyb3I6IGVycm9yPy5tZXNzYWdlLFxuICAgICAgZXJyb3JDb2RlOiBlcnJvcj8uc3RhdHVzLFxuICAgICAgZnVsbEVycm9yOiBlcnJvclxuICAgIH0pO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW1NVUEFCQVNFX0FETUlOXSBFcnJvciBjcmVhdGluZyB1c2VyIGludml0YXRpb246Jywge1xuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICBzdGF0dXM6IGVycm9yLnN0YXR1cyxcbiAgICAgICAgZGV0YWlsczogZXJyb3JcbiAgICAgIH0pO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIHVzZXIgaW52aXRhdGlvbjogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhO1xuICB9XG5cbiAgLy8gQ3JlYXIgdXN1YXJpbyBjb24gY29udHJhc2XDsWEgZXNwZWPDrWZpY2EgeSBvcGNpb25hbG1lbnRlIGVudmlhciBlbWFpbCBkZSBjb25maXJtYWNpw7NuXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVVc2VyV2l0aFBhc3N3b3JkKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIHVzZXJEYXRhOiBhbnksIHNlbmRDb25maXJtYXRpb25FbWFpbDogYm9vbGVhbiA9IHRydWUpIHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBbU1VQQUJBU0VfQURNSU5dIENyZWF0aW5nIHVzZXIgd2l0aCBwYXNzd29yZDonLCB7XG4gICAgICBlbWFpbCxcbiAgICAgIHVzZXJEYXRhLFxuICAgICAgc2VuZENvbmZpcm1hdGlvbkVtYWlsLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9KTtcblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW4uYXV0aC5hZG1pbi5jcmVhdGVVc2VyKHtcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgICB1c2VyX21ldGFkYXRhOiB1c2VyRGF0YSxcbiAgICAgIGVtYWlsX2NvbmZpcm06IGZhbHNlIC8vIE5vIGNvbmZpcm1hciBhdXRvbcOhdGljYW1lbnRlXG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZygn8J+TiiBbU1VQQUJBU0VfQURNSU5dIFVzZXIgY3JlYXRpb24gcmVzdWx0OicsIHtcbiAgICAgIGhhc0RhdGE6ICEhZGF0YSxcbiAgICAgIGhhc1VzZXI6ICEhZGF0YT8udXNlcixcbiAgICAgIHVzZXJJZDogZGF0YT8udXNlcj8uaWQsXG4gICAgICB1c2VyRW1haWw6IGRhdGE/LnVzZXI/LmVtYWlsLFxuICAgICAgZW1haWxDb25maXJtZWQ6IGRhdGE/LnVzZXI/LmVtYWlsX2NvbmZpcm1lZF9hdCxcbiAgICAgIHVzZXJNZXRhZGF0YTogZGF0YT8udXNlcj8udXNlcl9tZXRhZGF0YSxcbiAgICAgIGVycm9yOiBlcnJvcj8ubWVzc2FnZSxcbiAgICAgIGVycm9yQ29kZTogZXJyb3I/LnN0YXR1c1xuICAgIH0pO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW1NVUEFCQVNFX0FETUlOXSBFcnJvciBjcmVhdGluZyB1c2VyIHdpdGggcGFzc3dvcmQ6Jywge1xuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICBzdGF0dXM6IGVycm9yLnN0YXR1cyxcbiAgICAgICAgZGV0YWlsczogZXJyb3JcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3IgfTtcbiAgICB9XG5cbiAgICAvLyBFbnZpYXIgZW1haWwgZGUgY29uZmlybWFjacOzbiBzb2xvIHNpIHNlIHNvbGljaXRhXG4gICAgaWYgKGRhdGE/LnVzZXIgJiYgc2VuZENvbmZpcm1hdGlvbkVtYWlsKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpyBFbnZpYW5kbyBlbWFpbCBkZSBjb25maXJtYWNpw7NuLi4uJyk7XG5cbiAgICAgIGNvbnN0IHsgZXJyb3I6IGVtYWlsRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW4uYXV0aC5hZG1pbi5nZW5lcmF0ZUxpbmsoe1xuICAgICAgICB0eXBlOiAnc2lnbnVwJyxcbiAgICAgICAgZW1haWw6IGVtYWlsLFxuICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQsIC8vIFJlcXVlcmlkbyBwYXJhIGdlbmVyYXRlTGlua1xuICAgICAgICBvcHRpb25zOiB7XG4gICAgICAgICAgcmVkaXJlY3RUbzogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0lURV9VUkx9L2F1dGgvY29uZmlybWVkYFxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgaWYgKGVtYWlsRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4pqg77iPIEVycm9yIGVudmlhbmRvIGVtYWlsIGRlIGNvbmZpcm1hY2nDs246JywgZW1haWxFcnJvcik7XG4gICAgICAgIC8vIE5vIGZhbGxhciBjb21wbGV0YW1lbnRlLCBlbCB1c3VhcmlvIHB1ZWRlIGNvbmZpcm1hciBtYW51YWxtZW50ZVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBFbWFpbCBkZSBjb25maXJtYWNpw7NuIGVudmlhZG8gZXhpdG9zYW1lbnRlJyk7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChkYXRhPy51c2VyICYmICFzZW5kQ29uZmlybWF0aW9uRW1haWwpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OnIEVtYWlsIGRlIGNvbmZpcm1hY2nDs24gb21pdGlkbyAoc2UgZW52aWFyw6EgZGVzcHXDqXMgZGVsIHBhZ28pJyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgZGF0YSwgZXJyb3I6IG51bGwgfTtcbiAgfVxuXG4gIC8vIEVudmlhciBlbWFpbCBkZSBjb25maXJtYWNpw7NuIHBhcmEgdXN1YXJpbyBleGlzdGVudGVcbiAgc3RhdGljIGFzeW5jIHNlbmRDb25maXJtYXRpb25FbWFpbEZvclVzZXIodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5OnIFtTVVBBQkFTRV9BRE1JTl0gRW52aWFuZG8gZW1haWwgZGUgY29uZmlybWFjacOzbiBwYXJhIHVzdWFyaW86JywgdXNlcklkKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBPYnRlbmVyIGRhdG9zIGRlbCB1c3VhcmlvXG4gICAgICBjb25zdCB7IGRhdGE6IHVzZXJEYXRhLCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4uZ2V0VXNlckJ5SWQodXNlcklkKTtcblxuICAgICAgaWYgKHVzZXJFcnJvciB8fCAhdXNlckRhdGE/LnVzZXIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igb2J0ZW5pZW5kbyBkYXRvcyBkZWwgdXN1YXJpbzonLCB1c2VyRXJyb3IpO1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVc3VhcmlvIG5vIGVuY29udHJhZG8nIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHVzZXIgPSB1c2VyRGF0YS51c2VyO1xuXG4gICAgICAvLyBQYXJhIHVzdWFyaW9zIHByZS1yZWdpc3RyYWRvcywgYWN0dWFsaXphciBlbCBlc3RhZG8gZGUgY29uZmlybWFjacOzbiBkaXJlY3RhbWVudGVcbiAgICAgIC8vIHlhIHF1ZSBlbCBwYWdvIGV4aXRvc28gY29uZmlybWEgbGEgaW50ZW5jacOzbiBkZWwgdXN1YXJpb1xuICAgICAgY29uc3QgeyBlcnJvcjogdXBkYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW4uYXV0aC5hZG1pbi51cGRhdGVVc2VyQnlJZChcbiAgICAgICAgdXNlci5pZCxcbiAgICAgICAge1xuICAgICAgICAgIGVtYWlsX2NvbmZpcm06IHRydWUsXG4gICAgICAgICAgdXNlcl9tZXRhZGF0YToge1xuICAgICAgICAgICAgLi4udXNlci51c2VyX21ldGFkYXRhLFxuICAgICAgICAgICAgcGF5bWVudF92ZXJpZmllZDogdHJ1ZSxcbiAgICAgICAgICAgIGVtYWlsX2NvbmZpcm1lZF92aWFfcGF5bWVudDogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpcm1lZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICBpZiAodXBkYXRlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4pqg77iPIEVycm9yIGNvbmZpcm1hbmRvIGVtYWlsIGRlbCB1c3VhcmlvOicsIHVwZGF0ZUVycm9yKTtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiB1cGRhdGVFcnJvci5tZXNzYWdlIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgVXN1YXJpbyBjb25maXJtYWRvIGF1dG9tw6F0aWNhbWVudGUgZGVzcHXDqXMgZGVsIHBhZ28gZXhpdG9zbycpO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbiBzZW5kQ29uZmlybWF0aW9uRW1haWxGb3JVc2VyOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnRXJyb3IgZGVzY29ub2NpZG8nIH07XG4gICAgfVxuICB9XG5cbiAgLy8gRW52aWFyIGVtYWlsIGRlIGNvbmZpcm1hY2nDs24gcGFyYSB1c3VhcmlvIGV4aXN0ZW50ZSAobcOpdG9kbyBsZWdhY3kpXG4gIHN0YXRpYyBhc3luYyBzZW5kQ29uZmlybWF0aW9uRW1haWwoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgY29uc29sZS5sb2coJ/Cfk6cgW1NVUEFCQVNFX0FETUlOXSBFbnZpYW5kbyBlbWFpbCBkZSBjb25maXJtYWNpw7NuIHBhcmE6JywgZW1haWwpO1xuXG4gICAgY29uc3QgeyBlcnJvcjogZW1haWxFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pbi5hdXRoLmFkbWluLmdlbmVyYXRlTGluayh7XG4gICAgICB0eXBlOiAnc2lnbnVwJyxcbiAgICAgIGVtYWlsOiBlbWFpbCxcbiAgICAgIHBhc3N3b3JkOiBwYXNzd29yZCxcbiAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgcmVkaXJlY3RUbzogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0lURV9VUkx9L2F1dGgvY29uZmlybWVkYFxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgaWYgKGVtYWlsRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KaoO+4jyBFcnJvciBlbnZpYW5kbyBlbWFpbCBkZSBjb25maXJtYWNpw7NuOicsIGVtYWlsRXJyb3IpO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlbWFpbEVycm9yLm1lc3NhZ2UgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBFbWFpbCBkZSBjb25maXJtYWNpw7NuIGVudmlhZG8gZXhpdG9zYW1lbnRlJyk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgfVxuICB9XG5cbiAgLy8gQ3JlYXIgcGVyZmlsIGRlIHVzdWFyaW9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZVVzZXJQcm9maWxlKHByb2ZpbGU6IFBhcnRpYWw8RXh0ZW5kZWRVc2VyUHJvZmlsZT4pOiBQcm9taXNlPEV4dGVuZGVkVXNlclByb2ZpbGU+IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAuaW5zZXJ0KFtwcm9maWxlXSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB1c2VyIHByb2ZpbGU6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIHVzZXIgcHJvZmlsZTogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhO1xuICB9XG5cbiAgLy8gQ3JlYXIgbyBhY3R1YWxpemFyIHBlcmZpbCBkZSB1c3VhcmlvXG4gIHN0YXRpYyBhc3luYyB1cHNlcnRVc2VyUHJvZmlsZShwcm9maWxlOiBQYXJ0aWFsPEV4dGVuZGVkVXNlclByb2ZpbGU+KTogUHJvbWlzZTxFeHRlbmRlZFVzZXJQcm9maWxlPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgLnVwc2VydChbcHJvZmlsZV0sIHsgb25Db25mbGljdDogJ3VzZXJfaWQnIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBzZXJ0aW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cHNlcnQgdXNlciBwcm9maWxlOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGE7XG4gIH1cbiAgXG4gIC8vIFJlZ2lzdHJhciBjYW1iaW8gZGUgcGxhblxuICBzdGF0aWMgYXN5bmMgbG9nUGxhbkNoYW5nZShwbGFuQ2hhbmdlOiBPbWl0PFVzZXJQbGFuSGlzdG9yeSwgJ2lkJyB8ICdjcmVhdGVkX2F0Jz4pOiBQcm9taXNlPFVzZXJQbGFuSGlzdG9yeT4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgIC5mcm9tKCd1c2VyX3BsYW5faGlzdG9yeScpXG4gICAgICAuaW5zZXJ0KFtwbGFuQ2hhbmdlXSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpO1xuICAgIFxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9nZ2luZyBwbGFuIGNoYW5nZTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBsb2cgcGxhbiBjaGFuZ2U6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGRhdGE7XG4gIH1cbiAgXG4gIC8vIFJlZ2lzdHJhciBhY2Nlc28gYSBjYXJhY3RlcsOtc3RpY2FcbiAgc3RhdGljIGFzeW5jIGxvZ0ZlYXR1cmVBY2Nlc3MoYWNjZXNzTG9nOiBPbWl0PEZlYXR1cmVBY2Nlc3NMb2csICdpZCcgfCAnY3JlYXRlZF9hdCc+KTogUHJvbWlzZTxGZWF0dXJlQWNjZXNzTG9nPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ2ZlYXR1cmVfYWNjZXNzX2xvZycpXG4gICAgICAuaW5zZXJ0KFthY2Nlc3NMb2ddKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG4gICAgXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2dnaW5nIGZlYXR1cmUgYWNjZXNzOicsIGVycm9yKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGxvZyBmZWF0dXJlIGFjY2VzczogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gZGF0YTtcbiAgfVxuICBcbiAgLy8gT2J0ZW5lciBwZXJmaWwgZGUgdXN1YXJpbyBwb3IgSURcbiAgc3RhdGljIGFzeW5jIGdldFVzZXJQcm9maWxlKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxFeHRlbmRlZFVzZXJQcm9maWxlIHwgbnVsbD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKVxuICAgICAgLnNpbmdsZSgpO1xuICAgIFxuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB1c2VyIHByb2ZpbGU6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggdXNlciBwcm9maWxlOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBkYXRhO1xuICB9XG4gIFxuICAvLyBBY3R1YWxpemFyIHRyYW5zYWNjacOzbiBjb24gdXNlcl9pZFxuICBzdGF0aWMgYXN5bmMgdXBkYXRlVHJhbnNhY3Rpb25XaXRoVXNlcih0cmFuc2FjdGlvbklkOiBzdHJpbmcsIHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ3N0cmlwZV90cmFuc2FjdGlvbnMnKVxuICAgICAgLnVwZGF0ZSh7XG4gICAgICAgIHVzZXJfaWQ6IHVzZXJJZCxcbiAgICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KVxuICAgICAgLmVxKCdpZCcsIHRyYW5zYWN0aW9uSWQpO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB0cmFuc2FjdGlvbiB3aXRoIHVzZXJfaWQ6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIHRyYW5zYWN0aW9uOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuICB9XG5cbiAgLy8gQWN0aXZhciB0cmFuc2FjY2nDs24gKG1hcmNhciBjb21vIGFjdGl2YWRhKVxuICBzdGF0aWMgYXN5bmMgYWN0aXZhdGVUcmFuc2FjdGlvbih0cmFuc2FjdGlvbklkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAuZnJvbSgnc3RyaXBlX3RyYW5zYWN0aW9ucycpXG4gICAgICAudXBkYXRlKHsgYWN0aXZhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkgfSlcbiAgICAgIC5lcSgnaWQnLCB0cmFuc2FjdGlvbklkKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWN0aXZhdGluZyB0cmFuc2FjdGlvbjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBhY3RpdmF0ZSB0cmFuc2FjdGlvbjogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH1cbiAgfVxuXG4gIC8vIE9idGVuZXIgY29udGVvIGRlIGRvY3VtZW50b3MgZGVsIHVzdWFyaW9cbiAgc3RhdGljIGFzeW5jIGdldERvY3VtZW50c0NvdW50KHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgICBjb25zdCB7IGNvdW50LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ2RvY3VtZW50b3MnKVxuICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgZG9jdW1lbnRzIGNvdW50OicsIGVycm9yKTtcbiAgICAgIHJldHVybiAwOyAvLyBSZXRvcm5hciAwIGVuIGNhc28gZGUgZXJyb3IgZW4gbHVnYXIgZGUgbGFuemFyIGV4Y2VwY2nDs25cbiAgICB9XG5cbiAgICByZXR1cm4gY291bnQgfHwgMDtcbiAgfVxuXG4gIC8vIE9idGVuZXIgdXN1YXJpbyBwb3IgZW1haWwgZGVzZGUgU3VwYWJhc2UgQXV0aFxuICBzdGF0aWMgYXN5bmMgZ2V0VXNlckJ5RW1haWwoZW1haWw6IHN0cmluZyk6IFByb21pc2U8eyBpZDogc3RyaW5nOyBlbWFpbDogc3RyaW5nOyBlbWFpbF9jb25maXJtZWRfYXQ/OiBzdHJpbmcgfSB8IG51bGw+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhOiB7IHVzZXJzIH0sIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4ubGlzdFVzZXJzKCk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgYnkgZW1haWw6JywgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBnZXQgdXNlciBieSBlbWFpbDogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXVzZXJzIHx8IHVzZXJzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cblxuICAgICAgLy8gRmlsdHJhciBwb3IgZW1haWwgeWEgcXVlIGxhIEFQSSBubyBwZXJtaXRlIGZpbHRybyBkaXJlY3RvXG4gICAgICBjb25zdCB1c2VyID0gdXNlcnMuZmluZCh1ID0+IHUuZW1haWwgPT09IGVtYWlsKTtcblxuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBpZDogdXNlci5pZCxcbiAgICAgICAgZW1haWw6IHVzZXIuZW1haWwhLFxuICAgICAgICBlbWFpbF9jb25maXJtZWRfYXQ6IHVzZXIuZW1haWxfY29uZmlybWVkX2F0XG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBnZXRVc2VyQnlFbWFpbDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZUFkbWluIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIiwiU3VwYWJhc2VBZG1pblNlcnZpY2UiLCJjcmVhdGVTdHJpcGVUcmFuc2FjdGlvbiIsInRyYW5zYWN0aW9uIiwiZGF0YSIsImVycm9yIiwiZnJvbSIsImluc2VydCIsInNlbGVjdCIsInNpbmdsZSIsImNvbnNvbGUiLCJFcnJvciIsIm1lc3NhZ2UiLCJnZXRUcmFuc2FjdGlvbkJ5U2Vzc2lvbklkIiwic2Vzc2lvbklkIiwiZXEiLCJjb2RlIiwiY3JlYXRlVXNlcldpdGhJbnZpdGF0aW9uIiwiZW1haWwiLCJ1c2VyRGF0YSIsImxvZyIsInJlZGlyZWN0VG8iLCJORVhUX1BVQkxJQ19BUFBfVVJMIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiYWRtaW4iLCJpbnZpdGVVc2VyQnlFbWFpbCIsImhhc0RhdGEiLCJoYXNVc2VyIiwidXNlciIsInVzZXJJZCIsImlkIiwidXNlckVtYWlsIiwidXNlckF1ZCIsImF1ZCIsInVzZXJSb2xlIiwicm9sZSIsImVtYWlsQ29uZmlybWVkIiwiZW1haWxfY29uZmlybWVkX2F0IiwidXNlck1ldGFkYXRhIiwidXNlcl9tZXRhZGF0YSIsImFwcE1ldGFkYXRhIiwiYXBwX21ldGFkYXRhIiwiZXJyb3JDb2RlIiwic3RhdHVzIiwiZnVsbEVycm9yIiwiZGV0YWlscyIsImNyZWF0ZVVzZXJXaXRoUGFzc3dvcmQiLCJwYXNzd29yZCIsInNlbmRDb25maXJtYXRpb25FbWFpbCIsImNyZWF0ZVVzZXIiLCJlbWFpbF9jb25maXJtIiwiZW1haWxFcnJvciIsImdlbmVyYXRlTGluayIsInR5cGUiLCJvcHRpb25zIiwiTkVYVF9QVUJMSUNfU0lURV9VUkwiLCJzZW5kQ29uZmlybWF0aW9uRW1haWxGb3JVc2VyIiwidXNlckVycm9yIiwiZ2V0VXNlckJ5SWQiLCJzdWNjZXNzIiwidXBkYXRlRXJyb3IiLCJ1cGRhdGVVc2VyQnlJZCIsIl9vYmplY3RTcHJlYWQiLCJwYXltZW50X3ZlcmlmaWVkIiwiZW1haWxfY29uZmlybWVkX3ZpYV9wYXltZW50IiwiY29uZmlybWVkX2F0IiwiY3JlYXRlVXNlclByb2ZpbGUiLCJwcm9maWxlIiwidXBzZXJ0VXNlclByb2ZpbGUiLCJ1cHNlcnQiLCJvbkNvbmZsaWN0IiwibG9nUGxhbkNoYW5nZSIsInBsYW5DaGFuZ2UiLCJsb2dGZWF0dXJlQWNjZXNzIiwiYWNjZXNzTG9nIiwiZ2V0VXNlclByb2ZpbGUiLCJ1cGRhdGVUcmFuc2FjdGlvbldpdGhVc2VyIiwidHJhbnNhY3Rpb25JZCIsInVwZGF0ZSIsInVzZXJfaWQiLCJ1cGRhdGVkX2F0IiwiYWN0aXZhdGVUcmFuc2FjdGlvbiIsImFjdGl2YXRlZF9hdCIsImdldERvY3VtZW50c0NvdW50IiwiY291bnQiLCJoZWFkIiwiZ2V0VXNlckJ5RW1haWwiLCJ1c2VycyIsImxpc3RVc2VycyIsImxlbmd0aCIsImZpbmQiLCJ1Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
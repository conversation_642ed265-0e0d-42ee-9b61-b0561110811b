(()=>{var e={};e.id=3302,e.ids=[3302],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28319:(e,r,t)=>{"use strict";function s(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function a(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?s(Object(t),!0).forEach(function(r){var s,a,o;s=e,a=r,o=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(a))in s?Object.defineProperty(s,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):s[a]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}t.d(r,{E:()=>o,SupabaseAdminService:()=>i});let o=(0,t(41370).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class i{static async createStripeTransaction(e){let{data:r,error:t}=await o.from("stripe_transactions").insert([e]).select().single();if(t)throw console.error("Error creating stripe transaction:",t),Error(`Failed to create transaction: ${t.message}`);return r}static async getTransactionBySessionId(e){let{data:r,error:t}=await o.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching transaction:",t),Error(`Failed to fetch transaction: ${t.message}`);return r}static async createUserWithInvitation(e,r){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:r,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:t,error:s}=await o.auth.admin.inviteUserByEmail(e,{data:r,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!t,hasUser:!!t?.user,userId:t?.user?.id,userEmail:t?.user?.email,userAud:t?.user?.aud,userRole:t?.user?.role,emailConfirmed:t?.user?.email_confirmed_at,userMetadata:t?.user?.user_metadata,appMetadata:t?.user?.app_metadata,error:s?.message,errorCode:s?.status,fullError:s}),s)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:s.message,status:s.status,details:s}),Error(`Failed to create user invitation: ${s.message}`);return t}static async createUserWithPassword(e,r,t,s=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:t,sendConfirmationEmail:s,timestamp:new Date().toISOString()});let{data:a,error:i}=await o.auth.admin.createUser({email:e,password:r,user_metadata:t,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!a,hasUser:!!a?.user,userId:a?.user?.id,userEmail:a?.user?.email,emailConfirmed:a?.user?.email_confirmed_at,userMetadata:a?.user?.user_metadata,error:i?.message,errorCode:i?.status}),i)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:i.message,status:i.status,details:i}),{data:null,error:i};if(a?.user&&s){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:t}=await o.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});t?console.error("⚠️ Error enviando email de confirmaci\xf3n:",t):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else a?.user&&!s&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:a,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:r,error:t}=await o.auth.admin.getUserById(e);if(t||!r?.user)return console.error("Error obteniendo datos del usuario:",t),{success:!1,error:"Usuario no encontrado"};let s=r.user,{error:i}=await o.auth.admin.updateUserById(s.id,{email_confirm:!0,user_metadata:a(a({},s.user_metadata),{},{payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()})});if(i)return console.error("⚠️ Error confirmando email del usuario:",i),{success:!1,error:i.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,r){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:t}=await o.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return t?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",t),{success:!1,error:t.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:r,error:t}=await o.from("user_profiles").insert([e]).select().single();if(t)throw console.error("Error creating user profile:",t),Error(`Failed to create user profile: ${t.message}`);return r}static async upsertUserProfile(e){let{data:r,error:t}=await o.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(t)throw console.error("Error upserting user profile:",t),Error(`Failed to upsert user profile: ${t.message}`);return r}static async logPlanChange(e){let{data:r,error:t}=await o.from("user_plan_history").insert([e]).select().single();if(t)throw console.error("Error logging plan change:",t),Error(`Failed to log plan change: ${t.message}`);return r}static async logFeatureAccess(e){let{data:r,error:t}=await o.from("feature_access_log").insert([e]).select().single();if(t)throw console.error("Error logging feature access:",t),Error(`Failed to log feature access: ${t.message}`);return r}static async getUserProfile(e){let{data:r,error:t}=await o.from("user_profiles").select("*").eq("user_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching user profile:",t),Error(`Failed to fetch user profile: ${t.message}`);return r}static async updateTransactionWithUser(e,r){let{error:t}=await o.from("stripe_transactions").update({user_id:r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error updating transaction with user_id:",t),Error(`Failed to update transaction: ${t.message}`)}static async activateTransaction(e){let{error:r}=await o.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error activating transaction:",r),Error(`Failed to activate transaction: ${r.message}`)}static async getDocumentsCount(e){let{count:r,error:t}=await o.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return t?(console.error("Error getting documents count:",t),0):r||0}static async getUserByEmail(e){try{let{data:{users:r},error:t}=await o.auth.admin.listUsers();if(t)throw console.error("Error getting user by email:",t),Error(`Failed to get user by email: ${t.message}`);if(!r||0===r.length)return null;let s=r.find(r=>r.email===e);if(!s)return null;return{id:s.id,email:s.email,email_confirmed_at:s.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73274:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var s={};t.r(s),t.d(s,{POST:()=>u});var a=t(12693),o=t(79378),i=t(26833),n=t(32644),c=t(28319);async function u(e){try{let{email:r}=await e.json();if(!r)return n.NextResponse.json({error:"Email es requerido"},{status:400});let{data:t,error:s}=await c.E.auth.admin.generateLink({type:"recovery",email:r,options:{redirectTo:"http://localhost:3000/auth/reset-password"}});if(s)return console.error("Error generating password reset link:",s),n.NextResponse.json({error:"Error generando enlace de recuperaci\xf3n"},{status:500});return n.NextResponse.json({success:!0,link:t.properties?.action_link,message:"Enlace de recuperaci\xf3n generado"})}catch(e){return console.error("Error in generate-password-reset:",e),n.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/generate-password-reset/route",pathname:"/api/auth/generate-password-reset",filename:"route",bundlePath:"app/api/auth/generate-password-reset/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:m}=l;function g(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4979,8082,1370],()=>t(73274));module.exports=s})();
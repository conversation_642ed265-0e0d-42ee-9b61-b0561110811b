(()=>{var e={};e.id=4015,e.ids=[4015],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28319:(e,r,t)=>{"use strict";function a(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?a(Object(t),!0).forEach(function(r){var a,o,s;a=e,o=r,s=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[o]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}t.d(r,{E:()=>s,SupabaseAdminService:()=>n});let s=(0,t(41370).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class n{static async createStripeTransaction(e){let{data:r,error:t}=await s.from("stripe_transactions").insert([e]).select().single();if(t)throw console.error("Error creating stripe transaction:",t),Error(`Failed to create transaction: ${t.message}`);return r}static async getTransactionBySessionId(e){let{data:r,error:t}=await s.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching transaction:",t),Error(`Failed to fetch transaction: ${t.message}`);return r}static async createUserWithInvitation(e,r){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:r,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:t,error:a}=await s.auth.admin.inviteUserByEmail(e,{data:r,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!t,hasUser:!!t?.user,userId:t?.user?.id,userEmail:t?.user?.email,userAud:t?.user?.aud,userRole:t?.user?.role,emailConfirmed:t?.user?.email_confirmed_at,userMetadata:t?.user?.user_metadata,appMetadata:t?.user?.app_metadata,error:a?.message,errorCode:a?.status,fullError:a}),a)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:a.message,status:a.status,details:a}),Error(`Failed to create user invitation: ${a.message}`);return t}static async createUserWithPassword(e,r,t,a=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:t,sendConfirmationEmail:a,timestamp:new Date().toISOString()});let{data:o,error:n}=await s.auth.admin.createUser({email:e,password:r,user_metadata:t,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!o,hasUser:!!o?.user,userId:o?.user?.id,userEmail:o?.user?.email,emailConfirmed:o?.user?.email_confirmed_at,userMetadata:o?.user?.user_metadata,error:n?.message,errorCode:n?.status}),n)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:n.message,status:n.status,details:n}),{data:null,error:n};if(o?.user&&a){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:t}=await s.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});t?console.error("⚠️ Error enviando email de confirmaci\xf3n:",t):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else o?.user&&!a&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:o,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:r,error:t}=await s.auth.admin.getUserById(e);if(t||!r?.user)return console.error("Error obteniendo datos del usuario:",t),{success:!1,error:"Usuario no encontrado"};let a=r.user,{error:n}=await s.auth.admin.updateUserById(a.id,{email_confirm:!0,user_metadata:o(o({},a.user_metadata),{},{payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()})});if(n)return console.error("⚠️ Error confirmando email del usuario:",n),{success:!1,error:n.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,r){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:t}=await s.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return t?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",t),{success:!1,error:t.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:r,error:t}=await s.from("user_profiles").insert([e]).select().single();if(t)throw console.error("Error creating user profile:",t),Error(`Failed to create user profile: ${t.message}`);return r}static async upsertUserProfile(e){let{data:r,error:t}=await s.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(t)throw console.error("Error upserting user profile:",t),Error(`Failed to upsert user profile: ${t.message}`);return r}static async logPlanChange(e){let{data:r,error:t}=await s.from("user_plan_history").insert([e]).select().single();if(t)throw console.error("Error logging plan change:",t),Error(`Failed to log plan change: ${t.message}`);return r}static async logFeatureAccess(e){let{data:r,error:t}=await s.from("feature_access_log").insert([e]).select().single();if(t)throw console.error("Error logging feature access:",t),Error(`Failed to log feature access: ${t.message}`);return r}static async getUserProfile(e){let{data:r,error:t}=await s.from("user_profiles").select("*").eq("user_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching user profile:",t),Error(`Failed to fetch user profile: ${t.message}`);return r}static async updateTransactionWithUser(e,r){let{error:t}=await s.from("stripe_transactions").update({user_id:r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error updating transaction with user_id:",t),Error(`Failed to update transaction: ${t.message}`)}static async activateTransaction(e){let{error:r}=await s.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error activating transaction:",r),Error(`Failed to activate transaction: ${r.message}`)}static async getDocumentsCount(e){let{count:r,error:t}=await s.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return t?(console.error("Error getting documents count:",t),0):r||0}static async getUserByEmail(e){try{let{data:{users:r},error:t}=await s.auth.admin.listUsers();if(t)throw console.error("Error getting user by email:",t),Error(`Failed to get user by email: ${t.message}`);if(!r||0===r.length)return null;let a=r.find(r=>r.email===e);if(!a)return null;return{id:a.id,email:a.email,email_confirmed_at:a.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65162:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var a={};t.r(a),t.d(a,{POST:()=>p});var o=t(12693),s=t(79378),n=t(26833),i=t(32644),c=t(28319),u=t(84344);function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function d(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var a,o,s;a=e,o=r,s=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[o]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function p(e){console.log("\uD83D\uDE80 [PRE-REGISTER-PAID] Endpoint llamado");try{console.log("\uD83D\uDCE5 [PRE-REGISTER-PAID] Parseando body...");let r=await e.json();console.log("\uD83D\uDCE5 [PRE-REGISTER-PAID] Body parseado:",d(d({},r),{},{password:"[HIDDEN]"}));let{email:t,password:a,customerName:o,planId:s}=r;if(!t||!a||!s)return console.log("❌ [PRE-REGISTER-PAID] Validaci\xf3n b\xe1sica fall\xf3"),i.NextResponse.json({error:"Email, contrase\xf1a y plan son requeridos"},{status:400});if(a.length<6)return console.log("❌ [PRE-REGISTER-PAID] Contrase\xf1a muy corta"),i.NextResponse.json({error:"La contrase\xf1a debe tener al menos 6 caracteres"},{status:400});console.log("\uD83D\uDD0D [PRE-REGISTER-PAID] Validando plan...");let n=(0,u.IE)(s);if(!n)return console.log("❌ [PRE-REGISTER-PAID] Plan no v\xe1lido:",s),i.NextResponse.json({error:"Plan no v\xe1lido"},{status:400});if("free"===s)return console.log("❌ [PRE-REGISTER-PAID] Plan gratuito no permitido"),i.NextResponse.json({error:"Este endpoint es solo para planes de pago"},{status:400});console.log("\uD83D\uDD04 Pre-registrando usuario para plan de pago:",{email:t,planId:s,customerName:o,timestamp:new Date().toISOString()});try{let e={name:o||t.split("@")[0],plan:s,payment_verified:!1,pre_registered:!0,pre_registration_date:new Date().toISOString(),awaiting_payment:!0},{data:r,error:l}=await c.SupabaseAdminService.createUserWithPassword(t,a,e,!1);if(l){if(console.error("Error creando usuario:",l),l.message?.includes("User already registered")||l.message?.includes("email_address_not_authorized"))return i.NextResponse.json({error:"Ya existe una cuenta con este email. Por favor, usa otro email o inicia sesi\xf3n."},{status:409});return i.NextResponse.json({error:"Error creando la cuenta. Por favor, int\xe9ntalo de nuevo."},{status:500})}if(!r?.user)return i.NextResponse.json({error:"Error creando la cuenta"},{status:500});let d=r.user.id;console.log("✅ Usuario pre-registrado exitosamente:",d);try{await c.SupabaseAdminService.createUserProfile({user_id:d,subscription_plan:s,monthly_token_limit:(0,u.t4)(s),current_month_tokens:0,current_month:new Date().toISOString().split("T")[0],payment_verified:!1,plan_features:n.features,security_flags:{pre_registered:!0,awaiting_payment:!0,created_at:new Date().toISOString()}}),console.log("✅ Perfil de usuario creado en estado pendiente")}catch(e){console.error("Error creando perfil:",e)}return i.NextResponse.json({success:!0,userId:d,message:"Usuario pre-registrado exitosamente",data:{email:t,planId:s,awaitingPayment:!0}})}catch(e){return console.error("Error en pre-registro:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}catch(e){return console.error("Error procesando pre-registro:",e),i.NextResponse.json({error:"Error procesando la solicitud"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/auth/pre-register-paid/route",pathname:"/api/auth/pre-register-paid",filename:"route",bundlePath:"app/api/auth/pre-register-paid/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:_}=m;function h(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84344:(e,r,t)=>{"use strict";t.d(r,{IE:()=>o,Nu:()=>n,qo:()=>a,t4:()=>s});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function o(e){return a[e]||null}function s(e){let r=o(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function n(e,r){let t=o(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4979,8082,1370],()=>t(65162));module.exports=a})();
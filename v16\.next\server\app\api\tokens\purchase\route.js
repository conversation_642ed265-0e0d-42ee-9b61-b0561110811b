(()=>{var e={};e.id=3566,e.ids=[3566],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10167:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(12693),n=t(79378),o=t(26833),i=t(32644),u=t(83760),d=t(82649);async function c(e){try{let{tokenAmount:r,price:t}=await e.json();if(!r||!t)return i.NextResponse.json({error:"Par\xe1metros requeridos: tokenAmount y price"},{status:400});if(1e6!==r||10!==t)return i.NextResponse.json({error:"Solo se permite la compra de 1,000,000 tokens por 10.00€"},{status:400});let a=(0,u.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:s},error:n}=await a.auth.getUser();if(n||!s)return i.NextResponse.json({error:"Usuario no autenticado"},{status:401});let{data:o,error:c}=await a.from("user_profiles").select("subscription_plan, payment_verified").eq("user_id",s.id).single();if(c||!o)return i.NextResponse.json({error:"Perfil de usuario no encontrado"},{status:404});if("free"===o.subscription_plan)return i.NextResponse.json({error:"Los usuarios con plan gratuito no pueden comprar tokens adicionales"},{status:403});if(!o.payment_verified)return i.NextResponse.json({error:"Debe tener un plan de pago verificado para comprar tokens adicionales"},{status:403});if(!d._4)return console.error("Stripe not initialized"),i.NextResponse.json({error:"Error de configuraci\xf3n de Stripe"},{status:500});let p=await d._4.checkout.sessions.create({payment_method_types:["card"],line_items:[{price_data:{currency:"eur",product_data:{name:"Tokens Adicionales - OposiAI",description:"1,000,000 tokens adicionales para tu cuenta",images:[]},unit_amount:Math.round(100*t)},quantity:1}],mode:"payment",customer_email:s.email,client_reference_id:s.id,metadata:{type:"token_purchase",user_id:s.id,token_amount:r.toString(),price:t.toString(),created_at:new Date().toISOString()},success_url:"http://localhost:3000/app?token_purchase=success",cancel_url:"http://localhost:3000/app?token_purchase=cancelled",automatic_tax:{enabled:!0},billing_address_collection:"required"});return console.log("Token purchase session created:",p.id),i.NextResponse.json({sessionId:p.id,url:p.url})}catch(e){return console.error("Error creating token purchase session:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tokens/purchase/route",pathname:"/api/tokens/purchase",filename:"route",bundlePath:"app/api/tokens/purchase/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:_}=p;function f(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82649:(e,r,t)=>{"use strict";t.d(r,{L6:()=>u,Md:()=>o,aN:()=>i,_4:()=>d});var a=t(57052),s=t(84344);let n={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:s.qo.free.limits,planConfig:s.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:s.qo.usuario.limits,planConfig:s.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:s.qo.pro.limits,planConfig:s.qo.pro}};function o(e){return n[e]||null}function i(e){return e in n}let u={success:"http://localhost:3000/thank-you",cancel:"http://localhost:3000/upgrade-plan",webhook:"http://localhost:3000/api/stripe/webhook"},d=new a.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0})},84344:(e,r,t)=>{"use strict";t.d(r,{IE:()=>s,Nu:()=>o,qo:()=>a,t4:()=>n});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return a[e]||null}function n(e){let r=s(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function o(e,r){let t=s(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4979,8082,1370,3760,7052],()=>t(10167));module.exports=a})();
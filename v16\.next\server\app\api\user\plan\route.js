(()=>{var e={};e.id=643,e.ids=[643],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11345:(e,r,t)=>{"use strict";t.d(r,{createServerSupabaseClient:()=>u});var s=t(83760),i=t(33465);function n(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach(function(r){var s,i,n;s=e,i=r,n=t[r],(i=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(i))in s?Object.defineProperty(s,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function u(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},cookies:{getAll:()=>e.getAll(),setAll(r){try{r.filter(e=>!e.name.includes("auth-token")&&!e.name.includes("refresh-token")).forEach(({name:r,value:t,options:s})=>e.set(r,t,o(o({},s),{},{maxAge:void 0,expires:void 0})))}catch{}}}})}},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34072:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(12693),n=t(79378),o=t(26833),u=t(32644),a=t(11345);async function p(){try{let e=await (0,a.createServerSupabaseClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(!r||t)return u.NextResponse.json({error:"No autorizado"},{status:401});let{data:s,error:i}=await e.from("user_profiles").select("subscription_plan").eq("user_id",r.id).single();if(i)return console.error("Error obteniendo perfil:",i),u.NextResponse.json({plan:"free"});return u.NextResponse.json({plan:s?.subscription_plan||"free"})}catch(e){return console.error("Error en API user/plan:",e),u.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/user/plan/route",pathname:"/api/user/plan",filename:"route",bundlePath:"app/api/user/plan/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:f}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4979,8082,1370,3760,3465],()=>t(34072));module.exports=s})();
"use strict";(()=>{var e={};e.id=397,e.ids=[397],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46683:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>I,routeModule:()=>g,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var o={};t.r(o),t.d(o,{GET:()=>f,PUT:()=>m});var i=t(12693),s=t(79378),n=t(26833),a=t(32644),u=t(83760),p=t(85315),l=t(28319);function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}function d(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var o,i,s;o=e,i=r,s=t[r],(i=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,r||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(i))in o?Object.defineProperty(o,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):o[i]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function f(e){try{let r=(0,u.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return a.NextResponse.json({error:"No autorizado"},{status:401});let i=await p.o.getUserAccessInfo(t.id);console.log("[API /user/profile] accessInfo recuperado:",JSON.stringify(i,null,2));let{data:s,error:n}=await l.E.from("user_profiles").select("*").eq("user_id",t.id).single();if(console.log("[API /user/profile] Perfil directo de BD:",JSON.stringify(s,null,2)),n&&console.error("[API /user/profile] Error perfil directo BD:",n),!i)return a.NextResponse.json({error:"Perfil no encontrado"},{status:404});let{data:c,error:f}=await r.from("user_profiles").select("*").eq("user_id",t.id).single();if(f)return a.NextResponse.json({error:"Error obteniendo perfil"},{status:500});let m=await p.o.checkUpgradeNeeded(t.id);return a.NextResponse.json({user:{id:t.id,email:t.email,name:t.user_metadata?.name||t.email?.split("@")[0],created_at:t.created_at},profile:d(d({},c),{},{plan_name:c.subscription_plan}),access:{plan:i.plan||"free",features:Array.isArray(i.features)?i.features:[],limits:i.limits||{},currentUsage:i.currentUsage||{},paymentVerified:i.paymentVerified||!1},upgrade:m||{needsUpgrade:!1},tokenUsage:{current:c.current_month_tokens||0,limit:c.monthly_token_limit||0,percentage:c.monthly_token_limit>0?Math.round((c.current_month_tokens||0)/c.monthly_token_limit*100):0,remaining:Math.max(0,(c.monthly_token_limit||0)-(c.current_month_tokens||0))}})}catch(e){return console.error("Error in profile API:",e),a.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function m(e){try{let r=(0,u.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:o}=await r.auth.getUser();if(o||!t)return a.NextResponse.json({error:"No autorizado"},{status:401});let{name:i,preferences:s}=await e.json();if(i){let{error:e}=await l.E.auth.admin.updateUserById(t.id,{user_metadata:{name:i}});e&&console.error("Error updating user metadata:",e)}if(s){let{error:e}=await r.from("user_profiles").update({security_flags:d(d({},s),{},{updated_at:new Date().toISOString()}),updated_at:new Date().toISOString()}).eq("user_id",t.id);if(e)return console.error("Error updating profile preferences:",e),a.NextResponse.json({error:"Error actualizando preferencias"},{status:500})}return a.NextResponse.json({success:!0,message:"Perfil actualizado correctamente"})}catch(e){return console.error("Error updating profile:",e),a.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:j}=g;function I(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4979,8082,1370,3760,8844,5315],()=>t(46683));module.exports=o})();
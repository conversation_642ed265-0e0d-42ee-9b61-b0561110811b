"use strict";(()=>{var e={};e.id=3407,e.ids=[3407],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29389:(e,r,t)=>{t.d(r,{U:()=>a});var s=t(83760);function a(){return(0,s.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}a()},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},90939:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>l,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>d,PUT:()=>c});var a=t(12693),o=t(79378),n=t(26833),i=t(32644),u=t(29389),p=t(85315);async function d(e){try{let r=(0,u.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"No autorizado"},{status:401});let{feature:a,tokensToUse:o=0,action:n,quantity:d=1}=await e.json();if(!a)return i.NextResponse.json({error:"Caracter\xedstica requerida"},{status:400});let c=await p.o.validateFeatureAccess(t.id,a,o);if(!c.allowed)return i.NextResponse.json({allowed:!1,reason:c.reason,needsUpgrade:!0},{status:403});if(n){let e=await p.o.canUserPerformAction(t.id,n,d);if(!e.allowed)return i.NextResponse.json({allowed:!1,reason:e.reason,needsUpgrade:!0},{status:403})}return i.NextResponse.json({allowed:!0,remainingUsage:c.remainingUsage,planLimits:c.planLimits})}catch(e){return console.error("Error validating access:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function c(e){try{let r=(0,u.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return i.NextResponse.json({error:"No autorizado"},{status:401});let{tokensUsed:a,activity:o}=await e.json();if(!a||!o)return i.NextResponse.json({error:"Tokens y actividad requeridos"},{status:400});if(!await p.o.updateTokenUsage(t.id,a,o))return i.NextResponse.json({error:"Error actualizando uso de tokens"},{status:500});let n=await p.o.getUserAccessInfo(t.id);return i.NextResponse.json({success:!0,tokensUsed:a,currentUsage:n?.currentUsage||null})}catch(e){return console.error("Error updating token usage:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/user/validate-access/route",pathname:"/api/user/validate-access",filename:"route",bundlePath:"app/api/user/validate-access/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:j}=l;function w(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4979,8082,1370,3760,8844,5315],()=>t(90939));module.exports=s})();
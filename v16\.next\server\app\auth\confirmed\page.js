(()=>{var e={};e.id=5223,e.ids=[5223],e.modules={13:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(96554),n=t(50653),o=t(48921),a=t(81815),i=t(99631);function u(){let e=(0,n.useRouter)();(0,o.U)();let{0:r,1:t}=(0,s.useState)("loading"),{0:u,1:c}=(0,s.useState)("Verificando tu confirmaci\xf3n de email...");return(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:(0,i.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8 max-w-md w-full text-center",children:[(()=>{switch(r){case"loading":default:return(0,i.jsx)(a.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"});case"success":case"redirecting":return(0,i.jsx)(a.A3x,{className:"w-12 h-12 text-green-600 mx-auto mb-4"});case"error":return(0,i.jsx)(a.eHT,{className:"w-12 h-12 text-red-600 mx-auto mb-4"})}})(),(0,i.jsx)("h1",{className:"text-2xl font-semibold text-gray-800 mb-4",children:(()=>{switch(r){case"loading":return"Confirmando tu email...";case"success":return"\xa1Email confirmado!";case"redirecting":return"\xa1Confirmaci\xf3n exitosa!";case"error":return"Error en la confirmaci\xf3n";default:return"Procesando..."}})()}),(0,i.jsx)("p",{className:"text-gray-600 mb-6",children:u}),"error"===r&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("button",{onClick:()=>e.push("/auth/login"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"}),(0,i.jsx)("button",{onClick:()=>e.push("/"),className:"w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors",children:"Volver al Inicio"})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34516:(e,r,t)=>{Promise.resolve().then(t.bind(t,81257))},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67911:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=t(67061),n=t(79378),o=t(1852),a=t.n(o),i=t(13547),u={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>i[e]);t.d(r,u);let c={children:["",{children:["auth",{children:["confirmed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81257)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/confirmed/page",pathname:"/auth/confirmed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81257:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirmed\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},87252:(e,r,t)=>{Promise.resolve().then(t.bind(t,13))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4979,6096,1815,4445],()=>t(67911));module.exports=s})();
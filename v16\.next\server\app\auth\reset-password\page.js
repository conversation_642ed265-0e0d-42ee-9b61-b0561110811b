(()=>{var e={};e.id=4089,e.ids=[4089],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5338:(e,s,r)=>{Promise.resolve().then(r.bind(r,96961))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39309:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75315:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(67061),a=r(79378),n=r(1852),o=r.n(n),i=r(13547),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let d={children:["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39309)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81778:(e,s,r)=>{Promise.resolve().then(r.bind(r,39309))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96961:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(96554),a=r(50653),n=r(48921),o=r(29959),i=r(81815),l=r(99631);function d(){let e=(0,a.useRouter)();(0,a.useSearchParams)();let{0:s,1:r}=(0,t.useState)(""),{0:d,1:c}=(0,t.useState)(""),{0:u,1:m}=(0,t.useState)(!1),{0:p,1:x}=(0,t.useState)(""),{0:h,1:b}=(0,t.useState)(!0),{0:f,1:g}=(0,t.useState)(!1),{0:v,1:j}=(0,t.useState)(null),w=async r=>{if(r.preventDefault(),x(""),!f){x("No se ha establecido una sesi\xf3n v\xe1lida para cambiar la contrase\xf1a. Por favor, aseg\xfarate de usar el enlace de tu email."),o.oR.error("Error de sesi\xf3n. Intenta usar el enlace de tu email de nuevo.");return}if(s.length<6)return void x("La nueva contrase\xf1a debe tener al menos 6 caracteres.");if(s!==d)return void x("Las contrase\xf1as no coinciden.");m(!0);let t=(0,n.U)(),{data:a,error:i}=await t.auth.getUser();if(i||!a.user){x("No se pudo verificar la sesi\xf3n actual antes de actualizar. Intenta de nuevo."),m(!1);return}let l=a.user.user_metadata?.requires_terms_acceptance_and_final_password_setup===!0||a.user.user_metadata?.requires_initial_password_change===!0,c={};l&&(c.requires_terms_acceptance_and_final_password_setup=!1,c.temporary_password_set=!1,c.requires_initial_password_change=!1);let{error:u}=await t.auth.updateUser({password:s,data:c});m(!1),u?(console.error("ResetPasswordForm: Error al actualizar contrase\xf1a:",u),x("Auth session missing!"===u.message?"Error de sesi\xf3n: Tu sesi\xf3n ha expirado o es inv\xe1lida. Por favor, usa el enlace de tu email de nuevo.":u.message),o.oR.error("Auth session missing!"===u.message?"Error de sesi\xf3n. Usa el enlace de tu email.":"Error al actualizar la contrase\xf1a.")):(o.oR.success("\xa1Contrase\xf1a establecida/actualizada exitosamente!"),console.log("ResetPasswordForm: Contrase\xf1a actualizada. Redirigiendo a /app"),e.push("/app"))};return h?(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,l.jsx)(i.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Verificando enlace..."}),(0,l.jsx)("p",{className:"text-gray-600 mt-2",children:"Esto puede tardar unos segundos."})]}):v?(0,l.jsx)("div",{className:"min-h-screen bg-red-50 flex flex-col justify-center items-center p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,l.jsx)(i.Ohp,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Enlace Inv\xe1lido o Expirado"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:v}),(0,l.jsx)("button",{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Volver a Inicio de Sesi\xf3n"})]})}):f||h?(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsx)(i.F5$,{className:"w-12 h-12 text-blue-600"})}),(0,l.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Crea tu Nueva Contrase\xf1a"})]}),(0,l.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,l.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,l.jsxs)("form",{className:"space-y-6",onSubmit:w,children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Nueva Contrase\xf1a"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:s,onChange:e=>r(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"M\xednimo 6 caracteres"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar Nueva Contrase\xf1a"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:d,onChange:e=>c(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Repite la contrase\xf1a"})})]}),p&&(0,l.jsx)("div",{className:"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200",children:p}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{type:"submit",disabled:u||!f,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.TwU,{className:"animate-spin h-4 w-4 mr-2"})," Actualizando..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.YrT,{className:"h-4 w-4 mr-2"})," Establecer Contrase\xf1a"]})})})]})})})]}):(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,l.jsx)(i.F5$,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-700",children:"Acceso no Autorizado"}),(0,l.jsx)("p",{className:"text-gray-500 mt-2 mb-6 max-w-md text-center",children:"Esta p\xe1gina es para establecer o restablecer tu contrase\xf1a usando un enlace seguro enviado a tu email. Si necesitas restablecer tu contrase\xf1a, solic\xedtalo desde la p\xe1gina de inicio de sesi\xf3n."}),(0,l.jsx)("button",{onClick:()=>e.push("/login"),className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Inicio de Sesi\xf3n"})]})}function c(){return(0,l.jsx)(t.Suspense,{fallback:(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:[(0,l.jsx)(i.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Cargando..."})]}),children:(0,l.jsx)(d,{})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4979,6096,1815,4445],()=>r(75315));module.exports=t})();
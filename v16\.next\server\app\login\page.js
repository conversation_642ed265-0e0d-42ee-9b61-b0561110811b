(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9871:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(67061),a=r(79378),o=r(1852),n=r.n(o),i=r(13547),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40492)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68652:(e,t,r)=>{Promise.resolve().then(r.bind(r,40492))},74075:e=>{"use strict";e.exports=require("zlib")},74732:(e,t,r)=>{Promise.resolve().then(r.bind(r,90128))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90128:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(96554),a=r(50653),o=r(41150),n=r(99631);function i(){let{0:e,1:t}=(0,s.useState)(""),{0:r,1:i}=(0,s.useState)(""),{0:l,1:c}=(0,s.useState)(""),{iniciarSesion:d,error:u,isLoading:p,user:m}=(0,o.A)();(0,a.useRouter)();let x=async t=>(t.preventDefault(),c(""),e.trim())?r.trim()?void await d(e,r):void c("Por favor, ingresa tu contrase\xf1a"):void c("Por favor, ingresa tu email");return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"OposiAI"}),(0,n.jsx)("h2",{className:"mt-2 text-center text-xl font-semibold text-gray-900",children:"Inicia sesi\xf3n en tu cuenta"}),(0,n.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu asistente inteligente para oposiciones"})]}),(0,n.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,n.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,n.jsxs)("form",{className:"space-y-6",onSubmit:x,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,n.jsx)("div",{className:"mt-1",children:(0,n.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",disabled:p})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Contrase\xf1a"}),(0,n.jsx)("div",{className:"mt-1",children:(0,n.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>i(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",disabled:p})})]}),l&&(0,n.jsxs)("div",{className:"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200",children:[l,l.includes("sincronizaci\xf3n de tiempo")&&(0,n.jsxs)("div",{className:"mt-2 text-gray-600 text-xs",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Sugerencia:"})," Este error puede ocurrir cuando la hora de tu dispositivo no est\xe1 sincronizada correctamente."]}),(0,n.jsxs)("ol",{className:"list-decimal pl-5 mt-1",children:[(0,n.jsx)("li",{children:"Verifica que la fecha y hora de tu dispositivo est\xe9n configuradas correctamente"}),(0,n.jsx)("li",{children:"Activa la sincronizaci\xf3n autom\xe1tica de hora en tu sistema"}),(0,n.jsx)("li",{children:"Reinicia el navegador e intenta nuevamente"})]})]})]}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"submit",disabled:p,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:p?(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Iniciando sesi\xf3n..."]}):"Iniciar sesi\xf3n"})})]}),(0,n.jsx)("div",{className:"mt-6",children:(0,n.jsx)("p",{className:"text-center text-sm text-gray-600",children:"\xbfNo tienes una cuenta? Contacta con el administrador para solicitar acceso."})})]})})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4979,6096,4445],()=>r(9871));module.exports=s})();
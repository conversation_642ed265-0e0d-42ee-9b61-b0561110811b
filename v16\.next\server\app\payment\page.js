(()=>{var e={};e.id=8407,e.ids=[8407],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13469:(e,r,a)=>{Promise.resolve().then(a.bind(a,26571))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26571:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>d});var t=a(96554),s=a(50653),o=a(74595),n=a(29959),i=a(99631);function l(){let e=(0,s.useRouter)(),r=(0,s.useSearchParams)().get("plan")||"free",{0:a,1:l}=(0,t.useState)(""),{0:d,1:u}=(0,t.useState)(""),{0:c,1:m}=(0,t.useState)(""),{0:p,1:f}=(0,t.useState)(""),{0:h,1:x}=(0,t.useState)(!1),g=(0,o.Md)(r),b=async t=>{if(t.preventDefault(),!a.trim())return void n.Ay.error("Por favor, ingresa tu email");if(!d.trim())return void n.Ay.error("Por favor, ingresa una contrase\xf1a");if(d.length<6)return void n.Ay.error("La contrase\xf1a debe tener al menos 6 caracteres");if(d!==c)return void n.Ay.error("Las contrase\xf1as no coinciden");x(!0);try{if("free"===r){let t=await fetch("/api/auth/register-free",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:d,customerName:p})}),s=await t.json();t.ok&&s.success?(n.Ay.success("Registro exitoso. Revisa tu email para confirmar tu cuenta."),e.push(`/thank-you?plan=${r}&email_sent=true`)):429===t.status?n.Ay.error("Demasiados intentos. Int\xe9ntalo en 15 minutos."):n.Ay.error(s.error||"Error al crear la cuenta gratuita")}else{console.log("\uD83D\uDD04 Iniciando nuevo flujo de pre-registro para plan de pago");let e=await fetch("/api/auth/pre-register-paid",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:d,customerName:p||a.split("@")[0],planId:r})}),t=await e.json();if(!e.ok)return void n.Ay.error(t.error||"Error al crear la cuenta");console.log("✅ Usuario pre-registrado exitosamente:",t.userId);let s=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:r,email:a,customerName:p,userId:t.userId})}),o=await s.json();s.ok&&o.url?(console.log("\uD83D\uDD04 Redirigiendo a Stripe Checkout..."),window.location.href=o.url):n.Ay.error(o.error||"Error al crear la sesi\xf3n de pago")}}catch(e){console.error("Error en handleSubmit:",e),n.Ay.error("Error al procesar la solicitud. Por favor, intenta de nuevo.")}finally{x(!1)}};return g?(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:g.name}),(0,i.jsxs)("p",{className:"text-2xl font-semibold text-blue-600 mt-2",children:[0===g.price?"Gratis":`€${(g.price/100).toFixed(2)}`,("pro"===r||"usuario"===r)&&g.price>0&&(0,i.jsx)("span",{className:"text-sm text-gray-500",children:"/mes"})]})]}),(0,i.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Caracter\xedsticas incluidas:"}),(0,i.jsx)("ul",{className:"space-y-2",children:g.features.map((e,r)=>(0,i.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,i.jsx)("svg",{className:"h-4 w-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},r))})]}),(0,i.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),(0,i.jsx)("input",{type:"email",id:"email",required:!0,value:a,onChange:e=>l(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:h})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Contrase\xf1a *"}),(0,i.jsx)("input",{type:"password",id:"password",required:!0,value:d,onChange:e=>u(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"M\xednimo 6 caracteres",disabled:h,minLength:6})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar Contrase\xf1a *"}),(0,i.jsx)("input",{type:"password",id:"confirmPassword",required:!0,value:c,onChange:e=>m(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Repite tu contrase\xf1a",disabled:h,minLength:6})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"customerName",className:"block text-sm font-medium text-gray-700",children:"Nombre (opcional)"}),(0,i.jsx)("input",{type:"text",id:"customerName",value:p,onChange:e=>f(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Tu nombre",disabled:h})]}),(0,i.jsx)("button",{type:"submit",disabled:h,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:h?"Procesando...":"free"===r?"Solicitar Acceso Gratuito":"Proceder al Pago"})]})]})]})}):(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Cargando detalles del plan o redirigiendo..."})}function d(){return(0,i.jsx)(t.Suspense,{fallback:(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,i.jsx)("div",{className:"max-w-md mx-auto",children:(0,i.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,i.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})})})}),children:(0,i.jsx)(l,{})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37547:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var t=a(67061),s=a(79378),o=a(1852),n=a.n(o),i=a(13547),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(r,l);let d={children:["",{children:["payment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,67439)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/payment/page",pathname:"/payment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67439:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx","default")},69501:(e,r,a)=>{Promise.resolve().then(a.bind(a,67439))},74075:e=>{"use strict";e.exports=require("zlib")},74595:(e,r,a)=>{"use strict";a.d(r,{Md:()=>o,NB:()=>s});var t=a(91212);let s={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:t.qo.free.limits,planConfig:t.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:t.qo.usuario.limits,planConfig:t.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:t.qo.pro.limits,planConfig:t.qo.pro}};function o(e){return s[e]||null}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91212:(e,r,a)=>{"use strict";a.d(r,{IE:()=>s,qk:()=>n,qo:()=>t});let t={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return t[e]||null}function o(e,r){let a=s(e);return!(!a||a.restrictedFeatures.includes(r))&&a.features.includes(r)}async function n(e){try{let r=await fetch("/api/user/plan");if(!r.ok)return console.error("Error obteniendo plan del usuario"),o("free",e);let{plan:a}=await r.json();return o(a||"free",e)}catch(r){return console.error("Error verificando acceso a caracter\xedstica:",r),o("free",e)}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[4979,6096,4445],()=>a(37547));module.exports=t})();
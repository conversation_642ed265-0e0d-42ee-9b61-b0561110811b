(()=>{var e={};e.id=525,e.ids=[525],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10311:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(67061),a=t(79378),n=t(1852),o=t.n(n),i=t(13547),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["plan-estudios",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,40101)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/plan-estudios/page",pathname:"/plan-estudios",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40101:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx","default")},52987:(e,r,t)=>{Promise.resolve().then(t.bind(t,40101))},53241:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(96554),a=t(81815),n=t(53766),o=t(25580),i=t(21232),l=t(24548),d=t(29399),c=t(29959),u=t(41150),m=t(91212),p=t(68941),x=t(99631);let g=()=>{let{0:e,1:r}=(0,s.useState)(null),{0:t,1:g}=(0,s.useState)(null),{0:h,1:b}=(0,s.useState)(!0),{0:f,1:j}=(0,s.useState)(!1),{0:y,1:v}=(0,s.useState)(!1),{0:w,1:N}=(0,s.useState)([]),P=(0,s.useRef)(null),{user:$}=(0,u.A)(),{0:E,1:C}=(0,s.useState)(null);(0,s.useEffect)(()=>{R()},[]),(0,s.useEffect)(()=>{(async()=>{$&&C(await (0,m.qk)("study_planning")?"paid":"free")})()},[$]);let R=async()=>{b(!0);try{let e=await (0,n.jg)();if(!e){c.oR.error("No se encontr\xf3 un temario configurado"),r(null),v(!1),g(null);return}r(e);let t=await (0,o.vD)(e.id);if(v(t),!t){c.oR.error("Necesitas configurar tu planificaci\xf3n antes de generar el plan de estudios"),g(null);return}let s=await (0,i.fF)(e.id);s&&s.plan_data?(console.log("✅ Plan de estudios existente encontrado"),g(s.plan_data),c.oR.success("Plan de estudios cargado desde la base de datos")):g(null)}catch(e){console.error("Error al cargar datos:",e),c.oR.error("Error al cargar los datos"),r(null),v(!1),g(null)}finally{b(!1)}},q=async()=>{let r;if(e){if(!y)return void c.oR.error("Necesitas configurar tu planificaci\xf3n antes de generar el plan de estudios");j(!0);try{r=c.oR.loading("La generaci\xf3n del plan de estudios puede tardar unos minutos. Si encuentra alg\xfan fallo una vez finalizado, vuelve a generar. OposiAI puede cometer errores de configuraci\xf3n.",{duration:0});let t=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generarPlanEstudios",peticion:e.id,contextos:[]})});if(!t.ok){let e=(await t.json().catch(()=>({}))).error||`Error en la API: ${t.status}`;throw Error(e)}let{result:s}=await t.json();g(s),c.oR.success("\xa1Plan de estudios generado exitosamente!",{id:r})}catch(e){console.error("Error al generar plan:",e),(e instanceof Error?e.message:"Error desconocido").includes("planificaci\xf3n configurada")?c.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"',{id:r}):c.oR.error("Error al generar el plan de estudios. Int\xe9ntalo de nuevo.",{id:r})}finally{j(!1)}}},_=r=>{let t=`# Plan de Estudios - ${e?.titulo}

`;return t+=`${r.introduccion}

## Resumen del Plan

- **Tiempo total de estudio:** ${r.resumen.tiempoTotalEstudio}
- **N\xfamero de temas:** ${r.resumen.numeroTemas}
- **Duraci\xf3n estudio nuevo:** ${r.resumen.duracionEstudioNuevo}
- **Duraci\xf3n repaso final:** ${r.resumen.duracionRepasoFinal}

## Cronograma Semanal

`,r.semanas.forEach(e=>{t+=`### Semana ${e.numero} (${e.fechaInicio} - ${e.fechaFin})

**Objetivo:** ${e.objetivoPrincipal}

`,e.dias.forEach(e=>{t+=`**${e.dia} (${e.horas}h):**
`,e.tareas.forEach(e=>{t+=`- ${e.titulo} (${e.duracionEstimada})
`,e.descripcion&&(t+=`  ${e.descripcion}
`)}),t+="\n"})}),t+=`## Estrategia de Repasos

${r.estrategiaRepasos}

## Pr\xf3ximos Pasos

${r.proximosPasos}
`};return h?(0,x.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,x.jsxs)("div",{className:"text-center",children:[(0,x.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,x.jsx)("p",{className:"text-gray-600",children:"Cargando datos..."})]})}):"free"===E?(0,x.jsx)(p.A,{feature:"study_planning",benefits:["Planes de estudio personalizados con IA","Cronogramas adaptativos a tu ritmo","Seguimiento autom\xe1tico de progreso","Recomendaciones inteligentes de repaso"]}):e&&y?(0,x.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,x.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:[(0,x.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4",children:(0,x.jsxs)("div",{className:"flex items-center justify-between",children:[(0,x.jsxs)("div",{children:[(0,x.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-1",children:"Mi Plan de Estudios"}),(0,x.jsxs)("p",{className:"text-sm text-gray-600",children:["Plan personalizado generado con IA para: ",(0,x.jsx)("strong",{children:e.titulo})]})]}),(0,x.jsx)("div",{className:"flex items-center space-x-3",children:t&&(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("button",{onClick:()=>{g(null),q()},disabled:f,className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 text-sm",children:[(0,x.jsx)(a.jTZ,{className:"w-4 h-4 mr-2"}),"Regenerar"]}),(0,x.jsxs)("button",{onClick:()=>{if(!t)return;let r=new Blob([_(t)],{type:"text/markdown"}),s=URL.createObjectURL(r),a=document.createElement("a");a.href=s,a.download=`plan-estudios-${e?.titulo||"temario"}.md`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s),c.oR.success("Plan descargado exitosamente")},className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[(0,x.jsx)(a.a4x,{className:"w-4 h-4 mr-2"}),"Descargar"]}),(0,x.jsxs)("button",{onClick:()=>{if(!t)return;let r=_(t),s=window.open("","_blank");s&&(s.document.write(`
        <html>
          <head>
            <title>Plan de Estudios - ${e?.titulo}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
              h1, h2, h3 { color: #333; }
              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }
              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }
              ul, ol { margin-left: 20px; }
              strong { color: #2563eb; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            <div id="content"></div>
            <script>
              // Convertir markdown a HTML b\xe1sico para impresi\xf3n
              const markdown = ${JSON.stringify(r)};
              const content = markdown
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')
                .replace(/\\n/g, '<br>');
              document.getElementById('content').innerHTML = content;
              window.print();
            </script>
          </body>
        </html>
      `),s.document.close())},className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[(0,x.jsx)(a.Mvz,{className:"w-4 h-4 mr-2"}),"Imprimir"]})]})})]})}),(0,x.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6",children:[(0,x.jsxs)("div",{className:"flex items-center mb-3",children:[(0,x.jsx)(a.jH2,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,x.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Documentos Seleccionados"})]}),(0,x.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Selecciona los documentos que quieres consultar durante el estudio de tu plan personalizado."}),(0,x.jsx)(d.A,{ref:P,onSelectionChange:N}),w.length>0&&(0,x.jsx)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,x.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,x.jsx)("strong",{children:w.length})," documento",1!==w.length?"s":""," seleccionado",1!==w.length?"s":""," para consulta."]})})]}),t?(0,x.jsx)(l.A,{plan:t,temarioId:e.id}):(0,x.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,x.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,x.jsx)(a.wIk,{className:"w-10 h-10 text-blue-600"})}),(0,x.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Genera tu Plan de Estudios Personalizado"}),(0,x.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Nuestro asistente de IA analizar\xe1 tu planificaci\xf3n, disponibilidad de tiempo, y las caracter\xedsticas de cada tema para crear un plan de estudios completamente personalizado y realista."}),(0,x.jsx)("button",{onClick:q,disabled:f,className:"flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto",children:f?(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"}),"Generando plan con IA..."]}):(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(a.x_j,{className:"w-5 h-5 mr-3"}),"Generar Plan de Estudios"]})})]})]})}):(0,x.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,x.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,x.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,x.jsx)(a.x_j,{className:"w-8 h-8 text-yellow-600"})}),(0,x.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Configuraci\xf3n Requerida"}),(0,x.jsx)("p",{className:"text-gray-600 mb-4",children:"Para generar tu plan de estudios personalizado, necesitas:"}),(0,x.jsxs)("ul",{className:"text-left text-sm text-gray-600 mb-6 space-y-2",children:[(0,x.jsxs)("li",{className:"flex items-center",children:[(0,x.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),"Tener un temario configurado"]}),(0,x.jsxs)("li",{className:"flex items-center",children:[(0,x.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),"Completar la planificaci\xf3n inteligente"]})]}),(0,x.jsxs)("a",{href:"/temario",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,x.jsx)(a.x_j,{className:"w-4 h-4 mr-2"}),"Ir a Mi Temario"]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63603:(e,r,t)=>{Promise.resolve().then(t.bind(t,53241))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4979,6096,1815,7334,3823,4445,2997],()=>t(10311));module.exports=s})();
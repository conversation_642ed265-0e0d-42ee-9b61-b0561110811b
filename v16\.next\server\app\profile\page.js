(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9172:(e,s,r)=>{Promise.resolve().then(r.bind(r,56857))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33140:(e,s,r)=>{Promise.resolve().then(r.bind(r,56612))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56612:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx","default")},56857:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var a=r(96554),t=r(50653),i=r(81815),l=r(41150),n=r(99631);function c({userProfile:e,onUpdate:s}){let{0:r,1:t}=(0,a.useState)(!1),{0:l,1:c}=(0,a.useState)(e.user.name),{0:d,1:o}=(0,a.useState)(!1),{0:m,1:x}=(0,a.useState)(null),{0:u,1:p}=(0,a.useState)(!1),h=async()=>{try{if(o(!0),x(null),!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:l})})).ok)throw Error("Error actualizando perfil");p(!0),t(!1),s(),setTimeout(()=>p(!1),3e3)}catch(e){console.error("Error updating profile:",e),x(e instanceof Error?e.message:"Error desconocido")}finally{o(!1)}},g=e=>new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Informaci\xf3n de la Cuenta"}),!r&&(0,n.jsxs)("button",{onClick:()=>t(!0),className:"flex items-center text-blue-600 hover:text-blue-700 transition-colors",children:[(0,n.jsx)(i.WXf,{className:"w-4 h-4 mr-1"}),"Editar"]})]}),m&&(0,n.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[(0,n.jsx)(i.y3G,{className:"w-5 h-5 text-red-500 mr-2"}),(0,n.jsx)("span",{className:"text-red-700",children:m})]}),u&&(0,n.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[(0,n.jsx)(i.YrT,{className:"w-5 h-5 text-green-500 mr-2"}),(0,n.jsx)("span",{className:"text-green-700",children:"Perfil actualizado correctamente"})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informaci\xf3n Personal"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,n.jsx)(i.JXP,{className:"w-4 h-4 inline mr-1"}),"Nombre"]}),r?(0,n.jsx)("input",{type:"text",value:l,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Tu nombre"}):(0,n.jsx)("p",{className:"text-gray-900",children:e.user.name})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,n.jsx)(i.pHD,{className:"w-4 h-4 inline mr-1"}),"Email"]}),(0,n.jsx)("p",{className:"text-gray-900",children:e.user.email}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"El email no se puede modificar"})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,n.jsx)(i.wIk,{className:"w-4 h-4 inline mr-1"}),"Miembro desde"]}),(0,n.jsx)("p",{className:"text-gray-900",children:g(e.user.created_at)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ID de Usuario"}),(0,n.jsx)("p",{className:"text-gray-600 font-mono text-sm",children:e.user.id})]})]}),r&&(0,n.jsxs)("div",{className:"flex items-center gap-3 mt-4 pt-4 border-t border-gray-200",children:[(0,n.jsxs)("button",{onClick:h,disabled:d,className:"flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50",children:[d?(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,n.jsx)(i.Bc_,{className:"w-4 h-4 mr-2"}),d?"Guardando...":"Guardar"]}),(0,n.jsxs)("button",{onClick:()=>{c(e.user.name),t(!1),x(null)},disabled:d,className:"flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors disabled:opacity-50",children:[(0,n.jsx)(i.yGN,{className:"w-4 h-4 mr-2"}),"Cancelar"]})]})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informaci\xf3n de Suscripci\xf3n"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,n.jsx)(i.lZI,{className:"w-4 h-4 inline mr-1"}),"Plan Actual"]}),(0,n.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${(e=>{switch(e){case"free":default:return"bg-gray-100 text-gray-800";case"usuario":return"bg-blue-100 text-blue-800";case"pro":return"bg-purple-100 text-purple-800"}})(e.profile.subscription_plan)}`,children:(e=>{switch(e){case"free":return"Plan Gratuito";case"usuario":return"Plan Usuario";case"pro":return"Plan Pro";default:return e}})(e.profile.subscription_plan)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Estado de Pago"}),(0,n.jsx)("div",{className:"flex items-center",children:e.profile.payment_verified?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.YrT,{className:"w-4 h-4 text-green-500 mr-1"}),(0,n.jsx)("span",{className:"text-green-700",children:"Verificado"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.Ohp,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,n.jsx)("span",{className:"text-yellow-700",children:"Pendiente"})]})})]}),"free"!==e.profile.subscription_plan&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Renovaci\xf3n Autom\xe1tica"}),(0,n.jsx)("div",{className:"flex items-center",children:e.profile.auto_renew?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.YrT,{className:"w-4 h-4 text-green-500 mr-1"}),(0,n.jsx)("span",{className:"text-green-700",children:"Activa"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.yGN,{className:"w-4 h-4 text-red-500 mr-1"}),(0,n.jsx)("span",{className:"text-red-700",children:"Inactiva"})]})})]}),e.profile.plan_expires_at&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e.profile.auto_renew?"Pr\xf3xima Renovaci\xf3n":"Expira el"}),(0,n.jsx)("p",{className:"text-gray-900",children:g(e.profile.plan_expires_at)})]}),e.profile.last_payment_date&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdaltimo Pago"}),(0,n.jsx)("p",{className:"text-gray-900",children:g(e.profile.last_payment_date)})]})]}),"free"===e.profile.subscription_plan&&(0,n.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,n.jsxs)("a",{href:"/upgrade-plan",className:"inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(i.lZI,{className:"w-4 h-4 mr-2"}),"Actualizar Plan"]})})]})]})]})}function d({userId:e}){let{0:s,1:r}=(0,a.useState)([]),{0:t,1:l}=(0,a.useState)(!0),{0:c,1:d}=(0,a.useState)(null),{0:o,1:m}=(0,a.useState)("all"),{0:x,1:u}=(0,a.useState)(0),p=async()=>{try{l(!0),d(null);let e=new URLSearchParams({limit:"20"});"all"!==o&&e.append("type",o);let s=await fetch(`/api/user/notifications?${e}`);if(!s.ok)throw Error("Error cargando notificaciones");let a=await s.json();if(a.success)r(a.data.notifications),u(a.data.total);else throw Error(a.error||"Error desconocido")}catch(e){console.error("Error loading notifications:",e),d(e instanceof Error?e.message:"Error desconocido")}finally{l(!1)}},h=e=>{switch(e){case"subscription_cancelled":case"payment_failed":return(0,n.jsx)(i.yGN,{className:"w-5 h-5 text-red-500"});case"grace_period_ending":return(0,n.jsx)(i.Ohp,{className:"w-5 h-5 text-yellow-500"});case"plan_expired":return(0,n.jsx)(i.y3G,{className:"w-5 h-5 text-red-500"});case"welcome":return(0,n.jsx)(i.YrT,{className:"w-5 h-5 text-green-500"});default:return(0,n.jsx)(i.zd,{className:"w-5 h-5 text-blue-500"})}},g=e=>{switch(e){case"subscription_cancelled":return"Suscripci\xf3n Cancelada";case"grace_period_ending":return"Per\xedodo de Gracia";case"plan_expired":return"Plan Expirado";case"payment_failed":return"Pago Fallido";case"welcome":return"Bienvenida";default:return"Notificaci\xf3n"}},b=e=>{switch(e){case"sent":return(0,n.jsx)(i.YrT,{className:"w-4 h-4 text-green-500"});case"failed":return(0,n.jsx)(i.yGN,{className:"w-4 h-4 text-red-500"});case"pending":return(0,n.jsx)(i.Ohp,{className:"w-4 h-4 text-yellow-500"});default:return(0,n.jsx)(i.pHD,{className:"w-4 h-4 text-gray-500"})}},f=e=>{let s=new Date(e),r=Math.floor((new Date().getTime()-s.getTime())/36e5);return r<1?"Hace unos minutos":r<24?`Hace ${r} hora${r>1?"s":""}`:s.toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},j=[{value:"all",label:"Todas las notificaciones"},{value:"subscription_cancelled",label:"Suscripciones canceladas"},{value:"grace_period_ending",label:"Per\xedodos de gracia"},{value:"plan_expired",label:"Planes expirados"},{value:"payment_failed",label:"Pagos fallidos"},{value:"welcome",label:"Bienvenida"}];return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Historial de Notificaciones"}),(0,n.jsxs)("button",{onClick:p,disabled:t,className:"flex items-center text-blue-600 hover:text-blue-700 transition-colors disabled:opacity-50",children:[(0,n.jsx)(i.jTZ,{className:`w-4 h-4 mr-1 ${t?"animate-spin":""}`}),"Actualizar"]})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,n.jsx)(i.K7R,{className:"w-4 h-4 text-gray-500"}),(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Filtrar por tipo:"})]}),(0,n.jsx)("select",{value:o,onChange:e=>m(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:j.map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,n.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:x}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Total de notificaciones"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-600",children:s.filter(e=>"sent"===e.status).length}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Enviadas exitosamente"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-red-600",children:s.filter(e=>"failed"===e.status).length}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Fallos de env\xedo"})]})]})}),t?(0,n.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):c?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(i.y3G,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error cargando notificaciones"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:c}),(0,n.jsx)("button",{onClick:p,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Reintentar"})]}):0===s.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(i.zd,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No hay notificaciones"}),(0,n.jsx)("p",{className:"text-gray-600",children:"all"===o?"A\xfan no has recibido ninguna notificaci\xf3n por email.":`No hay notificaciones del tipo "${j.find(e=>e.value===o)?.label}".`})]}):(0,n.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,n.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,n.jsx)("div",{className:"flex items-start justify-between",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0 mt-1",children:h(e.type)}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-blue-600",children:g(e.type)}),(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[b(e.status),(0,n.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.status})]})]}),(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-1",children:e.subject}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:f(e.sentAt)}),e.metadata&&(0,n.jsxs)("div",{className:"mt-2 text-xs text-gray-600",children:[e.metadata.planName&&(0,n.jsxs)("span",{className:"inline-block bg-gray-100 px-2 py-1 rounded mr-2",children:["Plan: ",e.metadata.planName]}),void 0!==e.metadata.daysRemaining&&(0,n.jsxs)("span",{className:"inline-block bg-yellow-100 px-2 py-1 rounded mr-2",children:[e.metadata.daysRemaining," d\xedas restantes"]}),void 0!==e.metadata.hoursRemaining&&(0,n.jsxs)("span",{className:"inline-block bg-red-100 px-2 py-1 rounded mr-2",children:[e.metadata.hoursRemaining," horas restantes"]})]})]})]})})},e.id))})]})}function o({userProfile:e}){var s;let{access:r,tokenUsage:a,profile:t}=e;if(!r||!a||!t)return(0,n.jsx)("div",{className:"p-6",children:(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("p",{className:"text-gray-500",children:"Cargando informaci\xf3n del plan..."})})});let l=(e,s)=>"string"==typeof s||0===s?0:Math.min(e/s*100,100),c=e=>e>=90?"bg-red-500":e>=75?"bg-yellow-500":e>=50?"bg-blue-500":"bg-green-500",d=e=>-1===e||"Ilimitado"===e||"string"==typeof e,o=(e,s)=>s?e.replace("este mes","(sin l\xedmites)"):e,m=[{icon:i.jH2,label:"Documentos",current:r.currentUsage?.documents||0,limit:d(r.limits?.documents)?"Ilimitado":r.limits?.documents||0,color:"blue",description:o("Documentos PDF/TXT subidos",d(r.limits?.documents))},{icon:i.NLe,label:"Tests",current:r.currentUsage?.tests||0,limit:d(r.limits?.tests)?"Ilimitado":r.limits?.tests||0,color:"green",description:o("Tests generados este mes",d(r.limits?.tests))},{icon:i.s_k,label:"Flashcards",current:r.currentUsage?.flashcards||0,limit:d(r.limits?.flashcards)?"Ilimitado":r.limits?.flashcards||0,color:"purple",description:o("Flashcards creadas este mes",d(r.limits?.flashcards))},{icon:i.x_j,label:"Mapas Mentales",current:r.currentUsage?.mindMaps||0,limit:d(r.limits?.mindMaps)?"Ilimitado":r.limits?.mindMaps||0,color:"indigo",description:o("Mapas mentales generados este mes",d(r.limits?.mindMaps))}],x=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString();return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Uso y L\xedmites del Plan"}),(0,n.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${"free"===t.subscription_plan?"bg-gray-100 text-gray-800":"usuario"===t.subscription_plan?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"}`,children:(e=>{switch(e){case"free":return"Plan Gratuito";case"usuario":return"Plan Usuario";case"pro":return"Plan Pro";default:return e}})(t.subscription_plan)})]}),(0,n.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.Ojn,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tokens de IA"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Uso mensual de procesamiento de IA"})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:x(a.current||0)}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["de ",x(a.limit||0)]})]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,n.jsx)("div",{className:`h-3 rounded-full transition-all duration-300 ${c(a.percentage||0)}`,style:{width:`${a.percentage||0}%`}})}),(0,n.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,n.jsxs)("span",{className:(s=a.percentage||0)>=90?"text-red-600":s>=75?"text-yellow-600":"text-gray-600",children:[(a.percentage||0).toFixed(1),"% utilizado"]}),(0,n.jsxs)("span",{className:"text-gray-600",children:[x(a.remaining||0)," restantes"]})]}),(a.percentage||0)>=90&&(0,n.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[(0,n.jsx)(i.eHT,{className:"w-5 h-5 text-red-500 mr-2"}),(0,n.jsx)("span",{className:"text-red-700 text-sm",children:"Te est\xe1s acercando al l\xedmite de tokens. Considera actualizar tu plan."})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:m.map(e=>{let s=e.icon,r=l(e.current,e.limit),a="string"==typeof e.limit;return(0,n.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(s,{className:`w-5 h-5 text-${e.color}-600 mr-2`}),(0,n.jsx)("span",{className:"font-medium text-gray-900",children:e.label})]}),(0,n.jsx)("div",{className:"text-right",children:a?(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,n.jsx)(i.YrT,{className:"w-3 h-3 mr-1"}),"Ilimitado"]})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"font-semibold text-gray-900",children:e.current}),(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:["de ",e.limit]})]})})]}),!a&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,n.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${c(r)}`,style:{width:`${r}%`}})}),(0,n.jsxs)("div",{className:"text-xs text-gray-600",children:[r.toFixed(1),"% utilizado"]})]}),a&&(0,n.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"✓ Acceso completo sin restricciones"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:e.description})]},e.label)})}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Funciones Disponibles"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:(r.features||[]).map(e=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.YrT,{className:"w-4 h-4 text-green-500 mr-2"}),(0,n.jsx)("span",{className:"text-sm text-gray-700",children:{document_upload:"Subida de documentos",test_generation:"Generaci\xf3n de tests",flashcard_generation:"Creaci\xf3n de flashcards",mind_map_generation:"Mapas mentales",study_planning:"Planificaci\xf3n de estudios",ai_tutor:"Tutor de IA",summaries:"Res\xfamenes autom\xe1ticos",advanced_analytics:"An\xe1lisis avanzado"}[e]||e})]},e))}),"free"===t.subscription_plan&&(0,n.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"\xbfNecesitas m\xe1s funciones y l\xedmites m\xe1s altos?"}),(0,n.jsxs)("a",{href:"/upgrade-plan",className:"inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm",children:[(0,n.jsx)(i.ARf,{className:"w-4 h-4 mr-2"}),"Actualizar Plan"]})]})]})]})}var m=r(72267),x=r(29959);function u({userProfile:e,onUpdate:s}){let r=(0,t.useRouter)(),{0:l,1:c}=(0,a.useState)(!1),{0:d,1:o}=(0,a.useState)(null),{0:u,1:p}=(0,a.useState)(!1),{0:h,1:g}=(0,a.useState)(!1),{0:b,1:f}=(0,a.useState)(!0),{0:j,1:N}=(0,a.useState)(!1),{0:y,1:v}=(0,a.useState)(!1),{0:w,1:_}=(0,a.useState)(!1),k=async()=>{let s;_(!0);try{s=x.oR.loading("Enviando enlace de recuperaci\xf3n...");let r=(0,m.U)(),{error:a}=await r.auth.resetPasswordForEmail(e.user.email,{redirectTo:`${window.location.origin}/auth/reset-password`});if(a)throw a;x.oR.success("Se ha enviado un enlace a tu email para restablecer la contrase\xf1a.",{id:s,duration:6e3}),v(!1)}catch(e){console.error("Error al solicitar reseteo de contrase\xf1a:",e),x.oR.error("No se pudo enviar el enlace de recuperaci\xf3n. Int\xe9ntalo de nuevo m\xe1s tarde.",{id:s})}finally{_(!1)}},C=async()=>{try{if(c(!0),o(null),!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({preferences:{email_notifications:b,marketing_emails:j}})})).ok)throw Error("Error actualizando preferencias");p(!0),s(),setTimeout(()=>p(!1),3e3)}catch(e){console.error("Error updating preferences:",e),o(e instanceof Error?e.message:"Error desconocido")}finally{c(!1)}},P=async()=>{try{let e=(0,m.U)();await e.auth.signOut(),r.push("/")}catch(e){console.error("Error logging out:",e)}},E=async()=>{try{c(!0),o(null);let e=await fetch("/api/user/cancel-subscription",{method:"POST",headers:{"Content-Type":"application/json"}}),r=await e.json();if(!e.ok)throw Error(r.error||"Error cancelando suscripci\xf3n");p(!0),g(!1),s(),x.oR.success(`Suscripci\xf3n cancelada exitosamente. Mantendr\xe1s acceso hasta: ${new Date(r.details.periodEnd).toLocaleDateString("es-ES")}`,{duration:6e3}),setTimeout(()=>p(!1),5e3)}catch(e){console.error("Error canceling subscription:",e),o(e instanceof Error?e.message:"Error desconocido"),g(!1)}finally{c(!1)}};return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Configuraci\xf3n de la Cuenta"})}),d&&(0,n.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,n.jsx)("span",{className:"text-red-700",children:d})}),u&&(0,n.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[(0,n.jsx)(i.YrT,{className:"w-5 h-5 text-green-500 mr-2"}),(0,n.jsx)("span",{className:"text-green-700",children:"Configuraci\xf3n actualizada correctamente"})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(i.zd,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Preferencias de Notificaciones"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Notificaciones por Email"}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir notificaciones importantes sobre tu cuenta y suscripci\xf3n"})]}),(0,n.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:b,onChange:e=>f(e.target.checked),className:"sr-only peer"}),(0,n.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Emails de Marketing"}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir informaci\xf3n sobre nuevas funciones y ofertas especiales"})]}),(0,n.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:j,onChange:e=>N(e.target.checked),className:"sr-only peer"}),(0,n.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,n.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,n.jsxs)("button",{onClick:C,disabled:l,className:"flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50",children:[l?(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,n.jsx)(i.Bc_,{className:"w-4 h-4 mr-2"}),l?"Guardando...":"Guardar Preferencias"]})})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(i.pcC,{className:"w-5 h-5 text-green-600 mr-2"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Seguridad"})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cambiar Contrase\xf1a"}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir\xe1s un enlace en tu email para establecer una nueva contrase\xf1a."})]}),(0,n.jsx)("button",{onClick:()=>v(!0),className:"bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm",children:"Cambiar"})]})})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(i.VSk,{className:"w-5 h-5 text-gray-600 mr-2"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Acciones de Cuenta"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cerrar Sesi\xf3n"}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Cerrar sesi\xf3n en este dispositivo"})]}),(0,n.jsxs)("button",{onClick:P,className:"flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm",children:[(0,n.jsx)(i.QeK,{className:"w-4 h-4 mr-2"}),"Cerrar Sesi\xf3n"]})]}),"free"!==e.profile.subscription_plan&&(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cancelar Suscripci\xf3n"}),(0,n.jsx)("p",{className:"text-xs text-gray-600",children:"Cancelar tu suscripci\xf3n actual (mantendr\xe1s acceso hasta el final del per\xedodo)"})]}),(0,n.jsxs)("button",{onClick:()=>g(!0),className:"flex items-center bg-red-100 text-red-700 px-4 py-2 rounded-md hover:bg-red-200 transition-colors text-sm",children:[(0,n.jsx)(i.lZI,{className:"w-4 h-4 mr-2"}),"Cancelar"]})]})]})]})]}),y&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(i.pHD,{className:"w-6 h-6 text-blue-500 mr-2"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Restablecer Contrase\xf1a"})]}),(0,n.jsxs)("p",{className:"text-gray-600 mb-6",children:["Se enviar\xe1 un enlace seguro a tu direcci\xf3n de correo electr\xf3nico ",(0,n.jsxs)("strong",{children:["(",e.user.email,")"]})," para que puedas establecer una nueva contrase\xf1a."]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{onClick:()=>v(!1),disabled:w,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"}),(0,n.jsxs)("button",{onClick:k,disabled:w,className:"px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 flex items-center",children:[w?(0,n.jsx)(i.TwU,{className:"animate-spin mr-2"}):(0,n.jsx)(i.pHD,{className:"mr-2"}),w?"Enviando...":"Enviar Enlace"]})]})]})}),h&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(i.lZI,{className:"w-6 h-6 text-red-500 mr-2"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Confirmar Cancelaci\xf3n"})]}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"\xbfEst\xe1s seguro de que quieres cancelar tu suscripci\xf3n? Mantendr\xe1s acceso a las funciones premium hasta el final de tu per\xedodo de facturaci\xf3n actual."}),(0,n.jsxs)("div",{className:"flex gap-3",children:[(0,n.jsx)("button",{onClick:E,disabled:l,className:"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center justify-center",children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Cancelando..."]}):"S\xed, cancelar suscripci\xf3n"}),(0,n.jsx)("button",{onClick:()=>g(!1),className:"flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Mantener suscripci\xf3n"})]})]})})]})}function p(){let{user:e}=(0,l.A)(),s=(0,t.useRouter)(),{0:r,1:m}=(0,a.useState)("account"),{0:x,1:p}=(0,a.useState)(null),{0:h,1:g}=(0,a.useState)(!0),{0:b,1:f}=(0,a.useState)(null),j=async()=>{try{g(!0),f(null);let e=await fetch("/api/user/profile");if(!e.ok)throw Error("Error cargando perfil de usuario");let s=await e.json();p(s)}catch(e){console.error("Error loading user profile:",e),f(e instanceof Error?e.message:"Error desconocido")}finally{g(!1)}},N=[{id:"account",label:"Informaci\xf3n de Cuenta",icon:i.JXP},{id:"notifications",label:"Notificaciones",icon:i.zd},{id:"usage",label:"Uso y L\xedmites",icon:i.lZI},{id:"settings",label:"Configuraci\xf3n",icon:i.VSk}];return h?(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):b||!x?(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error cargando perfil"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:b||"No se pudo cargar la informaci\xf3n del perfil"}),(0,n.jsx)("button",{onClick:()=>s.push("/app"),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Volver al Dashboard"})]})}):(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsxs)("button",{onClick:()=>s.push("/app"),className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,n.jsx)(i.kRp,{className:"w-5 h-5 mr-2"}),"Volver al Dashboard"]})}),(0,n.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Mi Perfil"}),(0,n.jsx)("div",{className:"w-24"})," "]})})}),(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,n.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-8",children:[(0,n.jsx)("div",{className:"lg:col-span-3",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(i.JXP,{className:"w-10 h-10 text-blue-600"})}),(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:x.user.name}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:x.user.email}),(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"free"===x.profile.subscription_plan?"bg-gray-100 text-gray-800":"usuario"===x.profile.subscription_plan?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"}`,children:["Plan ","free"===x.profile.subscription_plan?"Gratuito":"usuario"===x.profile.subscription_plan?"Usuario":"Pro"]})})]}),(0,n.jsx)("nav",{className:"space-y-1",children:N.map(e=>{let s=e.icon;return(0,n.jsxs)("button",{onClick:()=>m(e.id),className:`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${r===e.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[(0,n.jsx)(s,{className:"w-4 h-4 mr-3"}),e.label]},e.id)})})]})}),(0,n.jsx)("div",{className:"lg:col-span-9",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:["account"===r&&(0,n.jsx)(c,{userProfile:x,onUpdate:j}),"notifications"===r&&(0,n.jsx)(d,{userId:x.user.id}),"usage"===r&&(0,n.jsx)(o,{userProfile:x}),"settings"===r&&(0,n.jsx)(u,{userProfile:x,onUpdate:j})]})})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},91799:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=r(67061),t=r(79378),i=r(1852),l=r.n(i),n=r(13547),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56612)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,16277)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,17560,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,86417,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,34766,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[4979,6096,1815,4445],()=>r(91799));module.exports=a})();
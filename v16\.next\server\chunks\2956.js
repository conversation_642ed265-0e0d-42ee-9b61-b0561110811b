exports.id=2956,exports.ids=[2956],exports.modules={15737:e=>{"use strict";let t=["nodebuffer","arraybuffer","fragments"],r="undefined"!=typeof Blob;r&&t.push("blob"),e.exports={BINARY_TYPES:t,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:r,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},20673:(e,t,r)=>{"use strict";let{isUtf8:s}=r(79428),{hasBlob:i}=r(15737);function o(e){let t=e.length,r=0;for(;r<t;)if((128&e[r])==0)r++;else if((224&e[r])==192){if(r+1===t||(192&e[r+1])!=128||(254&e[r])==192)return!1;r+=2}else if((240&e[r])==224){if(r+2>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||224===e[r]&&(224&e[r+1])==128||237===e[r]&&(224&e[r+1])==160)return!1;r+=3}else{if((248&e[r])!=240||r+3>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||(192&e[r+3])!=128||240===e[r]&&(240&e[r+1])==128||244===e[r]&&e[r+1]>143||e[r]>244)return!1;r+=4}return!0}if(e.exports={isBlob:function(e){return i&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:o,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},s)e.exports.isValidUTF8=function(e){return e.length<24?o(e):s(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=r(47990);e.exports.isValidUTF8=function(e){return e.length<32?o(e):t(e)}}catch(e){}},31804:e=>{"use strict";let t=Symbol("kDone"),r=Symbol("kRun");class s{constructor(e){this[t]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=s},39727:()=>{},47990:()=>{},54246:(e,t,r)=>{"use strict";let s;function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var s,i,o;s=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in s?Object.defineProperty(s,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):s[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let n=r(74075),a=r(55245),h=r(31804),{kStatusCode:l}=r(15737),c=Buffer[Symbol.species],d=Buffer.from([0,0,255,255]),f=Symbol("permessage-deflate"),u=Symbol("total-length"),_=Symbol("callback"),p=Symbol("buffers"),m=Symbol("error");class y{constructor(e,t,r){this._maxPayload=0|r,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,s||(s=new h(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[_];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!r)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(r.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?r.client_max_window_bits=t.clientMaxWindowBits:(!0===r.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let r=e[t];if(r.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(r=r[0],"client_max_window_bits"===t){if(!0!==r){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else if("server_max_window_bits"===t){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==r)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=r})}),e}decompress(e,t,r){s.add(s=>{this._decompress(e,t,(e,t)=>{s(),r(e,t)})})}compress(e,t,r){s.add(s=>{this._compress(e,t,(e,t)=>{s(),r(e,t)})})}_decompress(e,t,r){let s=this._isServer?"client":"server";if(!this._inflate){let e=`${s}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=n.createInflateRaw(o(o({},this._options.zlibInflateOptions),{},{windowBits:t})),this._inflate[f]=this,this._inflate[u]=0,this._inflate[p]=[],this._inflate.on("error",v),this._inflate.on("data",g)}this._inflate[_]=r,this._inflate.write(e),t&&this._inflate.write(d),this._inflate.flush(()=>{let e=this._inflate[m];if(e){this._inflate.close(),this._inflate=null,r(e);return}let i=a.concat(this._inflate[p],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[p]=[],t&&this.params[`${s}_no_context_takeover`]&&this._inflate.reset()),r(null,i)})}_compress(e,t,r){let s=this._isServer?"server":"client";if(!this._deflate){let e=`${s}_max_window_bits`,t="number"!=typeof this.params[e]?n.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=n.createDeflateRaw(o(o({},this._options.zlibDeflateOptions),{},{windowBits:t})),this._deflate[u]=0,this._deflate[p]=[],this._deflate.on("data",b)}this._deflate[_]=r,this._deflate.write(e),this._deflate.flush(n.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=a.concat(this._deflate[p],this._deflate[u]);t&&(e=new c(e.buffer,e.byteOffset,e.length-4)),this._deflate[_]=null,this._deflate[u]=0,this._deflate[p]=[],t&&this.params[`${s}_no_context_takeover`]&&this._deflate.reset(),r(null,e)})}}function b(e){this[p].push(e),this[u]+=e.length}function g(e){if(this[u]+=e.length,this[f]._maxPayload<1||this[u]<=this[f]._maxPayload)return void this[p].push(e);this[m]=RangeError("Max payload size exceeded"),this[m].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[m][l]=1009,this.removeListener("data",g),this.reset()}function v(e){if(this[f]._inflate=null,this[m])return void this[_](this[m]);e[l]=1007,this[_](e)}e.exports=y},54342:(e,t,r)=>{"use strict";function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var s,i,o;s=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in s?Object.defineProperty(s,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):s[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r(63559);let{Duplex:o}=r(27910);function n(e){e.emit("close")}function a(){!this.destroyed&&this._writableState.finished&&this.destroy()}function h(e){this.removeListener("error",h),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let r=!0,s=new o(i(i({},t),{},{autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1}));return e.on("message",function(t,r){let i=!r&&s._readableState.objectMode?t.toString():t;s.push(i)||e.pause()}),e.once("error",function(e){s.destroyed||(r=!1,s.destroy(e))}),e.once("close",function(){s.destroyed||s.push(null)}),s._destroy=function(t,i){if(e.readyState===e.CLOSED){i(t),process.nextTick(n,s);return}let o=!1;e.once("error",function(e){o=!0,i(e)}),e.once("close",function(){o||i(t),process.nextTick(n,s)}),r&&e.terminate()},s._final=function(t){if(e.readyState===e.CONNECTING)return void e.once("open",function(){s._final(t)});null!==e._socket&&(e._socket._writableState.finished?(t(),s._readableState.endEmitted&&s.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},s._read=function(){e.isPaused&&e.resume()},s._write=function(t,r,i){if(e.readyState===e.CONNECTING)return void e.once("open",function(){s._write(t,r,i)});e.send(t,i)},s.on("end",a),s.on("error",h),s}},55245:(e,t,r)=>{"use strict";let{EMPTY_BUFFER:s}=r(15737),i=Buffer[Symbol.species];function o(e,t,r,s,i){for(let o=0;o<i;o++)r[s+o]=e[o]^t[3&o]}function n(e,t){for(let r=0;r<e.length;r++)e[r]^=t[3&r]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new i(e):ArrayBuffer.isView(e)?t=new i(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(e.exports={concat:function(e,t){if(0===e.length)return s;if(1===e.length)return e[0];let r=Buffer.allocUnsafe(t),o=0;for(let t=0;t<e.length;t++){let s=e[t];r.set(s,o),o+=s.length}return o<t?new i(r.buffer,r.byteOffset,o):r},mask:o,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:n},!process.env.WS_NO_BUFFER_UTIL)try{let t=r(39727);e.exports.mask=function(e,r,s,i,n){n<48?o(e,r,s,i,n):t.mask(e,r,s,i,n)},e.exports.unmask=function(e,r){e.length<32?n(e,r):t.unmask(e,r)}}catch(e){}},55419:(e,t,r)=>{"use strict";let s,{Duplex:i}=r(27910),{randomFillSync:o}=r(55511),n=r(54246),{EMPTY_BUFFER:a,kWebSocket:h,NOOP:l}=r(15737),{isBlob:c,isValidStatusCode:d}=r(20673),{mask:f,toBuffer:u}=r(55245),_=Symbol("kByteLength"),p=Buffer.alloc(4),m=8192;class y{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=l,this[h]=void 0}static frame(e,t){let r,i,n=!1,a=2,h=!1;t.mask&&(r=t.maskBuffer||p,t.generateMask?t.generateMask(r):(8192===m&&(void 0===s&&(s=Buffer.alloc(8192)),o(s,0,8192),m=0),r[0]=s[m++],r[1]=s[m++],r[2]=s[m++],r[3]=s[m++]),h=(r[0]|r[1]|r[2]|r[3])==0,a=6),"string"==typeof e?i=(!t.mask||h)&&void 0!==t[_]?t[_]:(e=Buffer.from(e)).length:(i=e.length,n=t.mask&&t.readOnly&&!h);let l=i;i>=65536?(a+=8,l=127):i>125&&(a+=2,l=126);let c=Buffer.allocUnsafe(n?i+a:a);return(c[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(c[0]|=64),c[1]=l,126===l?c.writeUInt16BE(i,2):127===l&&(c[2]=c[3]=0,c.writeUIntBE(i,4,6)),t.mask)?(c[1]|=128,c[a-4]=r[0],c[a-3]=r[1],c[a-2]=r[2],c[a-1]=r[3],h)?[c,e]:n?(f(e,r,c,a,i),[c]):(f(e,r,e,0,i),[c,e]):[c,e]}close(e,t,r,s){let i;if(void 0===e)i=a;else if("number"==typeof e&&d(e))if(void 0!==t&&t.length){let r=Buffer.byteLength(t);if(r>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+r)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let o={[_]:i.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,i,!1,o,s]):this.sendFrame(y.frame(i,o),s)}ping(e,t,r){let s,i;if("string"==typeof e?(s=Buffer.byteLength(e),i=!1):c(e)?(s=e.size,i=!1):(s=(e=u(e)).length,i=u.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let o={[_]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,o,r]):this.getBlobData(e,!1,o,r):0!==this._state?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(y.frame(e,o),r)}pong(e,t,r){let s,i;if("string"==typeof e?(s=Buffer.byteLength(e),i=!1):c(e)?(s=e.size,i=!1):(s=(e=u(e)).length,i=u.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let o={[_]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,o,r]):this.getBlobData(e,!1,o,r):0!==this._state?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(y.frame(e,o),r)}send(e,t,r){let s,i,o=this._extensions[n.extensionName],a=t.binary?2:1,h=t.compress;"string"==typeof e?(s=Buffer.byteLength(e),i=!1):c(e)?(s=e.size,i=!1):(s=(e=u(e)).length,i=u.readOnly),this._firstFragment?(this._firstFragment=!1,h&&o&&o.params[o._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(h=s>=o._threshold),this._compress=h):(h=!1,a=0),t.fin&&(this._firstFragment=!0);let l={[_]:s,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:h};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,l,r]):this.getBlobData(e,this._compress,l,r):0!==this._state?this.enqueue([this.dispatch,e,this._compress,l,r]):this.dispatch(e,this._compress,l,r)}getBlobData(e,t,r,s){this._bufferedBytes+=r[_],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(b,this,e,s);return}this._bufferedBytes-=r[_];let i=u(e);t?this.dispatch(i,t,r,s):(this._state=0,this.sendFrame(y.frame(i,r),s),this.dequeue())}).catch(e=>{process.nextTick(g,this,e,s)})}dispatch(e,t,r,s){if(!t)return void this.sendFrame(y.frame(e,r),s);let i=this._extensions[n.extensionName];this._bufferedBytes+=r[_],this._state=1,i.compress(e,r.fin,(e,t)=>{if(this._socket.destroyed)return void b(this,Error("The socket was closed while data was being compressed"),s);this._bufferedBytes-=r[_],this._state=0,r.readOnly=!1,this.sendFrame(y.frame(t,r),s),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][_],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][_],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}function b(e,t,r){"function"==typeof r&&r(t);for(let r=0;r<e._queue.length;r++){let s=e._queue[r],i=s[s.length-1];"function"==typeof i&&i(t)}}function g(e,t,r){b(e,t,r),e.onerror(t)}e.exports=y},58305:(e,t,r)=>{"use strict";let{tokenChars:s}=r(20673);function i(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}e.exports={format:function(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>[t].concat(Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,r,o=Object.create(null),n=Object.create(null),a=!1,h=!1,l=!1,c=-1,d=-1,f=-1,u=0;for(;u<e.length;u++)if(d=e.charCodeAt(u),void 0===t)if(-1===f&&1===s[d])-1===c&&(c=u);else if(0!==u&&(32===d||9===d))-1===f&&-1!==c&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let r=e.slice(c,f);44===d?(i(o,r,n),n=Object.create(null)):t=r,c=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`);else if(void 0===r)if(-1===f&&1===s[d])-1===c&&(c=u);else if(32===d||9===d)-1===f&&-1!==c&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u),i(n,e.slice(c,f),!0),44===d&&(i(o,t,n),n=Object.create(null),t=void 0),c=f=-1}else if(61===d&&-1!==c&&-1===f)r=e.slice(c,u),c=f=-1;else throw SyntaxError(`Unexpected character at index ${u}`);else if(h){if(1!==s[d])throw SyntaxError(`Unexpected character at index ${u}`);-1===c?c=u:a||(a=!0),h=!1}else if(l)if(1===s[d])-1===c&&(c=u);else if(34===d&&-1!==c)l=!1,f=u;else if(92===d)h=!0;else throw SyntaxError(`Unexpected character at index ${u}`);else if(34===d&&61===e.charCodeAt(u-1))l=!0;else if(-1===f&&1===s[d])-1===c&&(c=u);else if(-1!==c&&(32===d||9===d))-1===f&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let s=e.slice(c,f);a&&(s=s.replace(/\\/g,""),a=!1),i(n,r,s),44===d&&(i(o,t,n),n=Object.create(null),t=void 0),r=void 0,c=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`);if(-1===c||l||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===f&&(f=u);let _=e.slice(c,f);return void 0===t?i(o,_,n):(void 0===r?i(n,_,!0):a?i(n,r,_.replace(/\\/g,"")):i(n,r,_),i(o,t,n)),o}}},63559:(e,t,r)=>{"use strict";function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var s,i,o;s=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in s?Object.defineProperty(s,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):s[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let o=r(94735),n=r(55591),a=r(81630),h=r(91645),l=r(34631),{randomBytes:c,createHash:d}=r(55511),{Duplex:f,Readable:u}=r(27910),{URL:_}=r(79551),p=r(54246),m=r(66855),y=r(55419),{isBlob:b}=r(20673),{BINARY_TYPES:g,EMPTY_BUFFER:v,GUID:S,kForOnEventAttribute:E,kListener:w,kStatusCode:k,kWebSocket:x,NOOP:O}=r(15737),{EventTarget:{addEventListener:P,removeEventListener:T}}=r(74960),{format:N,parse:C}=r(58305),{toBuffer:L}=r(55245),j=Symbol("kAborted"),B=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],I=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class U extends o{constructor(e,t,r){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=v,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=U.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(r=t,t=[]):t=[t]),function e(t,r,s,o){let h,l,f,u,m=i(i({allowSynchronousEvents:!0,autoPong:!0,protocolVersion:B[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10},o),{},{socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0});if(t._autoPong=m.autoPong,!B.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${B.join(", ")})`);if(r instanceof _)h=r;else try{h=new _(r)}catch(e){throw SyntaxError(`Invalid URL: ${r}`)}"http:"===h.protocol?h.protocol="ws:":"https:"===h.protocol&&(h.protocol="wss:"),t._url=h.href;let y="wss:"===h.protocol,b="ws+unix:"===h.protocol;if("ws:"===h.protocol||y||b?b&&!h.pathname?l="The URL's pathname is empty":h.hash&&(l="The URL contains a fragment identifier"):l='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',l){let e=SyntaxError(l);if(0!==t._redirects)return void D(t,e);throw e}let g=y?443:80,v=c(16).toString("base64"),E=y?n.request:a.request,w=new Set;if(m.createConnection=m.createConnection||(y?M:W),m.defaultPort=m.defaultPort||g,m.port=h.port||g,m.host=h.hostname.startsWith("[")?h.hostname.slice(1,-1):h.hostname,m.headers=i(i({},m.headers),{},{"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"}),m.path=h.pathname+h.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(f=new p(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=N({[p.extensionName]:f.offer()})),s.length){for(let e of s){if("string"!=typeof e||!I.test(e)||w.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");w.add(e)}m.headers["Sec-WebSocket-Protocol"]=s.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(h.username||h.password)&&(m.auth=`${h.username}:${h.password}`),b){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=b,t._originalSecure=y,t._originalHostOrSocketPath=b?m.socketPath:h.host;let e=o&&o.headers;if(o=i(i({},o),{},{headers:{}}),e)for(let[t,r]of Object.entries(e))o.headers[t.toLowerCase()]=r}else if(0===t.listenerCount("redirect")){let e=b?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&h.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||y)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),u=t._req=E(m),t._redirects&&t.emit("redirect",t.url,u)}else u=t._req=E(m);m.timeout&&u.on("timeout",()=>{A(t,u,"Opening handshake has timed out")}),u.on("error",e=>{null===u||u[j]||(u=t._req=null,D(t,e))}),u.on("response",i=>{let n=i.headers.location,a=i.statusCode;if(n&&m.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>m.maxRedirects)return void A(t,u,"Maximum redirects exceeded");u.abort();try{i=new _(n,r)}catch(e){D(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,i,s,o)}else t.emit("unexpected-response",u,i)||A(t,u,`Unexpected server response: ${i.statusCode}`)}),u.on("upgrade",(e,r,s)=>{let i;if(t.emit("upgrade",e),t.readyState!==U.CONNECTING)return;u=t._req=null;let o=e.headers.upgrade;if(void 0===o||"websocket"!==o.toLowerCase())return void A(t,r,"Invalid Upgrade header");let n=d("sha1").update(v+S).digest("base64");if(e.headers["sec-websocket-accept"]!==n)return void A(t,r,"Invalid Sec-WebSocket-Accept header");let a=e.headers["sec-websocket-protocol"];if(void 0!==a?w.size?w.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":w.size&&(i="Server sent no subprotocol"),i)return void A(t,r,i);a&&(t._protocol=a);let h=e.headers["sec-websocket-extensions"];if(void 0!==h){let e;if(!f)return void A(t,r,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=C(h)}catch(e){A(t,r,"Invalid Sec-WebSocket-Extensions header");return}let s=Object.keys(e);if(1!==s.length||s[0]!==p.extensionName)return void A(t,r,"Server indicated an extension that was not requested");try{f.accept(e[p.extensionName])}catch(e){A(t,r,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=f}t.setSocket(r,s,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(u,t):u.end()}(this,e,t,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let s=new m({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation}),i=new y(e,this._extensions,r.generateMask);this._receiver=s,this._sender=i,this._socket=e,s[x]=this,i[x]=this,e[x]=this,s.on("conclude",$),s.on("drain",G),s.on("error",V),s.on("message",z),s.on("ping",H),s.on("pong",X),i.onerror=Z,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",J),e.on("data",Q),e.on("end",ee),e.on("error",et),this._readyState=U.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=U.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=U.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==U.CLOSED){if(this.readyState===U.CONNECTING)return void A(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===U.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=U.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),Y(this)}}pause(){this.readyState!==U.CONNECTING&&this.readyState!==U.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===U.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==U.OPEN)return void F(this,e,r);void 0===t&&(t=!this._isServer),this._sender.ping(e||v,t,r)}pong(e,t,r){if(this.readyState===U.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==U.OPEN)return void F(this,e,r);void 0===t&&(t=!this._isServer),this._sender.pong(e||v,t,r)}resume(){this.readyState!==U.CONNECTING&&this.readyState!==U.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===U.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(r=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==U.OPEN)return void F(this,e,r);let s=i({binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0},t);this._extensions[p.extensionName]||(s.compress=!1),this._sender.send(e||v,s,r)}terminate(){if(this.readyState!==U.CLOSED){if(this.readyState===U.CONNECTING)return void A(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=U.CLOSING,this._socket.destroy())}}}function D(e,t){e._readyState=U.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function W(e){return e.path=e.socketPath,h.connect(e)}function M(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=h.isIP(e.host)?"":e.host),l.connect(e)}function A(e,t,r){e._readyState=U.CLOSING;let s=Error(r);Error.captureStackTrace(s,A),t.setHeader?(t[j]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(D,e,s)):(t.destroy(s),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function F(e,t,r){if(t){let r=b(t)?t.size:L(t).length;e._socket?e._sender._bufferedBytes+=r:e._bufferedAmount+=r}if(r){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(r,t)}}function $(e,t){let r=this[x];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,void 0!==r._socket[x]&&(r._socket.removeListener("data",Q),process.nextTick(K,r._socket),1005===e?r.close():r.close(e,t))}function G(){let e=this[x];e.isPaused||e._socket.resume()}function V(e){let t=this[x];void 0!==t._socket[x]&&(t._socket.removeListener("data",Q),process.nextTick(K,t._socket),t.close(e[k])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function q(){this[x].emitClose()}function z(e,t){this[x].emit("message",e,t)}function H(e){let t=this[x];t._autoPong&&t.pong(e,!this._isServer,O),t.emit("ping",e)}function X(e){this[x].emit("pong",e)}function K(e){e.resume()}function Z(e){let t=this[x];t.readyState!==U.CLOSED&&(t.readyState===U.OPEN&&(t._readyState=U.CLOSING,Y(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function Y(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function J(){let e,t=this[x];this.removeListener("close",J),this.removeListener("data",Q),this.removeListener("end",ee),t._readyState=U.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[x]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",q),t._receiver.on("finish",q))}function Q(e){this[x]._receiver.write(e)||this.pause()}function ee(){let e=this[x];e._readyState=U.CLOSING,e._receiver.end(),this.end()}function et(){let e=this[x];this.removeListener("error",et),this.on("error",O),e&&(e._readyState=U.CLOSING,this.destroy())}Object.defineProperty(U,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(U.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(U,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(U.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(U,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(U.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(U,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(U.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(U.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(U.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[E])return t[w];return null},set(t){for(let t of this.listeners(e))if(t[E]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[E]:!0})}})}),U.prototype.addEventListener=P,U.prototype.removeEventListener=T,e.exports=U},64678:(e,t,r)=>{"use strict";let{tokenChars:s}=r(20673);e.exports={parse:function(e){let t=new Set,r=-1,i=-1,o=0;for(;o<e.length;o++){let n=e.charCodeAt(o);if(-1===i&&1===s[n])-1===r&&(r=o);else if(0!==o&&(32===n||9===n))-1===i&&-1!==r&&(i=o);else if(44===n){if(-1===r)throw SyntaxError(`Unexpected character at index ${o}`);-1===i&&(i=o);let s=e.slice(r,i);if(t.has(s))throw SyntaxError(`The "${s}" subprotocol is duplicated`);t.add(s),r=i=-1}else throw SyntaxError(`Unexpected character at index ${o}`)}if(-1===r||-1!==i)throw SyntaxError("Unexpected end of input");let n=e.slice(r,o);if(t.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);return t.add(n),t}}},66855:(e,t,r)=>{"use strict";let{Writable:s}=r(27910),i=r(54246),{BINARY_TYPES:o,EMPTY_BUFFER:n,kStatusCode:a,kWebSocket:h}=r(15737),{concat:l,toArrayBuffer:c,unmask:d}=r(55245),{isValidStatusCode:f,isValidUTF8:u}=r(20673),_=Buffer[Symbol.species];class p extends s{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||o[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[h]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,r){if(8===this._opcode&&0==this._state)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new _(t.buffer,t.byteOffset+e,t.length-e),new _(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],s=t.length-e;e>=r.length?t.set(this._buffers.shift(),s):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),s),this._buffers[0]=new _(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let r=(64&t[0])==64;if(r&&!this._extensions[i.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=r}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*r+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=n;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&d(t,this._mask)}if(this._opcode>7)return void this.controlMessage(t,e);if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,r)=>{if(e)return t(e);if(r.length){if(this._messageLength+=r.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(r)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let s;s="nodebuffer"===this._binaryType?l(r,t):"arraybuffer"===this._binaryType?c(l(r,t)):"blob"===this._binaryType?new Blob(r):r,this._allowSynchronousEvents?(this.emit("message",s,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!0),this._state=0,this.startLoop(e)}))}else{let s=l(r,t);if(!this._skipUTF8Validation&&!u(s))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",s,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,n),this.end();else{let r=e.readUInt16BE(0);if(!f(r))return void t(this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let s=new _(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!u(s))return void t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",r,s),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,r,s,i){this._loop=!1,this._errored=!0;let o=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(o,this.createError),o.code=i,o[a]=s,o}}e.exports=p},72956:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Receiver:()=>i,Sender:()=>o,WebSocket:()=>n,WebSocketServer:()=>a,createWebSocketStream:()=>s,default:()=>h});var s=r(54342),i=r(66855),o=r(55419),n=r(63559),a=r(74467);let h=n},74467:(e,t,r)=>{"use strict";function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var s,i,o;s=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in s?Object.defineProperty(s,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):s[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let o=r(94735),n=r(81630),{Duplex:a}=r(27910),{createHash:h}=r(55511),l=r(58305),c=r(54246),d=r(64678),f=r(63559),{GUID:u,kWebSocket:_}=r(15737),p=/^[+/0-9A-Za-z]{22}==$/;class m extends o{constructor(e,t){if(super(),null==(e=i({allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:f},e)).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=n.createServer((e,t)=>{let r=n.STATUS_CODES[426];t.writeHead(426,{"Content-Length":r.length,"Content-Type":"text/plain"}),t.end(r)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let r of Object.keys(t))e.removeListener(r,t[r])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,r,s)=>{this.handleUpgrade(t,r,s,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(y,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(y,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{y(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,s){t.on("error",b);let i=e.headers["sec-websocket-key"],o=e.headers.upgrade,n=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void v(this,e,t,405,"Invalid HTTP method");if(void 0===o||"websocket"!==o.toLowerCase())return void v(this,e,t,400,"Invalid Upgrade header");if(void 0===i||!p.test(i))return void v(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");if(8!==n&&13!==n)return void v(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");if(!this.shouldHandle(e))return void g(t,400);let a=e.headers["sec-websocket-protocol"],h=new Set;if(void 0!==a)try{h=d.parse(a)}catch(r){v(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let f=e.headers["sec-websocket-extensions"],u={};if(this.options.perMessageDeflate&&void 0!==f){let r=new c(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=l.parse(f);e[c.extensionName]&&(r.accept(e[c.extensionName]),u[c.extensionName]=r)}catch(r){v(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let o={origin:e.headers[`${8===n?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(o,(o,n,a,l)=>{if(!o)return g(t,n||401,a,l);this.completeUpgrade(u,i,h,e,t,r,s)});if(!this.options.verifyClient(o))return g(t,401)}this.completeUpgrade(u,i,h,e,t,r,s)}completeUpgrade(e,t,r,s,i,o,n){if(!i.readable||!i.writable)return i.destroy();if(i[_])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return g(i,503);let a=h("sha1").update(t+u).digest("base64"),d=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${a}`],f=new this.options.WebSocket(null,void 0,this.options);if(r.size){let e=this.options.handleProtocols?this.options.handleProtocols(r,s):r.values().next().value;e&&(d.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[c.extensionName]){let t=e[c.extensionName].params,r=l.format({[c.extensionName]:[t]});d.push(`Sec-WebSocket-Extensions: ${r}`),f._extensions=e}this.emit("headers",d,s),i.write(d.concat("\r\n").join("\r\n")),i.removeListener("error",b),f.setSocket(i,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(y,this)})),n(f,s)}}function y(e){e._state=2,e.emit("close")}function b(){this.destroy()}function g(e,t,r,s){r=r||n.STATUS_CODES[t],s=i({Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r)},s),e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${n.STATUS_CODES[t]}\r
`+Object.keys(s).map(e=>`${e}: ${s[e]}`).join("\r\n")+"\r\n\r\n"+r)}function v(e,t,r,s,i){if(e.listenerCount("wsClientError")){let s=Error(i);Error.captureStackTrace(s,v),e.emit("wsClientError",s,r,t)}else g(r,s,i)}e.exports=m},74960:(e,t,r)=>{"use strict";let{kForOnEventAttribute:s,kListener:i}=r(15737),o=Symbol("kCode"),n=Symbol("kData"),a=Symbol("kError"),h=Symbol("kMessage"),l=Symbol("kReason"),c=Symbol("kTarget"),d=Symbol("kType"),f=Symbol("kWasClean");class u{constructor(e){this[c]=null,this[d]=e}get target(){return this[c]}get type(){return this[d]}}Object.defineProperty(u.prototype,"target",{enumerable:!0}),Object.defineProperty(u.prototype,"type",{enumerable:!0});class _ extends u{constructor(e,t={}){super(e),this[o]=void 0===t.code?0:t.code,this[l]=void 0===t.reason?"":t.reason,this[f]=void 0!==t.wasClean&&t.wasClean}get code(){return this[o]}get reason(){return this[l]}get wasClean(){return this[f]}}Object.defineProperty(_.prototype,"code",{enumerable:!0}),Object.defineProperty(_.prototype,"reason",{enumerable:!0}),Object.defineProperty(_.prototype,"wasClean",{enumerable:!0});class p extends u{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[h]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[h]}}Object.defineProperty(p.prototype,"error",{enumerable:!0}),Object.defineProperty(p.prototype,"message",{enumerable:!0});class m extends u{constructor(e,t={}){super(e),this[n]=void 0===t.data?null:t.data}get data(){return this[n]}}function y(e,t,r){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}Object.defineProperty(m.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:_,ErrorEvent:p,Event:u,EventTarget:{addEventListener(e,t,r={}){let o;for(let o of this.listeners(e))if(!r[s]&&o[i]===t&&!o[s])return;if("message"===e)o=function(e,r){let s=new m("message",{data:r?e:e.toString()});s[c]=this,y(t,this,s)};else if("close"===e)o=function(e,r){let s=new _("close",{code:e,reason:r.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});s[c]=this,y(t,this,s)};else if("error"===e)o=function(e){let r=new p("error",{error:e,message:e.message});r[c]=this,y(t,this,r)};else{if("open"!==e)return;o=function(){let e=new u("open");e[c]=this,y(t,this,e)}}o[s]=!!r[s],o[i]=t,r.once?this.once(e,o):this.on(e,o)},removeEventListener(e,t){for(let r of this.listeners(e))if(r[i]===t&&!r[s]){this.removeListener(e,r);break}}},MessageEvent:m}}};
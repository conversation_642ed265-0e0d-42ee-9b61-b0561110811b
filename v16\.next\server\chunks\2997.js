"use strict";exports.id=2997,exports.ids=[2997],exports.modules={5089:(e,r,a)=>{a.d(r,{w:()=>c,x:()=>l});var t=a(72267),o=a(99409),n=a(26564),i=a(7950),s=a(55622);async function l(){try{let{user:e}=await (0,o.iF)();if(!e)throw Error("Usuario no autenticado");let{data:r}=await t.N.from("documentos").select("id").eq("user_id",e.id),a=await (0,n.oE)(),l=await (0,i.Lx)(),c=await (0,i.oC)(),d=0,u=0,m=0,f=0,p=0,h=(await Promise.all(a.map(async e=>{let r=await (0,s.yV)(e.id);return d+=r.total,u+=r.paraHoy,m+=r.nuevas,f+=r.aprendiendo,p+=r.repasando,{id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,paraHoy:r.paraHoy}}))).sort((e,r)=>new Date(r.fechaCreacion).getTime()-new Date(e.fechaCreacion).getTime()).slice(0,5),g=l.map(e=>({id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,numeroPreguntas:e.numero_preguntas||0})).slice(0,5);return{totalDocumentos:r?.length||0,totalColeccionesFlashcards:a.length,totalTests:l.length,totalFlashcards:d,flashcardsParaHoy:u,flashcardsNuevas:m,flashcardsAprendiendo:f,flashcardsRepasando:p,testsRealizados:c.totalTests,porcentajeAcierto:c.porcentajeAcierto,coleccionesRecientes:h,testsRecientes:g}}catch(e){return console.error("Error al obtener estad\xedsticas del dashboard:",e),{totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}}}async function c(e=10){try{let{user:r}=await (0,o.iF)();if(!r)return[];let a=await (0,n.oE)();if(0===a.length)return[];let i=new Date;i.setHours(23,59,59,999);let{data:s,error:l}=await t.N.from("progreso_flashcards").select("flashcard_id, proxima_revision, estado").lte("proxima_revision",i.toISOString()).order("proxima_revision",{ascending:!0}).limit(e);if(l)return console.error("Error al obtener progreso de flashcards:",l),[];if(!s||0===s.length)return[];let c=s.map(e=>e.flashcard_id),{data:d,error:u}=await t.N.from("flashcards").select("id, pregunta, coleccion_id").in("id",c);if(u)return console.error("Error al obtener flashcards:",u),[];let m=[];for(let e of s){let r=d?.find(r=>r.id===e.flashcard_id);if(r){let t=a.find(e=>e.id===r.coleccion_id);t&&m.push({id:r.id,pregunta:r.pregunta,coleccionTitulo:t.titulo,coleccionId:t.id,proximaRevision:e.proxima_revision,estado:e.estado||"nuevo"})}}return m}catch(e){return console.error("Error al obtener pr\xf3ximas flashcards:",e),[]}}},7950:(e,r,a)=>{a.d(r,{Gl:()=>d,Kj:()=>l,Lx:()=>i,OA:()=>c,_4:()=>n,dd:()=>m,hg:()=>s,oC:()=>u});var t=a(72267),o=a(99409);async function n(e,r,a){try{console.log("\uD83D\uDCDD Creando nuevo test:",e);let{user:n}=await (0,o.iF)();if(!n)return console.error("❌ No hay usuario autenticado para crear test"),null;console.log("\uD83D\uDC64 Usuario autenticado:",n.id);let{data:i,error:s}=await t.N.from("tests").insert([{titulo:e,descripcion:r,documentos_ids:a,user_id:n.id}]).select();if(s)return console.error("❌ Error al crear test:",s),null;return console.log("✅ Test creado exitosamente:",i?.[0]?.id),i?.[0]?.id||null}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al crear test:",e),null}}async function i(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:a}=await t.N.from("tests").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(a)return console.error("Error al obtener tests:",a),[];return r||[]}catch(e){return console.error("Error al obtener tests:",e),[]}}async function s(e){let{data:r,error:a}=await t.N.from("preguntas_test").select("*").eq("test_id",e);return a?(console.error("Error al obtener preguntas de test:",a),[]):r||[]}async function l(e){let{count:r,error:a}=await t.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",e);return a?(console.error("Error al obtener conteo de preguntas:",a),0):r||0}async function c(e){let{error:r}=await t.N.from("preguntas_test").insert(e);return!r||(console.error("Error al guardar preguntas de test:",r),!1)}async function d(e,r,a,o){let{error:n}=await t.N.from("estadisticas_test").insert([{test_id:e,pregunta_id:r,respuesta_seleccionada:a,es_correcta:o,fecha_respuesta:new Date().toISOString()}]);return!n||(console.error("Error al registrar respuesta de test:",n),!1)}async function u(){let{data:e,error:r}=await t.N.from("estadisticas_test").select("*");if(r)return console.error("Error al obtener estad\xedsticas de tests:",r),{totalTests:0,totalPreguntas:0,totalRespuestasCorrectas:0,totalRespuestasIncorrectas:0,porcentajeAcierto:0};let a=new Set(e?.map(e=>e.test_id)||[]),o=new Set(e?.map(e=>e.pregunta_id)||[]),n=e?.filter(e=>e.es_correcta).length||0,i=(e?.length||0)-n,s=e&&e.length>0?Math.round(n/e.length*100):0;return{totalTests:a.size,totalPreguntas:o.size,totalRespuestasCorrectas:n,totalRespuestasIncorrectas:i,porcentajeAcierto:s}}async function m(e){let{data:r,error:a}=await t.N.from("estadisticas_test").select("*").eq("test_id",e);if(a)return console.error("Error al obtener estad\xedsticas del test:",a),{testId:e,totalPreguntas:0,totalCorrectas:0,totalIncorrectas:0,porcentajeAcierto:0,fechasRealizacion:[],preguntasMasFalladas:[]};let{data:o}=await t.N.from("preguntas_test").select("*").eq("test_id",e),n=r?.filter(e=>e.es_correcta).length||0,i=(r?.length||0)-n,s=r&&r.length>0?Math.round(n/r.length*100):0,l=new Set;r?.forEach(e=>{let r=new Date(e.fecha_respuesta);l.add(`${r.getDate()}/${r.getMonth()+1}/${r.getFullYear()}`)});let c=Array.from(l),d=new Map;r?.forEach(e=>{let r=d.get(e.pregunta_id)||{fallos:0,aciertos:0};e.es_correcta?r.aciertos++:r.fallos++,d.set(e.pregunta_id,r)});let u=Array.from(d.entries()).map(([e,r])=>({preguntaId:e,totalFallos:r.fallos,totalAciertos:r.aciertos,pregunta:o?.find(r=>r.id===e)?.pregunta||"Desconocida"})).sort((e,r)=>r.totalFallos-e.totalFallos).slice(0,5);return{testId:e,totalPreguntas:o?.length||0,totalCorrectas:n,totalIncorrectas:i,porcentajeAcierto:s,fechasRealizacion:c,preguntasMasFalladas:u}}},11820:(e,r,a)=>{a.d(r,{iF:()=>o});var t=a(48921);async function o(){try{let{data:{user:e},error:r}=await t.N.auth.getUser();if(r){if("Auth session missing!"===r.message)return{user:null,error:null};return{user:null,error:r.message}}return{user:e,error:null}}catch(e){return{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"}}}},21232:(e,r,a)=>{a.d(r,{$S:()=>i,d7:()=>s,fF:()=>n});var t=a(48921),o=a(11820);async function n(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return null;let{data:n,error:i}=await t.N.from("temarios").select("id").eq("id",e).eq("user_id",r.id).single();if(i){if("PGRST116"===i.code)return console.log("Temario no encontrado:",e),null;return console.error("Error al verificar temario:",i),null}if(!n)return console.log("Temario no encontrado:",e),null;let{data:s,error:l}=await t.N.from("planes_estudios").select("*").eq("user_id",r.id).eq("temario_id",e).eq("activo",!0).single();if(l){if("PGRST116"===l.code)return null;return console.error("Error al obtener plan activo:",l),null}return s}catch(e){return console.error("Error al obtener plan activo:",e),null}}async function i(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return[];let{data:n,error:i}=await t.N.from("progreso_plan_estudios").select("*").eq("plan_id",e).eq("user_id",r.id).order("semana_numero",{ascending:!0}).order("creado_en",{ascending:!0});if(i)return console.error("Error al obtener progreso del plan:",i),[];return n||[]}catch(e){return console.error("Error al obtener progreso del plan:",e),[]}}async function s(e,r,a,n,i,s,l,c,d){try{let{user:u,error:m}=await (0,o.iF)();if(!u||m)return!1;let{data:f}=await t.N.from("progreso_plan_estudios").select("id").eq("plan_id",e).eq("user_id",u.id).eq("semana_numero",r).eq("dia_nombre",a).eq("tarea_titulo",n).single();if(f){let{error:e}=await t.N.from("progreso_plan_estudios").update({completado:s,fecha_completado:s?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d,actualizado_en:new Date().toISOString()}).eq("id",f.id);if(e)return console.error("Error al actualizar progreso:",e),!1}else{let{error:o}=await t.N.from("progreso_plan_estudios").insert([{plan_id:e,user_id:u.id,semana_numero:r,dia_nombre:a,tarea_titulo:n,tarea_tipo:i,completado:s,fecha_completado:s?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d}]);if(o)return console.error("Error al crear progreso:",o),!1}return!0}catch(e){return console.error("Error al guardar progreso de tarea:",e),!1}}},24548:(e,r,a)=>{a.d(r,{A:()=>q});var t=a(96554),o=a.n(t),n=a(81815),i=a(21232),s=a(29959);function l(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function c(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?l(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}let d={firstDayOfWeek:1,locale:"es-ES",dateFormat:"YYYY-MM-DD",displayFormat:"DD/MM/YYYY"},u=["Lunes","Martes","Mi\xe9rcoles","Jueves","Viernes","S\xe1bado","Domingo"],m=["LU","MA","MI","JU","VI","SA","DO"],f=["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"];function p(e){if(!e||"string"!=typeof e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return null;let r=new Date(e+"T00:00:00.000Z");if(isNaN(r.getTime()))return null;let a=r.getUTCFullYear(),t=String(r.getUTCMonth()+1).padStart(2,"0"),o=String(r.getUTCDate()).padStart(2,"0");return`${a}-${t}-${o}`!==e?null:r}function h(e){if(!e||isNaN(e.getTime()))return"";let r=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0");return`${r}-${a}-${t}`}function g(e,r){let a=p(e);if(!a)return null;let t=function(e){let r=u.findIndex(r=>r.toLowerCase()===e.toLowerCase());return r>=0?r:-1}(r);if(-1===t)return null;let o=new Date(a);return o.setDate(a.getDate()+t),o}function b(e,r){return new Date(e,r,1)}function y(e){var r;return r=new Date,!!e&&!!r&&e.getFullYear()===r.getFullYear()&&e.getMonth()===r.getMonth()&&e.getDate()===r.getDate()}function x(e,r,a){return e.getFullYear()===r&&e.getMonth()===a}function v(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function j(e,r){let a=h(e),t=r.mapaDias.get(a);return t?.tareas||[]}function w(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function N(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?w(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):w(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}function _(e){}function O(){let e=new Date;return{mesActual:e.getMonth(),yearActual:e.getFullYear(),fechaSeleccionada:void 0,calendarioExpandido:!0,primerDiaSemana:1,vistaTamaño:"normal"}}function E(e){e&&e.toISOString()}function S(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function D(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?S(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):S(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}var P=a(99631);let A=({plan:e,progresoPlan:r,fechaSeleccionada:a,onFechaSeleccionada:o,onMesChanged:i,className:s=""})=>{let{estadoCalendario:l,isLoading:u,error:w,navegarMes:S,irAHoy:A,tituloMes:T,esFechaSeleccionable:C}=function(e,r,a){let{preferences:o,updatePreference:n}=function(){let{0:e,1:r}=(0,t.useState)(O);(0,t.useEffect)(()=>{r(O())},[]);let a=(0,t.useCallback)(e=>{r(r=>{let a=N(N({},r),e);return _(e),a})},[]),o=(0,t.useCallback)((e,r)=>{a({[e]:r})},[a]);return{preferences:e,updatePreferences:a,updatePreference:o,clearPreferences:()=>{r(O())}}}(),{0:i,1:s}=(0,t.useState)(()=>{var e;void 0!==o.primerDiaSemana&&(e=o.primerDiaSemana,d=c(c({},d),{},{firstDayOfWeek:e}));let r=new Date,t=function(){let e=O();if(!e.fechaSeleccionada)return null;try{let r=new Date(e.fechaSeleccionada);return isNaN(r.getTime())?null:r}catch{return null}}();return{yearActual:o.yearActual??r.getFullYear(),mesActual:o.mesActual??r.getMonth(),fechaSeleccionada:a||t||null,fechasCalendario:[],diasCalendario:[]}}),{0:l,1:u}=(0,t.useState)(!1),{0:m,1:w}=(0,t.useState)(null),S=(0,t.useRef)(e),P=(0,t.useRef)(r),A=S.current!==e,T=JSON.stringify(P.current)!==JSON.stringify(r),C=(0,t.useMemo)(()=>{if(!e||!A&&!T&&S.current&&P.current)return null;try{u(!0),w(null);let a=function(e,r,a={}){let t=[],o=new Map,n=function(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?v(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):v(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}({incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!1},a);if(!e||!e.semanas||!Array.isArray(e.semanas)){var i;return t.push("Plan de estudios inv\xe1lido o sin semanas"),i=t,{datosPlan:{fechaInicio:new Date,fechaFin:new Date,totalSemanas:0,mapaDias:new Map,rangoFechas:{minYear:new Date().getFullYear(),maxYear:new Date().getFullYear(),minMonth:new Date().getMonth(),maxMonth:new Date().getMonth()}},estadisticas:{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:i}}if(n.validarFechas){let r=function(e){let r=[],a=[],t=[];if(!e.semanas||!Array.isArray(e.semanas))return r.push("El plan no contiene semanas v\xe1lidas"),{esValido:!1,errores:r,advertencias:a,fechasProblematicas:t};let o=null;for(let n of e.semanas){if(!n||"number"!=typeof n.numero){r.push(`Semana inv\xe1lida encontrada`);continue}let e=p(n.fechaInicio),i=p(n.fechaFin);if(!e){r.push(`Fecha de inicio inv\xe1lida en semana ${n.numero}: ${n.fechaInicio}`),t.push({semana:n.numero,fecha:n.fechaInicio,problema:"Fecha de inicio inv\xe1lida"});continue}if(!i){r.push(`Fecha de fin inv\xe1lida en semana ${n.numero}: ${n.fechaFin}`),t.push({semana:n.numero,fecha:n.fechaFin,problema:"Fecha de fin inv\xe1lida"});continue}i<=e&&r.push(`La fecha de fin debe ser posterior a la de inicio en semana ${n.numero}`),o&&e<o&&a.push(`La semana ${n.numero} comienza antes de que termine la anterior`),o=i}return{esValido:0===r.length,errores:r,advertencias:a,fechasProblematicas:t}}(e);r.esValido||t.push(...r.errores)}let s=null,l=null;for(let a of e.semanas){if(!a||"number"!=typeof a.numero){t.push(`Semana inv\xe1lida encontrada`);continue}let e=p(a.fechaInicio),i=p(a.fechaFin);if(!e||!i){t.push(`Fechas inv\xe1lidas en semana ${a.numero}`);continue}if((!s||e<s)&&(s=e),(!l||i>l)&&(l=i),a.dias&&Array.isArray(a.dias))for(let e of a.dias){let i=function(e,r,a,t){var o,n,i;if(!e||!e.dia)return{error:`D\xeda inv\xe1lido en semana ${r.numero}`};let s=g(r.fechaInicio,e.dia);if(!s)return{error:`No se pudo calcular la fecha para ${e.dia} en semana ${r.numero}`};let l=[];if(e.tareas&&Array.isArray(e.tareas))for(let t of e.tareas){if(!t||!t.titulo)continue;let o=a.find(a=>a.semana_numero===r.numero&&a.dia_nombre===e.dia&&a.tarea_titulo===t.titulo);l.push({tarea:t,semanaNumero:r.numero,diaNombre:e.dia,completada:o?.completado||!1,fechaCompletado:o?.fecha_completado})}t.ordenarTareasPorTipo&&l.sort((e,r)=>{let a={estudio:0,repaso:1,practica:2,evaluacion:3};return(a[e.tarea.tipo]||99)-(a[r.tarea.tipo]||99)});let c=l.length,d=l.filter(e=>e.completada).length,u=c>0?d/c*100:0,m=(o=s,n=c,i=d,y(o)?"hoy":0===n?"normal":0===i?"con-tareas":i===n?"completado":"parcial");return{diaCalendario:{fecha:s,dia:s.getDate(),estaEnMesActual:!0,esHoy:y(s),estado:m,tareas:l,totalTareas:c,tareasCompletadas:d,porcentajeCompletado:u}}}(e,a,r,n);if(i.error){t.push(i.error);continue}if(i.diaCalendario){let e=h(i.diaCalendario.fecha);o.set(e,i.diaCalendario),i.diaCalendario.totalTareas}}}return{datosPlan:{fechaInicio:s||new Date,fechaFin:l||new Date,totalSemanas:e.semanas.length,mapaDias:o,rangoFechas:function(e,r){if(!e||!r){let e=new Date;return{minYear:e.getFullYear(),maxYear:e.getFullYear(),minMonth:e.getMonth(),maxMonth:e.getMonth()}}return{minYear:e.getFullYear(),maxYear:r.getFullYear(),minMonth:e.getMonth(),maxMonth:r.getMonth()}}(s,l)},estadisticas:n.calcularEstadisticas?function(e,r){let a=0,t=0,o=0,n=0;for(let r of e.values())r.totalTareas>0&&(o++,a+=r.totalTareas,t+=r.tareasCompletadas,r.tareasCompletadas===r.totalTareas&&n++);return{totalTareas:a,tareasCompletadas:t,porcentajeGeneral:a>0?t/a*100:0,diasConTareas:o,diasCompletados:n}}(o,0):{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:t}}(e,r,{incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!0});return a.errores.length>0&&(console.warn("Errores al procesar el plan:",a.errores),w(a.errores[0])),a.datosPlan}catch(e){return w(e instanceof Error?e.message:"Error desconocido al procesar el plan"),console.error("Error al procesar plan para calendario:",e),null}finally{u(!1)}},[e,r,A,T]),F=(0,t.useMemo)(()=>(function(e,r){let a=[],t=b(e,r),o=new Date(e,r+1,0),n=function(e,r){let a=b(e,r).getDay();return 1===d.firstDayOfWeek?0===a?6:a-1:a}(e,r);for(let e=n-1;e>=0;e--){let r=new Date(t);r.setDate(r.getDate()-(e+1)),a.push(r)}for(let t=1;t<=o.getDate();t++)a.push(new Date(e,r,t));let i=42-a.length;for(let e=1;e<=i;e++){let r=new Date(o);r.setDate(r.getDate()+e),a.push(r)}return a})(i.yearActual,i.mesActual),[i.yearActual,i.mesActual]);(0,t.useMemo)(()=>C?F.map(e=>{let r=h(e),a=C.mapaDias.get(r);return a?D(D({},a),{},{estaEnMesActual:x(e,i.yearActual,i.mesActual),esHoy:y(e)}):{fecha:e,dia:e.getDate(),estaEnMesActual:x(e,i.yearActual,i.mesActual),esHoy:y(e),estado:y(e)?"hoy":x(e,i.yearActual,i.mesActual)?"normal":"fuera-mes",tareas:[],totalTareas:0,tareasCompletadas:0,porcentajeCompletado:0}}):[],[F,C,i.yearActual,i.mesActual]);let k=(0,t.useCallback)(e=>{s(r=>{var a,t,o,i;let{year:s,month:l}="anterior"===e?(a=r.yearActual,0===(t=r.mesActual)?{year:a-1,month:11}:{year:a,month:t-1}):(o=r.yearActual,11===(i=r.mesActual)?{year:o+1,month:0}:{year:o,month:i+1});return n("yearActual",s),n("mesActual",l),D(D({},r),{},{yearActual:s,mesActual:l})})},[n]),q=(0,t.useCallback)((e,r)=>{n("yearActual",e),n("mesActual",r),s(a=>D(D({},a),{},{yearActual:e,mesActual:r}))},[n]),M=(0,t.useCallback)(e=>{E(e),s(r=>D(D({},r),{},{fechaSeleccionada:e}))},[]),I=(0,t.useCallback)(()=>{let e=new Date;n("yearActual",e.getFullYear()),n("mesActual",e.getMonth()),E(e),s(r=>D(D({},r),{},{yearActual:e.getFullYear(),mesActual:e.getMonth(),fechaSeleccionada:e}))},[n]),$=(0,t.useCallback)(e=>C?j(e,C):[],[C]),R=(0,t.useCallback)(e=>{if(!C)return"normal";let r=h(e),a=C.mapaDias.get(r);return a?.estado||"normal"},[C]),z=(0,t.useCallback)(e=>{if(!C)return!1;let r=h(e);return void 0!==C.mapaDias.get(r)||e>=C.fechaInicio&&e<=C.fechaFin},[C]),Y=(0,t.useMemo)(()=>{let e=new Date(i.yearActual,i.mesActual);return`${f[e.getMonth()]} ${i.yearActual}`},[i.yearActual,i.mesActual]),H=(0,t.useMemo)(()=>i.fechaSeleccionada&&C?j(i.fechaSeleccionada,C):[],[i.fechaSeleccionada,C]),U=(0,t.useMemo)(()=>{if(!i.fechaSeleccionada||!C)return null;let e=h(i.fechaSeleccionada),r=C.mapaDias.get(e);return r?{total:r.totalTareas,completadas:r.tareasCompletadas,porcentaje:r.porcentajeCompletado}:null},[i.fechaSeleccionada,C]);return{estadoCalendario:i,datosPlan:C,isLoading:l,error:m,navegarMes:k,irAMes:q,seleccionarFecha:M,irAHoy:I,obtenerTareasDelDia:$,obtenerEstadoDia:R,esFechaSeleccionable:z,tituloMes:Y,tareasDelDiaSeleccionado:H,estadisticasDelDia:U}}(e,r,a),F=e=>{C(e.fecha)&&o(e.fecha)},k=e=>{S(e),i&&i(l.yearActual,l.mesActual)},q=(e,r)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),F(r))},M=(e,r)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),r())},I=e=>{let r=["relative","aspect-square","flex","items-center","justify-center","text-xs","sm:text-sm","font-medium","cursor-pointer","calendario-day-hover","calendario-estado-transition","rounded-none","sm:rounded-lg","border","border-transparent","min-h-[2.5rem]","sm:min-h-[3rem]"];switch(e.estaEnMesActual?r.push("text-gray-700","hover:text-gray-900"):r.push("text-gray-300","hover:text-gray-400"),e.estado){case"hoy":r.push("bg-blue-100","text-blue-900","border-blue-300","font-bold","ring-2","ring-blue-400","ring-opacity-50","calendario-pulso");break;case"con-tareas":r.push("bg-orange-50","text-orange-800","border-orange-200","hover:bg-orange-100","hover:border-orange-300");break;case"completado":r.push("bg-green-50","text-green-800","border-green-200","hover:bg-green-100","hover:border-green-300");break;case"parcial":r.push("bg-yellow-50","text-yellow-800","border-yellow-200","hover:bg-yellow-100","hover:border-yellow-300");break;case"normal":e.estaEnMesActual&&r.push("hover:bg-gray-50","hover:border-gray-200")}return a&&e.fecha.getTime()===a.getTime()&&r.push("ring-2","ring-blue-500","ring-opacity-75","bg-blue-50","border-blue-300"),C(e.fecha)||r.push("cursor-not-allowed","opacity-50"),r.join(" ")},$=e=>{if(0===e.totalTareas)return null;let r=e.porcentajeCompletado,a="bg-orange-400";return 100===r?a="bg-green-400":r>0&&(a="bg-yellow-400"),(0,P.jsx)("div",{className:"absolute bottom-1 right-1",children:(0,P.jsx)("div",{className:`w-2 h-2 rounded-full ${a}`,title:`${e.tareasCompletadas}/${e.totalTareas} tareas completadas`})})};return w?(0,P.jsxs)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${s}`,children:[(0,P.jsxs)("div",{className:"flex items-center text-red-800",children:[(0,P.jsx)(n.wIk,{className:"w-5 h-5 mr-2"}),(0,P.jsx)("span",{className:"font-medium",children:"Error en el calendario"})]}),(0,P.jsx)("p",{className:"text-red-600 text-sm mt-1",children:w})]}):(0,P.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-fade-in ${s}`,children:[(0,P.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,P.jsxs)("div",{className:"flex items-center justify-between",children:[(0,P.jsx)("button",{onClick:()=>k("anterior"),onKeyDown:e=>M(e,()=>k("anterior")),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes anterior",tabIndex:0,children:(0,P.jsx)(n.irw,{className:"w-5 h-5 text-gray-600"})}),(0,P.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,P.jsx)(n.wIk,{className:"w-4 h-4 text-gray-600 hidden sm:block"}),(0,P.jsx)("h3",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:T})]}),(0,P.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,P.jsx)("button",{onClick:A,onKeyDown:e=>M(e,A),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",title:"Ir a hoy","aria-label":"Ir a hoy",tabIndex:0,children:(0,P.jsx)(n.V5Y,{className:"w-4 h-4 text-gray-600"})}),(0,P.jsx)("button",{onClick:()=>k("siguiente"),onKeyDown:e=>M(e,()=>k("siguiente")),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes siguiente",tabIndex:0,children:(0,P.jsx)(n.fOo,{className:"w-5 h-5 text-gray-600"})})]})]})}),(0,P.jsx)("div",{className:"grid grid-cols-7 bg-gray-100 border-b border-gray-200",children:m.map(e=>(0,P.jsx)("div",{className:"py-1 sm:py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wide",children:e},e))}),(0,P.jsx)("div",{className:"grid grid-cols-7 gap-0",children:u?Array.from({length:42},(e,r)=>(0,P.jsx)("div",{className:"aspect-square flex items-center justify-center border-r border-b border-gray-100 last:border-r-0",children:(0,P.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded animate-pulse"})},r)):l.diasCalendario.map((e,r)=>(0,P.jsx)("div",{className:`border-r border-b border-gray-100 last:border-r-0 ${5===Math.floor(r/7)?"border-b-0":""}`,children:(0,P.jsxs)("button",{onClick:()=>F(e),onKeyDown:r=>q(r,e),className:`${I(e)} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`,disabled:!C(e.fecha),tabIndex:C(e.fecha)?0:-1,"aria-label":`${e.dia} de ${T}${e.totalTareas>0?`, ${e.totalTareas} tareas`:""}${e.esHoy?", hoy":""}${"completado"===e.estado?", completado":"parcial"===e.estado?", parcialmente completado":"con-tareas"===e.estado?", con tareas pendientes":""}`,"aria-pressed":a&&e.fecha.getTime()===a.getTime()?"true":"false",children:[e.dia,$(e)]})},r))}),(0,P.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 border-t border-gray-200",children:(0,P.jsxs)("div",{className:"flex items-center justify-center space-x-2 sm:space-x-4 text-xs text-gray-600",children:[(0,P.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,P.jsx)("div",{className:"w-2 h-2 bg-orange-400 rounded-full"}),(0,P.jsx)("span",{className:"hidden sm:inline",children:"Pendientes"}),(0,P.jsx)("span",{className:"sm:hidden",children:"Pend."})]}),(0,P.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,P.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),(0,P.jsx)("span",{className:"hidden sm:inline",children:"Parcial"}),(0,P.jsx)("span",{className:"sm:hidden",children:"Parc."})]}),(0,P.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,P.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,P.jsx)("span",{className:"hidden sm:inline",children:"Completado"}),(0,P.jsx)("span",{className:"sm:hidden",children:"Comp."})]})]})})]})},T=({fecha:e,tareas:r,isLoading:a=!1,onTareaClick:t,className:i=""})=>{let s=e=>{switch(e){case"estudio":return(0,P.jsx)(n.H9b,{className:"w-4 h-4"});case"repaso":return(0,P.jsx)(n.jTZ,{className:"w-4 h-4"});case"practica":return(0,P.jsx)(n.aze,{className:"w-4 h-4"});case"evaluacion":return(0,P.jsx)(n.x_j,{className:"w-4 h-4"});default:return(0,P.jsx)(n.Ohp,{className:"w-4 h-4"})}},l=e=>{switch(e){case"estudio":return"text-blue-600 bg-blue-50 border-blue-200";case"repaso":return"text-green-600 bg-green-50 border-green-200";case"practica":return"text-purple-600 bg-purple-50 border-purple-200";case"evaluacion":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},c=e=>{switch(e){case"estudio":return"Estudio";case"repaso":return"Repaso";case"practica":return"Pr\xe1ctica";case"evaluacion":return"Evaluaci\xf3n";default:return"Tarea"}},u=o().useMemo(()=>{if(!r.length)return null;let e=r.filter(e=>e.completada).length,a=r.length,t=Math.round(e/a*100);return{completadas:e,total:a,porcentaje:t}},[r]),m=e=>{t&&t(e)},f=(e,r)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),m(r))};return e?(0,P.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-slide-in ${i}`,children:[(0,P.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,P.jsxs)("div",{className:"flex items-center justify-between",children:[(0,P.jsxs)("div",{children:[(0,P.jsx)("h4",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:function(e){return!e||isNaN(e.getTime())?"":e.toLocaleDateString(d.locale,{day:"2-digit",month:"2-digit",year:"numeric"})}(e)}),u&&(0,P.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600",children:[u.completadas," de ",u.total," tareas",(0,P.jsx)("span",{className:"hidden sm:inline",children:" completadas"})]})]}),u&&(0,P.jsx)("div",{className:"text-right",children:(0,P.jsxs)("div",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${100===u.porcentaje?"bg-green-100 text-green-800":u.porcentaje>0?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:[u.porcentaje,"%"]})})]})}),(0,P.jsx)("div",{className:"p-3 sm:p-4",children:a?(0,P.jsx)("div",{className:"space-y-3",children:Array.from({length:3},(e,r)=>(0,P.jsx)("div",{className:"animate-pulse",children:(0,P.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,P.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded"}),(0,P.jsxs)("div",{className:"flex-1",children:[(0,P.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,P.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},r))}):0===r.length?(0,P.jsxs)("div",{className:"text-center py-6",children:[(0,P.jsx)(n.y3G,{className:"w-8 h-8 mx-auto mb-2 text-gray-400"}),(0,P.jsx)("p",{className:"text-sm text-gray-500",children:"No hay tareas programadas"}),(0,P.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"para este d\xeda"})]}):(0,P.jsx)("div",{className:"space-y-3",children:r.map((e,r)=>(0,P.jsx)("div",{className:`border rounded-lg p-3 calendario-estado-transition ${t?"cursor-pointer hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 calendario-day-hover":""} ${e.completada?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-gray-300"}`,onClick:()=>m(e),onKeyDown:r=>f(r,e),tabIndex:t?0:-1,role:t?"button":void 0,"aria-label":`Tarea: ${e.tarea.titulo}${e.completada?", completada":", pendiente"}`,children:(0,P.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,P.jsx)("div",{className:`flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center ${e.completada?"bg-green-500 border-green-500":"border-gray-300"}`,children:e.completada&&(0,P.jsx)(n.YrT,{className:"w-3 h-3 text-white"})}),(0,P.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,P.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,P.jsxs)("div",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${l(e.tarea.tipo)}`,children:[s(e.tarea.tipo),(0,P.jsx)("span",{className:"ml-1",children:c(e.tarea.tipo)})]}),e.tarea.duracionEstimada&&(0,P.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,P.jsx)(n.Ohp,{className:"w-3 h-3 mr-1"}),e.tarea.duracionEstimada]})]}),(0,P.jsx)("h5",{className:`font-medium text-sm ${e.completada?"text-green-800 line-through":"text-gray-900"}`,children:e.tarea.titulo}),e.tarea.descripcion&&(0,P.jsx)("p",{className:`text-xs mt-1 ${e.completada?"text-green-600":"text-gray-600"}`,children:e.tarea.descripcion}),(0,P.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,P.jsxs)("div",{className:"text-xs text-gray-500",children:["Semana ",e.semanaNumero," • ",e.diaNombre]}),e.completada&&e.fechaCompletado&&(0,P.jsx)("div",{className:"text-xs text-green-600",children:"✓ Completada"})]})]})]})},r))})}),r.length>0&&!a&&(0,P.jsx)("div",{className:"bg-gray-50 px-4 py-2 border-t border-gray-200",children:(0,P.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,P.jsxs)("span",{children:[r.filter(e=>e.completada).length," completadas"]}),(0,P.jsxs)("span",{children:[r.filter(e=>!e.completada).length," pendientes"]})]})})]}):(0,P.jsx)("div",{className:`bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 ${i}`,children:(0,P.jsxs)("div",{className:"text-center text-gray-500",children:[(0,P.jsx)(n.wIk,{className:"w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400"}),(0,P.jsx)("p",{className:"text-xs sm:text-sm",children:"Selecciona un d\xeda en el calendario"}),(0,P.jsx)("p",{className:"text-xs text-gray-400 mt-1 hidden sm:block",children:"para ver las tareas programadas"})]})})},C=({isOpen:e,onClose:r,plan:a,progresoPlan:o,fechaSeleccionada:i,onFechaSeleccionada:s,tareasDelDia:l,onTareaClick:c})=>((0,t.useEffect)(()=>{let a=e=>{"Escape"===e.key&&r()};return e&&(document.addEventListener("keydown",a),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",a),document.body.style.overflow="unset"}},[e,r]),e)?(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden calendario-modal-overlay",onClick:r,"aria-hidden":"true"}),(0,P.jsx)("div",{className:"fixed inset-0 z-50 lg:hidden",children:(0,P.jsx)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:(0,P.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-t-lg sm:rounded-lg text-left overflow-hidden shadow-xl calendario-modal-content sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,P.jsxs)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between",children:[(0,P.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Calendario del Plan"}),(0,P.jsx)("button",{onClick:r,className:"p-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Cerrar calendario",children:(0,P.jsx)(n.yGN,{className:"w-5 h-5 text-gray-600"})})]}),(0,P.jsxs)("div",{className:"bg-white px-4 py-4 space-y-4 max-h-[70vh] overflow-y-auto",children:[(0,P.jsx)(A,{plan:a,progresoPlan:o,fechaSeleccionada:i,onFechaSeleccionada:s}),i&&(0,P.jsx)(T,{fecha:i,tareas:l,onTareaClick:e=>{c&&c(e),r()}})]}),(0,P.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-t border-gray-200",children:(0,P.jsx)("button",{onClick:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Cerrar"})})]})})})]}):null;function F(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function k(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?F(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):F(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}let q=({plan:e,temarioId:r})=>{let{0:a,1:o}=(0,t.useState)([]),{0:l,1:c}=(0,t.useState)(null),{0:d,1:u}=(0,t.useState)(!0),{0:m,1:f}=(0,t.useState)(null),{0:p,1:b}=(0,t.useState)(!1),y=(0,t.useRef)({});(0,t.useEffect)(()=>{x()},[r]);let x=async()=>{try{let e=await (0,i.fF)(r);if(!e){console.warn("No se encontr\xf3 plan activo para el temario:",r),c(null),o([]),u(!1);return}c(e.id);let a=await (0,i.$S)(e.id);o(a)}catch(e){console.error("Error al cargar progreso:",e),c(null),o([])}finally{u(!1)}},v=async(e,r,t)=>{if(!l)return void s.oR.error("No se pudo identificar el plan de estudios");try{let n=a.find(a=>a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo),c=!n?.completado;await (0,i.d7)(l,r,t,e.titulo,e.tipo,c)?(o(a=>{let o=a.findIndex(a=>a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo);if(!(o>=0))return[...a,{id:`temp-${Date.now()}`,plan_id:l,user_id:"",semana_numero:r,dia_nombre:t,tarea_titulo:e.titulo,tarea_tipo:e.tipo,completado:c,fecha_completado:c?new Date().toISOString():void 0,creado_en:new Date().toISOString(),actualizado_en:new Date().toISOString()}];{let e=[...a];return e[o]=k(k({},e[o]),{},{completado:c,fecha_completado:c?new Date().toISOString():void 0}),e}}),s.oR.success(c?"Tarea completada":"Tarea marcada como pendiente")):s.oR.error("Error al actualizar el progreso")}catch(e){console.error("Error al actualizar tarea:",e),s.oR.error("Error al actualizar el progreso")}},j=(e,r,t)=>a.some(a=>a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo&&a.completado),w=r=>{if(f(r),e&&e.semanas)for(let a of e.semanas)for(let e of a.dias||[]){let t=g(a.fechaInicio,e.dia);if(t&&h(t)===h(r)){let e=y.current[a.numero];e&&e.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"});return}}},N=()=>{if(!m||!e||!e.semanas)return[];let r=[];for(let t of e.semanas)for(let e of t.dias||[]){let o=g(t.fechaInicio,e.dia);if(o&&h(o)===h(m))for(let o of e.tareas||[]){let n=j(o,t.numero,e.dia);r.push({tarea:o,semanaNumero:t.numero,diaNombre:e.dia,completada:n,fechaCompletado:a.find(r=>r.semana_numero===t.numero&&r.dia_nombre===e.dia&&r.tarea_titulo===o.titulo)?.fecha_completado})}}return r},_=e=>{let r=y.current[e.semanaNumero];r&&r.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})},O=(()=>{if(!e||!e.semanas||!Array.isArray(e.semanas))return{completadas:0,total:0,porcentaje:0};let r=e.semanas.reduce((e,r)=>r&&r.dias&&Array.isArray(r.dias)?e+r.dias.reduce((e,r)=>r&&r.tareas&&Array.isArray(r.tareas)?e+r.tareas.length:e,0):e,0),t=a.filter(e=>e.completado).length;return{completadas:t,total:r,porcentaje:r>0?Math.round(t/r*100):0}})();return d?(0,P.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,P.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,P.jsx)("span",{className:"ml-3 text-gray-600",children:"Cargando progreso..."})]}):e?r&&""!==r.trim()?(0,P.jsxs)("div",{className:"space-y-6",children:[(0,P.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,P.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Introducci\xf3n"}),(0,P.jsx)("p",{className:"text-blue-800",children:e.introduccion||"Introducci\xf3n no disponible"})]}),(0,P.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4",children:[(0,P.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,P.jsx)("h3",{className:"font-semibold text-gray-900",children:"Progreso General"}),(0,P.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[O.porcentaje,"%"]})]}),(0,P.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,P.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:`${O.porcentaje}%`}})}),(0,P.jsxs)("p",{className:"text-sm text-gray-600",children:[O.completadas," de ",O.total," tareas completadas"]})]}),e.resumen&&(0,P.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,P.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,P.jsxs)("div",{className:"flex items-center",children:[(0,P.jsx)(n.Ohp,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,P.jsxs)("div",{children:[(0,P.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Total"}),(0,P.jsx)("p",{className:"font-semibold",children:e.resumen.tiempoTotalEstudio||"No disponible"})]})]})}),(0,P.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,P.jsxs)("div",{className:"flex items-center",children:[(0,P.jsx)(n.H9b,{className:"w-5 h-5 text-green-600 mr-2"}),(0,P.jsxs)("div",{children:[(0,P.jsx)("p",{className:"text-sm text-gray-600",children:"Temas"}),(0,P.jsx)("p",{className:"font-semibold",children:e.resumen.numeroTemas||"No disponible"})]})]})}),(0,P.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,P.jsxs)("div",{className:"flex items-center",children:[(0,P.jsx)(n.x_j,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,P.jsxs)("div",{children:[(0,P.jsx)("p",{className:"text-sm text-gray-600",children:"Estudio Nuevo"}),(0,P.jsx)("p",{className:"font-semibold",children:e.resumen.duracionEstudioNuevo||"No disponible"})]})]})}),(0,P.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,P.jsxs)("div",{className:"flex items-center",children:[(0,P.jsx)(n.jTZ,{className:"w-5 h-5 text-orange-600 mr-2"}),(0,P.jsxs)("div",{children:[(0,P.jsx)("p",{className:"text-sm text-gray-600",children:"Repaso Final"}),(0,P.jsx)("p",{className:"font-semibold",children:e.resumen.duracionRepasoFinal||"No disponible"})]})]})})]}),e.semanas&&e.semanas.length>0&&(0,P.jsxs)("div",{className:"space-y-6",children:[(0,P.jsxs)("div",{className:"flex items-center justify-between",children:[(0,P.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,P.jsx)(n.wIk,{className:"w-5 h-5 mr-2"}),"Cronograma Semanal"]}),(0,P.jsxs)("button",{onClick:()=>b(!0),className:"lg:hidden flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",children:[(0,P.jsx)(n.wIk,{className:"w-4 h-4 mr-2"}),"Ver Calendario"]})]}),(0,P.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,P.jsx)("div",{className:"lg:col-span-8 space-y-6",children:e.semanas.map((e,r)=>(0,P.jsxs)("div",{ref:r=>{y.current[e.numero]=r},className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,P.jsxs)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:[(0,P.jsxs)("div",{className:"flex items-center justify-between",children:[(0,P.jsxs)("h4",{className:"text-lg font-semibold text-gray-900",children:["Semana ",e?.numero||"N/A"]}),(0,P.jsxs)("span",{className:"text-sm text-gray-600",children:[e?.fechaInicio||"N/A"," - ",e?.fechaFin||"N/A"]})]}),(0,P.jsx)("p",{className:"text-gray-700 mt-2",children:e?.objetivoPrincipal||"Objetivo no especificado"})]}),(0,P.jsx)("div",{className:"p-6 space-y-4",children:e.dias&&Array.isArray(e.dias)?e.dias.map((r,a)=>(0,P.jsxs)("div",{className:"border border-gray-100 rounded-lg p-4",children:[(0,P.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,P.jsx)("h5",{className:"font-semibold text-gray-900",children:r?.dia||"D\xeda no especificado"}),(0,P.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded",children:[r?.horas||0,"h"]})]}),(0,P.jsx)("div",{className:"space-y-2",children:r.tareas&&Array.isArray(r.tareas)?r.tareas.map((a,t)=>{let o=j(a,e.numero,r.dia);return(0,P.jsxs)("div",{className:`flex items-start p-3 rounded-lg border transition-all cursor-pointer ${o?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-blue-300"}`,onClick:()=>v(a,e.numero,r.dia),children:[(0,P.jsx)("div",{className:`flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ${o?"bg-green-500 border-green-500":"border-gray-300 hover:border-blue-400"}`,children:o&&(0,P.jsx)(n.YrT,{className:"w-3 h-3 text-white"})}),(0,P.jsxs)("div",{className:"flex-1",children:[(0,P.jsx)("h6",{className:`font-medium ${o?"text-green-800 line-through":"text-gray-900"}`,children:a?.titulo||"Tarea sin t\xedtulo"}),a?.descripcion&&(0,P.jsx)("p",{className:`text-sm mt-1 ${o?"text-green-700":"text-gray-600"}`,children:a.descripcion}),(0,P.jsxs)("div",{className:"flex items-center mt-2 space-x-3",children:[(0,P.jsx)("span",{className:`text-xs px-2 py-1 rounded ${a?.tipo==="estudio"?"bg-blue-100 text-blue-800":a?.tipo==="repaso"?"bg-yellow-100 text-yellow-800":a?.tipo==="practica"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:a?.tipo||"general"}),(0,P.jsx)("span",{className:"text-xs text-gray-500",children:a?.duracionEstimada||"No especificado"})]})]})]},t)}):(0,P.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay tareas disponibles"})})]},a)):(0,P.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay d\xedas disponibles"})})]},r))}),(0,P.jsxs)("div",{className:"hidden lg:block lg:col-span-4 space-y-4",children:[(0,P.jsx)(A,{plan:e,progresoPlan:a,fechaSeleccionada:m,onFechaSeleccionada:w,className:"sticky top-4"}),(0,P.jsx)(T,{fecha:m,tareas:N(),onTareaClick:_,className:"sticky top-4"})]})]})]}),(0,P.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,P.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"Estrategia de Repasos"}),(0,P.jsx)("p",{className:"text-yellow-800",children:"string"==typeof e.estrategiaRepasos?e.estrategiaRepasos:e.estrategiaRepasos&&"object"==typeof e.estrategiaRepasos&&e.estrategiaRepasos.descripcion||"Estrategia de repasos no disponible"})]}),(0,P.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,P.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"Pr\xf3ximos Pasos y Consejos"}),(0,P.jsx)("p",{className:"text-purple-800",children:"string"==typeof e.proximosPasos?e.proximosPasos:e.proximosPasos&&"object"==typeof e.proximosPasos&&e.proximosPasos.descripcion||"Pr\xf3ximos pasos no disponibles"})]}),(0,P.jsx)(C,{isOpen:p,onClose:()=>b(!1),plan:e,progresoPlan:a,fechaSeleccionada:m,onFechaSeleccionada:w,tareasDelDia:N(),onTareaClick:_})]}):(0,P.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,P.jsx)("div",{className:"text-center",children:(0,P.jsx)("p",{className:"text-gray-600",children:"ID de temario no v\xe1lido"})})}):(0,P.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,P.jsx)("div",{className:"text-center",children:(0,P.jsx)("p",{className:"text-gray-600",children:"No se pudo cargar el plan de estudios"})})})}},25580:(e,r,a)=>{a.d(r,{Pk:()=>c,u9:()=>l,vD:()=>s});var t=a(48921),o=a(11820);function n(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function i(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?n(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}async function s(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return console.log("No hay usuario autenticado o error:",a),!1;let{data:n,error:i}=await t.N.from("temarios").select("id").eq("id",e).eq("user_id",r.id).single();if(i){if("PGRST116"===i.code)return!1;return console.error("Error al verificar temario:",i),!1}if(!n)return!1;let{data:s,error:l}=await t.N.from("planificacion_usuario").select("id").eq("user_id",r.id).eq("temario_id",e).eq("completado",!0).limit(1);if(l)return console.error("Error al verificar planificaci\xf3n:",l),!1;return s&&s.length>0}catch(e){return console.error("Error al verificar planificaci\xf3n:",e),!1}}async function l(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return null;let{data:n,error:i}=await t.N.from("planificacion_usuario").select("*").eq("user_id",r.id).eq("temario_id",e).single();if(i){if("PGRST116"===i.code)return null;return console.error("Error al obtener planificaci\xf3n:",i),null}return n}catch(e){return console.error("Error al obtener planificaci\xf3n:",e),null}}async function c(e,r){try{let{user:a,error:n}=await (0,o.iF)();if(!a||n)return console.error("No hay usuario autenticado"),null;let s=await l(e);if(s){let{data:e,error:a}=await t.N.from("planificacion_usuario").update(i(i({},r),{},{completado:!0,actualizado_en:new Date().toISOString()})).eq("id",s.id).select().single();if(a)return console.error("Error al actualizar planificaci\xf3n:",a),null;return e.id}{let{data:o,error:n}=await t.N.from("planificacion_usuario").insert([i(i({user_id:a.id,temario_id:e},r),{},{completado:!0})]).select().single();if(n)return console.error("Error al crear planificaci\xf3n:",n),null;return o.id}}catch(e){return console.error("Error al guardar planificaci\xf3n:",e),null}}},26564:(e,r,a)=>{a.d(r,{Iv:()=>h,Og:()=>u,Q1:()=>c,QU:()=>y,_W:()=>p,_p:()=>f,as:()=>x,kO:()=>m,oE:()=>l,qJ:()=>s,xq:()=>b,yK:()=>d,yf:()=>g});var t=a(72267),o=a(99409);function n(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function i(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?n(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}async function s(e,r){try{let{user:a}=await (0,o.iF)();if(!a)return console.error("No hay usuario autenticado"),null;let{data:n,error:i}=await t.N.from("colecciones_flashcards").insert([{titulo:e,descripcion:r,user_id:a.id}]).select();if(i)return console.error("Error al crear colecci\xf3n de flashcards:",i),null;return n?.[0]?.id||null}catch(e){return console.error("Error al crear colecci\xf3n de flashcards:",e),null}}async function l(){try{let{user:e,error:r}=await (0,o.iF)();if(r)return console.error("Error al obtener usuario:",r),[];if(!e)return console.error("No hay usuario autenticado"),[];let{data:a,error:n}=await t.N.from("colecciones_flashcards").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(n)return console.error("Error al obtener colecciones de flashcards:",n),[];if(!a||0===a.length)return[];return await Promise.all(a.map(async e=>{try{let{data:r,error:a}=await t.N.from("flashcards").select("id").eq("coleccion_id",e.id);if(a)return console.error("Error al contar flashcards para colecci\xf3n",e.id,":",a),i(i({},e),{},{numero_flashcards:0,pendientes_hoy:0});let{data:o,error:n}=await t.N.from("flashcards").select(`
              id,
              progreso_flashcards!inner(
                proxima_revision,
                estado
              )
            `).eq("coleccion_id",e.id).lte("progreso_flashcards.proxima_revision",new Date().toISOString()),s=n?0:o?.length||0;return i(i({},e),{},{numero_flashcards:r?.length||0,pendientes_hoy:s})}catch(r){return console.error("Error al procesar colecci\xf3n",e.id,":",r),i(i({},e),{},{numero_flashcards:0,pendientes_hoy:0})}}))}catch(e){return console.error("Error general al obtener colecciones de flashcards:",e),[]}}async function c(e){let{data:r,error:a}=await t.N.from("flashcards").select("*").eq("coleccion_id",e).order("creado_en",{ascending:!0});return a?(console.error("Error al obtener flashcards:",a),[]):r||[]}async function d(e){let{data:r,error:a}=await t.N.from("flashcards").insert(e).select();return a?(console.error("Error al guardar flashcards:",a),null):r?.map(e=>e.id)||null}async function u(e){let r=await c(e),{data:a,error:o}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",r.map(e=>e.id));if(o)return console.error("Error al obtener progreso de flashcards:",o),[];let n=new Date,s=new Date(n.getFullYear(),n.getMonth(),n.getDate());return r.map(e=>{let r=a?.find(r=>r.flashcard_id===e.id);if(!r)return i(i({},e),{},{debeEstudiar:!0});let t=new Date(r.proxima_revision),o=new Date(t.getFullYear(),t.getMonth(),t.getDate());return i(i({},e),{},{debeEstudiar:o<=s,progreso:{factor_facilidad:r.factor_facilidad,intervalo:r.intervalo,repeticiones:r.repeticiones,estado:r.estado,proxima_revision:r.proxima_revision}})})}async function m(e,r=10){try{let a=await u(e),o=a.map(e=>e.id),{data:n,error:s}=await t.N.from("historial_revisiones").select("flashcard_id, dificultad").in("flashcard_id",o);if(s)return console.error("Error al obtener historial de revisiones:",s),a.slice(0,r);let l=new Map;return n?.forEach(e=>{let r=l.get(e.flashcard_id)||{dificil:0,total:0};r.total++,"dificil"===e.dificultad&&r.dificil++,l.set(e.flashcard_id,r)}),a.map(e=>{let r=l.get(e.id),a=r?r.dificil/r.total:0;return i(i({},e),{},{ratioDificultad:a})}).sort((e,r)=>r.ratioDificultad-e.ratioDificultad).slice(0,r)}catch(e){return console.error("Error al obtener flashcards m\xe1s dif\xedciles:",e),[]}}async function f(e,r=10){try{return[...await u(e)].sort(()=>Math.random()-.5).slice(0,r)}catch(e){return console.error("Error al obtener flashcards aleatorias:",e),[]}}async function p(e,r=10){try{let a=await u(e),o=a.map(e=>e.id),{data:n,error:s}=await t.N.from("historial_revisiones").select("flashcard_id, fecha").in("flashcard_id",o).order("fecha",{ascending:!1});if(s)return console.error("Error al obtener \xfaltimas revisiones:",s),a.slice(0,r);let l=new Map;return n?.forEach(e=>{l.has(e.flashcard_id)||l.set(e.flashcard_id,e.fecha)}),a.map(e=>{let r=l.get(e.id);return i(i({},e),{},{ultimaRevision:new Date(r||0)})}).sort((e,r)=>e.ultimaRevision.getTime()-r.ultimaRevision.getTime()).slice(0,r)}catch(e){return console.error("Error al obtener flashcards no recientes:",e),[]}}async function h(e,r,a=10){try{return(await u(e)).filter(e=>e.progreso?e.progreso.estado===r:"nuevo"===r).slice(0,a)}catch(e){return console.error("Error al obtener flashcards por estado:",e),[]}}async function g(e,r){try{let a=2.5,o=1,n=0,i="nuevo",s=!1,{data:l,error:c}=await t.N.from("progreso_flashcards").select("factor_facilidad, intervalo, repeticiones, estado").eq("flashcard_id",e).single();!c&&l&&(a=l.factor_facilidad||2.5,o=l.intervalo||1,n=l.repeticiones||0,i=l.estado||"nuevo",s=!0);let d=a,u=o,m=n,f=i;"dificil"===r?(d=Math.max(1.3,a-.3),m=0,u=1,f="aprendiendo"):(m++,"normal"===r?d=a-.15:"facil"===r&&(d=a+.1),d=Math.max(1.3,Math.min(2.5,d)),1===m?(u=1,f="aprendiendo"):2===m?(u=6,f="repasando"):f=(u=Math.round(o*d))>30?"aprendido":"repasando");let p=new Date,h=new Date(p);h.setDate(h.getDate()+u);let g=null;if(s){let{error:r}=await t.N.from("progreso_flashcards").update({factor_facilidad:d,intervalo:u,repeticiones:m,estado:f,ultima_revision:p.toISOString(),proxima_revision:h.toISOString()}).eq("flashcard_id",e);g=r}else{let{error:r}=await t.N.from("progreso_flashcards").insert({flashcard_id:e,factor_facilidad:d,intervalo:u,repeticiones:m,estado:f,ultima_revision:p.toISOString(),proxima_revision:h.toISOString()});g=r}if(g)return console.error("Error al guardar progreso:",g),!1;let{error:b}=await t.N.from("historial_revisiones").insert({flashcard_id:e,dificultad:r,factor_facilidad:d,intervalo:u,repeticiones:m,fecha:p.toISOString()});return!0}catch(e){return!1}}async function b(e,r,a){try{let{error:o}=await t.N.from("flashcards").update({pregunta:r,respuesta:a,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return!1;return!0}catch(e){return!1}}async function y(e){try{let{error:r}=await t.N.from("progreso_flashcards").delete().eq("flashcard_id",e);if(r)return!1;let{error:a}=await t.N.from("historial_revisiones").delete().eq("flashcard_id",e);if(a)return!1;let{error:o,count:n}=await t.N.from("flashcards").delete({count:"exact"}).eq("id",e);if(o||0===n)return!1;return!0}catch(e){return!1}}async function x(e){try{let{user:r}=await (0,o.iF)();if(!r)return!1;let{data:a,error:n}=await t.N.from("flashcards").select("id").eq("coleccion_id",e);if(n)return!1;let i=a?.map(e=>e.id)||[];if(i.length>0){let{error:r}=await t.N.from("progreso_flashcards").delete().in("flashcard_id",i);if(r)return!1;let{error:a}=await t.N.from("historial_revisiones").delete().in("flashcard_id",i);if(a)return!1;let{error:o}=await t.N.from("flashcards").delete().eq("coleccion_id",e);if(o)return!1}let{error:s,count:l}=await t.N.from("colecciones_flashcards").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(s||0===l)return!1;return!0}catch(e){return!1}}},29399:(e,r,a)=>{a.d(r,{A:()=>d});var t=a(96554),o=a(93823),n=a(47463),i=a(99631);function s(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function l(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?s(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}let c=(0,t.forwardRef)(({onSelectionChange:e},r)=>{let{0:a,1:s}=(0,t.useState)([]),{0:c,1:d}=(0,t.useState)([]),{0:u,1:m}=(0,t.useState)(!0),f=async()=>{m(!0);try{let e=await (0,n.R1)();s(e)}catch(e){console.error("Error al cargar documentos:",e)}finally{m(!1)}};(0,t.useEffect)(()=>{f()},[]),(0,t.useImperativeHandle)(r,()=>({recargarDocumentos:f}));let p=a.map(e=>({value:e.id,label:`${e.numero_tema?`Tema ${e.numero_tema}: `:""}${e.titulo} ${e.categoria?`(${e.categoria})`:""}`}));return(0,i.jsxs)("div",{className:"mb-3",children:[(0,i.jsx)("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Selecciona los documentos para consultar:"}),(0,i.jsx)(o.Ay,{instanceId:"document-selector",isMulti:!0,isLoading:u,options:p,className:"basic-multi-select",classNamePrefix:"select",placeholder:"Selecciona uno o m\xe1s documentos...",noOptionsMessage:()=>"No hay documentos disponibles",onChange:r=>{d(r||[]),e(r.map(e=>a.find(r=>r.id===e.value)).filter(Boolean))},value:c,styles:{control:e=>l(l({},e),{},{minHeight:"36px",fontSize:"14px"}),multiValue:e=>l(l({},e),{},{fontSize:"12px"}),placeholder:e=>l(l({},e),{},{fontSize:"14px"})}}),0===c.length&&(0,i.jsx)("p",{className:"text-red-500 text-xs italic mt-0.5",children:"Debes seleccionar al menos un documento"})]})});c.displayName="DocumentSelector";let d=c},47463:(e,r,a)=>{a.d(r,{vW:()=>d.vW,fW:()=>d.fW,xq:()=>u.xq,qJ:()=>u.qJ,Yp:()=>d.Yp,_4:()=>f._4,CM:()=>d.CM,sq:()=>d.sq,Q3:()=>c,hE:()=>l,yK:()=>u.yK,QE:()=>d.QE,OA:()=>f.OA,oE:()=>u.oE,Sl:()=>d.Sl,sj:()=>d.sj,R1:()=>s,yV:()=>m.yV,wU:()=>m.wU,oC:()=>f.oC,dd:()=>f.dd,C9:()=>d.C9,hg:()=>f.hg,Kj:()=>f.Kj,Lx:()=>f.Lx,Gl:()=>f.Gl});var t=a(72267),o=a(99409);function n(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function i(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?n(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}async function s(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:a}=await t.N.from("documentos").select("*").eq("user_id",e.id).order("numero_tema",{ascending:!0});if(a)return console.error("Error al obtener documentos:",a),[];return r||[]}catch(e){return console.error("Error al obtener documentos:",e),[]}}async function l(e){try{let{user:r}=await (0,o.iF)();if(!r)return console.error("No hay usuario autenticado"),null;let a=i(i({},e),{},{user_id:r.id,tipo_original:e.tipo_original}),{data:n,error:s}=await t.N.from("documentos").insert([a]).select();if(s)return console.error("Error al guardar documento:",s),null;return n?.[0]?.id||null}catch(e){return console.error("Error al guardar documento:",e),null}}async function c(e){try{console.log("\uD83D\uDDD1️ Iniciando eliminaci\xf3n de documento:",e);let{user:r}=await (0,o.iF)();if(!r)return console.error("❌ No hay usuario autenticado para eliminar documento"),!1;console.log("\uD83D\uDC64 Usuario autenticado:",r.id),console.log("\uD83D\uDCC4 Eliminando documento ID:",e);let{error:a,count:n}=await t.N.from("documentos").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(a)return console.error("❌ Error al eliminar documento de Supabase:",a),!1;if(console.log("✅ Documento eliminado exitosamente. Filas afectadas:",n),0===n)return console.warn("⚠️ No se elimin\xf3 ning\xfan documento. Posibles causas: documento no existe o no pertenece al usuario"),!1;return!0}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al eliminar documento:",e),!1}}var d=a(53867),u=a(26564),m=a(55622),f=a(7950);a(5089)},53766:(e,r,a)=>{a.d(r,{B$:()=>p,Il:()=>g,Se:()=>m,cN:()=>h,cm:()=>d,jg:()=>l,oS:()=>f,r5:()=>c,sW:()=>u,xv:()=>b,yr:()=>s});var t=a(48921),o=a(11820);function n(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function i(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?n(Object(a),!0).forEach(function(r){var t,o,n;t=e,o=r,n=a[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}async function s(){try{let{user:e,error:r}=await (0,o.iF)();if(!e||r)return!1;let{data:a,error:n}=await t.N.from("temarios").select("id").eq("user_id",e.id).limit(1);if(n){if("PGRST116"===n.code||n.message?.includes("relation")||n.message?.includes("does not exist"))return!1;return console.error("Error al verificar temario en Supabase:",n),!1}return a&&a.length>0}catch(e){return console.error("Error general al verificar temario:",e),!1}}async function l(){try{let{user:e,error:r}=await (0,o.iF)();if(!e||r)return null;let{data:a,error:n}=await t.N.from("temarios").select("*").eq("user_id",e.id).single();if(n){if("PGRST116"===n.code)return null;return console.error("Error al obtener temario en Supabase:",n),null}return a}catch(e){return console.error("Error general al obtener temario:",e),null}}async function c(e,r,a){try{let{user:n,error:i}=await (0,o.iF)();if(!n||i)return console.error("No hay usuario autenticado o error:",i),null;let{data:s,error:l}=await t.N.from("temarios").insert([{titulo:e,descripcion:r,tipo:a,user_id:n.id}]).select().single();if(l)return console.error("Error al crear temario:",l),null;return s.id}catch(e){return console.error("Error al crear temario:",e),null}}async function d(e){try{let{data:r,error:a}=await t.N.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});if(a)return console.error("Error al obtener temas:",a),[];return r||[]}catch(e){return console.error("Error al obtener temas:",e),[]}}async function u(e,r){try{let a=r.map(r=>i(i({},r),{},{temario_id:e})),{error:o}=await t.N.from("temas").insert(a);if(o)return console.error("Error al crear temas:",o),!1;return!0}catch(e){return console.error("Error al crear temas:",e),!1}}async function m(e,r,a){try{let{error:o}=await t.N.from("temarios").update({titulo:r,descripcion:a,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar temario:",o),!1;return!0}catch(e){return console.error("Error al actualizar temario:",e),!1}}async function f(e,r,a){try{let{error:o}=await t.N.from("temas").update({titulo:r,descripcion:a,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar tema:",e),!1}}async function p(e){try{let{error:r}=await t.N.from("temas").delete().eq("id",e);if(r)return console.error("Error al eliminar tema:",r),!1;return!0}catch(e){return console.error("Error al eliminar tema:",e),!1}}async function h(e,r){try{let a={completado:r,actualizado_en:new Date().toISOString()};r?a.fecha_completado=new Date().toISOString():a.fecha_completado=null;let{error:o}=await t.N.from("temas").update(a).eq("id",e);if(o)return console.error("Error al actualizar estado del tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar estado del tema:",e),!1}}async function g(e){try{let{data:r,error:a}=await t.N.from("temas").select("completado").eq("temario_id",e);if(a)return console.error("Error al obtener estad\xedsticas del temario:",a),null;let o=r.length,n=r.filter(e=>e.completado).length;return{totalTemas:o,temasCompletados:n,porcentajeCompletado:o>0?n/o*100:0}}catch(e){return console.error("Error al obtener estad\xedsticas del temario:",e),null}}async function b(e){try{let{error:r}=await t.N.from("temarios").delete().eq("id",e);if(r)return console.error("Error al eliminar temario:",r),!1;return!0}catch(e){return console.error("Error al eliminar temario:",e),!1}}},53867:(e,r,a)=>{a.d(r,{C9:()=>u,CM:()=>l,QE:()=>d,Sl:()=>c,Yp:()=>o,fW:()=>i,sj:()=>n,sq:()=>m,vW:()=>s});var t=a(72267);async function o(e,r=!1){try{let{data:{user:a}}=await t.N.auth.getUser();if(!a)return console.error("No hay usuario autenticado para crear conversaci\xf3n"),null;r&&await l();let{data:o,error:n}=await t.N.from("conversaciones").insert([{titulo:e,activa:r,user_id:a.id}]).select();if(n)return console.error("Error al crear conversaci\xf3n:",n),null;return o?.[0]?.id||null}catch(e){return console.error("Error inesperado al crear conversaci\xf3n:",e),null}}async function n(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para obtener conversaciones"),[];let{data:r,error:a}=await t.N.from("conversaciones").select("*").eq("user_id",e.id).order("actualizado_en",{ascending:!1});if(a)return console.error("Error al obtener conversaciones:",a),[];return r||[]}catch(e){return console.error("Error inesperado al obtener conversaciones:",e),[]}}async function i(e,r){let{error:a}=await t.N.from("conversaciones").update({titulo:r,actualizado_en:new Date().toISOString()}).eq("id",e);return!a||(console.error("Error al actualizar conversaci\xf3n:",a),!1)}async function s(e){try{await l();let{error:r}=await t.N.from("conversaciones").update({activa:!0,actualizado_en:new Date().toISOString()}).eq("id",e);if(r)return console.error("Error al activar conversaci\xf3n:",r),!1;return!0}catch(e){return console.error("Error inesperado al activar conversaci\xf3n:",e),!1}}async function l(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para desactivar conversaciones"),!1;let{error:r}=await t.N.from("conversaciones").update({activa:!1}).eq("user_id",e.id).eq("activa",!0);if(r)return console.error("Error al desactivar todas las conversaciones:",r),!1;return!0}catch(e){return console.error("Error inesperado al desactivar conversaciones:",e),!1}}async function c(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.warn("No hay usuario autenticado para obtener conversaci\xf3n activa"),null;let{data:r,error:a}=await t.N.from("conversaciones").select("*").eq("user_id",e.id);a&&console.error("Error al obtener todas las conversaciones:",a);let{data:o,error:n}=await t.N.from("conversaciones").select("*").eq("user_id",e.id).eq("activa",!0).limit(1);if(n){if("406"===n.code||n.message.includes("406"))return null;return console.error("Error al obtener conversaci\xf3n activa:",n),null}return o&&o.length>0?o[0]:null}catch(e){return console.error("Error inesperado al obtener conversaci\xf3n activa:",e),null}}async function d(e){try{let{data:r,error:a}=await t.N.from("conversaciones").select("id").eq("id",e.conversacion_id).single();if(a)return console.error("Error al verificar la conversaci\xf3n:",a),null;let{data:o,error:n}=await t.N.from("mensajes").insert([e]).select();if(n)return console.error("Error al guardar mensaje:",n),null;let{error:i}=await t.N.from("conversaciones").update({actualizado_en:new Date().toISOString()}).eq("id",e.conversacion_id);return i&&console.error("Error al actualizar la fecha de la conversaci\xf3n:",i),o?.[0]?.id||null}catch(e){return console.error("Error inesperado al guardar mensaje:",e),null}}async function u(e){let{data:r,error:a}=await t.N.from("mensajes").select("*").eq("conversacion_id",e).order("timestamp",{ascending:!0});return a?(console.error("Error al obtener mensajes:",a),[]):r||[]}async function m(e){try{let{data:{user:r}}=await t.N.auth.getUser();if(!r)return console.error("No hay usuario autenticado para eliminar conversaci\xf3n"),!1;let{error:a}=await t.N.from("mensajes").delete().eq("conversacion_id",e);if(a)return console.error("Error al eliminar mensajes de la conversaci\xf3n:",a),!1;let{error:o,count:n}=await t.N.from("conversaciones").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(o)return console.error("Error al eliminar conversaci\xf3n:",o),!1;if(0===n)return!1;return!0}catch(e){return console.error("Error inesperado al eliminar conversaci\xf3n:",e),!1}}},55622:(e,r,a)=>{a.d(r,{wU:()=>i,yV:()=>n});var t=a(72267),o=a(26564);async function n(e){let r=await (0,o.Q1)(e);if(0===r.length)return{total:0,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0};let{data:a,error:n}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",r.map(e=>e.id));if(n)return console.error("Error al obtener progreso de flashcards:",n),{total:r.length,nuevas:r.length,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:r.length};let i={total:r.length,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0},s=new Date;return r.forEach(e=>{let r=a?.find(r=>r.flashcard_id===e.id);if(r){switch(r.estado){case"nuevo":i.nuevas++;break;case"aprendiendo":i.aprendiendo++;break;case"repasando":i.repasando++;break;case"aprendido":i.aprendidas++}let e=new Date(r.proxima_revision);new Date(e.getFullYear(),e.getMonth(),e.getDate())<=new Date(s.getFullYear(),s.getMonth(),s.getDate())&&i.paraHoy++}else i.nuevas++,i.paraHoy++}),i}async function i(e){try{let r=await (0,o.Q1)(e);if(0===r.length)return{totalSesiones:0,totalRevisiones:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};let a=r.map(e=>e.id),{data:n,error:i}=await t.N.from("historial_revisiones").select("*").in("flashcard_id",a).order("fecha",{ascending:!0});if(i)return console.error("Error al obtener revisiones:",i),null;let{data:s,error:l}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",a);l&&console.error("Error al obtener progreso:",l);let c={totalSesiones:0,totalRevisiones:n?n.length:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};if(n&&n.length>0){n.forEach(e=>{"dificil"===e.dificultad?c.distribucionDificultad.dificil++:"normal"===e.dificultad?c.distribucionDificultad.normal++:"facil"===e.dificultad&&c.distribucionDificultad.facil++});let e=new Set;n.forEach(r=>{let a=new Date(r.fecha).toISOString().split("T")[0];e.add(a)}),c.totalSesiones=e.size;let a=new Map;r.forEach(e=>{a.set(e.id,{dificil:0,normal:0,facil:0,total:0})}),n.forEach(e=>{let r=a.get(e.flashcard_id);r&&("dificil"===e.dificultad?r.dificil++:"normal"===e.dificultad?r.normal++:"facil"===e.dificultad&&r.facil++,r.total++)}),c.tarjetasMasDificiles=r.map(e=>{let r=a.get(e.id)||{dificil:0,normal:0,facil:0,total:0};return{id:e.id,pregunta:e.pregunta,dificil:r.dificil,normal:r.normal,facil:r.facil,totalRevisiones:r.total}}).filter(e=>e.totalRevisiones>0).sort((e,r)=>{let a=e.totalRevisiones>0?e.dificil/e.totalRevisiones:0;return(r.totalRevisiones>0?r.dificil/r.totalRevisiones:0)-a}).slice(0,10)}return c}catch(e){return console.error("Error al calcular estad\xedsticas detalladas:",e),null}}},68941:(e,r,a)=>{a.d(r,{A:()=>s}),a(96554);var t=a(97334),o=a.n(t),n=a(81815),i=a(99631);function s({feature:e,featureDescription:r,benefits:a=[],className:t=""}){let s=a.length>0?a:["Acceso ilimitado a todas las funcionalidades","Generaci\xf3n de contenido sin l\xedmites","Soporte prioritario","Nuevas funcionalidades en primicia"],l={ai_tutor_chat:{name:"Chat con IA",description:"Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas."},study_planning:{name:"Planificaci\xf3n de Estudios",description:"Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos."},summary_a1_a2:{name:"Res\xfamenes Avanzados",description:"Genera res\xfamenes inteligentes y estructurados de tus documentos de estudio."}}[e]||{name:e,description:r||"Esta funcionalidad avanzada te ayudar\xe1 a mejorar tu preparaci\xf3n."};return(0,i.jsx)("div",{className:`min-h-screen bg-gray-50 flex items-center justify-center p-4 ${t}`,children:(0,i.jsx)("div",{className:"max-w-2xl w-full",children:(0,i.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)(n.F5$,{className:"w-10 h-10"})}),(0,i.jsx)("h1",{className:"text-3xl font-bold mb-4",children:l.name}),(0,i.jsx)("p",{className:"text-blue-100 text-lg leading-relaxed",children:l.description})]}),(0,i.jsxs)("div",{className:"px-8 py-8",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6",children:[(0,i.jsx)(n.usP,{className:"w-4 h-4 mr-2"}),"Funcionalidad Premium"]}),(0,i.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Actualiza tu plan para acceder"}),(0,i.jsx)("p",{className:"text-gray-600 text-lg",children:"Esta funcionalidad est\xe1 disponible para usuarios con planes de pago. Actualiza ahora y desbloquea todo el potencial de OposiAI."})]}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"\xbfQu\xe9 obtienes al actualizar?"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map((e,r)=>(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,i.jsx)(n.YrT,{className:"w-4 h-4 text-green-600"})}),(0,i.jsx)("span",{className:"text-gray-700",children:e})]},r))})]}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsxs)(o(),{href:"/upgrade-plan",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,i.jsx)(n.ei4,{className:"w-5 h-5 mr-2"}),"Actualizar Plan"]}),(0,i.jsx)(o(),{href:"/app",className:"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors",children:"Volver al Dashboard"})]}),(0,i.jsx)("div",{className:"mt-8 text-center",children:(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfTienes preguntas? ",(0,i.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Cont\xe1ctanos"})]})})]})]})})})}},91212:(e,r,a)=>{a.d(r,{IE:()=>o,qk:()=>i,qo:()=>t});let t={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function o(e){return t[e]||null}function n(e,r){let a=o(e);return!(!a||a.restrictedFeatures.includes(r))&&a.features.includes(r)}async function i(e){try{let r=await fetch("/api/user/plan");if(!r.ok)return console.error("Error obteniendo plan del usuario"),n("free",e);let{plan:a}=await r.json();return n(a||"free",e)}catch(r){return console.error("Error verificando acceso a caracter\xedstica:",r),n("free",e)}}}};
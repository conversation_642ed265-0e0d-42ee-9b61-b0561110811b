"use strict";exports.id=3760,exports.ids=[3760],exports.modules={80533:(e,t)=>{t.qg=function(e,t){let r=new s,n=e.length;if(n<2)return r;let o=t?.decode||u,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let a=e.indexOf(";",i),s=-1===a?n:a;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let u=l(e,i,t),f=c(e,t,u),p=e.slice(u,f);if(void 0===r[p]){let n=l(e,t+1,s),i=c(e,s,n),a=o(e.slice(n,i));r[p]=a}i=s+1}while(i<n);return r},t.lK=function(e,t,s){let l=s?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let c=l(t);if(!n.test(c))throw TypeError(`argument val is invalid: ${t}`);let u=e+"="+c;if(!s)return u;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw TypeError(`option maxAge is invalid: ${s.maxAge}`);u+="; Max-Age="+s.maxAge}if(s.domain){if(!o.test(s.domain))throw TypeError(`option domain is invalid: ${s.domain}`);u+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError(`option path is invalid: ${s.path}`);u+="; Path="+s.path}if(s.expires){var f;if(f=s.expires,"[object Date]"!==a.call(f)||!Number.isFinite(s.expires.valueOf()))throw TypeError(`option expires is invalid: ${s.expires}`);u+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${s.priority}`)}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${s.sameSite}`)}return u};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,n=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,i=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,s=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},83760:(e,t,r)=>{let n;r.d(t,{createBrowserClient:()=>S,createServerClient:()=>P});var o=r(41370);let i="0.6.1";var a=r(80533);let s={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},l=/^(.*)[.](0|[1-9][0-9]*)$/;function c(e,t){if(e===t)return!0;let r=e.match(l);return!!r&&r[1]===t}function u(e,t,r){let n=r??3180,o=encodeURIComponent(t);if(o.length<=n)return[{name:e,value:t}];let i=[];for(;o.length>0;){let e=o.slice(0,n),t=e.lastIndexOf("%");t>n-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}i.push(r),o=o.slice(e.length)}return i.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function f(e,t){let r=await t(e);if(r)return r;let n=[];for(let r=0;;r++){let o=`${e}.${r}`,i=await t(o);if(!i)break;n.push(i)}return n.length>0?n.join(""):null}let p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),d=" 	\n\r=".split(""),h=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<d.length;t+=1)e[d[t].charCodeAt(0)]=-2;for(let t=0;t<p.length;t+=1)e[p[t].charCodeAt(0)]=t;return e})();function g(e){let t=[],r=0,n=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){let t=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(n,t)}}(e,e=>{for(r=r<<8|e,n+=8;n>=6;){let e=r>>n-6&63;t.push(p[e]),n-=6}}),n>0)for(r<<=6-n,n=6;n>=6;){let e=r>>n-6&63;t.push(p[e]),n-=6}return t.join("")}function m(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},o=0,i=0;for(let t=0;t<e.length;t+=1){let a=h[e.charCodeAt(t)];if(a>-1)for(o=o<<6|a,i+=6;i>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(o>>i-8&255,n,r),i-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let v="base64-";function w(e,t){let r,n,o=e.cookies??null,i=e.cookieEncoding,l={},p={};if(o)if("get"in o){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let n=await o.get(t[e]);(n||"string"==typeof n)&&r.push({name:t[e],value:n})}return r};if(r=async t=>await e(t),"set"in o&&"remove"in o)n=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:n,options:i}=e[t];n?await o.set(r,n,i):await o.remove(r,i)}};else if(t)n=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in o)if(r=async()=>await o.getAll(),"setAll"in o)n=o.setAll;else if(t)n=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).`);else if(t||1)if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],n=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};else{let e=()=>{let e=(0,a.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),n=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,a.lK)(e,t,r)})}}return t?{getAll:r,setAll:n,setItems:l,removedItems:p,storage:{isServer:!0,getItem:async e=>{if("string"==typeof l[e])return l[e];if(p[e])return null;let t=await r([e]),n=await f(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!n)return null;let o=n;return"string"==typeof n&&n.startsWith(v)&&(o=m(n.substring(v.length))),o},setItem:async(t,o)=>{t.endsWith("-code-verifier")&&await O({getAll:r,setAll:n,setItems:{[t]:o},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:i}),l[t]=o,delete p[t]},removeItem:async e=>{delete l[e],p[e]=!0}}}:{getAll:r,setAll:n,setItems:l,removedItems:p,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),n=await f(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!n)return null;let o=n;return n.startsWith(v)&&(o=m(n.substring(v.length))),o},setItem:async(t,o)=>{let a=await r([t]),l=new Set((a?.map(({name:e})=>e)||[]).filter(e=>c(e,t))),f=o;"base64url"===i&&(f=v+g(o));let p=u(t,f);p.forEach(({name:e})=>{l.delete(e)});let d=y(y(y({},s),e?.cookieOptions),{},{maxAge:0}),h=y(y(y({},s),e?.cookieOptions),{},{maxAge:s.maxAge});delete d.name,delete h.name;let m=[...[...l].map(e=>({name:e,value:"",options:d})),...p.map(({name:e,value:t})=>({name:e,value:t,options:h}))];m.length>0&&await n(m)},removeItem:async t=>{let o=await r([t]),i=(o?.map(({name:e})=>e)||[]).filter(e=>c(e,t)),a=y(y(y({},s),e?.cookieOptions),{},{maxAge:0});delete a.name,i.length>0&&await n(i.map(e=>({name:e,value:"",options:a})))}}}}async function O({getAll:e,setAll:t,setItems:r,removedItems:n},o){let i=o.cookieEncoding,a=o.cookieOptions??null,l=await e([...r?Object.keys(r):[],...n?Object.keys(n):[]]),f=l?.map(({name:e})=>e)||[],p=Object.keys(n).flatMap(e=>f.filter(t=>c(t,e))),d=Object.keys(r).flatMap(e=>{let t=new Set(f.filter(t=>c(t,e))),n=r[e];"base64url"===i&&(n=v+g(n));let o=u(e,n);return o.forEach(e=>{t.delete(e.name)}),p.push(...t),o}),h=y(y(y({},s),a),{},{maxAge:0}),m=y(y(y({},s),a),{},{maxAge:s.maxAge});delete h.name,delete m.name,await t([...p.map(e=>({name:e,value:"",options:h})),...d.map(({name:e,value:t})=>({name:e,value:t,options:m}))])}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function S(e,t,r){let a=r?.isSingleton===!0;if(a&&n)return n;if(!e||!t)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:s}=w(j(j({},r),{},{cookieEncoding:r?.cookieEncoding??"base64url"}),!1),l=(0,o.UU)(e,t,j(j({},r),{},{global:j(j({},r?.global),{},{headers:j(j({},r?.global?.headers),{},{"X-Client-Info":`supabase-ssr/${i} createBrowserClient`})}),auth:j(j(j({},r?.auth),r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null),{},{flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:s})}));return a&&(n=l),l}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function P(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:n,getAll:a,setAll:s,setItems:l,removedItems:c}=w(A(A({},r),{},{cookieEncoding:r?.cookieEncoding??"base64url"}),!0),u=(0,o.UU)(e,t,A(A({},r),{},{global:A(A({},r?.global),{},{headers:A(A({},r?.global?.headers),{},{"X-Client-Info":`supabase-ssr/${i} createServerClient`})}),auth:A(A(A({},r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null),r?.auth),{},{flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:n})}));return u.auth.onAuthStateChange(async e=>{(Object.keys(l).length>0||Object.keys(c).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await O({getAll:a,setAll:s,setItems:l,removedItems:c},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),u}}};
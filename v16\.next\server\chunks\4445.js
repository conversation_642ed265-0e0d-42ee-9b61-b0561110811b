exports.id=4445,exports.ids=[4445],exports.modules={5322:(e,t,s)=>{Promise.resolve().then(s.bind(s,36145))},15427:(e,t,s)=>{"use strict";s.d(t,{M:()=>c,W:()=>u});var r=s(96554),n=s(29959),a=s(99631);function i(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?i(Object(s),!0).forEach(function(t){var r,n,a;r=e,n=t,a=s[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in r?Object.defineProperty(r,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):i(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}let l=(0,r.createContext)(void 0),u=({children:e})=>{let{0:t,1:s}=(0,r.useState)([]),i=(0,r.useCallback)(e=>{let t=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,r=o(o({},e),{},{id:t,status:"pending",createdAt:new Date});return s(e=>[...e,r]),n.oR.loading(`Iniciando: ${r.title}`,{id:`task_start_${t}`,duration:2e3}),t},[]),u=(0,r.useCallback)((e,t)=>{s(s=>s.map(s=>{if(s.id===e){let r=o(o({},s),t);return"processing"===t.status&&"pending"===s.status?(n.oR.dismiss(`task_start_${e}`),n.oR.loading(`Procesando: ${s.title}`,{id:`task_processing_${e}`})):"completed"===t.status&&"completed"!==s.status?(n.oR.dismiss(`task_processing_${e}`),n.oR.success(`Completado: ${s.title}`,{id:`task_completed_${e}`,duration:4e3}),r.completedAt=new Date):"error"===t.status&&"error"!==s.status&&(n.oR.dismiss(`task_processing_${e}`),n.oR.error(`Error: ${s.title}`,{id:`task_error_${e}`,duration:5e3})),r}return s}))},[]),c=(0,r.useCallback)(e=>{s(t=>t.filter(t=>t.id!==e)),n.oR.dismiss(`task_start_${e}`),n.oR.dismiss(`task_processing_${e}`),n.oR.dismiss(`task_completed_${e}`),n.oR.dismiss(`task_error_${e}`)},[]),d=(0,r.useCallback)(e=>t.find(t=>t.id===e),[t]),m=(0,r.useCallback)(e=>t.filter(t=>t.type===e),[t]),h=(0,r.useCallback)(()=>{s(e=>e.filter(e=>"completed"!==e.status&&"error"!==e.status))},[]),p=(0,r.useMemo)(()=>t.filter(e=>"pending"===e.status||"processing"===e.status),[t]),f=(0,r.useMemo)(()=>t.filter(e=>"completed"===e.status||"error"===e.status),[t]),x=(0,r.useMemo)(()=>({tasks:t,addTask:i,updateTask:u,removeTask:c,getTask:d,getTasksByType:m,clearCompletedTasks:h,activeTasks:p,completedTasks:f}),[t,i,u,c,d,m,h,p,f]);return(0,a.jsx)(l.Provider,{value:x,children:e})},c=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useBackgroundTasks must be used within a BackgroundTasksProvider");return e}},16277:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>a}),s(61135);var r=s(36145),n=s(78067);let a={title:"OposiAI - Asistente IA para Oposiciones",description:"Aplicaci\xf3n de preguntas y respuestas con IA para temarios de oposiciones"};function i({children:e}){return(0,n.jsx)("html",{lang:"es",children:(0,n.jsx)("body",{className:"font-sans bg-gray-100",children:(0,n.jsx)(r.default,{children:e})})})}},23759:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var r=s(96554),n=s(41150),a=s(15427);function i(){return null}s(72267);var o=s(55727),l=s(97510),u=s(9949),c=s(67660),d=s(19342),m=s(50815),h=s(26544),p=s(99631);let f=()=>{let{activeTasks:e,completedTasks:t,removeTask:s,clearCompletedTasks:n}=(0,a.M)(),{0:i,1:f}=(0,r.useState)(!1),{0:x,1:g}=(0,r.useState)(!1);if(0===e.length+t.length)return null;let b=e=>{switch(e.status){case"pending":return(0,p.jsx)(o.A,{className:"h-4 w-4 text-yellow-500"});case"processing":return(0,p.jsx)(l.A,{className:"h-4 w-4 text-blue-500 animate-spin"});case"completed":return(0,p.jsx)(u.A,{className:"h-4 w-4 text-green-500"});case"error":return(0,p.jsx)(c.A,{className:"h-4 w-4 text-red-500"});default:return(0,p.jsx)(o.A,{className:"h-4 w-4 text-gray-500"})}},v=e=>{switch(e){case"mapa-mental":return"Mapa Mental";case"test":return"Test";case"flashcards":return"Flashcards";default:return"Tarea"}},j=e=>e.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"});return(0,p.jsx)("div",{className:"fixed bottom-4 right-4 z-50 max-w-sm",children:(0,p.jsxs)("div",{className:"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden",children:[(0,p.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between",onClick:()=>f(!i),children:[(0,p.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.jsx)(l.A,{className:"h-5 w-5"}),(0,p.jsxs)("span",{className:"font-medium",children:["Tareas (",e.length," activas)"]})]}),i?(0,p.jsx)(d.A,{className:"h-5 w-5"}):(0,p.jsx)(m.A,{className:"h-5 w-5"})]}),i&&(0,p.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[e.length>0&&(0,p.jsxs)("div",{className:"p-3 border-b border-gray-100",children:[(0,p.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Tareas Activas"}),(0,p.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,p.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[b(e),(0,p.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:v(e.type)}),(0,p.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),void 0!==e.progress&&(0,p.jsx)("div",{className:"mt-1",children:(0,p.jsx)("div",{className:"bg-gray-200 rounded-full h-1.5",children:(0,p.jsx)("div",{className:"bg-blue-500 h-1.5 rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})})})]}),(0,p.jsx)("span",{className:"text-xs text-gray-400",children:j(e.createdAt)})]},e.id))})]}),t.length>0&&(0,p.jsxs)("div",{className:"p-3",children:[(0,p.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,p.jsxs)("button",{onClick:()=>g(!x),className:"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1",children:[(0,p.jsxs)("span",{children:["Completadas (",t.length,")"]}),x?(0,p.jsx)(m.A,{className:"h-4 w-4"}):(0,p.jsx)(d.A,{className:"h-4 w-4"})]}),t.length>0&&(0,p.jsx)("button",{onClick:n,className:"text-xs text-gray-500 hover:text-red-600 transition-colors",children:"Limpiar"})]}),x&&(0,p.jsx)("div",{className:"space-y-2",children:t.map(e=>(0,p.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[b(e),(0,p.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:v(e.type)}),(0,p.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),e.error&&(0,p.jsx)("p",{className:"text-xs text-red-500 truncate",children:e.error})]}),(0,p.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.jsx)("span",{className:"text-xs text-gray-400",children:e.completedAt?j(e.completedAt):j(e.createdAt)}),(0,p.jsx)("button",{onClick:()=>s(e.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:(0,p.jsx)(h.A,{className:"h-4 w-4"})})]})]},e.id))})]})]})]})})};var x=s(29959);function g({children:e}){return(0,p.jsx)(a.W,{children:(0,p.jsxs)(n.O,{children:[(0,p.jsx)(i,{}),(0,p.jsx)(x.l$,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,style:{background:"#10b981",color:"#fff"}},error:{duration:5e3,style:{background:"#ef4444",color:"#fff"}}}}),(0,p.jsx)(f,{}),e]})})}},36145:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(50005).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx","default")},41150:(e,t,s)=>{"use strict";s.d(t,{O:()=>m,A:()=>h});var r=s(96554),n=s(50653),a=s(72267),i=s(99409),o=s(15427);let l=({timeout:e,onTimeout:t,enabled:s=!0})=>{let n=(0,r.useRef)(null),a=(0,r.useRef)(Date.now()),{tasks:i}=(0,o.M)();i.some(e=>"pending"===e.status||"processing"===e.status);let l=(0,r.useCallback)(()=>{s&&(n.current&&clearTimeout(n.current),a.current=Date.now(),n.current=setTimeout(()=>{0===i.filter(e=>"pending"===e.status||"processing"===e.status).length?t():(console.log("\uD83D\uDD04 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto"),setTimeout(()=>l(),6e4))},e))},[e,t,s,i]),u=(0,r.useCallback)(()=>{n.current&&(clearTimeout(n.current),n.current=null)},[]),c=(0,r.useCallback)(()=>s&&n.current?Math.max(0,e-(Date.now()-a.current)):0,[e,s]),d=["mousedown","mousemove","keypress","scroll","touchstart","click","keydown"];return(0,r.useEffect)(()=>{if(!s)return void u();let e=()=>{l()};return d.forEach(t=>{document.addEventListener(t,e,!0)}),l(),()=>{d.forEach(t=>{document.removeEventListener(t,e,!0)}),u()}},[s,l,u]),{resetTimer:l,clearTimer:u,getTimeRemaining:c}},u=(e=5,t,s=!0)=>l({timeout:60*e*1e3,onTimeout:t,enabled:s});var c=s(99631);let d=(0,r.createContext)(void 0),m=({children:e})=>{let{0:t,1:s}=(0,r.useState)(null),{0:o,1:l}=(0,r.useState)(null),{0:m,1:h}=(0,r.useState)(!0),{0:p,1:f}=(0,r.useState)(null),{0:x,1:g}=(0,r.useState)(!1),{0:b,1:v}=(0,r.useState)(60),j=(0,n.useRouter)(),y=(0,n.usePathname)();(0,r.useEffect)(()=>{h(!0);let{data:e}=a.N.auth.onAuthStateChange((e,t)=>{l(t),s(t?.user??null),f(null),("INITIAL_SESSION"===e||"SIGNED_IN"===e||"SIGNED_OUT"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e)&&h(!1)});return a.N.auth.getSession().then(({data:{session:e},error:t})=>{t&&(f(t.message),h(!1))}).catch(e=>{f(e.message),h(!1)}),()=>{e?.subscription.unsubscribe()}},[]),(0,r.useEffect)(()=>m||y.startsWith("/api")||y.startsWith("/_next")?void 0:o&&"/login"===y?void j.replace("/app"):o||["/","/login","/payment","/thank-you","/auth/callback","/auth/confirmed","/auth/unauthorized","/auth/reset-password","/auth/confirm-reset","/auth/confirm-invitation"].includes(y)||y.startsWith("/api")||y.startsWith("/_next")?void 0:void j.replace("/login"),[o,m,y,j]);let w=(0,r.useCallback)(async(e,t)=>{h(!0),f(null);try{let{user:s,session:r,error:n}=await (0,i.F3)(e,t);if(n)return f(n),h(!1),{user:null,session:null,error:n};return r&&(await new Promise(e=>setTimeout(e,300)),j.replace("/app")),{user:s,session:r,error:null}}catch(t){let e=t instanceof Error&&t.message?t.message:"Error desconocido durante el inicio de sesi\xf3n.";return f(e),h(!1),{user:null,session:null,error:e}}},[j]),N=(0,r.useCallback)(async()=>{h(!0),f(null);let{error:e}=await (0,i.n4)();e&&(f(e),h(!1))},[]),k=(0,r.useCallback)(()=>!!t&&!!o&&!m,[t,o,m]),P=(0,r.useCallback)(async()=>{await N()},[N]);(0,r.useCallback)(()=>{g(!1)},[]),(0,r.useCallback)(async()=>{g(!1),await N()},[N]);let{resetTimer:C}=u(5,P,k());return(0,c.jsxs)(d.Provider,{value:{user:t,session:o,isLoading:m,error:p,iniciarSesion:w,cerrarSesion:N,estaAutenticado:k},children:[e,!1]})},h=()=>{let e=(0,r.useContext)(d);if(void 0===e)throw Error("useAuth debe ser utilizado dentro de un AuthProvider");return e}},48921:(e,t,s)=>{"use strict";s.d(t,{N:()=>a,U:()=>n});var r=s(67764);function n(){return(0,r.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let a=n()},61135:()=>{},65833:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,73930,23)),Promise.resolve().then(s.t.bind(s,87508,23)),Promise.resolve().then(s.t.bind(s,77576,23)),Promise.resolve().then(s.t.bind(s,58723,23)),Promise.resolve().then(s.t.bind(s,31843,23)),Promise.resolve().then(s.t.bind(s,88411,23)),Promise.resolve().then(s.t.bind(s,36107,23)),Promise.resolve().then(s.t.bind(s,87925,23))},68370:(e,t,s)=>{Promise.resolve().then(s.bind(s,23759))},72267:(e,t,s)=>{"use strict";s.d(t,{N:()=>r.N,U:()=>r.U});var r=s(48921)},75561:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85726,23)),Promise.resolve().then(s.t.bind(s,58104,23)),Promise.resolve().then(s.t.bind(s,1852,23)),Promise.resolve().then(s.t.bind(s,68239,23)),Promise.resolve().then(s.t.bind(s,14535,23)),Promise.resolve().then(s.t.bind(s,66639,23)),Promise.resolve().then(s.t.bind(s,61919,23)),Promise.resolve().then(s.t.bind(s,15753,23))},99409:(e,t,s)=>{"use strict";s.d(t,{F3:()=>n,iF:()=>i,n4:()=>a});var r=s(72267);async function n(e,t){try{if(!e||!t)return{user:null,session:null,error:"Por favor, ingresa tu email y contrase\xf1a"};let{data:s,error:n}=await r.N.auth.signInWithPassword({email:e.trim(),password:t});if(n){if(n.message.includes("issued in the future")||n.message.includes("clock for skew"))return{user:null,session:null,error:"Error de sincronizaci\xf3n de tiempo. Por favor, verifica que la hora de tu dispositivo est\xe9 correctamente configurada."};if(n.message.includes("Invalid login credentials"))return{user:null,session:null,error:"Email o contrase\xf1a incorrectos. Por favor, verifica tus credenciales."};return{user:null,session:null,error:n.message}}if(s&&s.user&&s.session)return await new Promise(e=>setTimeout(e,800)),await r.N.auth.getSession(),{user:s.user,session:s.session,error:null};return{user:null,session:null,error:"Respuesta inesperada del servidor al iniciar sesi\xf3n."}}catch(e){return{user:null,session:null,error:e instanceof Error&&e.message?e.message:"Ha ocurrido un error inesperado al iniciar sesi\xf3n"}}}async function a(){try{console.log("\uD83D\uDD13 Iniciando proceso de logout...");let{error:e}=await r.N.auth.signOut({scope:"global"});if(e)return console.error("❌ Error en signOut:",e.message),{error:e.message};return console.log("✅ SignOut exitoso"),{error:null}}catch(e){return console.error("❌ Error inesperado en logout:",e),{error:"Ha ocurrido un error inesperado al cerrar sesi\xf3n"}}}async function i(){try{let{data:{user:e},error:t}=await r.N.auth.getUser();if(t){if("Auth session missing!"===t.message)return{user:null,error:null};return{user:null,error:t.message}}return{user:e,error:null}}catch(e){return{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"}}}}};
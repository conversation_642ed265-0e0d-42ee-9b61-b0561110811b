"use strict";exports.id=5315,exports.ids=[5315],exports.modules={85315:(e,t,r)=>{r.d(t,{o:()=>g});var n=r(28319),a=r(84344),o=r(83256);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,a,o;n=e,a=t,o=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[a]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class c{static async logWebhookEvent(e){try{let t=e.success?"✅":"❌",r=new Date().toISOString();console.log(`${t} [WEBHOOK] ${r}`,s(s({eventType:e.eventType,eventId:e.eventId,success:e.success,processingTime:`${e.processingTime}ms`,message:e.message},e.error&&{error:e.error}),e.data&&{data:e.data})),await this.logToExternalService(e)}catch(e){console.error("Error logging webhook event:",e)}}static async logFeatureAccess(e,t,n,a,o=0,i){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:s}=await Promise.resolve().then(r.bind(r,28319));await s.logFeatureAccess({user_id:e,feature_name:t,access_granted:n,plan_at_time:a,tokens_used:o,denial_reason:i})}let c=n?"✅":"❌";console.log(`${c} [FEATURE_ACCESS]`,s({userId:e,feature:t,granted:n,plan:a,tokens:o},i&&{reason:i}))}catch(e){console.error("Error logging feature access:",e)}}static async logPlanChange(e,t,n,a,o,i){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:s}=await Promise.resolve().then(r.bind(r,28319));await s.logPlanChange({user_id:e,old_plan:t||void 0,new_plan:n,changed_by:a,reason:o,transaction_id:i})}console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:e,oldPlan:t,newPlan:n,changedBy:a,reason:o,transactionId:i})}catch(e){console.error("Error logging plan change:",e)}}static async logCriticalError(e,t,r){try{let n={context:e,message:t.message,stack:t.stack,timestamp:new Date().toISOString(),additionalData:r};console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",n),await this.sendCriticalAlert(n)}catch(e){console.error("Error logging critical error:",e)}}static logPerformanceMetrics(e,t,r,n){let a=s({operation:e,duration:`${t}ms`,success:r,timestamp:new Date().toISOString()},n);console.log("\uD83D\uDCCA [PERFORMANCE]",a),this.sendMetrics(a)}static async logToExternalService(e){}static async sendCriticalAlert(e){}static sendMetrics(e){"true"===process.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}static async getWebhookStats(e="day"){return{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}}}}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,a,o;n=e,a=t,o=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[a]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class g{static async validateFeatureAccess(e,t,r=0){try{let o=await n.SupabaseAdminService.getUserProfile(e);if(!o)return await c.logFeatureAccess(e,t,!1,"unknown",0,"User profile not found"),{allowed:!1,reason:"Perfil de usuario no encontrado"};if("free"!==o.subscription_plan&&!o.payment_verified)return await c.logFeatureAccess(e,t,!1,o.subscription_plan,0,"Payment not verified"),{allowed:!1,reason:"Pago no verificado. Complete el proceso de pago para acceder a esta caracter\xedstica."};if(!(0,a.Nu)(o.subscription_plan,t))return await c.logFeatureAccess(e,t,!1,o.subscription_plan,0,`Feature not available in ${o.subscription_plan} plan`),{allowed:!1,reason:`La caracter\xedstica ${t} no est\xe1 disponible en su plan ${o.subscription_plan}`};if(r>0){let n=await this.validateTokenUsage(o,r);if(!n.allowed)return await c.logFeatureAccess(e,t,!1,o.subscription_plan,r,n.reason),n}return await c.logFeatureAccess(e,t,!0,o.subscription_plan,r),{allowed:!0,remainingUsage:o.monthly_token_limit-o.current_month_tokens,planLimits:{monthlyTokens:o.monthly_token_limit,currentTokens:o.current_month_tokens}}}catch(n){return console.error("Error validating feature access:",n),await c.logFeatureAccess(e,t,!1,"error",r,"Internal validation error"),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async validateTokenUsage(e,t){let r=new Date().toISOString().slice(0,7)+"-01";e.current_month!==r&&(await n.SupabaseAdminService.upsertUserProfile(u(u({},e),{},{current_month_tokens:0,current_month:r,updated_at:new Date().toISOString()})),e.current_month_tokens=0,e.current_month=r);let a=e.current_month_tokens+t;return a>e.monthly_token_limit?{allowed:!1,reason:`L\xedmite mensual de tokens alcanzado. Usado: ${e.current_month_tokens}/${e.monthly_token_limit}`,remainingUsage:Math.max(0,e.monthly_token_limit-e.current_month_tokens)}:{allowed:!0,remainingUsage:e.monthly_token_limit-a}}static async getUserAccessInfo(e){try{let t=await n.SupabaseAdminService.getUserProfile(e);if(!t)return null;let o=(0,a.IE)(t.subscription_plan);if(!o)return null;let i={tokens:t.current_month_tokens,tokenLimit:t.monthly_token_limit,month:t.current_month,documents:0,tests:0,flashcards:0,mindMaps:0};try{let t=await n.SupabaseAdminService.getDocumentsCount(e);i.documents=t}catch(e){console.error("Error getting documents count:",e)}if("free"===t.subscription_plan)try{let{FreeAccountService:t}=await Promise.resolve().then(r.bind(r,83256)),n=await t.getFreeAccountStatus(e);n&&(i=u(u({},i),{},{tests:n.usageCount.tests||0,flashcards:n.usageCount.flashcards||0,mindMaps:n.usageCount.mindMaps||0}))}catch(e){console.error("Error getting free account usage:",e)}let s=u(u({},o.limits),{},{tests:o.limits.testsPerWeek??0,flashcards:o.limits.flashcardsPerWeek??0,mindMaps:o.limits.mindMapsPerWeek??0});return{userId:e,plan:t.subscription_plan,paymentVerified:t.payment_verified,features:o.features||[],limits:s,currentUsage:i}}catch(e){return console.error("Error getting user access info:",e),null}}static async canUserPerformAction(e,t,r=1){try{let a=await n.SupabaseAdminService.getUserProfile(e);if(!a)return{allowed:!1,reason:"Usuario no encontrado"};let i=await this.validateFeatureAccess(e,{test_generation:"test_generation",flashcard_generation:"flashcard_generation",mind_map_generation:"mind_map_generation",ai_chat:"ai_tutor_chat",study_planning:"study_planning",summary_generation:"summary_a1_a2"}[t]);if(!i.allowed)return i;if("free"===a.subscription_plan){let n={test_generation:"tests",flashcard_generation:"flashcards",mind_map_generation:"mindMaps"}[t];if(n){let a=await o.FreeAccountService.canPerformAction(e,n,r);if(!a.allowed)return{allowed:!1,reason:a.reason||`L\xedmite alcanzado para ${t}`,remainingUsage:a.remaining}}}return{allowed:!0,remainingUsage:i.remainingUsage}}catch(e){return console.error("Error checking user action:",e),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async updateTokenUsage(e,t,r){try{let r=await n.SupabaseAdminService.getUserProfile(e);if(!r)return!1;let a=r.current_month_tokens+t;return await n.SupabaseAdminService.upsertUserProfile(u(u({},r),{},{current_month_tokens:a,updated_at:new Date().toISOString()})),console.log(`✅ Tokens actualizados para usuario ${e}: +${t} (Total: ${a}/${r.monthly_token_limit})`),!0}catch(e){return console.error("Error updating token usage:",e),!1}}static async checkUpgradeNeeded(e){try{let t=await n.SupabaseAdminService.getUserProfile(e);if(!t)return{needsUpgrade:!1};let r=t.current_month_tokens/t.monthly_token_limit*100;if(r>=90){let e="free"===t.subscription_plan?"usuario":"pro";return{needsUpgrade:!0,reason:`Has usado el ${r.toFixed(1)}% de tus tokens mensuales`,suggestedPlan:e}}return{needsUpgrade:!1}}catch(e){return console.error("Error checking upgrade need:",e),{needsUpgrade:!1}}}}}};
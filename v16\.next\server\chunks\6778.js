"use strict";exports.id=6778,exports.ids=[6778],exports.modules={6778:(e,t,r)=>{r.r(t),r.d(t,{getTokenUsageStats:()=>l,saveTokenUsageServer:()=>a,updateUserPlanLimits:()=>u});var o=r(11345),n=r(84344);async function a(e){try{console.log("\uD83D\uDD04 saveTokenUsageServer iniciado con data:",e);let t=await (0,o.createServerSupabaseClient)();console.log("✅ Cliente Supabase del servidor creado");let{data:{user:r},error:n}=await t.auth.getUser();if(console.log("\uD83D\uDC64 Usuario obtenido:",r?`ID: ${r.id}, Email: ${r.email}`:"No autenticado"),n||!r)return void console.warn("❌ No hay usuario autenticado para guardar tokens:",n?.message);let a={user_id:r.id,activity_type:e.activity,model_name:e.model,prompt_tokens:e.usage.promptTokens,completion_tokens:e.usage.completionTokens,total_tokens:e.usage.totalTokens,estimated_cost:e.usage.estimatedCost||0,usage_month:new Date().toISOString().slice(0,7)+"-01"};console.log("\uD83D\uDCDD Registro a insertar:",a);let{error:l}=await t.from("user_token_usage").insert([a]);if(l)return void console.error("❌ Error al guardar uso de tokens:",l);console.log("✅ Registro insertado exitosamente en user_token_usage");let u=await s(t,r.id,e.usage.totalTokens);if(!u.allowed)return void console.warn("⚠️ L\xedmite de tokens alcanzado:",u.reason);await i(t,r.id,e.usage.totalTokens)}catch(e){console.error("❌ Error en saveTokenUsageServer:",e)}}async function i(e,t,r){try{let o=new Date().toISOString().slice(0,7)+"-01",{data:a,error:i}=await e.from("user_profiles").select("*").eq("user_id",t).single();if(i&&"PGRST116"!==i.code)return void console.error("Error al obtener perfil:",i);if(a){let n=a.current_month===o?a.current_month_tokens+r:r,{error:i}=await e.from("user_profiles").update({current_month_tokens:n,current_month:o,updated_at:new Date().toISOString()}).eq("user_id",t);i?console.error("Error al actualizar perfil:",i):console.log("✅ Perfil de usuario actualizado")}else{let a="free",i=(0,n.t4)(a),{error:s}=await e.from("user_profiles").insert([{user_id:t,subscription_plan:a,monthly_token_limit:i,current_month_tokens:r,current_month:o,payment_verified:!1}]);s?console.error("Error al crear perfil:",s):console.log("✅ Perfil de usuario creado con l\xedmite din\xe1mico:",i)}}catch(e){console.error("Error en updateMonthlyTokenCount:",e)}}async function s(e,t,r){try{let o=new Date().toISOString().slice(0,7)+"-01",{data:n,error:a}=await e.from("user_profiles").select("subscription_plan, monthly_token_limit, current_month_tokens, current_month, payment_verified").eq("user_id",t).single();if(a||!n)return{allowed:!1,reason:"Perfil de usuario no encontrado"};if("free"!==n.subscription_plan&&!n.payment_verified)return{allowed:!1,reason:"Pago no verificado"};let i=n.current_month===o?n.current_month_tokens:0;if(i+r>n.monthly_token_limit)return{allowed:!1,reason:`L\xedmite mensual alcanzado: ${i+r}/${n.monthly_token_limit}`,currentUsage:{current:i,limit:n.monthly_token_limit,requested:r,plan:n.subscription_plan}};return{allowed:!0,currentUsage:{current:i,limit:n.monthly_token_limit,remaining:n.monthly_token_limit-i-r}}}catch(e){return console.error("Error validating token limits:",e),{allowed:!1,reason:"Error de validaci\xf3n"}}}async function l(e){try{let t=await (0,o.createServerSupabaseClient)(),r=new Date().toISOString().slice(0,7)+"-01",{data:a,error:i}=await t.from("user_profiles").select("*").eq("user_id",e).single();if(i||!a)return null;let s=a.current_month===r?a.current_month_tokens:0,l=(0,n.IE)(a.subscription_plan),u=a.monthly_token_limit>0?s/a.monthly_token_limit*100:0;return{currentMonth:{used:s,limit:a.monthly_token_limit,percentage:Math.round(u),remaining:a.monthly_token_limit-s},plan:{name:l?.name||a.subscription_plan,features:l?.features||[]},paymentVerified:a.payment_verified||"free"===a.subscription_plan}}catch(e){return console.error("Error getting token usage stats:",e),null}}async function u(e,t){try{let r=await (0,o.createServerSupabaseClient)(),a=(0,n.t4)(t),{error:i}=await r.from("user_profiles").update({subscription_plan:t,monthly_token_limit:a,updated_at:new Date().toISOString()}).eq("user_id",e);if(i)return console.error("Error updating user plan limits:",i),!1;return console.log(`✅ Plan actualizado para usuario ${e}: ${t} (${a} tokens)`),!0}catch(e){return console.error("Error updating user plan limits:",e),!1}}}};
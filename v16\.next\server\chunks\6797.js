"use strict";exports.id=6797,exports.ids=[6797],exports.modules={76797:(r,e,t)=>{t.d(e,{guardarDocumentoServer:()=>a});var o=t(11345);function n(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,o)}return t}function i(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?n(Object(t),!0).forEach(function(e){var o,n,i;o=r,n=e,i=t[e],(n=function(r){var e=function(r,e){if("object"!=typeof r||null===r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var o=t.call(r,e||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(r)}(r,"string");return"symbol"==typeof e?e:String(e)}(n))in o?Object.defineProperty(o,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}async function a(r){try{let e=await (0,o.createServerSupabaseClient)(),{data:{user:t},error:n}=await e.auth.getUser();if(n||!t)return console.error("No hay usuario autenticado:",n?.message),null;let a=i(i({},r),{},{user_id:t.id,tipo_original:r.tipo_original}),{data:u,error:c}=await e.from("documentos").insert([a]).select();if(c)return console.error("Error al guardar documento:",c),null;return u?.[0]?.id||null}catch(r){return console.error("Error al guardar documento:",r),null}}}};
"use strict";exports.id=7334,exports.ids=[7334],exports.modules={1683:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return f}});let n=r(71663),l=r(1874),a=r(81486),o=r(93264),u=r(91616),i=r(17771),c=r(94744);function f(e,t){let{serverResponse:{flightData:r,canonicalUrl:f},navigatedAt:s}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof r)return(0,o.handleExternalUrl)(e,d,r,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of r){let{segmentPath:r,tree:i}=t,y=(0,l.applyRouterStatePatchToTree)(["",...r],p,i,e.canonicalUrl);if(null===y)return e;if((0,a.isNavigatingToNewRootLayout)(p,y))return(0,o.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let g=f?(0,n.createHrefFromUrl)(f):void 0;g&&(d.canonicalUrl=g);let b=(0,c.createEmptyCacheNode)();(0,u.applyFlightData)(s,h,b,t),d.patchedTree=y,d.cache=b,h=b,p=y}return(0,i.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1874:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,a){let f,[s,d,p,h,y]=r;if(1===t.length){let e=c(r,n);return(0,i.addRefreshMarkerToActiveParallelSegments)(e,a),e}let[g,b]=t;if(!(0,u.matchSegment)(g,s))return null;if(2===t.length)f=c(d[b],n);else if(null===(f=e((0,o.getNextFlightSegmentPath)(t),d[b],n,a)))return null;let v=[t[0],l(l({},d),{},{[b]:f}),p,h];return y&&(v[4]=!0),(0,i.addRefreshMarkerToActiveParallelSegments)(v,a),v}}});let a=r(37545),o=r(3127),u=r(90653),i=r(67460);function c(e,t){let[r,n]=e,[l,o]=t;if(l===a.DEFAULT_SEGMENT_KEY&&r!==a.DEFAULT_SEGMENT_KEY)return e;if((0,u.matchSegment)(r,l)){let t={};for(let e in n)void 0!==o[e]?t[e]=c(n[e],o[e]):t[e]=n[e];for(let e in o)t[e]||(t[e]=o[e]);let l=[r,t];return e[2]&&(l[2]=e[2]),e[3]&&(l[3]=e[3]),e[4]&&(l[4]=e[4]),l}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2762:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let n=r(62912),l=r(89724),a=r(77923),o=r(37545);function u(e,t,r,u,i,c){let{segmentPath:f,seedData:s,tree:d,head:p}=u,h=t,y=r;for(let t=0;t<f.length;t+=2){let r=f[t],u=f[t+1],g=t===f.length-2,b=(0,a.createRouterCacheKey)(u),v=y.parallelRoutes.get(r);if(!v)continue;let _=h.parallelRoutes.get(r);_&&_!==v||(_=new Map(v),h.parallelRoutes.set(r,_));let P=v.get(b),m=_.get(b);if(g){if(s&&(!m||!m.lazyData||m===P)){let t=s[0],r=s[1],a=s[3];m={lazyData:null,rsc:c||t!==o.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:c&&P?new Map(P.parallelRoutes):new Map,navigatedAt:e},P&&c&&(0,n.invalidateCacheByRouterState)(m,P,d),c&&(0,l.fillLazyItemsTillLeafWithHead)(e,m,P,d,s,p,i),_.set(b,m)}continue}m&&P&&(m===P&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),loading:m.loading},_.set(b,m)),h=m,y=P)}}function i(e,t,r,n,l){u(e,t,r,n,l,!0)}function c(e,t,r,n,l){u(e,t,r,n,l,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6782:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let n=r(96554),l=r(63951),a="next-route-announcer";function o(e){let{tree:t}=e,[r,o]=(0,n.useState)(null);(0,n.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[u,i]=(0,n.useState)(""),c=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&i(e),c.current=e},[t]),r?(0,l.createPortal)(u,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8178:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return l}});let n=r(22849);function l(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:l,hash:a}=(0,n.parsePath)(e);return""+t+r+l+a}},9333:(e,t)=>{function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},15311:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return l}});let n=r(22849);function l(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},17356:(e,t,r)=>{r.r(t),r.d(t,{_:()=>l});var n=0;function l(e){return"__private_"+n+++"_"+e}},17771:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(49394);function l(e){return void 0!==e}function a(e,t){var r,a;let o=null==(r=t.shouldScroll)||r,u=e.nextUrl;if(l(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?u=r:u||(u=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17811:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(71663),l=r(49394);function a(e,t){var r;let{url:a,tree:o}=t,u=(0,n.createHrefFromUrl)(a),i=o||e.tree,c=e.cache;return{canonicalUrl:u,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:i,nextUrl:null!=(r=(0,l.extractPathFromFlightRouterState)(i))?r:a.pathname}}r(51492),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19998:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return i},mountFormInstance:function(){return v},mountLinkInstance:function(){return b},onLinkVisibilityChanged:function(){return P},onNavigationIntent:function(){return m},pingVisibleLinks:function(){return j},setLinkForCurrentNavigation:function(){return f},unmountLinkForCurrentNavigation:function(){return s},unmountPrefetchableInstance:function(){return _}}),r(85706);let n=r(94744),l=r(89874),a=r(42465),o=r(96554),u=null,i={pending:!0},c={pending:!1};function f(e){(0,o.startTransition)(()=>{null==u||u.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(i),u=e})}function s(e){u===e&&(u=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;P(t.target,e)}},{rootMargin:"200px"}):null;function y(e,t){void 0!==d.get(e)&&_(e),d.set(e,t),null!==h&&h.observe(e)}function g(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function b(e,t,r,n,l,a){if(l){let l=g(t);if(null!==l){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:a};return y(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,r,n){let l=g(t);null!==l&&y(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:null})}function _(e){let t=d.get(e);if(void 0!==t){d.delete(e),p.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==h&&h.unobserve(e)}function P(e,t){let r=d.get(e);void 0!==r&&(r.isVisible=t,t?p.add(r):p.delete(r),O(r))}function m(e,t){let r=d.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,O(r))}function O(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function j(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of p){let o=n.prefetchTask;if(null!==o&&n.cacheVersion===r&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let u=(0,a.createCacheKey)(n.prefetchHref,e),i=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(u,t,n.kind===l.PrefetchKind.FULL,i),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21956:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[u,i]=a,c=(0,n.createRouterCacheKey)(i),f=r.parallelRoutes.get(u);if(!f)return;let s=t.parallelRoutes.get(u);if(s&&s!==f||(s=new Map(f),t.parallelRoutes.set(u,s)),o)return void s.delete(c);let d=f.get(c),p=s.get(c);p&&d&&(p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},s.set(c,p)),e(p,d,(0,l.getNextFlightSegmentPath)(a)))}}});let n=r(77923),l=r(3127);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22040:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return i},isBot:function(){return u}});let n=r(22548),l=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function o(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function u(e){return l.test(e)||o(e)}function i(e){return l.test(e)?"dom":o(e)?"html":void 0}},22098:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(96544),l=r(71663),a=r(1874),o=r(81486),u=r(93264),i=r(17771),c=r(89724),f=r(94744),s=r(24301),d=r(72950),p=r(67460);function h(e,t){let{origin:r}=t,h={},y=e.canonicalUrl,g=e.tree;h.preserveCustomHistoryState=!1;let b=(0,f.createEmptyCacheNode)(),v=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);b.lazyData=(0,n.fetchServerResponse)(new URL(y,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let _=Date.now();return b.lazyData.then(async r=>{let{flightData:n,canonicalUrl:f}=r;if("string"==typeof n)return(0,u.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(b.lazyData=null,n)){let{tree:n,seedData:i,head:d,isRootRender:P}=r;if(!P)return console.log("REFRESH FAILED"),e;let m=(0,a.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===m)return(0,s.handleSegmentMismatch)(e,t,n);if((0,o.isNavigatingToNewRootLayout)(g,m))return(0,u.handleExternalUrl)(e,h,y,e.pushRef.pendingPush);let O=f?(0,l.createHrefFromUrl)(f):void 0;if(f&&(h.canonicalUrl=O),null!==i){let e=i[1],t=i[3];b.rsc=e,b.prefetchRsc=null,b.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(_,b,void 0,n,i,d,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:_,state:e,updatedTree:m,updatedCache:b,includeNextUrl:v,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=b,h.patchedTree=m,g=m}return(0,i.handleMutable)(e,h)},()=>e)}r(42465),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22548:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},22849:(e,t)=>{function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},24082:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(9333),l=r(22849),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,l.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24301:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let n=r(93264);function l(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(60217),l=r(60912);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,l.hasBasePath)(r.pathname)}catch(e){return!1}}},33918:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(89874),r(93264),r(1683),r(17811),r(22098),r(48324),r(72240),r(47653);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35987:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return u},urlObjectKeys:function(){return o}});let n=r(79760)._(r(77931)),l=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",o=e.pathname||"",u=e.hash||"",i=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let f=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||l.test(a))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),u&&"#"!==u[0]&&(u="#"+u),f&&"?"!==f[0]&&(f="?"+f),""+a+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(f=f.replace("#","%23"))+u}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return a(e)}},36345:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let n=r(73055);function l(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41668:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},42465:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return s},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return i},createCacheKey:function(){return f},getCurrentCacheVersion:function(){return o},navigate:function(){return l},prefetch:function(){return n},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return u}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,l=r,a=r,o=r,u=r,i=r,c=r,f=r;var s=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47531:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return s},handleAliasedPrefetchEntry:function(){return f}});let n=r(37545),l=r(94744),a=r(1874),o=r(71663),u=r(77923),i=r(2762),c=r(17771);function f(e,t,r,f,d){let p,h=t.tree,y=t.cache,g=(0,o.createHrefFromUrl)(f);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=s(r,Object.fromEntries(f.searchParams));let{seedData:o,isRootRender:c,pathToSegment:d}=t,b=["",...d];r=s(r,Object.fromEntries(f.searchParams));let v=(0,a.applyRouterStatePatchToTree)(b,h,r,g),_=(0,l.createEmptyCacheNode)();if(c&&o){let t=o[1];_.loading=o[3],_.rsc=t,function e(t,r,l,a,o){if(0!==Object.keys(a[1]).length)for(let i in a[1]){let c,f=a[1][i],s=f[0],d=(0,u.createRouterCacheKey)(s),p=null!==o&&void 0!==o[2][i]?o[2][i]:null;if(null!==p){let e=p[1],r=p[3];c={lazyData:null,rsc:s.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=r.parallelRoutes.get(i);h?h.set(d,c):r.parallelRoutes.set(i,new Map([[d,c]])),e(t,c,l,f,p)}}(e,_,y,r,o)}else _.rsc=y.rsc,_.prefetchRsc=y.prefetchRsc,_.loading=y.loading,_.parallelRoutes=new Map(y.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(e,_,y,t);v&&(h=v,y=_,p=!0)}return!!p&&(d.patchedTree=h,d.cache=y,d.canonicalUrl=g,d.hashFragment=f.hash,(0,c.handleMutable)(t,d))}function s(e,t){let[r,l,...a]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),l,...a];let o={};for(let[e,r]of Object.entries(l))o[e]=s(r,t);return[r,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47653:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return C}});let a=r(56960),o=r(67832),u=r(12011),i=r(89874),c=r(36345),f=r(71663),s=r(93264),d=r(1874),p=r(81486),h=r(17771),y=r(89724),g=r(94744),b=r(72950),v=r(24301),_=r(67460),P=r(3127),m=r(36235),O=r(15556),j=r(59254),R=r(48534),E=r(60912),T=r(48002);r(42465);let{createFromFetch:w,createTemporaryReferenceSet:S,encodeReply:M}=r(86173);async function A(e,t,r){let n,i,{actionId:f,actionArgs:s}=r,d=S(),p=(0,T.extractInfoFromServerReferenceId)(f),h="use-cache"===p.type?(0,T.omitUnusedArgs)(s,p):s,y=await M(h,{temporaryReferences:d}),g=await fetch("",{method:"POST",headers:l(l({Accept:u.RSC_CONTENT_TYPE_HEADER,[u.ACTION_HEADER]:f,[u.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree))},{}),t?{[u.NEXT_URL]:t}:{}),body:y}),b=g.headers.get("x-action-redirect"),[v,_]=(null==b?void 0:b.split(";"))||[];switch(_){case"push":n=O.RedirectType.push;break;case"replace":n=O.RedirectType.replace;break;default:n=void 0}let m=!!g.headers.get(u.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(g.headers.get("x-action-revalidated")||"[[],0,0]");i={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){i={paths:[],tag:!1,cookie:!1}}let j=v?(0,c.assignLocation)(v,new URL(e.canonicalUrl,window.location.href)):void 0,R=g.headers.get("content-type");if(null==R?void 0:R.startsWith(u.RSC_CONTENT_TYPE_HEADER)){let e=await w(Promise.resolve(g),{callServer:a.callServer,findSourceMapURL:o.findSourceMapURL,temporaryReferences:d});return v?{actionFlightData:(0,P.normalizeFlightData)(e.f),redirectLocation:j,redirectType:n,revalidatedParts:i,isPrerender:m}:{actionResult:e.a,actionFlightData:(0,P.normalizeFlightData)(e.f),redirectLocation:j,redirectType:n,revalidatedParts:i,isPrerender:m}}if(g.status>=400)throw Object.defineProperty(Error("text/plain"===R?await g.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:j,redirectType:n,revalidatedParts:i,isPrerender:m}}function C(e,t){let{resolve:r,reject:n}=t,l={},a=e.tree;l.preserveCustomHistoryState=!1;let o=e.nextUrl&&(0,b.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,u=Date.now();return A(e,o,t).then(async c=>{let b,{actionResult:P,actionFlightData:T,redirectLocation:w,redirectType:S,isPrerender:M,revalidatedParts:A}=c;if(w&&(S===O.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=b=(0,f.createHrefFromUrl)(w,!1)),!T)return(r(P),w)?(0,s.handleExternalUrl)(e,l,w.href,e.pushRef.pendingPush):e;if("string"==typeof T)return r(P),(0,s.handleExternalUrl)(e,l,T,e.pushRef.pendingPush);let C=A.paths.length>0||A.tag||A.cookie;for(let n of T){let{tree:i,seedData:c,head:f,isRootRender:h}=n;if(!h)return console.log("SERVER ACTION APPLY FAILED"),r(P),e;let m=(0,d.applyRouterStatePatchToTree)([""],a,i,b||e.canonicalUrl);if(null===m)return r(P),(0,v.handleSegmentMismatch)(e,t,i);if((0,p.isNavigatingToNewRootLayout)(a,m))return r(P),(0,s.handleExternalUrl)(e,l,b||e.canonicalUrl,e.pushRef.pendingPush);if(null!==c){let t=c[1],r=(0,g.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=c[3],(0,y.fillLazyItemsTillLeafWithHead)(u,r,void 0,i,c,f,void 0),l.cache=r,l.prefetchCache=new Map,C&&await (0,_.refreshInactiveParallelSegments)({navigatedAt:u,state:e,updatedTree:m,updatedCache:r,includeNextUrl:!!o,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=m,a=m}return w&&b?(C||((0,j.createSeededPrefetchCacheEntry)({url:w,data:{flightData:T,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?i.PrefetchKind.FULL:i.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),n((0,m.getRedirectError)((0,E.hasBasePath)(b)?(0,R.removeBasePath)(b):b,S||O.RedirectType.push))):r(P),(0,h.handleMutable)(e,l)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48002:(e,t)=>{function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},48324:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let n=r(62616),l=r(59254),a=new n.PromiseQueue(5),o=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48534:(e,t,r)=>{function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(60912),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48762:(e,t,r)=>{function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},49394:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return f},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],a=Array.isArray(t),o=a?t[1]:t;!o||o.startsWith(l.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):a&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(795),l=r(37545),a=r(90653),o=e=>"/"===e[0]?e.slice(1):e,u=e=>"string"==typeof e?"children"===e?"":e:e[1];function i(e){return e.reduce((e,t)=>""===(t=o(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===l.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(l.PAGE_SEGMENT_KEY))return"";let a=[u(r)],o=null!=(t=e[1])?t:{},f=o.children?c(o.children):void 0;if(void 0!==f)a.push(f);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let r=c(t);void 0!==r&&a.push(r)}return i(a)}function f(e,t){let r=function e(t,r){let[l,o]=t,[i,f]=r,s=u(l),d=u(i);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>s.startsWith(e)||d.startsWith(e)))return"";if(!(0,a.matchSegment)(l,i)){var p;return null!=(p=c(r))?p:""}for(let t in o)if(f[t]){let r=e(o[t],f[t]);if(null!==r)return u(i)+"/"+r}return null}(e,t);return null==r||"/"===r?r:i(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51492:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],l=t.parallelRoutes,o=new Map(l);for(let t in n){let r=n[t],u=r[0],i=(0,a.createRouterCacheKey)(u),c=l.get(t);if(void 0!==c){let n=c.get(i);if(void 0!==n){let l=e(n,r),a=new Map(c);a.set(i,l),o.set(t,a)}}}let u=t.rsc,i=b(u)&&"pending"===u.status;return{lazyData:null,rsc:u,head:t.head,prefetchHead:i?t.prefetchHead:[null,null],prefetchRsc:i?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let n=r(37545),l=r(90653),a=r(77923),o=r(81486),u=r(59254),i={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,r,o,u,c,d,p,h){return function e(t,r,o,u,c,d,p,h,y,g,b){let v=o[1],_=u[1],P=null!==d?d[2]:null;c||!0===u[4]&&(c=!0);let m=r.parallelRoutes,O=new Map(m),j={},R=null,E=!1,T={};for(let r in _){let o,u=_[r],s=v[r],d=m.get(r),w=null!==P?P[r]:null,S=u[0],M=g.concat([r,S]),A=(0,a.createRouterCacheKey)(S),C=void 0!==s?s[0]:void 0,N=void 0!==d?d.get(A):void 0;if(null!==(o=S===n.DEFAULT_SEGMENT_KEY?void 0!==s?{route:s,node:null,dynamicRequestTree:null,children:null}:f(t,s,u,N,c,void 0!==w?w:null,p,h,M,b):y&&0===Object.keys(u[1]).length?f(t,s,u,N,c,void 0!==w?w:null,p,h,M,b):void 0!==s&&void 0!==C&&(0,l.matchSegment)(S,C)&&void 0!==N&&void 0!==s?e(t,N,s,u,c,w,p,h,y,M,b):f(t,s,u,N,c,void 0!==w?w:null,p,h,M,b))){if(null===o.route)return i;null===R&&(R=new Map),R.set(r,o);let e=o.node;if(null!==e){let t=new Map(d);t.set(A,e),O.set(r,t)}let t=o.route;j[r]=t;let n=o.dynamicRequestTree;null!==n?(E=!0,T[r]=n):T[r]=t}else j[r]=u,T[r]=u}if(null===R)return null;let w={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:O,navigatedAt:t};return{route:s(u,j),node:w,dynamicRequestTree:E?s(u,T):null,children:R}}(e,t,r,o,!1,u,c,d,p,[],h)}function f(e,t,r,n,l,c,f,p,h,y){return!l&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,r))?i:function e(t,r,n,l,o,i,c,f){let p,h,y,g,b=r[1],v=0===Object.keys(b).length;if(void 0!==n&&n.navigatedAt+u.DYNAMIC_STALETIME_MS>t)p=n.rsc,h=n.loading,y=n.head,g=n.navigatedAt;else if(null===l)return d(t,r,null,o,i,c,f);else if(p=l[1],h=l[3],y=v?o:null,g=t,l[4]||i&&v)return d(t,r,l,o,i,c,f);let _=null!==l?l[2]:null,P=new Map,m=void 0!==n?n.parallelRoutes:null,O=new Map(m),j={},R=!1;if(v)f.push(c);else for(let r in b){let n=b[r],l=null!==_?_[r]:null,u=null!==m?m.get(r):void 0,s=n[0],d=c.concat([r,s]),p=(0,a.createRouterCacheKey)(s),h=e(t,n,void 0!==u?u.get(p):void 0,l,o,i,d,f);P.set(r,h);let y=h.dynamicRequestTree;null!==y?(R=!0,j[r]=y):j[r]=n;let g=h.node;if(null!==g){let e=new Map;e.set(p,g),O.set(r,e)}}return{route:r,node:{lazyData:null,rsc:p,prefetchRsc:null,head:y,prefetchHead:null,loading:h,parallelRoutes:O,navigatedAt:g},dynamicRequestTree:R?s(r,j):null,children:P}}(e,r,n,c,f,p,h,y)}function s(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function d(e,t,r,n,l,o,u){let i=s(t,t[1]);return i[3]="refetch",{route:t,node:function e(t,r,n,l,o,u,i){let c=r[1],f=null!==n?n[2]:null,s=new Map;for(let r in c){let n=c[r],d=null!==f?f[r]:null,p=n[0],h=u.concat([r,p]),y=(0,a.createRouterCacheKey)(p),g=e(t,n,void 0===d?null:d,l,o,h,i),b=new Map;b.set(y,g),s.set(r,b)}let d=0===s.size;d&&i.push(u);let p=null!==n?n[1]:null,h=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:s,prefetchRsc:void 0!==p?p:null,prefetchHead:d?l:[null,null],loading:void 0!==h?h:null,rsc:v(),head:d?v():null,navigatedAt:t}}(e,t,r,n,l,o,u),dynamicRequestTree:i,children:null}}function p(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:o,head:u}=t;o&&function(e,t,r,n,o){let u=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=u.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(n,t)){u=e;continue}}}return}!function e(t,r,n,o){if(null===t.dynamicRequestTree)return;let u=t.children,i=t.node;if(null===u){null!==i&&(function e(t,r,n,o,u){let i=r[1],c=n[1],f=o[2],s=t.parallelRoutes;for(let t in i){let r=i[t],n=c[t],o=f[t],d=s.get(t),p=r[0],h=(0,a.createRouterCacheKey)(p),g=void 0!==d?d.get(h):void 0;void 0!==g&&(void 0!==n&&(0,l.matchSegment)(p,n[0])&&null!=o?e(g,r,n,o,u):y(r,g,null))}let d=t.rsc,p=o[1];null===d?t.rsc=p:b(d)&&d.resolve(p);let h=t.head;b(h)&&h.resolve(u)}(i,t.route,r,n,o),t.dynamicRequestTree=null);return}let c=r[1],f=n[2];for(let t in r){let r=c[t],n=f[t],a=u.get(t);if(void 0!==a){let t=a.route[0];if((0,l.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,o)}}}(u,r,n,o)}(e,r,n,o,u)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)y(e.route,r,t);else for(let e of n.values())h(e,t);e.dynamicRequestTree=null}function y(e,t,r){let n=e[1],l=t.parallelRoutes;for(let e in n){let t=n[e],o=l.get(e);if(void 0===o)continue;let u=t[0],i=(0,a.createRouterCacheKey)(u),c=o.get(i);void 0!==c&&y(t,c,r)}let o=t.rsc;b(o)&&(null===r?o.resolve(null):o.reject(r));let u=t.head;b(u)&&u.resolve(null)}let g=Symbol();function b(e){return e&&e.tag===g}function v(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=g,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54925:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let n=r(77923);function l(e,t){return function e(t,r,l){if(0===Object.keys(r).length)return[t,l];if(r.children){let[a,o]=r.children,u=t.parallelRoutes.get("children");if(u){let t=(0,n.createRouterCacheKey)(a),r=u.get(t);if(r){let n=e(r,o,l+"/"+t);if(n)return n}}}for(let a in r){if("children"===a)continue;let[o,u]=r[a],i=t.parallelRoutes.get(a);if(!i)continue;let c=(0,n.createRouterCacheKey)(o),f=i.get(c);if(!f)continue;let s=e(f,u,l+"/"+c);if(s)return s}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59254:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return h},STATIC_STALETIME_MS:function(){return y},createSeededPrefetchCacheEntry:function(){return s},getOrCreatePrefetchCacheEntry:function(){return f},prunePrefetchCache:function(){return p}});let a=r(96544),o=r(89874),u=r(48324);function i(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function c(e,t,r){return i(e,t===o.PrefetchKind.FULL,r)}function f(e){let{url:t,nextUrl:r,tree:n,prefetchCache:a,kind:u,allowAliasing:c=!0}=e,f=function(e,t,r,n,a){for(let u of(void 0===t&&(t=o.PrefetchKind.TEMPORARY),[r,null])){let r=i(e,!0,u),c=i(e,!1,u),f=e.search?r:c,s=n.get(f);if(s&&a){if(s.url.pathname===e.pathname&&s.url.search!==e.search)return l(l({},s),{},{aliased:!0});return s}let d=n.get(c);if(a&&e.search&&t!==o.PrefetchKind.FULL&&d&&!d.key.includes("%"))return l(l({},d),{},{aliased:!0})}if(t!==o.PrefetchKind.FULL&&a){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return l(l({},t),{},{aliased:!0})}}(t,u,r,a,c);return f?(f.status=g(f),f.kind!==o.PrefetchKind.FULL&&u===o.PrefetchKind.FULL&&f.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return d({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:null!=u?u:o.PrefetchKind.TEMPORARY})}),u&&f.kind===o.PrefetchKind.TEMPORARY&&(f.kind=u),f):d({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:u||o.PrefetchKind.TEMPORARY})}function s(e){let{nextUrl:t,tree:r,prefetchCache:n,url:l,data:a,kind:u}=e,i=a.couldBeIntercepted?c(l,u,t):c(l,u),f={treeAtTimeOfPrefetch:r,data:Promise.resolve(a),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:i,status:o.PrefetchCacheEntryStatus.fresh,url:l};return n.set(i,f),f}function d(e){let{url:t,kind:r,tree:n,nextUrl:i,prefetchCache:f}=e,s=c(t,r),d=u.prefetchQueue.enqueue(()=>(0,a.fetchServerResponse)(t,{flightRouterState:n,nextUrl:i,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:a}=e,o=n.get(a);if(!o)return;let u=c(t,o.kind,r);return n.set(u,l(l({},o),{},{key:u})),n.delete(a),u}({url:t,existingCacheKey:s,nextUrl:i,prefetchCache:f})),e.prerendered){let t=f.get(null!=r?r:s);t&&(t.kind=o.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),p={treeAtTimeOfPrefetch:n,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:s,status:o.PrefetchCacheEntryStatus.fresh,url:t};return f.set(s,p),p}function p(e){for(let[t,r]of e)g(r)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),y=1e3*Number("300");function g(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:l}=e;return -1!==l?Date.now()<r+l?o.PrefetchCacheEntryStatus.fresh:o.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+h?n?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:t===o.PrefetchKind.AUTO&&Date.now()<r+y?o.PrefetchCacheEntryStatus.stale:t===o.PrefetchKind.FULL&&Date.now()<r+y?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},60217:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return y},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return o},getURL:function(){return u},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return s},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return _}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,l=Array(n),a=0;a<n;a++)l[a]=arguments[a];return r||(r=!0,t=e(...l)),t}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>l.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=o();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function s(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await s(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function _(e){return JSON.stringify({message:e.message,stack:e.stack})}},60912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return l}});let n=r(15311);function l(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62616:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let n=r(48762),l=r(17356);var a=l._("_maxConcurrency"),o=l._("_runningCount"),u=l._("_queue"),i=l._("_processNext");class c{enqueue(e){let t,r,l=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,o)[o]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,o)[o]--,n._(this,i)[i]()}};return n._(this,u)[u].push({promiseFn:l,task:a}),n._(this,i)[i](),l}bump(e){let t=n._(this,u)[u].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,u)[u].splice(t,1)[0];n._(this,u)[u].unshift(e),n._(this,i)[i](!0)}}constructor(e=5){Object.defineProperty(this,i,{value:f}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,u,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,o)[o]=0,n._(this,u)[u]=[]}}function f(e){if(void 0===e&&(e=!1),(n._(this,o)[o]<n._(this,a)[a]||e)&&n._(this,u)[u].length>0){var t;null==(t=n._(this,u)[u].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let n=r(77923);function l(e,t,r){for(let l in r[1]){let a=r[1][l][0],o=(0,n.createRouterCacheKey)(a),u=t.parallelRoutes.get(l);if(u){let t=new Map(u);t.delete(o),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67460:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,l,,a]=t;for(let o in n.includes(u.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),l)e(l[o],r)}},refreshInactiveParallelSegments:function(){return i}});let a=r(91616),o=r(96544),u=r(37545);async function i(e){let t=new Set;await c(l(l({},e),{},{rootTree:e.updatedTree,fetchedSegments:t}))}async function c(e){let{navigatedAt:t,state:r,updatedTree:n,updatedCache:l,includeNextUrl:u,fetchedSegments:i,rootTree:f=n,canonicalUrl:s}=e,[,d,p,h]=n,y=[];if(p&&p!==s&&"refresh"===h&&!i.has(p)){i.add(p);let e=(0,o.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[f[0],f[1],f[2],"refetch"],nextUrl:u?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,a.applyFlightData)(t,l,l,e)});y.push(e)}for(let e in d){let n=c({navigatedAt:t,state:r,updatedTree:d[e],updatedCache:l,includeNextUrl:u,fetchedSegments:i,rootTree:f,canonicalUrl:s});y.push(n)}await Promise.all(y)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70431:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[a,o]=r,[u,i]=t;return(0,l.matchSegment)(u,a)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),o[i]):!!Array.isArray(u)}}});let n=r(3127),l=r(90653);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71192:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[u,i]=a,c=(0,l.createRouterCacheKey)(i),f=r.parallelRoutes.get(u),s=t.parallelRoutes.get(u);s&&s!==f||(s=new Map(f),t.parallelRoutes.set(u,s));let d=null==f?void 0:f.get(c),p=s.get(c);if(o){p&&p.lazyData&&p!==d||s.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!d){p||s.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},s.set(c,p)),e(p,d,(0,n.getNextFlightSegmentPath)(a))}}});let n=r(3127),l=r(77923);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72240:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(96544),r(71663),r(1874),r(81486),r(93264),r(17771),r(91616),r(94744),r(24301),r(72950);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73055:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(8178),l=r(24082);function a(e,t){return(0,l.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77838:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let n=r(96554);function l(e,t){let r=(0,n.useRef)(null),l=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=a(e,n)),t&&(l.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77931:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[r,l]of Object.entries(e))if(Array.isArray(l))for(let e of l)t.append(r,n(e));else t.set(r,n(l));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return l}})},81486:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],l=r[0];if(Array.isArray(n)&&Array.isArray(l)){if(n[0]!==l[0]||n[2]!==l[2])return!0}else if(n!==l)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],o=Object.values(r[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85706:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return b},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return y},publicAppRouterInstance:function(){return _}});let n=r(89874),l=r(33918),a=r(96554),o=r(70232);r(42465);let u=r(80505),i=r(73055),c=r(94744),f=r(48324),s=r(19998);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:r,setState:n}=e,l=t.state;t.pending=r;let a=r.payload,u=t.action(l,a);function i(e){r.discarded||(t.state=e,d(t,n),r.resolve(e))}(0,o.isThenable)(u)?u.then(i,e=>{d(t,n),r.reject(e)}):i(u)}function h(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let l={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=o,p({actionQueue:e,action:o,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(r,e,t),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function y(){return null}function g(){return null}function b(e,t,r,l){let a=new URL((0,i.addBasePath)(e),location.href);(0,s.setLinkForCurrentNavigation)(l);(0,u.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:a,isExternalUrl:(0,c.isExternalURL)(a),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function v(e,t){(0,u.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let _={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),l=(0,c.createPrefetchURL)(e);if(null!==l){var a;(0,f.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:l,kind:null!=(a=null==t?void 0:t.kind)?a:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var r;b(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var r;b(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,u.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89724:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,o,u,i,c){if(0===Object.keys(o[1]).length){r.head=i;return}for(let f in o[1]){let s,d=o[1][f],p=d[0],h=(0,n.createRouterCacheKey)(p),y=null!==u&&void 0!==u[2][f]?u[2][f]:null;if(a){let n=a.parallelRoutes.get(f);if(n){let a,o=(null==c?void 0:c.kind)==="auto"&&c.status===l.PrefetchCacheEntryStatus.reusable,u=new Map(n),s=u.get(h);a=null!==y?{lazyData:null,rsc:y[1],prefetchRsc:null,head:null,prefetchHead:null,loading:y[3],parallelRoutes:new Map(null==s?void 0:s.parallelRoutes),navigatedAt:t}:o&&s?{lazyData:s.lazyData,rsc:s.rsc,prefetchRsc:s.prefetchRsc,head:s.head,prefetchHead:s.prefetchHead,parallelRoutes:new Map(s.parallelRoutes),loading:s.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==s?void 0:s.parallelRoutes),loading:null,navigatedAt:t},u.set(h,a),e(t,a,s,d,y||null,i,c),r.parallelRoutes.set(f,u);continue}}if(null!==y){let e=y[1],r=y[3];s={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else s={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=r.parallelRoutes.get(f);g?g.set(h,s):r.parallelRoutes.set(f,new Map([[h,s]])),e(t,s,void 0,d,y,i,c)}}}});let n=r(77923),l=r(89874);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91616:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(89724),l=r(2762);function a(e,t,r,a,o){let{tree:u,seedData:i,head:c,isRootRender:f}=a;if(null===i)return!1;if(f){let l=i[1];r.loading=i[3],r.rsc=l,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,u,i,c,o)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,l.fillCacheWithNewSubTreeData)(e,r,t,a,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},93264:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return m},navigateReducer:function(){return function e(t,r){let{url:n,isExternalUrl:j,navigateType:R,shouldScroll:E,allowAliasing:T}=r,w={},{hash:S}=n,M=(0,o.createHrefFromUrl)(n),A="push"===R;if((0,v.prunePrefetchCache)(t.prefetchCache),w.preserveCustomHistoryState=!1,w.pendingPush=A,j)return m(t,w,n.toString(),A);if(document.getElementById("__next-page-redirect"))return m(t,w,M,A);let C=(0,v.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:T}),{treeAtTimeOfPrefetch:N,data:x}=C;return h.prefetchQueue.bump(x),x.then(h=>{let{flightData:v,canonicalUrl:j,postponed:R}=h,T=Date.now(),x=!1;if(C.lastUsedTime||(C.lastUsedTime=T,x=!0),C.aliased){let a=(0,P.handleAliasedPrefetchEntry)(T,t,v,n,w);return!1===a?e(t,l(l({},r),{},{allowAliasing:!1})):a}if("string"==typeof v)return m(t,w,v,A);let U=j?(0,o.createHrefFromUrl)(j):M;if(S&&t.canonicalUrl.split("#",1)[0]===U.split("#",1)[0])return w.onlyHashChange=!0,w.canonicalUrl=U,w.shouldScroll=E,w.hashFragment=S,w.scrollableSegments=[],(0,d.handleMutable)(t,w);let L=t.tree,D=t.cache,I=[];for(let e of v){let{pathToSegment:r,seedData:l,head:o,isHeadPartial:d,isRootRender:h}=e,v=e.tree,P=["",...r],j=(0,i.applyRouterStatePatchToTree)(P,L,v,M);if(null===j&&(j=(0,i.applyRouterStatePatchToTree)(P,N,v,M)),null!==j){if(l&&h&&R){let e=(0,b.startPPRNavigation)(T,D,L,v,l,o,d,!1,I);if(null!==e){if(null===e.route)return m(t,w,M,A);j=e.route;let r=e.node;null!==r&&(w.cache=r);let l=e.dynamicRequestTree;if(null!==l){let r=(0,a.fetchServerResponse)(n,{flightRouterState:l,nextUrl:t.nextUrl});(0,b.listenForDynamicRequest)(e,r)}}else j=v}else{if((0,f.isNavigatingToNewRootLayout)(L,j))return m(t,w,M,A);let n=(0,y.createEmptyCacheNode)(),l=!1;for(let t of(C.status!==s.PrefetchCacheEntryStatus.stale||x?l=(0,p.applyFlightData)(T,D,n,e,C):(l=function(e,t,r,n){let l=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),O(n).map(e=>[...r,...e])))(0,_.clearCacheNodeDataForSegmentPath)(e,t,a),l=!0;return l}(n,D,r,v),C.lastUsedTime=T),(0,c.shouldHardNavigate)(P,L)?(n.rsc=D.rsc,n.prefetchRsc=D.prefetchRsc,(0,u.invalidateCacheBelowFlightSegmentPath)(n,D,r),w.cache=n):l&&(w.cache=n,D=n),O(v))){let e=[...r,...t];e[e.length-1]!==g.DEFAULT_SEGMENT_KEY&&I.push(e)}}L=j}}return w.patchedTree=L,w.canonicalUrl=U,w.scrollableSegments=I,w.hashFragment=S,w.shouldScroll=E,(0,d.handleMutable)(t,w)},()=>t)}}});let a=r(96544),o=r(71663),u=r(21956),i=r(1874),c=r(70431),f=r(81486),s=r(89874),d=r(17771),p=r(91616),h=r(48324),y=r(94744),g=r(37545),b=r(51492),v=r(59254),_=r(71192),P=r(47531);function m(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,d.handleMutable)(e,t)}function O(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,l]of Object.entries(n))for(let n of O(l))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(42465),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94744:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return C},createPrefetchURL:function(){return M},default:function(){return L},isExternalURL:function(){return S}});let a=r(79760),o=r(99631),u=a._(r(96554)),i=r(84926),c=r(89874),f=r(71663),s=r(46417),d=r(80505),p=a._(r(77576)),h=r(22040),y=r(73055),g=r(6782),b=r(49358),v=r(54925),_=r(73650),P=r(48534),m=r(60912),O=r(49394),j=r(9256),R=r(85706),E=r(36235),T=r(15556);r(19998);let w={};function S(e){return e.origin!==window.location.origin}function M(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,y.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function A(e){let{appRouterState:t}=e;return(0,u.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,a=l(l({},r.preserveCustomHistoryState?window.history.state:{}),{},{__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e});r.pendingPush&&(0,f.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(a,"",n)):window.history.replaceState(a,"",n)},[t]),(0,u.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function C(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function N(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function x(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,l=null!==n?n:r;return(0,u.useDeferredValue)(r,l)}function U(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,a=(0,d.useActionQueue)(r),{canonicalUrl:f}=a,{searchParams:h,pathname:y}=(0,u.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,m.hasBasePath)(e.pathname)?(0,P.removeBasePath)(e.pathname):e.pathname}},[f]);(0,u.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(w.pendingMpaPath=void 0,(0,d.dispatchAppRouterAction)({type:c.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,u.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let r=(0,E.getURLFromRedirectError)(t);(0,E.getRedirectTypeFromError)(t)===T.RedirectType.push?R.publicAppRouterInstance.push(r,{}):R.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:j}=a;if(j.mpaNavigation){if(w.pendingMpaPath!==f){let e=window.location;j.pendingPush?e.assign(f):e.replace(f),w.pendingMpaPath=f}(0,u.use)(_.unresolvedThenable)}(0,u.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,u.startTransition)(()=>{(0,d.dispatchAppRouterAction)({type:c.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=N(t),l&&r(l)),e(t,n,l)},window.history.replaceState=function(e,n,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=N(e),l&&r(l)),t(e,n,l)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,u.startTransition)(()=>{(0,R.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:S,tree:M,nextUrl:C,focusAndScrollRef:U}=a,L=(0,u.useMemo)(()=>(0,v.findHeadInCache)(S,M[1]),[S,M]),D=(0,u.useMemo)(()=>(0,O.getSelectedParams)(M),[M]),I=(0,u.useMemo)(()=>({parentTree:M,parentCacheNode:S,parentSegmentPath:null,url:f}),[M,S,f]),H=(0,u.useMemo)(()=>({tree:M,focusAndScrollRef:U,nextUrl:C}),[M,U,C]);if(null!==L){let[e,r]=L;t=(0,o.jsx)(x,{headCacheNode:e},r)}else t=null;let F=(0,o.jsxs)(b.RedirectBoundary,{children:[t,S.rsc,(0,o.jsx)(g.AppRouterAnnouncer,{tree:M})]});return F=(0,o.jsx)(p.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:F}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(A,{appRouterState:a}),(0,o.jsx)(k,{}),(0,o.jsx)(s.PathParamsContext.Provider,{value:D,children:(0,o.jsx)(s.PathnameContext.Provider,{value:y,children:(0,o.jsx)(s.SearchParamsContext.Provider,{value:h,children:(0,o.jsx)(i.GlobalLayoutRouterContext.Provider,{value:H,children:(0,o.jsx)(i.AppRouterContext.Provider,{value:R.publicAppRouterInstance,children:(0,o.jsx)(i.LayoutRouterContext.Provider,{value:I,children:F})})})})})})]})}function L(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:l}=e;return(0,j.useNavFailureHandler)(),(0,o.jsx)(p.ErrorBoundary,{errorComponent:p.default,children:(0,o.jsx)(U,{actionQueue:t,assetPrefix:l,globalError:[r,n]})})}let D=new Set,I=new Set;function k(){let[,e]=u.default.useState(0),t=D.size;return(0,u.useEffect)(()=>{let r=()=>e(e=>e+1);return I.add(r),t!==D.size&&r(),()=>{I.delete(r)}},[t,e]),[...D].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=D.size;return D.add(e),D.size!==t&&I.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97334:(e,t,r)=>{let n=["href","as","children","prefetch","passHref","replace","shallow","scroll","onClick","onMouseEnter","onTouchStart","legacyBehavior","onNavigate","ref","unstable_dynamicOnHover"];function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return _},useLinkStatus:function(){return m}});let o=r(79760),u=r(99631),i=o._(r(96554)),c=r(35987),f=r(84926),s=r(89874),d=r(77838),p=r(60217),h=r(73055);r(50148);let y=r(19998),g=r(24642),b=r(85706);function v(e){return"string"==typeof e?e:(0,c.formatUrl)(e)}function _(e){let t,r,l,[o,c]=(0,i.useOptimistic)(y.IDLE_LINK_STATUS),_=(0,i.useRef)(null),{href:m,as:O,children:j,prefetch:R=null,passHref:E,replace:T,shallow:w,scroll:S,onClick:M,onMouseEnter:A,onTouchStart:C,legacyBehavior:N=!1,onNavigate:x,ref:U,unstable_dynamicOnHover:L}=e,D=function(e,t){if(null==e)return{};var r,n,l=function(e,t){if(null==e)return{};var r,n,l={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(l[r]=e[r]);return l}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(l[r]=e[r])}return l}(e,n);t=j,N&&("string"==typeof t||"number"==typeof t)&&(t=(0,u.jsx)("a",{children:t}));let I=i.default.useContext(f.AppRouterContext),k=!1!==R,H=null===R?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:F,as:K}=i.default.useMemo(()=>{let e=v(m);return{href:e,as:O?v(O):e}},[m,O]);N&&(r=i.default.Children.only(t));let B=N?r&&"object"==typeof r&&r.ref:U,z=i.default.useCallback(e=>(null!==I&&(_.current=(0,y.mountLinkInstance)(e,F,I,H,k,c)),()=>{_.current&&((0,y.unmountLinkForCurrentNavigation)(_.current),_.current=null),(0,y.unmountPrefetchableInstance)(e)}),[k,F,I,H,c]),G={ref:(0,d.useMergedRef)(z,B),onClick(e){N||"function"!=typeof M||M(e),N&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,n,l,a,o){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,g.isLocalURL)(t)){l&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,b.dispatchNavigateAction)(r||t,l?"replace":"push",null==a||a,n.current)})}}(e,F,K,_,T,S,x))},onMouseEnter(e){N||"function"!=typeof A||A(e),N&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&k&&(0,y.onNavigationIntent)(e.currentTarget,!0===L)},onTouchStart:function(e){N||"function"!=typeof C||C(e),N&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&k&&(0,y.onNavigationIntent)(e.currentTarget,!0===L)}};return(0,p.isAbsoluteUrl)(K)?G.href=K:N&&!E&&("a"!==r.type||"href"in r.props)||(G.href=(0,h.addBasePath)(K)),l=N?i.default.cloneElement(r,G):(0,u.jsx)("a",a(a(a({},D),G),{},{children:t})),(0,u.jsx)(P.Provider,{value:o,children:l})}r(41668);let P=(0,i.createContext)(y.IDLE_LINK_STATUS),m=()=>(0,i.useContext)(P);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};
"use strict";exports.id=7879,exports.ids=[7879],exports.modules={144:(t,e)=>{var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;e.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var r=e.shift();if(r){if("object"!=typeof r)throw TypeError(r+"must be non-object");for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}}return t},e.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var n={arraySet:function(t,e,r,n,o){if(e.subarray&&t.subarray)return void t.set(e.subarray(r,r+n),o);for(var i=0;i<n;i++)t[o+i]=e[r+i]},flattenChunks:function(t){var e,r,n,o,i,a;for(e=0,n=0,r=t.length;e<r;e++)n+=t[e].length;for(e=0,a=new Uint8Array(n),o=0,r=t.length;e<r;e++)i=t[e],a.set(i,o),o+=i.length;return a}},o={arraySet:function(t,e,r,n,o){for(var i=0;i<n;i++)t[o+i]=e[r+i]},flattenChunks:function(t){return[].concat.apply([],t)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,n)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,o))},e.setTyped(r)},2714:(t,e,r)=>{var n,o=r(144),i=r(22500),a=r(62328),s=r(53134),u=r(46937),c=573;function h(t,e){return t.msg=u[e],e}function l(t){return(t<<1)-9*(t>4)}function f(t){for(var e=t.length;--e>=0;)t[e]=0}function d(t){var e=t.state,r=e.pending;r>t.avail_out&&(r=t.avail_out),0!==r&&(o.arraySet(t.output,e.pending_buf,e.pending_out,r,t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending&&(e.pending_out=0))}function p(t,e){i._tr_flush_block(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,d(t.strm)}function g(t,e){t.pending_buf[t.pending++]=e}function y(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function v(t,e){var r,n,o=t.max_chain_length,i=t.strstart,a=t.prev_length,s=t.nice_match,u=t.strstart>t.w_size-262?t.strstart-(t.w_size-262):0,c=t.window,h=t.w_mask,l=t.prev,f=t.strstart+258,d=c[i+a-1],p=c[i+a];t.prev_length>=t.good_match&&(o>>=2),s>t.lookahead&&(s=t.lookahead);do{if(c[(r=e)+a]!==p||c[r+a-1]!==d||c[r]!==c[i]||c[++r]!==c[i+1])continue;i+=2,r++;do;while(c[++i]===c[++r]&&c[++i]===c[++r]&&c[++i]===c[++r]&&c[++i]===c[++r]&&c[++i]===c[++r]&&c[++i]===c[++r]&&c[++i]===c[++r]&&c[++i]===c[++r]&&i<f);if(n=258-(f-i),i=f-258,n>a){if(t.match_start=e,a=n,n>=s)break;d=c[i+a-1],p=c[i+a]}}while((e=l[e&h])>u&&0!=--o);return a<=t.lookahead?a:t.lookahead}function m(t){var e,r,n,i,u,c=t.w_size;do{if(i=t.window_size-t.lookahead-t.strstart,t.strstart>=c+(c-262)){o.arraySet(t.window,t.window,c,c,0),t.match_start-=c,t.strstart-=c,t.block_start-=c,e=r=t.hash_size;do n=t.head[--e],t.head[e]=n>=c?n-c:0;while(--r);e=r=c;do n=t.prev[--e],t.prev[e]=n>=c?n-c:0;while(--r);i+=c}if(0===t.strm.avail_in)break;if(r=function(t,e,r,n){var i=t.avail_in;return(i>n&&(i=n),0===i)?0:(t.avail_in-=i,o.arraySet(e,t.input,t.next_in,i,r),1===t.state.wrap?t.adler=a(t.adler,e,i,r):2===t.state.wrap&&(t.adler=s(t.adler,e,i,r)),t.next_in+=i,t.total_in+=i,i)}(t.strm,t.window,t.strstart+t.lookahead,i),t.lookahead+=r,t.lookahead+t.insert>=3)for(u=t.strstart-t.insert,t.ins_h=t.window[u],t.ins_h=(t.ins_h<<t.hash_shift^t.window[u+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[u+3-1])&t.hash_mask,t.prev[u&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=u,u++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<262&&0!==t.strm.avail_in)}function b(t,e){for(var r,n;;){if(t.lookahead<262){if(m(t),t.lookahead<262&&0===e)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-262&&(t.match_length=v(t,r)),t.match_length>=3)if(n=i._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart;while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else n=i._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(p(t,!1),0===t.strm.avail_out))return 1}return(t.insert=t.strstart<2?t.strstart:2,4===e)?(p(t,!0),0===t.strm.avail_out)?3:4:t.last_lit&&(p(t,!1),0===t.strm.avail_out)?1:2}function x(t,e){for(var r,n,o;;){if(t.lookahead<262){if(m(t),t.lookahead<262&&0===e)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-262&&(t.match_length=v(t,r),t.match_length<=5&&(1===t.strategy||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){o=t.strstart+t.lookahead-3,n=i._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do++t.strstart<=o&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart);while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,n&&(p(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((n=i._tr_tally(t,0,t.window[t.strstart-1]))&&p(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return(t.match_available&&(n=i._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e)?(p(t,!0),0===t.strm.avail_out)?3:4:t.last_lit&&(p(t,!1),0===t.strm.avail_out)?1:2}function w(t,e,r,n,o){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=n,this.func=o}function F(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new o.Buf16(2*c),this.dyn_dtree=new o.Buf16(122),this.bl_tree=new o.Buf16(78),f(this.dyn_ltree),f(this.dyn_dtree),f(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new o.Buf16(16),this.heap=new o.Buf16(573),f(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new o.Buf16(573),f(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function S(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:113,t.adler=+(2!==e.wrap),e.last_flush=0,i._tr_init(e),0):h(t,-2)}function C(t){var e,r=S(t);return 0===r&&((e=t.state).window_size=2*e.w_size,f(e.head),e.max_lazy_match=n[e.level].max_lazy,e.good_match=n[e.level].good_length,e.nice_match=n[e.level].nice_length,e.max_chain_length=n[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),r}function k(t,e,r,n,i,a){if(!t)return -2;var s=1;if(-1===e&&(e=6),n<0?(s=0,n=-n):n>15&&(s=2,n-=16),i<1||i>9||8!==r||n<8||n>15||e<0||e>9||a<0||a>4)return h(t,-2);8===n&&(n=9);var u=new F;return t.state=u,u.strm=t,u.wrap=s,u.gzhead=null,u.w_bits=n,u.w_size=1<<u.w_bits,u.w_mask=u.w_size-1,u.hash_bits=i+7,u.hash_size=1<<u.hash_bits,u.hash_mask=u.hash_size-1,u.hash_shift=~~((u.hash_bits+3-1)/3),u.window=new o.Buf8(2*u.w_size),u.head=new o.Buf16(u.hash_size),u.prev=new o.Buf16(u.w_size),u.lit_bufsize=1<<i+6,u.pending_buf_size=4*u.lit_bufsize,u.pending_buf=new o.Buf8(u.pending_buf_size),u.d_buf=+u.lit_bufsize,u.l_buf=3*u.lit_bufsize,u.level=e,u.strategy=a,u.method=r,C(t)}n=[new w(0,0,0,0,function(t,e){var r=65535;for(65535>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(m(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+r;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,p(t,!1),0===t.strm.avail_out)||t.strstart-t.block_start>=t.w_size-262&&(p(t,!1),0===t.strm.avail_out))return 1}return(t.insert=0,4===e)?(p(t,!0),0===t.strm.avail_out)?3:4:(t.strstart>t.block_start&&(p(t,!1),t.strm.avail_out),1)}),new w(4,4,8,4,b),new w(4,5,16,8,b),new w(4,6,32,32,b),new w(4,4,16,16,x),new w(8,16,32,32,x),new w(8,16,128,128,x),new w(8,32,128,256,x),new w(32,128,258,1024,x),new w(32,258,258,4096,x)],e.deflateInit=function(t,e){return k(t,e,8,15,8,0)},e.deflateInit2=k,e.deflateReset=C,e.deflateResetKeep=S,e.deflateSetHeader=function(t,e){return t&&t.state&&2===t.state.wrap?(t.state.gzhead=e,0):-2},e.deflate=function(t,e){if(!t||!t.state||e>5||e<0)return t?h(t,-2):-2;if(o=t.state,!t.output||!t.input&&0!==t.avail_in||666===o.status&&4!==e)return h(t,0===t.avail_out?-5:-2);if(o.strm=t,r=o.last_flush,o.last_flush=e,42===o.status)if(2===o.wrap)t.adler=0,g(o,31),g(o,139),g(o,8),o.gzhead?(g(o,+!!o.gzhead.text+2*!!o.gzhead.hcrc+4*!!o.gzhead.extra+8*!!o.gzhead.name+16*!!o.gzhead.comment),g(o,255&o.gzhead.time),g(o,o.gzhead.time>>8&255),g(o,o.gzhead.time>>16&255),g(o,o.gzhead.time>>24&255),g(o,9===o.level?2:4*(o.strategy>=2||o.level<2)),g(o,255&o.gzhead.os),o.gzhead.extra&&o.gzhead.extra.length&&(g(o,255&o.gzhead.extra.length),g(o,o.gzhead.extra.length>>8&255)),o.gzhead.hcrc&&(t.adler=s(t.adler,o.pending_buf,o.pending,0)),o.gzindex=0,o.status=69):(g(o,0),g(o,0),g(o,0),g(o,0),g(o,0),g(o,9===o.level?2:4*(o.strategy>=2||o.level<2)),g(o,3),o.status=113);else{var r,o,a,u,c=8+(o.w_bits-8<<4)<<8,v=-1;c|=(o.strategy>=2||o.level<2?0:o.level<6?1:6===o.level?2:3)<<6,0!==o.strstart&&(c|=32),c+=31-c%31,o.status=113,y(o,c),0!==o.strstart&&(y(o,t.adler>>>16),y(o,65535&t.adler)),t.adler=1}if(69===o.status)if(o.gzhead.extra){for(a=o.pending;o.gzindex<(65535&o.gzhead.extra.length)&&(o.pending!==o.pending_buf_size||(o.gzhead.hcrc&&o.pending>a&&(t.adler=s(t.adler,o.pending_buf,o.pending-a,a)),d(t),a=o.pending,o.pending!==o.pending_buf_size));)g(o,255&o.gzhead.extra[o.gzindex]),o.gzindex++;o.gzhead.hcrc&&o.pending>a&&(t.adler=s(t.adler,o.pending_buf,o.pending-a,a)),o.gzindex===o.gzhead.extra.length&&(o.gzindex=0,o.status=73)}else o.status=73;if(73===o.status)if(o.gzhead.name){a=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>a&&(t.adler=s(t.adler,o.pending_buf,o.pending-a,a)),d(t),a=o.pending,o.pending===o.pending_buf_size)){u=1;break}u=o.gzindex<o.gzhead.name.length?255&o.gzhead.name.charCodeAt(o.gzindex++):0,g(o,u)}while(0!==u);o.gzhead.hcrc&&o.pending>a&&(t.adler=s(t.adler,o.pending_buf,o.pending-a,a)),0===u&&(o.gzindex=0,o.status=91)}else o.status=91;if(91===o.status)if(o.gzhead.comment){a=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>a&&(t.adler=s(t.adler,o.pending_buf,o.pending-a,a)),d(t),a=o.pending,o.pending===o.pending_buf_size)){u=1;break}u=o.gzindex<o.gzhead.comment.length?255&o.gzhead.comment.charCodeAt(o.gzindex++):0,g(o,u)}while(0!==u);o.gzhead.hcrc&&o.pending>a&&(t.adler=s(t.adler,o.pending_buf,o.pending-a,a)),0===u&&(o.status=103)}else o.status=103;if(103===o.status&&(o.gzhead.hcrc?(o.pending+2>o.pending_buf_size&&d(t),o.pending+2<=o.pending_buf_size&&(g(o,255&t.adler),g(o,t.adler>>8&255),t.adler=0,o.status=113)):o.status=113),0!==o.pending){if(d(t),0===t.avail_out)return o.last_flush=-1,0}else if(0===t.avail_in&&l(e)<=l(r)&&4!==e)return h(t,-5);if(666===o.status&&0!==t.avail_in)return h(t,-5);if(0!==t.avail_in||0!==o.lookahead||0!==e&&666!==o.status){var b=2===o.strategy?function(t,e){for(var r;;){if(0===t.lookahead&&(m(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,r=i._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(p(t,!1),0===t.strm.avail_out))return 1}return(t.insert=0,4===e)?(p(t,!0),0===t.strm.avail_out)?3:4:t.last_lit&&(p(t,!1),0===t.strm.avail_out)?1:2}(o,e):3===o.strategy?function(t,e){for(var r,n,o,a,s=t.window;;){if(t.lookahead<=258){if(m(t),t.lookahead<=258&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(n=s[o=t.strstart-1])===s[++o]&&n===s[++o]&&n===s[++o]){a=t.strstart+258;do;while(n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&n===s[++o]&&o<a);t.match_length=258-(a-o),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(r=i._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=i._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(p(t,!1),0===t.strm.avail_out))return 1}return(t.insert=0,4===e)?(p(t,!0),0===t.strm.avail_out)?3:4:t.last_lit&&(p(t,!1),0===t.strm.avail_out)?1:2}(o,e):n[o.level].func(o,e);if((3===b||4===b)&&(o.status=666),1===b||3===b)return 0===t.avail_out&&(o.last_flush=-1),0;if(2===b&&(1===e?i._tr_align(o):5!==e&&(i._tr_stored_block(o,0,0,!1),3===e&&(f(o.head),0===o.lookahead&&(o.strstart=0,o.block_start=0,o.insert=0))),d(t),0===t.avail_out))return o.last_flush=-1,0}return 4!==e?0:o.wrap<=0?1:(2===o.wrap?(g(o,255&t.adler),g(o,t.adler>>8&255),g(o,t.adler>>16&255),g(o,t.adler>>24&255),g(o,255&t.total_in),g(o,t.total_in>>8&255),g(o,t.total_in>>16&255),g(o,t.total_in>>24&255)):(y(o,t.adler>>>16),y(o,65535&t.adler)),d(t),o.wrap>0&&(o.wrap=-o.wrap),+(0===o.pending))},e.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&103!==e&&113!==e&&666!==e?h(t,-2):(t.state=null,113===e?h(t,-3):0):-2},e.deflateSetDictionary=function(t,e){var r,n,i,s,u,c,h,l,d=e.length;if(!t||!t.state||2===(s=(r=t.state).wrap)||1===s&&42!==r.status||r.lookahead)return -2;for(1===s&&(t.adler=a(t.adler,e,d,0)),r.wrap=0,d>=r.w_size&&(0===s&&(f(r.head),r.strstart=0,r.block_start=0,r.insert=0),l=new o.Buf8(r.w_size),o.arraySet(l,e,d-r.w_size,r.w_size,0),e=l,d=r.w_size),u=t.avail_in,c=t.next_in,h=t.input,t.avail_in=d,t.next_in=0,t.input=e,m(r);r.lookahead>=3;){n=r.strstart,i=r.lookahead-2;do r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+3-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++;while(--i);r.strstart=n,r.lookahead=2,m(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,t.next_in=c,t.input=h,t.avail_in=u,r.wrap=s,0},e.deflateInfo="pako deflate (from Nodeca project)"},2980:(t,e,r)=>{var n=r(2714),o=r(144),i=r(25923),a=r(46937),s=r(66931),u=Object.prototype.toString;function c(t){if(!(this instanceof c))return new c(t);this.options=o.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var e,r=this.options;r.raw&&r.windowBits>0?r.windowBits=-r.windowBits:r.gzip&&r.windowBits>0&&r.windowBits<16&&(r.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0;var h=n.deflateInit2(this.strm,r.level,r.method,r.windowBits,r.memLevel,r.strategy);if(0!==h)throw Error(a[h]);if(r.header&&n.deflateSetHeader(this.strm,r.header),r.dictionary){if(e="string"==typeof r.dictionary?i.string2buf(r.dictionary):"[object ArrayBuffer]"===u.call(r.dictionary)?new Uint8Array(r.dictionary):r.dictionary,0!==(h=n.deflateSetDictionary(this.strm,e)))throw Error(a[h]);this._dict_set=!0}}function h(t,e){var r=new c(e);if(r.push(t,!0),r.err)throw r.msg||a[r.err];return r.result}c.prototype.push=function(t,e){var r,a,s=this.strm,c=this.options.chunkSize;if(this.ended)return!1;a=e===~~e?e:4*(!0===e),"string"==typeof t?s.input=i.string2buf(t):"[object ArrayBuffer]"===u.call(t)?s.input=new Uint8Array(t):s.input=t,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new o.Buf8(c),s.next_out=0,s.avail_out=c),1!==(r=n.deflate(s,a))&&0!==r)return this.onEnd(r),this.ended=!0,!1;(0===s.avail_out||0===s.avail_in&&(4===a||2===a))&&("string"===this.options.to?this.onData(i.buf2binstring(o.shrinkBuf(s.output,s.next_out))):this.onData(o.shrinkBuf(s.output,s.next_out)))}while((s.avail_in>0||0===s.avail_out)&&1!==r);return 4===a?(r=n.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r):(2===a&&(this.onEnd(0),s.avail_out=0),!0)},c.prototype.onData=function(t){this.chunks.push(t)},c.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},e.Deflate=c,e.deflate=h,e.deflateRaw=function(t,e){return(e=e||{}).raw=!0,h(t,e)},e.gzip=function(t,e){return(e=e||{}).gzip=!0,h(t,e)}},22500:(t,e,r)=>{var n,o,i,a=r(144);function s(t){for(var e=t.length;--e>=0;)t[e]=0}var u=573,c=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],h=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],f=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],d=Array(576);s(d);var p=Array(60);s(p);var g=Array(512);s(g);var y=Array(256);s(y);var v=Array(29);s(v);var m=Array(30);function b(t,e,r,n,o){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=n,this.max_length=o,this.has_stree=t&&t.length}function x(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function w(t){return t<256?g[t]:g[256+(t>>>7)]}function F(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function S(t,e,r){t.bi_valid>16-r?(t.bi_buf|=e<<t.bi_valid&65535,F(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=r-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)}function C(t,e,r){S(t,r[2*e],r[2*e+1])}function k(t,e){var r=0;do r|=1&t,t>>>=1,r<<=1;while(--e>0);return r>>>1}function T(t,e,r){var n,o,i=Array(16),a=0;for(n=1;n<=15;n++)i[n]=a=a+r[n-1]<<1;for(o=0;o<=e;o++){var s=t[2*o+1];0!==s&&(t[2*o]=k(i[s]++,s))}}function O(t){var e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function A(t){t.bi_valid>8?F(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function P(t,e,r,n){var o=2*e,i=2*r;return t[o]<t[i]||t[o]===t[i]&&n[e]<=n[r]}function R(t,e,r){for(var n=t.heap[r],o=r<<1;o<=t.heap_len&&(o<t.heap_len&&P(e,t.heap[o+1],t.heap[o],t.depth)&&o++,!P(e,n,t.heap[o],t.depth));)t.heap[r]=t.heap[o],r=o,o<<=1;t.heap[r]=n}function N(t,e,r){var n,o,i,a,s=0;if(0!==t.last_lit)do n=t.pending_buf[t.d_buf+2*s]<<8|t.pending_buf[t.d_buf+2*s+1],o=t.pending_buf[t.l_buf+s],s++,0===n?C(t,o,e):(C(t,(i=y[o])+256+1,e),0!==(a=c[i])&&S(t,o-=v[i],a),C(t,i=w(--n),r),0!==(a=h[i])&&S(t,n-=m[i],a));while(s<t.last_lit);C(t,256,e)}function D(t,e){var r,n,o,i=e.dyn_tree,a=e.stat_desc.static_tree,s=e.stat_desc.has_stree,c=e.stat_desc.elems,h=-1;for(r=0,t.heap_len=0,t.heap_max=u;r<c;r++)0!==i[2*r]?(t.heap[++t.heap_len]=h=r,t.depth[r]=0):i[2*r+1]=0;for(;t.heap_len<2;)i[2*(o=t.heap[++t.heap_len]=h<2?++h:0)]=1,t.depth[o]=0,t.opt_len--,s&&(t.static_len-=a[2*o+1]);for(e.max_code=h,r=t.heap_len>>1;r>=1;r--)R(t,i,r);o=c;do r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],R(t,i,1),n=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=n,i[2*o]=i[2*r]+i[2*n],t.depth[o]=(t.depth[r]>=t.depth[n]?t.depth[r]:t.depth[n])+1,i[2*r+1]=i[2*n+1]=o,t.heap[1]=o++,R(t,i,1);while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var r,n,o,i,a,s,c=e.dyn_tree,h=e.max_code,l=e.stat_desc.static_tree,f=e.stat_desc.has_stree,d=e.stat_desc.extra_bits,p=e.stat_desc.extra_base,g=e.stat_desc.max_length,y=0;for(i=0;i<=15;i++)t.bl_count[i]=0;for(c[2*t.heap[t.heap_max]+1]=0,r=t.heap_max+1;r<u;r++)(i=c[2*c[2*(n=t.heap[r])+1]+1]+1)>g&&(i=g,y++),c[2*n+1]=i,!(n>h)&&(t.bl_count[i]++,a=0,n>=p&&(a=d[n-p]),s=c[2*n],t.opt_len+=s*(i+a),f&&(t.static_len+=s*(l[2*n+1]+a)));if(0!==y){do{for(i=g-1;0===t.bl_count[i];)i--;t.bl_count[i]--,t.bl_count[i+1]+=2,t.bl_count[g]--,y-=2}while(y>0);for(i=g;0!==i;i--)for(n=t.bl_count[i];0!==n;)!((o=t.heap[--r])>h)&&(c[2*o+1]!==i&&(t.opt_len+=(i-c[2*o+1])*c[2*o],c[2*o+1]=i),n--)}}(t,e),T(i,h,t.bl_count)}function j(t,e,r){var n,o,i=-1,a=e[1],s=0,u=7,c=4;for(0===a&&(u=138,c=3),e[(r+1)*2+1]=65535,n=0;n<=r;n++)o=a,a=e[(n+1)*2+1],++s<u&&o===a||(s<c?t.bl_tree[2*o]+=s:0!==o?(o!==i&&t.bl_tree[2*o]++,t.bl_tree[32]++):s<=10?t.bl_tree[34]++:t.bl_tree[36]++,s=0,i=o,0===a?(u=138,c=3):o===a?(u=6,c=3):(u=7,c=4))}function z(t,e,r){var n,o,i=-1,a=e[1],s=0,u=7,c=4;for(0===a&&(u=138,c=3),n=0;n<=r;n++)if(o=a,a=e[(n+1)*2+1],!(++s<u)||o!==a){if(s<c)do C(t,o,t.bl_tree);while(0!=--s);else 0!==o?(o!==i&&(C(t,o,t.bl_tree),s--),C(t,16,t.bl_tree),S(t,s-3,2)):s<=10?(C(t,17,t.bl_tree),S(t,s-3,3)):(C(t,18,t.bl_tree),S(t,s-11,7));s=0,i=o,0===a?(u=138,c=3):o===a?(u=6,c=3):(u=7,c=4)}}s(m);var B=!1;function M(t,e,r,n){S(t,0+ +!!n,3),A(t),F(t,r),F(t,~r),a.arraySet(t.pending_buf,t.window,e,r,t.pending),t.pending+=r}e._tr_init=function(t){B||(!function(){var t,e,r,a,s,u=Array(16);for(a=0,r=0;a<28;a++)for(t=0,v[a]=r;t<1<<c[a];t++)y[r++]=a;for(y[r-1]=a,s=0,a=0;a<16;a++)for(t=0,m[a]=s;t<1<<h[a];t++)g[s++]=a;for(s>>=7;a<30;a++)for(t=0,m[a]=s<<7;t<1<<h[a]-7;t++)g[256+s++]=a;for(e=0;e<=15;e++)u[e]=0;for(t=0;t<=143;)d[2*t+1]=8,t++,u[8]++;for(;t<=255;)d[2*t+1]=9,t++,u[9]++;for(;t<=279;)d[2*t+1]=7,t++,u[7]++;for(;t<=287;)d[2*t+1]=8,t++,u[8]++;for(T(d,287,u),t=0;t<30;t++)p[2*t+1]=5,p[2*t]=k(t,5);n=new b(d,c,257,286,15),o=new b(p,h,0,30,15),i=new b([],l,0,19,7)}(),B=!0),t.l_desc=new x(t.dyn_ltree,n),t.d_desc=new x(t.dyn_dtree,o),t.bl_desc=new x(t.bl_tree,i),t.bi_buf=0,t.bi_valid=0,O(t)},e._tr_stored_block=M,e._tr_flush_block=function(t,e,r,n){var o,i,a=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,r=0xf3ffc07f;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),D(t,t.l_desc),D(t,t.d_desc),a=function(t){var e;for(j(t,t.dyn_ltree,t.l_desc.max_code),j(t,t.dyn_dtree,t.d_desc.max_code),D(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*f[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),o=t.opt_len+3+7>>>3,(i=t.static_len+3+7>>>3)<=o&&(o=i)):o=i=r+5,r+4<=o&&-1!==e?M(t,e,r,n):4===t.strategy||i===o?(S(t,2+ +!!n,3),N(t,d,p)):(S(t,4+ +!!n,3),function(t,e,r,n){var o;for(S(t,e-257,5),S(t,r-1,5),S(t,n-4,4),o=0;o<n;o++)S(t,t.bl_tree[2*f[o]+1],3);z(t,t.dyn_ltree,e-1),z(t,t.dyn_dtree,r-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,a+1),N(t,t.dyn_ltree,t.dyn_dtree)),O(t),n&&A(t)},e._tr_tally=function(t,e,r){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&r,t.last_lit++,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[(y[r]+256+1)*2]++,t.dyn_dtree[2*w(e)]++),t.last_lit===t.lit_bufsize-1},e._tr_align=function(t){S(t,2,3),C(t,256,d),16===t.bi_valid?(F(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},23408:(t,e,r)=>{var n=r(77354),o=r(144),i=r(25923),a=r(76672),s=r(46937),u=r(66931),c=r(75153),h=Object.prototype.toString;function l(t){if(!(this instanceof l))return new l(t);this.options=o.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),e.windowBits>=0&&e.windowBits<16&&!(t&&t.windowBits)&&(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&(15&e.windowBits)==0&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new u,this.strm.avail_out=0;var r=n.inflateInit2(this.strm,e.windowBits);if(r!==a.Z_OK||(this.header=new c,n.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=i.string2buf(e.dictionary):"[object ArrayBuffer]"===h.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw)&&(r=n.inflateSetDictionary(this.strm,e.dictionary))!==a.Z_OK))throw Error(s[r])}function f(t,e){var r=new l(e);if(r.push(t,!0),r.err)throw r.msg||s[r.err];return r.result}l.prototype.push=function(t,e){var r,s,u,c,l,f=this.strm,d=this.options.chunkSize,p=this.options.dictionary,g=!1;if(this.ended)return!1;s=e===~~e?e:!0===e?a.Z_FINISH:a.Z_NO_FLUSH,"string"==typeof t?f.input=i.binstring2buf(t):"[object ArrayBuffer]"===h.call(t)?f.input=new Uint8Array(t):f.input=t,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new o.Buf8(d),f.next_out=0,f.avail_out=d),(r=n.inflate(f,a.Z_NO_FLUSH))===a.Z_NEED_DICT&&p&&(r=n.inflateSetDictionary(this.strm,p)),r===a.Z_BUF_ERROR&&!0===g&&(r=a.Z_OK,g=!1),r!==a.Z_STREAM_END&&r!==a.Z_OK)return this.onEnd(r),this.ended=!0,!1;f.next_out&&(0===f.avail_out||r===a.Z_STREAM_END||0===f.avail_in&&(s===a.Z_FINISH||s===a.Z_SYNC_FLUSH))&&("string"===this.options.to?(u=i.utf8border(f.output,f.next_out),c=f.next_out-u,l=i.buf2string(f.output,u),f.next_out=c,f.avail_out=d-c,c&&o.arraySet(f.output,f.output,u,c,0),this.onData(l)):this.onData(o.shrinkBuf(f.output,f.next_out))),0===f.avail_in&&0===f.avail_out&&(g=!0)}while((f.avail_in>0||0===f.avail_out)&&r!==a.Z_STREAM_END);return(r===a.Z_STREAM_END&&(s=a.Z_FINISH),s===a.Z_FINISH)?(r=n.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===a.Z_OK):(s===a.Z_SYNC_FLUSH&&(this.onEnd(a.Z_OK),f.avail_out=0),!0)},l.prototype.onData=function(t){this.chunks.push(t)},l.prototype.onEnd=function(t){t===a.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},e.Inflate=l,e.inflate=f,e.inflateRaw=function(t,e){return(e=e||{}).raw=!0,f(t,e)},e.ungzip=f},25923:(t,e,r)=>{var n=r(144),o=!0,i=!0;try{String.fromCharCode.apply(null,[0])}catch(t){o=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){i=!1}for(var a=new n.Buf8(256),s=0;s<256;s++)a[s]=s>=252?6:s>=248?5:s>=240?4:s>=224?3:s>=192?2:1;function u(t,e){if(e<65534&&(t.subarray&&i||!t.subarray&&o))return String.fromCharCode.apply(null,n.shrinkBuf(t,e));for(var r="",a=0;a<e;a++)r+=String.fromCharCode(t[a]);return r}a[254]=a[254]=1,e.string2buf=function(t){var e,r,o,i,a,s=t.length,u=0;for(i=0;i<s;i++)(64512&(r=t.charCodeAt(i)))==55296&&i+1<s&&(64512&(o=t.charCodeAt(i+1)))==56320&&(r=65536+(r-55296<<10)+(o-56320),i++),u+=r<128?1:r<2048?2:r<65536?3:4;for(a=0,e=new n.Buf8(u),i=0;a<u;i++)(64512&(r=t.charCodeAt(i)))==55296&&i+1<s&&(64512&(o=t.charCodeAt(i+1)))==56320&&(r=65536+(r-55296<<10)+(o-56320),i++),r<128?e[a++]=r:(r<2048?e[a++]=192|r>>>6:(r<65536?e[a++]=224|r>>>12:(e[a++]=240|r>>>18,e[a++]=128|r>>>12&63),e[a++]=128|r>>>6&63),e[a++]=128|63&r);return e},e.buf2binstring=function(t){return u(t,t.length)},e.binstring2buf=function(t){for(var e=new n.Buf8(t.length),r=0,o=e.length;r<o;r++)e[r]=t.charCodeAt(r);return e},e.buf2string=function(t,e){var r,n,o,i,s=e||t.length,c=Array(2*s);for(n=0,r=0;r<s;){if((o=t[r++])<128){c[n++]=o;continue}if((i=a[o])>4){c[n++]=65533,r+=i-1;continue}for(o&=2===i?31:3===i?15:7;i>1&&r<s;)o=o<<6|63&t[r++],i--;if(i>1){c[n++]=65533;continue}o<65536?c[n++]=o:(o-=65536,c[n++]=55296|o>>10&1023,c[n++]=56320|1023&o)}return u(c,n)},e.utf8border=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;r>=0&&(192&t[r])==128;)r--;return r<0||0===r?e:r+a[t[r]]>e?r:e}},43393:(t,e,r)=>{var n=r(144),o=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],i=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],s=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(t,e,r,u,c,h,l,f){var d,p,g,y,v,m,b,x,w,F=f.bits,S=0,C=0,k=0,T=0,O=0,A=0,P=0,R=0,N=0,D=0,j=null,z=0,B=new n.Buf16(16),M=new n.Buf16(16),V=null,I=0;for(S=0;S<=15;S++)B[S]=0;for(C=0;C<u;C++)B[e[r+C]]++;for(T=15,O=F;T>=1&&0===B[T];T--);if(O>T&&(O=T),0===T)return c[h++]=0x1400000,c[h++]=0x1400000,f.bits=1,0;for(k=1;k<T&&0===B[k];k++);for(O<k&&(O=k),R=1,S=1;S<=15;S++)if(R<<=1,(R-=B[S])<0)return -1;if(R>0&&(0===t||1!==T))return -1;for(S=1,M[1]=0;S<15;S++)M[S+1]=M[S]+B[S];for(C=0;C<u;C++)0!==e[r+C]&&(l[M[e[r+C]]++]=C);if(0===t?(j=V=l,m=19):1===t?(j=o,z-=257,V=i,I-=257,m=256):(j=a,V=s,m=-1),D=0,C=0,S=k,v=h,A=O,P=0,g=-1,y=(N=1<<O)-1,1===t&&N>852||2===t&&N>592)return 1;for(;;){b=S-P,l[C]<m?(x=0,w=l[C]):l[C]>m?(x=V[I+l[C]],w=j[z+l[C]]):(x=96,w=0),d=1<<S-P,k=p=1<<A;do c[v+(D>>P)+(p-=d)]=b<<24|x<<16|w;while(0!==p);for(d=1<<S-1;D&d;)d>>=1;if(0!==d?(D&=d-1,D+=d):D=0,C++,0==--B[S]){if(S===T)break;S=e[r+l[C]]}if(S>O&&(D&y)!==g){for(0===P&&(P=O),v+=k,R=1<<(A=S-P);A+P<T&&!((R-=B[A+P])<=0);)A++,R<<=1;if(N+=1<<A,1===t&&N>852||2===t&&N>592)return 1;c[g=D&y]=O<<24|A<<16|v-h}}return 0!==D&&(c[v+D]=S-P<<24|4194304),f.bits=O,0}},45232:t=>{t.exports=function(t,e){var r,n,o,i,a,s,u,c,h,l,f,d,p,g,y,v,m,b,x,w,F,S,C,k,T;r=t.state,n=t.next_in,k=t.input,o=n+(t.avail_in-5),i=t.next_out,T=t.output,a=i-(e-t.avail_out),s=i+(t.avail_out-257),u=r.dmax,c=r.wsize,h=r.whave,l=r.wnext,f=r.window,d=r.hold,p=r.bits,g=r.lencode,y=r.distcode,v=(1<<r.lenbits)-1,m=(1<<r.distbits)-1;t:do for(p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),b=g[d&v];;){if(d>>>=x=b>>>24,p-=x,0==(x=b>>>16&255))T[i++]=65535&b;else if(16&x)for(w=65535&b,(x&=15)&&(p<x&&(d+=k[n++]<<p,p+=8),w+=d&(1<<x)-1,d>>>=x,p-=x),p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),b=y[d&m];;){if(d>>>=x=b>>>24,p-=x,16&(x=b>>>16&255)){if(F=65535&b,p<(x&=15)&&(d+=k[n++]<<p,(p+=8)<x&&(d+=k[n++]<<p,p+=8)),(F+=d&(1<<x)-1)>u){t.msg="invalid distance too far back",r.mode=30;break t}if(d>>>=x,p-=x,F>(x=i-a)){if((x=F-x)>h&&r.sane){t.msg="invalid distance too far back",r.mode=30;break t}if(S=0,C=f,0===l){if(S+=c-x,x<w){w-=x;do T[i++]=f[S++];while(--x);S=i-F,C=T}}else if(l<x){if(S+=c+l-x,(x-=l)<w){w-=x;do T[i++]=f[S++];while(--x);if(S=0,l<w){w-=x=l;do T[i++]=f[S++];while(--x);S=i-F,C=T}}}else if(S+=l-x,x<w){w-=x;do T[i++]=f[S++];while(--x);S=i-F,C=T}for(;w>2;)T[i++]=C[S++],T[i++]=C[S++],T[i++]=C[S++],w-=3;w&&(T[i++]=C[S++],w>1&&(T[i++]=C[S++]))}else{S=i-F;do T[i++]=T[S++],T[i++]=T[S++],T[i++]=T[S++],w-=3;while(w>2);w&&(T[i++]=T[S++],w>1&&(T[i++]=T[S++]))}}else if((64&x)==0){b=y[(65535&b)+(d&(1<<x)-1)];continue}else{t.msg="invalid distance code",r.mode=30;break t}break}else if((64&x)==0){b=g[(65535&b)+(d&(1<<x)-1)];continue}else if(32&x){r.mode=12;break t}else{t.msg="invalid literal/length code",r.mode=30;break t}break}while(n<o&&i<s);n-=w=p>>3,p-=w<<3,d&=(1<<p)-1,t.next_in=n,t.next_out=i,t.avail_in=n<o?5+(o-n):5-(n-o),t.avail_out=i<s?257+(s-i):257-(i-s),r.hold=d,r.bits=p}},46937:t=>{t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},53134:t=>{var e=function(){for(var t,e=[],r=0;r<256;r++){t=r;for(var n=0;n<8;n++)t=1&t?0xedb88320^t>>>1:t>>>1;e[r]=t}return e}();t.exports=function(t,r,n,o){var i=o+n;t^=-1;for(var a=o;a<i;a++)t=t>>>8^e[(t^r[a])&255];return -1^t}},62328:t=>{t.exports=function(t,e,r,n){for(var o=65535&t,i=t>>>16&65535,a=0;0!==r;){a=r>2e3?2e3:r,r-=a;do i=i+(o=o+e[n++]|0)|0;while(--a);o%=65521,i%=65521}return o|i<<16}},66931:t=>{t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},75153:t=>{t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},76672:t=>{t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},77354:(t,e,r)=>{var n,o,i=r(144),a=r(62328),s=r(53134),u=r(45232),c=r(43393);function h(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function l(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new i.Buf16(320),this.work=new i.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function f(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=1,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new i.Buf32(852),e.distcode=e.distdyn=new i.Buf32(592),e.sane=1,e.back=-1,0):-2}function d(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,f(t)):-2}function p(t,e){var r,n;return t&&t.state?(n=t.state,e<0?(r=0,e=-e):(r=(e>>4)+1,e<48&&(e&=15)),e&&(e<8||e>15))?-2:(null!==n.window&&n.wbits!==e&&(n.window=null),n.wrap=r,n.wbits=e,d(t)):-2}function g(t,e){var r,n;return t?(t.state=n=new l,n.window=null,0!==(r=p(t,e))&&(t.state=null),r):-2}var y=!0;function v(t,e,r,n){var o,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new i.Buf8(a.wsize)),n>=a.wsize?(i.arraySet(a.window,e,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((o=a.wsize-a.wnext)>n&&(o=n),i.arraySet(a.window,e,r-n,o,a.wnext),(n-=o)?(i.arraySet(a.window,e,r-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=o,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=o))),0}e.inflateReset=d,e.inflateReset2=p,e.inflateResetKeep=f,e.inflateInit=function(t){return g(t,15)},e.inflateInit2=g,e.inflate=function(t,e){var r,l,f,d,p,g,m,b,x,w,F,S,C,k,T,O,A,P,R,N,D,j,z,B,M,V=0,I=new i.Buf8(4),U=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return -2;12===(l=t.state).mode&&(l.mode=13),g=t.next_out,d=t.output,b=t.avail_out,p=t.next_in,f=t.input,m=t.avail_in,x=l.hold,w=l.bits,F=m,S=b,z=0;e:for(;;)switch(l.mode){case 1:if(0===l.wrap){l.mode=13;break}for(;w<16;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(2&l.wrap&&35615===x){l.check=0,I[0]=255&x,I[1]=x>>>8&255,l.check=s(l.check,I,2,0),x=0,w=0,l.mode=2;break}if(l.flags=0,l.head&&(l.head.done=!1),!(1&l.wrap)||(((255&x)<<8)+(x>>8))%31){t.msg="incorrect header check",l.mode=30;break}if((15&x)!=8){t.msg="unknown compression method",l.mode=30;break}if(x>>>=4,w-=4,j=(15&x)+8,0===l.wbits)l.wbits=j;else if(j>l.wbits){t.msg="invalid window size",l.mode=30;break}l.dmax=1<<j,t.adler=l.check=1,l.mode=512&x?10:12,x=0,w=0;break;case 2:for(;w<16;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(l.flags=x,(255&l.flags)!=8){t.msg="unknown compression method",l.mode=30;break}if(57344&l.flags){t.msg="unknown header flags set",l.mode=30;break}l.head&&(l.head.text=x>>8&1),512&l.flags&&(I[0]=255&x,I[1]=x>>>8&255,l.check=s(l.check,I,2,0)),x=0,w=0,l.mode=3;case 3:for(;w<32;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}l.head&&(l.head.time=x),512&l.flags&&(I[0]=255&x,I[1]=x>>>8&255,I[2]=x>>>16&255,I[3]=x>>>24&255,l.check=s(l.check,I,4,0)),x=0,w=0,l.mode=4;case 4:for(;w<16;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}l.head&&(l.head.xflags=255&x,l.head.os=x>>8),512&l.flags&&(I[0]=255&x,I[1]=x>>>8&255,l.check=s(l.check,I,2,0)),x=0,w=0,l.mode=5;case 5:if(1024&l.flags){for(;w<16;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}l.length=x,l.head&&(l.head.extra_len=x),512&l.flags&&(I[0]=255&x,I[1]=x>>>8&255,l.check=s(l.check,I,2,0)),x=0,w=0}else l.head&&(l.head.extra=null);l.mode=6;case 6:if(1024&l.flags&&((C=l.length)>m&&(C=m),C&&(l.head&&(j=l.head.extra_len-l.length,l.head.extra||(l.head.extra=Array(l.head.extra_len)),i.arraySet(l.head.extra,f,p,C,j)),512&l.flags&&(l.check=s(l.check,f,C,p)),m-=C,p+=C,l.length-=C),l.length))break e;l.length=0,l.mode=7;case 7:if(2048&l.flags){if(0===m)break e;C=0;do j=f[p+C++],l.head&&j&&l.length<65536&&(l.head.name+=String.fromCharCode(j));while(j&&C<m);if(512&l.flags&&(l.check=s(l.check,f,C,p)),m-=C,p+=C,j)break e}else l.head&&(l.head.name=null);l.length=0,l.mode=8;case 8:if(4096&l.flags){if(0===m)break e;C=0;do j=f[p+C++],l.head&&j&&l.length<65536&&(l.head.comment+=String.fromCharCode(j));while(j&&C<m);if(512&l.flags&&(l.check=s(l.check,f,C,p)),m-=C,p+=C,j)break e}else l.head&&(l.head.comment=null);l.mode=9;case 9:if(512&l.flags){for(;w<16;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(x!==(65535&l.check)){t.msg="header crc mismatch",l.mode=30;break}x=0,w=0}l.head&&(l.head.hcrc=l.flags>>9&1,l.head.done=!0),t.adler=l.check=0,l.mode=12;break;case 10:for(;w<32;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}t.adler=l.check=h(x),x=0,w=0,l.mode=11;case 11:if(0===l.havedict)return t.next_out=g,t.avail_out=b,t.next_in=p,t.avail_in=m,l.hold=x,l.bits=w,2;t.adler=l.check=1,l.mode=12;case 12:if(5===e||6===e)break e;case 13:if(l.last){x>>>=7&w,w-=7&w,l.mode=27;break}for(;w<3;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}switch(l.last=1&x,w-=1,3&(x>>>=1)){case 0:l.mode=14;break;case 1:if(y){for(n=new i.Buf32(512),o=new i.Buf32(32),r=0;r<144;)l.lens[r++]=8;for(;r<256;)l.lens[r++]=9;for(;r<280;)l.lens[r++]=7;for(;r<288;)l.lens[r++]=8;for(c(1,l.lens,0,288,n,0,l.work,{bits:9}),r=0;r<32;)l.lens[r++]=5;c(2,l.lens,0,32,o,0,l.work,{bits:5}),y=!1}if(l.lencode=n,l.lenbits=9,l.distcode=o,l.distbits=5,l.mode=20,6===e){x>>>=2,w-=2;break e}break;case 2:l.mode=17;break;case 3:t.msg="invalid block type",l.mode=30}x>>>=2,w-=2;break;case 14:for(x>>>=7&w,w-=7&w;w<32;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if((65535&x)!=(x>>>16^65535)){t.msg="invalid stored block lengths",l.mode=30;break}if(l.length=65535&x,x=0,w=0,l.mode=15,6===e)break e;case 15:l.mode=16;case 16:if(C=l.length){if(C>m&&(C=m),C>b&&(C=b),0===C)break e;i.arraySet(d,f,p,C,g),m-=C,p+=C,b-=C,g+=C,l.length-=C;break}l.mode=12;break;case 17:for(;w<14;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(l.nlen=(31&x)+257,w-=5,l.ndist=(31&(x>>>=5))+1,w-=5,l.ncode=(15&(x>>>=5))+4,x>>>=4,w-=4,l.nlen>286||l.ndist>30){t.msg="too many length or distance symbols",l.mode=30;break}l.have=0,l.mode=18;case 18:for(;l.have<l.ncode;){for(;w<3;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}l.lens[U[l.have++]]=7&x,x>>>=3,w-=3}for(;l.have<19;)l.lens[U[l.have++]]=0;if(l.lencode=l.lendyn,l.lenbits=7,B={bits:l.lenbits},z=c(0,l.lens,0,19,l.lencode,0,l.work,B),l.lenbits=B.bits,z){t.msg="invalid code lengths set",l.mode=30;break}l.have=0,l.mode=19;case 19:for(;l.have<l.nlen+l.ndist;){for(;O=(V=l.lencode[x&(1<<l.lenbits)-1])>>>24,A=V>>>16&255,P=65535&V,!(O<=w);){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(P<16)x>>>=O,w-=O,l.lens[l.have++]=P;else{if(16===P){for(M=O+2;w<M;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(x>>>=O,w-=O,0===l.have){t.msg="invalid bit length repeat",l.mode=30;break}j=l.lens[l.have-1],C=3+(3&x),x>>>=2,w-=2}else if(17===P){for(M=O+3;w<M;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}x>>>=O,w-=O,j=0,C=3+(7&x),x>>>=3,w-=3}else{for(M=O+7;w<M;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}x>>>=O,w-=O,j=0,C=11+(127&x),x>>>=7,w-=7}if(l.have+C>l.nlen+l.ndist){t.msg="invalid bit length repeat",l.mode=30;break}for(;C--;)l.lens[l.have++]=j}}if(30===l.mode)break;if(0===l.lens[256]){t.msg="invalid code -- missing end-of-block",l.mode=30;break}if(l.lenbits=9,B={bits:l.lenbits},z=c(1,l.lens,0,l.nlen,l.lencode,0,l.work,B),l.lenbits=B.bits,z){t.msg="invalid literal/lengths set",l.mode=30;break}if(l.distbits=6,l.distcode=l.distdyn,B={bits:l.distbits},z=c(2,l.lens,l.nlen,l.ndist,l.distcode,0,l.work,B),l.distbits=B.bits,z){t.msg="invalid distances set",l.mode=30;break}if(l.mode=20,6===e)break e;case 20:l.mode=21;case 21:if(m>=6&&b>=258){t.next_out=g,t.avail_out=b,t.next_in=p,t.avail_in=m,l.hold=x,l.bits=w,u(t,S),g=t.next_out,d=t.output,b=t.avail_out,p=t.next_in,f=t.input,m=t.avail_in,x=l.hold,w=l.bits,12===l.mode&&(l.back=-1);break}for(l.back=0;O=(V=l.lencode[x&(1<<l.lenbits)-1])>>>24,A=V>>>16&255,P=65535&V,!(O<=w);){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(A&&(240&A)==0){for(R=O,N=A,D=P;O=(V=l.lencode[D+((x&(1<<R+N)-1)>>R)])>>>24,A=V>>>16&255,P=65535&V,!(R+O<=w);){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}x>>>=R,w-=R,l.back+=R}if(x>>>=O,w-=O,l.back+=O,l.length=P,0===A){l.mode=26;break}if(32&A){l.back=-1,l.mode=12;break}if(64&A){t.msg="invalid literal/length code",l.mode=30;break}l.extra=15&A,l.mode=22;case 22:if(l.extra){for(M=l.extra;w<M;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}l.length+=x&(1<<l.extra)-1,x>>>=l.extra,w-=l.extra,l.back+=l.extra}l.was=l.length,l.mode=23;case 23:for(;O=(V=l.distcode[x&(1<<l.distbits)-1])>>>24,A=V>>>16&255,P=65535&V,!(O<=w);){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if((240&A)==0){for(R=O,N=A,D=P;O=(V=l.distcode[D+((x&(1<<R+N)-1)>>R)])>>>24,A=V>>>16&255,P=65535&V,!(R+O<=w);){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}x>>>=R,w-=R,l.back+=R}if(x>>>=O,w-=O,l.back+=O,64&A){t.msg="invalid distance code",l.mode=30;break}l.offset=P,l.extra=15&A,l.mode=24;case 24:if(l.extra){for(M=l.extra;w<M;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}l.offset+=x&(1<<l.extra)-1,x>>>=l.extra,w-=l.extra,l.back+=l.extra}if(l.offset>l.dmax){t.msg="invalid distance too far back",l.mode=30;break}l.mode=25;case 25:if(0===b)break e;if(C=S-b,l.offset>C){if((C=l.offset-C)>l.whave&&l.sane){t.msg="invalid distance too far back",l.mode=30;break}C>l.wnext?(C-=l.wnext,k=l.wsize-C):k=l.wnext-C,C>l.length&&(C=l.length),T=l.window}else T=d,k=g-l.offset,C=l.length;C>b&&(C=b),b-=C,l.length-=C;do d[g++]=T[k++];while(--C);0===l.length&&(l.mode=21);break;case 26:if(0===b)break e;d[g++]=l.length,b--,l.mode=21;break;case 27:if(l.wrap){for(;w<32;){if(0===m)break e;m--,x|=f[p++]<<w,w+=8}if(S-=b,t.total_out+=S,l.total+=S,S&&(t.adler=l.check=l.flags?s(l.check,d,S,g-S):a(l.check,d,S,g-S)),S=b,(l.flags?x:h(x))!==l.check){t.msg="incorrect data check",l.mode=30;break}x=0,w=0}l.mode=28;case 28:if(l.wrap&&l.flags){for(;w<32;){if(0===m)break e;m--,x+=f[p++]<<w,w+=8}if(x!==(0|l.total)){t.msg="incorrect length check",l.mode=30;break}x=0,w=0}l.mode=29;case 29:z=1;break e;case 30:z=-3;break e;case 31:return -4;default:return -2}return(t.next_out=g,t.avail_out=b,t.next_in=p,t.avail_in=m,l.hold=x,l.bits=w,(l.wsize||S!==t.avail_out&&l.mode<30&&(l.mode<27||4!==e))&&v(t,t.output,t.next_out,S-t.avail_out))?(l.mode=31,-4):(F-=t.avail_in,S-=t.avail_out,t.total_in+=F,t.total_out+=S,l.total+=S,l.wrap&&S&&(t.adler=l.check=l.flags?s(l.check,d,S,t.next_out-S):a(l.check,d,S,t.next_out-S)),t.data_type=l.bits+64*!!l.last+128*(12===l.mode)+256*(20===l.mode||15===l.mode),(0===F&&0===S||4===e)&&0===z&&(z=-5),z)},e.inflateEnd=function(t){if(!t||!t.state)return -2;var e=t.state;return e.window&&(e.window=null),t.state=null,0},e.inflateGetHeader=function(t,e){var r;return t&&t.state&&(2&(r=t.state).wrap)!=0?(r.head=e,e.done=!1,0):-2},e.inflateSetDictionary=function(t,e){var r,n,o=e.length;return t&&t.state&&(0===(r=t.state).wrap||11===r.mode)?11===r.mode&&a(1,e,o,0)!==r.check?-3:v(t,e,o,o)?(r.mode=31,-4):(r.havedict=1,0):-2},e.inflateInfo="pako inflate (from Nodeca project)"},77879:(t,e,r)=>{r.d(e,{PDFDocument:()=>iL});var n,o,i,a,s,u,c,h,l,f,d,p,g,y,v,m,b,x,w,F,S,C,k,T,O,A,P,R=function(t,e){return(R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function N(t,e){function r(){this.constructor=t}R(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var D=function(){return(D=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function j(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})}function z(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}}function B(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var n=Array(t),o=0,e=0;e<r;e++)for(var i=arguments[e],a=0,s=i.length;a<s;a++,o++)n[o]=i[a];return n}for(var M="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",V=new Uint8Array(256),I=0;I<M.length;I++)V[M.charCodeAt(I)]=I;var U=function(t){for(var e="",r=t.length,n=0;n<r;n+=3)e+=M[t[n]>>2],e+=M[(3&t[n])<<4|t[n+1]>>4],e+=M[(15&t[n+1])<<2|t[n+2]>>6],e+=M[63&t[n+2]];return r%3==2?e=e.substring(0,e.length-1)+"=":r%3==1&&(e=e.substring(0,e.length-2)+"=="),e},E=function(t){var e,r,n,o,i,a=.75*t.length,s=t.length,u=0;"="===t[t.length-1]&&(a--,"="===t[t.length-2]&&a--);var c=new Uint8Array(a);for(e=0;e<s;e+=4)r=V[t.charCodeAt(e)],n=V[t.charCodeAt(e+1)],o=V[t.charCodeAt(e+2)],i=V[t.charCodeAt(e+3)],c[u++]=r<<2|n>>4,c[u++]=(15&n)<<4|o>>2,c[u++]=(3&o)<<6|63&i;return c},W=/^(data)?:?([\w\/\+]+)?;?(charset=[\w-]+|base64)?.*,/i,q=function(t){var e=t.trim(),r=e.substring(0,100).match(W);if(!r)return E(e);var n=r[0];return E(e.substring(n.length))},K=function(t){return t.charCodeAt(0)},G=function(t,e){return H(t.toString(16),e,"0").toUpperCase()},L=function(t){return G(t,2)},X=function(t){return String.fromCharCode(t)},H=function(t,e,r){for(var n="",o=0,i=e-t.length;o<i;o++)n+=r;return n+t},Z=function(t,e,r){for(var n=t.length,o=0;o<n;o++)e[r++]=t.charCodeAt(o);return n},Y=function(t){return t.replace(/\t|\u0085|\u2028|\u2029/g,"    ").replace(/[\b\v]/g,"")},J=["\\n","\\f","\\r","\\u000B"],Q=function(t){return/^[\n\f\r\u000B]$/.test(t)},_=function(t){return t.split(/[\n\f\r\u000B]/)},$=function(t){return t.replace(/[\n\f\r\u000B]/g," ")},tt=function(t,e){var r,n=t.charCodeAt(e),o=e+1,i=1;return n>=55296&&n<=56319&&t.length>o&&(r=t.charCodeAt(o))>=56320&&r<=57343&&(i=2),[t.slice(e,e+i),i]},te=function(t){for(var e=[],r=0,n=t.length;r<n;){var o=tt(t,r),i=o[0],a=o[1];e.push(i),r+=a}return e},tr=function(t){for(var e=J.join("|"),r=["$"],n=0,o=t.length;n<o;n++){var i=t[n];if(Q(i))throw TypeError("`wordBreak` must not include "+e);r.push(""===i?".":i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))}return RegExp("("+e+")|((.*?)("+r.join("|")+"))","gm")},tn=function(t,e,r,n){for(var o=tr(e),i=Y(t).match(o),a="",s=0,u=[],c=function(){""!==a&&u.push(a),a="",s=0},h=0,l=i.length;h<l;h++){var f=i[h];if(Q(f))c();else{var d=n(f);s+d>r&&c(),a+=f,s+=d}}return c(),u},to=/^D:(\d\d\d\d)(\d\d)?(\d\d)?(\d\d)?(\d\d)?(\d\d)?([+\-Z])?(\d\d)?'?(\d\d)?'?$/,ti=function(t){var e=t.match(to);if(e){var r=e[1],n=e[2],o=e[3],i=e[4],a=e[5],s=e[6],u=e[7],c=void 0===u?"Z":u,h=e[8],l=e[9];return new Date(r+"-"+(void 0===n?"01":n)+"-"+(void 0===o?"01":o)+"T"+(void 0===i?"00":i)+":"+(void 0===a?"00":a)+":"+(void 0===s?"00":s)+("Z"===c?"Z":""+c+(void 0===h?"00":h)+":"+(void 0===l?"00":l)))}},ta=function(t,e){for(var r,n,o=0;o<t.length;){var i=t.substring(o).match(e);if(!i)break;n=i,o+=(null!=(r=i.index)?r:0)+i[0].length}return{match:n,pos:o}},ts=function(t){return t[t.length-1]},tu=function(t){if(t instanceof Uint8Array)return t;for(var e=t.length,r=new Uint8Array(e),n=0;n<e;n++)r[n]=t.charCodeAt(n);return r},tc=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=t.length,n=[],o=0;o<r;o++){var i=t[o];n[o]=i instanceof Uint8Array?i:tu(i)}for(var a=0,o=0;o<r;o++)a+=t[o].length;for(var s=new Uint8Array(a),u=0,c=0;c<r;c++)for(var h=n[c],l=0,f=h.length;l<f;l++)s[u++]=h[l];return s},th=function(t){for(var e=0,r=0,n=t.length;r<n;r++)e+=t[r].length;for(var o=new Uint8Array(e),i=0,r=0,n=t.length;r<n;r++){var a=t[r];o.set(a,i),i+=a.length}return o},tl=function(t){for(var e="",r=0,n=t.length;r<n;r++)e+=X(t[r]);return e},tf=function(t,e){return t.id-e.id},td=function(t,e){for(var r=[],n=0,o=t.length;n<o;n++){var i=t[n],a=t[n-1];(0===n||e(i)!==e(a))&&r.push(i)}return r},tp=function(t){for(var e=t.length,r=0,n=Math.floor(e/2);r<n;r++){var o=r,i=e-r-1,a=t[r];t[o]=t[i],t[i]=a}return t},tg=function(t){for(var e=0,r=0,n=t.length;r<n;r++)e+=t[r];return e},ty=function(t,e){for(var r=Array(e-t),n=0,o=r.length;n<o;n++)r[n]=t+n;return r},tv=function(t,e){for(var r=Array(e.length),n=0,o=e.length;n<o;n++)r[n]=t[e[n]];return r},tm=function(t){if("string"==typeof t)return q(t);if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return t;throw TypeError("`input` must be one of `string | ArrayBuffer | Uint8Array`")},tb=function(){return new Promise(function(t){setTimeout(function(){return t()},0)})},tx=function(t,e){void 0===e&&(e=!0);var r=[];e&&r.push(65279);for(var n=0,o=t.length;n<o;){var i=t.codePointAt(n);if(i<65536)r.push(i),n+=1;else if(i<1114112)r.push(tw(i),tF(i)),n+=2;else throw Error("Invalid code point: 0x"+L(i))}return new Uint16Array(r)},tw=function(t){return Math.floor((t-65536)/1024)+55296},tF=function(t){return(t-65536)%1024+56320};!function(t){t.BigEndian="BigEndian",t.LittleEndian="LittleEndian"}(n||(n={}));for(var tS=function(t,e){if(void 0===e&&(e=!0),t.length<=1)return String.fromCodePoint(65533);for(var r=e?tO(t):n.BigEndian,o=2*!!e,i=[];t.length-o>=2;){var a=tT(t[o++],t[o++],r);if(tC(a))if(t.length-o<2)i.push(65533);else{var s=tT(t[o++],t[o++],r);tk(s)?i.push(a,s):i.push(65533)}else tk(a)?(o+=2,i.push(65533)):i.push(a)}return o<t.length&&i.push(65533),String.fromCodePoint.apply(String,i)},tC=function(t){return t>=55296&&t<=56319},tk=function(t){return t>=56320&&t<=57343},tT=function(t,e,r){if(r===n.LittleEndian)return e<<8|t;if(r===n.BigEndian)return t<<8|e;throw Error("Invalid byteOrder: "+r)},tO=function(t){return tA(t)?n.BigEndian:tP(t)?n.LittleEndian:n.BigEndian},tA=function(t){return 254===t[0]&&255===t[1]},tP=function(t){return 255===t[0]&&254===t[1]},tR=function(t){return tA(t)||tP(t)},tN=function(t){var e=String(t);if(1>Math.abs(t)){var r=parseInt(t.toString().split("e-")[1]);if(r){var n=t<0;n&&(t*=-1),t*=Math.pow(10,r-1),e="0."+Array(r).join("0")+t.toString().substring(2),n&&(e="-"+e)}}else{var r=parseInt(t.toString().split("+")[1]);r>20&&(r-=20,t/=Math.pow(10,r),e=t.toString()+Array(r+1).join("0"))}return e},tD=function(t){return Math.ceil(t.toString(2).length/8)},tj=function(t){for(var e=new Uint8Array(tD(t)),r=1;r<=e.length;r++)e[r-1]=t>>(e.length-r)*8;return e},tz=function(t){throw Error(t)},tB=r(97411),tM=r.n(tB),tV="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",tI=new Uint8Array(256),tU=0;tU<tV.length;tU++)tI[tV.charCodeAt(tU)]=tU;var tE=function(t){var e,r,n,o,i,a=.75*t.length,s=t.length,u=0;"="===t[t.length-1]&&(a--,"="===t[t.length-2]&&a--);var c=new Uint8Array(a);for(e=0;e<s;e+=4)r=tI[t.charCodeAt(e)],n=tI[t.charCodeAt(e+1)],o=tI[t.charCodeAt(e+2)],i=tI[t.charCodeAt(e+3)],c[u++]=r<<2|n>>4,c[u++]=(15&n)<<4|o>>2,c[u++]=(3&o)<<6|63&i;return c},tW=function(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},tq=function(t){return tW(tM().inflate(tE(t)))},tK=function(t,e,r){for(var n="",o=0,i=e-t.length;o<i;o++)n+=r;return n+t},tG={Courier:"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","Courier-Bold":"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","Courier-Oblique":"eJyFWVtT2zgU/isZP+3OhE5Iy/UtDaHNFhI2IdDS4UGxFUeLbKW6AKHT/77Hhnbb1fnUFw98x9K5fzpyvmZDU1Wy9tlxdnUenChlZ3e//+awc7B32D/Kutmpqf1EVJJeGJpglbQ706VWX4JshEHrX4Wdn4SiUnr7q5jga6nKdaPvXBYqVISMvdAqH9Slpjd3dvuEuFP1KIsL5fN1duxtkN1suBZW5F7auWxWjx69rAtZzEwl6hc73741j9nx553+QXenv9frHr456h729m672YJetVrV8sI45ZWpG0W93k+Cy7XK72rpXHZMK7MraV37WtbrvX7V6/VIxcR4lT87s9naxovOH/mfnd2jw6MuPY967XO3ffbb5+v2edAZFGYpO/Ot87JynXGdG7sxVnhZvOp0Blp3Zs1urjOTTtp7QknbiN4qVF3O87VsQ9huMveiLoQtvkvpxaHYvH+J6d4+Be/j9//e9Pe72cDlTZxsdrzfP+pmJ/LH/zu7ewfdbO6L99e0crf98+rlzybY59JblVM8Pn/Nrj/S+iZeEzLEbQSF4Vv3f7B8zLWoYvxLMOToUseSOlTLJs5lHcsKo7WwMb6RNm/qNRKIikSOogMsaBPG7CesrLVcIRFYJlyzo7tjVungYjSnNhMxvN5u1pLxnlxRhvHEaeHWMfwkrYlRUzNZ8g/Mm35tJfPuipqWQdU9865Tjwwo7znvJB/TWnEG50YbZg8nKwVEuuniWOmXIJgaLK2kPmTcJBJzLVPEuWdsH8TQ2xgaxtBJDI1i6DSG3sXQ+xgax9BfMfQhhs5i6DyGJjE0jaGLGPo7hmYxNI+hyxhaxNBVDF3H0McY+hRDNzG0pJPoTnqeNpYkA336sg5ySq5UrmweGBYNDWk7OjiYFmn5jTeG4Zwl02MM/zIdxHAB01AMy8WQiqF/YoihV6aFmWgxXcvQ3oYJZQwx3MDQCxP7EEP3MfQQQwyTbhnS5+sLFMKSO0zb91PV6JUu4FFfmAcmzvzp3ByXuplX4hJqpjqWZ7fc2bHSxir2PAC75MHSMZ4zAWzbxql27oRTCx/NMiiSVAZQQCNuN/6NGIR+xXTHiil8GuRYmilEWXJH2jPOjmLPA0eTO2kl0/s0C4nSig1HanQJkIwX/4V3KVwiwmkpCNGPBAC51FptnGIalTz1axPQpPh86POlTQHgRh+RB88NLi2Tr7Rk2hn0VSVyy9Xw0kpOcWG8yPl+K+iyJVn/LduFOV3GaOBmuDvUpbCh0iIwakxJQybD0rlg/ZAVX7ADZuQxtljRjMcPhWfggJ0inFdQEckFzWoQfLyb2ZLpTg30GoQzu1Nr0lWWSp5J2hjnU4LyE6BoQjqjEqTuE7agUPeKq+ZPwLJBqjMEWLRILdqCRa69dqEekaktF3yfLHAYBGqGBbAuoAUjrOSECIK7fyQdzb9/r2BUIcrJQR0IPi6TpAEa1Shg1MvkbkO0G2DVUYInHXDTQUJUQLs2T7IuGcEMqHiXdDIkmyQlHKCUWmBIDn29SUTucm0ss9kUaZ+BuM0BXgBrF0hB4Cuz/bbhQjvgMDPRFJTgAOGAVqugvdpoZswMwMFL4CCNWl4JXagVc7vaYmqYAD0qVSyjZJklTh0syoEdNaJBlNAJCNAYbNR8eaOBgfv8trTmTtbsHcjKUjkw9b4DyR6nGCVQV/NXkRGoKQscMigyN2DBDYjYy0cu8Als5JkJZQhJQSd5y6PRD56OSDBA40OiKkGAn1BrIN1TlBF09KBMOaQZOzFNjiXAwxOpPZMrz5O0fzAJRsSmVcwnDQUsMuk5c4RCX4AJp+VxKmBLhcyYNk/UK1+RH5J1fAYS560oZCUsY7lMsMIYRGmMCucMWE1BWwvNnAzNzQQ0uElVVA2mpsVv8gfiI5FDJeBScuglq1xS+SDMk6mbpi0viCW4XzsMSNEQGBEgmxcq59JwAjaySW8mID8G4LN0WSelA7DnAHI0P3NNwT5PiQ4ZodMe6b5LugIGgXGyFYAJPn25MWWT79pw30cwlwYsoq3Qr1XpCy13kD8Bp+rkVhRyfEIo1OOj0PwOedvNPkhbXwhlm1+Pb7/9C/NFF2U=","Courier-BoldOblique":"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",Helvetica:"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","Helvetica-Bold":"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","Helvetica-Oblique":"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","Helvetica-BoldOblique":"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","Times-Roman":"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","Times-Bold":"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","Times-Italic":"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","Times-BoldItalic":"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",Symbol:"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",ZapfDingbats:"eJxtmNtu20YQhl+F4FULyMGeD7pz3AY1ChtG7NpFA18w1NomIlECSRcxgrx7SVk7+wOdG8H5OJydf2Z2d5gf9cV+t0v9VK/r+6vXsXlOlbHe28paq229qj/t++m62aXZ4J/m8PRb1z9/baZxefK63Z6eXN5dVMvTCh83u277xr/6kLrnl2XNq7TpXnczuZyabdee98/b2VzM/x4/dd/T5qab2pd6PQ2vaVVfvDRD005puE3Lu7eH1HbN9hTjx4/77/X6y5lcnUmjVzHIVVDicVX/1W/SsO36dLMfu6nb9/X6TAoBD+5euvZbn8axXtuZ36dhPJrVQqgPQoh5hev91LWLkIv94W1Ygq9+aX+tZAx2tfz64284/sblN/rqfLP/mqrbt3FKu7G67Nv9cNgPzZQ2H6rz7bb6vLgZq89pTMO/M/xfEqturJpqSM/d7GJIm2oamk3aNcO3av80O5xh3yyKmm1193ZIT02bqovTKjP+MAf++7zsZvZ3276kYyWWXB0z99S18/PbafPHQ71W4fjn/fxnFO+ZvkrT0LVzTr78qB/+nk38bHM9exgP8zr1z9U7jt6840YW5uSJKcZOCaBBnKgm5mU8MVNYyMwWFvO7Ukagkmgg6sDWQ5yFFqjzUrLEaQ3BEmiwNsMSaZS0vgWfOkPHWQowNeTUc0kumnxZvsgPxlGai6VTGUqAVCTQ6QkWnc77DKEiLktSUBJKqHIQZ86d8gCpHYoiEzMsb1ubYy8vW50DChB5ZhGqrijD0EqUIeiaEHIfCg5Kpuu0ApiToaGPSY0uaQsyr65L2oKi1yFt1PLaQ3lzfXTgXodGoJYzglndSLDMPg1sTPJpQJHJigw0QrGERqD9YhyTOgONQDUyuF1zaxuokc/BW2ztXCMrGZ9WMW1oQZHIXWNBkSCfRZEL5BMUiZw6CzVSFCfUSGZFNjIldoKDkonTKQiJIGzWmFd3BizJJ9SINoLDriOfUCOZS+zg+KGD1qGiLNMLxtJD1/ns00ON6EzyUCM6vbxhoBKaqbG3DFQCNiL1iHccBPV0DHhQH/JW8EW90dkyFKGywCJU0WkVSvSGeiSUODWFFD0HYdPQVoiRgfPMA+/nnRgiAyNYSjpWNQcNSMrtFCUH4ZIRpSCWocFCSuhCEY6hoUClc0WC52BJlCYYLQdhN+hygRRRlo5BKRRLS6oihSqh+ZzzRGG1Mo4Iz1LoP0qsxDGFzk0JE42ji0jCPejomJKCuwil4m5CiRMEUMVSzVLDUstSx1Juc0oVWMpqY295qVltmtWmWW2a1aZZbZrVplltmtWmWW2G1WZYbYbVZlhthtVmWG2G1WZYbYbVZlhtltVmWW2W1WZZbZbVZlltltVmWW2W1QYjQCh7E2aAQHeGhCFgPoNoy8KNb2wxBhmGKBxoUZXlLGsLI6AsftEDHV0wIURVbANLcTKlGGBIKPOAxCmhePCKUwFzAmpDFRQvjA9R06Hq8TONvshgKDCuRAZTXigUxjxNFfKRo3CLhnIJBMFRvMZpqpNBMlQJzGT5WFQMVQI/AikPMIhEU1aDjqJvQwmjSHB05cC9jbYwc5UtAHNLhDw41ha+lEqF4JaH3gmB61SYcqInxTDmQK8v08vjqv4zDf1N0w3Lf4A8/vwPpfK11w=="};!function(t){t.Courier="Courier",t.CourierBold="Courier-Bold",t.CourierOblique="Courier-Oblique",t.CourierBoldOblique="Courier-BoldOblique",t.Helvetica="Helvetica",t.HelveticaBold="Helvetica-Bold",t.HelveticaOblique="Helvetica-Oblique",t.HelveticaBoldOblique="Helvetica-BoldOblique",t.TimesRoman="Times-Roman",t.TimesRomanBold="Times-Bold",t.TimesRomanItalic="Times-Italic",t.TimesRomanBoldItalic="Times-BoldItalic",t.Symbol="Symbol",t.ZapfDingbats="ZapfDingbats"}(o||(o={}));for(var tL={},tX=function(){function t(){var t=this;this.getWidthOfGlyph=function(e){return t.CharWidths[e]},this.getXAxisKerningForPair=function(e,r){return(t.KernPairXAmounts[e]||{})[r]}}return t.load=function(e){var r=tL[e];if(r)return r;var n=tq(tG[e]),o=Object.assign(new t,JSON.parse(n));return o.CharWidths=o.CharMetrics.reduce(function(t,e){return t[e.N]=e.WX,t},{}),o.KernPairXAmounts=o.KernPairs.reduce(function(t,e){var r=e[0],n=e[1],o=e[2];return t[r]||(t[r]={}),t[r][n]=o,t},{}),tL[e]=o,o},t}(),tH=JSON.parse(tq("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")),tZ=function(t,e){var r=this;this.canEncodeUnicodeCodePoint=function(t){return t in r.unicodeMappings},this.encodeUnicodeCodePoint=function(t){var e=r.unicodeMappings[t];if(!e){var n=String.fromCharCode(t),o="0x"+tK(t.toString(16),4,"0");throw Error(r.name+' cannot encode "'+n+'" ('+o+")")}return{code:e[0],name:e[1]}},this.name=t,this.supportedCodePoints=Object.keys(e).map(Number).sort(function(t,e){return t-e}),this.unicodeMappings=e},tY={Symbol:new tZ("Symbol",tH.symbol),ZapfDingbats:new tZ("ZapfDingbats",tH.zapfdingbats),WinAnsi:new tZ("WinAnsi",tH.win1252)},tJ=function(t){return Object.keys(t).map(function(e){return t[e]})},tQ=tJ(o),t_=function(t){return tQ.includes(t)},t$=function(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},t0=function(t){return"`"+t+"`"},t1=function(t){var e=typeof t;return"string"===e?"'"+t+"'":"undefined"===e?t0(t):t},t2=function(t,e,r){for(var n=Array(r.length),o=0,i=r.length;o<i;o++){var a=r[o];n[o]=t1(a)}var s=n.join(" or ");return t0(e)+" must be one of "+s+", but was actually "+t1(t)},t5=function(t,e,r){Array.isArray(r)||(r=tJ(r));for(var n=0,o=r.length;n<o;n++)if(t===r[n])return;throw TypeError(t2(t,e,r))},t3=function(t,e,r){Array.isArray(r)||(r=tJ(r)),t5(t,e,r.concat(void 0))},t4=function(t,e,r){Array.isArray(r)||(r=tJ(r));for(var n=0,o=t.length;n<o;n++)t5(t[n],e,r)},t6=function(t,e,r){for(var n=Array(r.length),o=0,i=r.length;o<i;o++){var a=r[o];"null"===a&&(n[o]=t0("null")),"undefined"===a&&(n[o]=t0("undefined")),"string"===a?n[o]=t0("string"):"number"===a?n[o]=t0("number"):"boolean"===a?n[o]=t0("boolean"):"symbol"===a?n[o]=t0("symbol"):"bigint"===a?n[o]=t0("bigint"):a===Array?n[o]=t0("Array"):a===Uint8Array?n[o]=t0("Uint8Array"):a===ArrayBuffer?n[o]=t0("ArrayBuffer"):n[o]=t0(a[1])}var s=n.join(" or ");return t0(e)+" must be of type "+s+", but was actually of type "+t0(null===t?"null":void 0===t?"undefined":"string"==typeof t?"string":isNaN(t)?"NaN":"number"==typeof t?"number":"boolean"==typeof t?"boolean":"symbol"==typeof t?"symbol":"bigint"==typeof t?"bigint":t.constructor&&t.constructor.name?t.constructor.name:t.name?t.name:t.constructor?String(t.constructor):String(t))},t8=function(t,e,r){for(var n,o=0,i=r.length;o<i;o++)if("null"===(n=r[o])?null===t:"undefined"===n?void 0===t:"string"===n?"string"==typeof t:"number"===n?"number"==typeof t&&!isNaN(t):"boolean"===n?"boolean"==typeof t:"symbol"===n?"symbol"==typeof t:"bigint"===n?"bigint"==typeof t:n===Date?t instanceof Date:n===Array?t instanceof Array:n===Uint8Array?t instanceof Uint8Array:n===ArrayBuffer?t instanceof ArrayBuffer:n===Function?t instanceof Function:t instanceof n[0])return;throw TypeError(t6(t,e,r))},t9=function(t,e,r){t8(t,e,r.concat("undefined"))},t7=function(t,e,r){for(var n=0,o=t.length;n<o;n++)t8(t[n],e,r)},et=function(t,e,r,n){if(t8(t,e,["number"]),t8(r,"min",["number"]),t8(n,"max",["number"]),n=Math.max(r,n),t<r||t>n)throw Error(t0(e)+" must be at least "+r+" and at most "+n+", but was actually "+t)},ee=function(t,e,r,n){t8(t,e,["number","undefined"]),"number"==typeof t&&et(t,e,r,n)},er=function(t,e,r){if(t8(t,e,["number"]),t%r!=0)throw Error(t0(e)+" must be a multiple of "+r+", but was actually "+t)},en=function(t,e){if(!Number.isInteger(t))throw Error(t0(e)+" must be an integer, but was actually "+t)},eo=function(t,e){if(![1,0].includes(Math.sign(t)))throw Error(t0(e)+" must be a positive number or 0, but was actually "+t)},ei=new Uint16Array(256),ea=0;ea<256;ea++)ei[ea]=ea;ei[22]=K("\x17"),ei[24]=K("˘"),ei[25]=K("ˇ"),ei[26]=K("ˆ"),ei[27]=K("˙"),ei[28]=K("˝"),ei[29]=K("˛"),ei[30]=K("˚"),ei[31]=K("˜"),ei[127]=K("�"),ei[128]=K("•"),ei[129]=K("†"),ei[130]=K("‡"),ei[131]=K("…"),ei[132]=K("—"),ei[133]=K("–"),ei[134]=K("ƒ"),ei[135]=K("⁄"),ei[136]=K("‹"),ei[137]=K("›"),ei[138]=K("−"),ei[139]=K("‰"),ei[140]=K("„"),ei[141]=K("“"),ei[142]=K("”"),ei[143]=K("‘"),ei[144]=K("’"),ei[145]=K("‚"),ei[146]=K("™"),ei[147]=K("ﬁ"),ei[148]=K("ﬂ"),ei[149]=K("Ł"),ei[150]=K("Œ"),ei[151]=K("Š"),ei[152]=K("Ÿ"),ei[153]=K("Ž"),ei[154]=K("ı"),ei[155]=K("ł"),ei[156]=K("œ"),ei[157]=K("š"),ei[158]=K("ž"),ei[159]=K("�"),ei[160]=K("€"),ei[173]=K("�");var es=function(t){for(var e=Array(t.length),r=0,n=t.length;r<n;r++)e[r]=ei[t[r]];return String.fromCodePoint.apply(String,e)},eu=function(){function t(t){this.populate=t,this.value=void 0}return t.prototype.getValue=function(){return this.value},t.prototype.access=function(){return this.value||(this.value=this.populate()),this.value},t.prototype.invalidate=function(){this.value=void 0},t.populatedBy=function(e){return new t(e)},t}(),ec=function(t){function e(e,r){var n=this;return t.call(this,"Method "+e+"."+r+"() not implemented")||this}return N(e,t),e}(Error),eh=function(t){function e(e){var r=this;return t.call(this,"Cannot construct "+e+" - it has a private constructor")||this}return N(e,t),e}(Error),el=function(t){function e(e,r){var n=this,o=function(t){var e,r;return null!=(e=null==t?void 0:t.name)?e:null==(r=null==t?void 0:t.constructor)?void 0:r.name},i="Expected instance of "+(Array.isArray(e)?e.map(o):[o(e)]).join(" or ")+", but got instance of "+(r?o(r):r);return t.call(this,i)||this}return N(e,t),e}(Error),ef=function(t){function e(e){var r=this;return t.call(this,e+" stream encoding not supported")||this}return N(e,t),e}(Error),ed=function(t){function e(e,r){var n=this;return t.call(this,"Cannot call "+e+"."+r+"() more than once")||this}return N(e,t),e}(Error);!function(t){N(function(e){return t.call(this,"Missing catalog (ref="+e+")")||this},t)}(Error);var ep=function(t){function e(){var e=this;return t.call(this,"Can't embed page with missing Contents")||this}return N(e,t),e}(Error),eg=function(t){function e(e){var r,n,o,i=this,a=null!=(o=null!=(n=null==(r=null==e?void 0:e.contructor)?void 0:r.name)?n:null==e?void 0:e.name)?o:e;return t.call(this,"Unrecognized stream type: "+a)||this}return N(e,t),e}(Error),ey=function(t){function e(){var e=this;return t.call(this,"Found mismatched contexts while embedding pages. All pages in the array passed to `PDFDocument.embedPages()` must be from the same document.")||this}return N(e,t),e}(Error),ev=function(t){function e(e){var r=this;return t.call(this,"Attempted to convert PDFArray with "+e+" elements to rectangle, but must have exactly 4 elements.")||this}return N(e,t),e}(Error),em=function(t){function e(e){var r=this;return t.call(this,'Attempted to convert "'+e+'" to a date, but it does not match the PDF date string format.')||this}return N(e,t),e}(Error),eb=function(t){function e(e,r){var n=this;return t.call(this,"Invalid targetIndex specified: targetIndex="+e+" must be less than Count="+r)||this}return N(e,t),e}(Error),ex=function(t){function e(e,r){var n=this;return t.call(this,"Failed to "+r+" at targetIndex="+e+" due to corrupt page tree: It is likely that one or more 'Count' entries are invalid")||this}return N(e,t),e}(Error),ew=function(t){function e(e,r,n){var o=this;return t.call(this,"index should be at least "+r+" and at most "+n+", but was actually "+e)||this}return N(e,t),e}(Error),eF=function(t){function e(){var e=this;return t.call(this,"Attempted to set invalid field value")||this}return N(e,t),e}(Error),eS=function(t){function e(){var e=this;return t.call(this,"Attempted to select multiple values for single-select field")||this}return N(e,t),e}(Error),eC=function(t){function e(e){var r=this;return t.call(this,"No /DA (default appearance) entry found for field: "+e)||this}return N(e,t),e}(Error),ek=function(t){function e(e){var r=this;return t.call(this,"No Tf operator found for DA of field: "+e)||this}return N(e,t),e}(Error),eT=function(t){function e(e,r){var n=this,o="Failed to parse number "+("(line:"+e.line+" col:"+e.column+" offset="+e.offset+'): "')+r+'"';return t.call(this,o)||this}return N(e,t),e}(Error),eO=function(t){function e(e,r){var n=this,o="Failed to parse PDF document "+("(line:"+e.line+" col:"+e.column+" offset="+e.offset)+"): "+r;return t.call(this,o)||this}return N(e,t),e}(Error),eA=function(t){function e(e,r,n){var o=this;return t.call(this,e,"Expected next byte to be "+r+" but it was actually "+n)||this}return N(e,t),e}(eO),eP=function(t){function e(e,r){var n=this;return t.call(this,e,"Failed to parse PDF object starting with the following byte: "+r)||this}return N(e,t),e}(eO),eR=function(t){function e(e){var r=this;return t.call(this,e,"Failed to parse invalid PDF object")||this}return N(e,t),e}(eO),eN=function(t){function e(e){var r=this;return t.call(this,e,"Failed to parse PDF stream")||this}return N(e,t),e}(eO),eD=function(t){function e(e){var r=this;return t.call(this,e,"Failed to parse PDF literal string due to unbalanced parenthesis")||this}return N(e,t),e}(eO),ej=function(t){function e(e){var r=this;return t.call(this,e,"Parser stalled")||this}return N(e,t),e}(eO),ez=function(t){function e(e){var r=this;return t.call(this,e,"No PDF header found")||this}return N(e,t),e}(eO),eB=function(t){function e(e,r){var n=this,o="Did not find expected keyword '"+tl(r)+"'";return t.call(this,e,o)||this}return N(e,t),e}(eO);!function(t){t[t.Null=0]="Null",t[t.Backspace=8]="Backspace",t[t.Tab=9]="Tab",t[t.Newline=10]="Newline",t[t.FormFeed=12]="FormFeed",t[t.CarriageReturn=13]="CarriageReturn",t[t.Space=32]="Space",t[t.ExclamationPoint=33]="ExclamationPoint",t[t.Hash=35]="Hash",t[t.Percent=37]="Percent",t[t.LeftParen=40]="LeftParen",t[t.RightParen=41]="RightParen",t[t.Plus=43]="Plus",t[t.Minus=45]="Minus",t[t.Dash=45]="Dash",t[t.Period=46]="Period",t[t.ForwardSlash=47]="ForwardSlash",t[t.Zero=48]="Zero",t[t.One=49]="One",t[t.Two=50]="Two",t[t.Three=51]="Three",t[t.Four=52]="Four",t[t.Five=53]="Five",t[t.Six=54]="Six",t[t.Seven=55]="Seven",t[t.Eight=56]="Eight",t[t.Nine=57]="Nine",t[t.LessThan=60]="LessThan",t[t.GreaterThan=62]="GreaterThan",t[t.A=65]="A",t[t.D=68]="D",t[t.E=69]="E",t[t.F=70]="F",t[t.O=79]="O",t[t.P=80]="P",t[t.R=82]="R",t[t.LeftSquareBracket=91]="LeftSquareBracket",t[t.BackSlash=92]="BackSlash",t[t.RightSquareBracket=93]="RightSquareBracket",t[t.a=97]="a",t[t.b=98]="b",t[t.d=100]="d",t[t.e=101]="e",t[t.f=102]="f",t[t.i=105]="i",t[t.j=106]="j",t[t.l=108]="l",t[t.m=109]="m",t[t.n=110]="n",t[t.o=111]="o",t[t.r=114]="r",t[t.s=115]="s",t[t.t=116]="t",t[t.u=117]="u",t[t.x=120]="x",t[t.LeftCurly=123]="LeftCurly",t[t.RightCurly=125]="RightCurly",t[t.Tilde=126]="Tilde"}(i||(i={}));let eM=i;var eV=function(){function t(t,e){this.major=String(t),this.minor=String(e)}return t.prototype.toString=function(){var t=X(129);return"%PDF-"+this.major+"."+this.minor+"\n%"+t+t+t+t},t.prototype.sizeInBytes=function(){return 12+this.major.length+this.minor.length},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=eM.Percent,t[e++]=eM.P,t[e++]=eM.D,t[e++]=eM.F,t[e++]=eM.Dash,e+=Z(this.major,t,e),t[e++]=eM.Period,e+=Z(this.minor,t,e),t[e++]=eM.Newline,t[e++]=eM.Percent,t[e++]=129,t[e++]=129,t[e++]=129,t[e++]=129,e-r},t.forVersion=function(e,r){return new t(e,r)},t}(),eI=function(){function t(){}return t.prototype.clone=function(t){throw new ec(this.constructor.name,"clone")},t.prototype.toString=function(){throw new ec(this.constructor.name,"toString")},t.prototype.sizeInBytes=function(){throw new ec(this.constructor.name,"sizeInBytes")},t.prototype.copyBytesInto=function(t,e){throw new ec(this.constructor.name,"copyBytesInto")},t}(),eU=function(t){function e(e){var r=t.call(this)||this;return r.numberValue=e,r.stringValue=tN(e),r}return N(e,t),e.prototype.asNumber=function(){return this.numberValue},e.prototype.value=function(){return this.numberValue},e.prototype.clone=function(){return e.of(this.numberValue)},e.prototype.toString=function(){return this.stringValue},e.prototype.sizeInBytes=function(){return this.stringValue.length},e.prototype.copyBytesInto=function(t,e){return e+=Z(this.stringValue,t,e),this.stringValue.length},e.of=function(t){return new e(t)},e}(eI),eE=function(t){function e(e){var r=t.call(this)||this;return r.array=[],r.context=e,r}return N(e,t),e.prototype.size=function(){return this.array.length},e.prototype.push=function(t){this.array.push(t)},e.prototype.insert=function(t,e){this.array.splice(t,0,e)},e.prototype.indexOf=function(t){var e=this.array.indexOf(t);return -1===e?void 0:e},e.prototype.remove=function(t){this.array.splice(t,1)},e.prototype.set=function(t,e){this.array[t]=e},e.prototype.get=function(t){return this.array[t]},e.prototype.lookupMaybe=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.context).lookupMaybe.apply(e,B([this.get(t)],r))},e.prototype.lookup=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.context).lookup.apply(e,B([this.get(t)],r))},e.prototype.asRectangle=function(){if(4!==this.size())throw new ev(this.size());var t=this.lookup(0,eU).asNumber(),e=this.lookup(1,eU).asNumber();return{x:t,y:e,width:this.lookup(2,eU).asNumber()-t,height:this.lookup(3,eU).asNumber()-e}},e.prototype.asArray=function(){return this.array.slice()},e.prototype.clone=function(t){for(var r=e.withContext(t||this.context),n=0,o=this.size();n<o;n++)r.push(this.array[n]);return r},e.prototype.toString=function(){for(var t="[ ",e=0,r=this.size();e<r;e++)t+=this.get(e).toString(),t+=" ";return t+"]"},e.prototype.sizeInBytes=function(){for(var t=3,e=0,r=this.size();e<r;e++)t+=this.get(e).sizeInBytes()+1;return t},e.prototype.copyBytesInto=function(t,e){var r=e;t[e++]=eM.LeftSquareBracket,t[e++]=eM.Space;for(var n=0,o=this.size();n<o;n++)e+=this.get(n).copyBytesInto(t,e),t[e++]=eM.Space;return t[e++]=eM.RightSquareBracket,e-r},e.prototype.scalePDFNumbers=function(t,e){for(var r=0,n=this.size();r<n;r++){var o=this.lookup(r);if(o instanceof eU){var i=r%2==0?t:e;this.set(r,eU.of(o.asNumber()*i))}}},e.withContext=function(t){return new e(t)},e}(eI),eW={},eq=function(t){function e(e,r){var n=this;if(e!==eW)throw new eh("PDFBool");return(n=t.call(this)||this).value=r,n}return N(e,t),e.prototype.asBoolean=function(){return this.value},e.prototype.clone=function(){return this},e.prototype.toString=function(){return String(this.value)},e.prototype.sizeInBytes=function(){return this.value?4:5},e.prototype.copyBytesInto=function(t,e){return this.value?(t[e++]=eM.t,t[e++]=eM.r,t[e++]=eM.u,t[e++]=eM.e,4):(t[e++]=eM.f,t[e++]=eM.a,t[e++]=eM.l,t[e++]=eM.s,t[e++]=eM.e,5)},e.True=new e(eW,!0),e.False=new e(eW,!1),e}(eI),eK=new Uint8Array(256);eK[eM.LeftParen]=1,eK[eM.RightParen]=1,eK[eM.LessThan]=1,eK[eM.GreaterThan]=1,eK[eM.LeftSquareBracket]=1,eK[eM.RightSquareBracket]=1,eK[eM.LeftCurly]=1,eK[eM.RightCurly]=1,eK[eM.ForwardSlash]=1,eK[eM.Percent]=1;var eG=new Uint8Array(256);eG[eM.Null]=1,eG[eM.Tab]=1,eG[eM.Newline]=1,eG[eM.FormFeed]=1,eG[eM.CarriageReturn]=1,eG[eM.Space]=1;for(var eL=new Uint8Array(256),eX=0;eX<256;eX++)eL[eX]=eG[eX]||eK[eX]?1:0;eL[eM.Hash]=1;var eH={},eZ=new Map,eY=function(t){function e(e,r){var n=this;if(e!==eH)throw new eh("PDFName");n=t.call(this)||this;for(var o="/",i=0,a=r.length;i<a;i++){var s=r[i],u=K(s);o+=u>=eM.ExclamationPoint&&u<=eM.Tilde&&!eL[u]?s:"#"+L(u)}return n.encodedName=o,n}return N(e,t),e.prototype.asBytes=function(){for(var t=[],e="",r=!1,n=function(e){void 0!==e&&t.push(e),r=!1},o=1,i=this.encodedName.length;o<i;o++){var a=this.encodedName[o],s=K(a),u=this.encodedName[o+1];r?s>=eM.Zero&&s<=eM.Nine||s>=eM.a&&s<=eM.f||s>=eM.A&&s<=eM.F?2!==(e+=a).length&&(u>="0"&&u<="9"||u>="a"&&u<="f"||u>="A"&&u<="F")||(n(parseInt(e,16)),e=""):n(s):s===eM.Hash?r=!0:n(s)}return new Uint8Array(t)},e.prototype.decodeText=function(){var t=this.asBytes();return String.fromCharCode.apply(String,Array.from(t))},e.prototype.asString=function(){return this.encodedName},e.prototype.value=function(){return this.encodedName},e.prototype.clone=function(){return this},e.prototype.toString=function(){return this.encodedName},e.prototype.sizeInBytes=function(){return this.encodedName.length},e.prototype.copyBytesInto=function(t,e){return e+=Z(this.encodedName,t,e),this.encodedName.length},e.of=function(t){var r=t.replace(/#([\dABCDEF]{2})/g,function(t,e){return X(parseInt(e,16))}),n=eZ.get(r);return n||(n=new e(eH,r),eZ.set(r,n)),n},e.Length=e.of("Length"),e.FlateDecode=e.of("FlateDecode"),e.Resources=e.of("Resources"),e.Font=e.of("Font"),e.XObject=e.of("XObject"),e.ExtGState=e.of("ExtGState"),e.Contents=e.of("Contents"),e.Type=e.of("Type"),e.Parent=e.of("Parent"),e.MediaBox=e.of("MediaBox"),e.Page=e.of("Page"),e.Annots=e.of("Annots"),e.TrimBox=e.of("TrimBox"),e.ArtBox=e.of("ArtBox"),e.BleedBox=e.of("BleedBox"),e.CropBox=e.of("CropBox"),e.Rotate=e.of("Rotate"),e.Title=e.of("Title"),e.Author=e.of("Author"),e.Subject=e.of("Subject"),e.Creator=e.of("Creator"),e.Keywords=e.of("Keywords"),e.Producer=e.of("Producer"),e.CreationDate=e.of("CreationDate"),e.ModDate=e.of("ModDate"),e}(eI);let eJ=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.asNull=function(){return null},e.prototype.clone=function(){return this},e.prototype.toString=function(){return"null"},e.prototype.sizeInBytes=function(){return 4},e.prototype.copyBytesInto=function(t,e){return t[e++]=eM.n,t[e++]=eM.u,t[e++]=eM.l,t[e++]=eM.l,4},e}(eI));var eQ=function(t){function e(e,r){var n=t.call(this)||this;return n.dict=e,n.context=r,n}return N(e,t),e.prototype.keys=function(){return Array.from(this.dict.keys())},e.prototype.values=function(){return Array.from(this.dict.values())},e.prototype.entries=function(){return Array.from(this.dict.entries())},e.prototype.set=function(t,e){this.dict.set(t,e)},e.prototype.get=function(t,e){void 0===e&&(e=!1);var r=this.dict.get(t);if(r!==eJ||e)return r},e.prototype.has=function(t){var e=this.dict.get(t);return void 0!==e&&e!==eJ},e.prototype.lookupMaybe=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=r.includes(eJ),i=(e=this.context).lookupMaybe.apply(e,B([this.get(t,o)],r));if(i!==eJ||o)return i},e.prototype.lookup=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=r.includes(eJ),i=(e=this.context).lookup.apply(e,B([this.get(t,o)],r));if(i!==eJ||o)return i},e.prototype.delete=function(t){return this.dict.delete(t)},e.prototype.asMap=function(){return new Map(this.dict)},e.prototype.uniqueKey=function(t){void 0===t&&(t="");for(var e=this.keys(),r=eY.of(this.context.addRandomSuffix(t,10));e.includes(r);)r=eY.of(this.context.addRandomSuffix(t,10));return r},e.prototype.clone=function(t){for(var r=e.withContext(t||this.context),n=this.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];r.set(s,u)}return r},e.prototype.toString=function(){for(var t="<<\n",e=this.entries(),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0],a=o[1];t+=i.toString()+" "+a.toString()+"\n"}return t+">>"},e.prototype.sizeInBytes=function(){for(var t=5,e=this.entries(),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0],a=o[1];t+=i.sizeInBytes()+a.sizeInBytes()+2}return t},e.prototype.copyBytesInto=function(t,e){var r=e;t[e++]=eM.LessThan,t[e++]=eM.LessThan,t[e++]=eM.Newline;for(var n=this.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];e+=s.copyBytesInto(t,e),t[e++]=eM.Space,e+=u.copyBytesInto(t,e),t[e++]=eM.Newline}return t[e++]=eM.GreaterThan,t[e++]=eM.GreaterThan,e-r},e.withContext=function(t){return new e(new Map,t)},e.fromMapWithContext=function(t,r){return new e(t,r)},e}(eI),e_=function(t){function e(e){var r=t.call(this)||this;return r.dict=e,r}return N(e,t),e.prototype.clone=function(t){throw new ec(this.constructor.name,"clone")},e.prototype.getContentsString=function(){throw new ec(this.constructor.name,"getContentsString")},e.prototype.getContents=function(){throw new ec(this.constructor.name,"getContents")},e.prototype.getContentsSize=function(){throw new ec(this.constructor.name,"getContentsSize")},e.prototype.updateDict=function(){var t=this.getContentsSize();this.dict.set(eY.Length,eU.of(t))},e.prototype.sizeInBytes=function(){return this.updateDict(),this.dict.sizeInBytes()+this.getContentsSize()+18},e.prototype.toString=function(){this.updateDict();var t=this.dict.toString();return t+="\nstream\n",t+=this.getContentsString(),t+="\nendstream"},e.prototype.copyBytesInto=function(t,e){this.updateDict();var r=e;e+=this.dict.copyBytesInto(t,e),t[e++]=eM.Newline,t[e++]=eM.s,t[e++]=eM.t,t[e++]=eM.r,t[e++]=eM.e,t[e++]=eM.a,t[e++]=eM.m,t[e++]=eM.Newline;for(var n=this.getContents(),o=0,i=n.length;o<i;o++)t[e++]=n[o];return t[e++]=eM.Newline,t[e++]=eM.e,t[e++]=eM.n,t[e++]=eM.d,t[e++]=eM.s,t[e++]=eM.t,t[e++]=eM.r,t[e++]=eM.e,t[e++]=eM.a,t[e++]=eM.m,e-r},e}(eI),e$=function(t){function e(e,r){var n=t.call(this,e)||this;return n.contents=r,n}return N(e,t),e.prototype.asUint8Array=function(){return this.contents.slice()},e.prototype.clone=function(t){return e.of(this.dict.clone(t),this.contents.slice())},e.prototype.getContentsString=function(){return tl(this.contents)},e.prototype.getContents=function(){return this.contents},e.prototype.getContentsSize=function(){return this.contents.length},e.of=function(t,r){return new e(t,r)},e}(e_),e0={},e1=new Map,e2=function(t){function e(e,r,n){var o=this;if(e!==e0)throw new eh("PDFRef");return(o=t.call(this)||this).objectNumber=r,o.generationNumber=n,o.tag=r+" "+n+" R",o}return N(e,t),e.prototype.clone=function(){return this},e.prototype.toString=function(){return this.tag},e.prototype.sizeInBytes=function(){return this.tag.length},e.prototype.copyBytesInto=function(t,e){return e+=Z(this.tag,t,e),this.tag.length},e.of=function(t,r){void 0===r&&(r=0);var n=t+" "+r+" R",o=e1.get(n);return o||(o=new e(e0,t,r),e1.set(n,o)),o},e}(eI),e5=function(){function t(t,e){this.name=t,this.args=e||[]}return t.prototype.clone=function(e){for(var r=Array(this.args.length),n=0,o=r.length;n<o;n++){var i=this.args[n];r[n]=i instanceof eI?i.clone(e):i}return t.of(this.name,r)},t.prototype.toString=function(){for(var t="",e=0,r=this.args.length;e<r;e++)t+=String(this.args[e])+" ";return t+this.name},t.prototype.sizeInBytes=function(){for(var t=0,e=0,r=this.args.length;e<r;e++){var n=this.args[e];t+=(n instanceof eI?n.sizeInBytes():n.length)+1}return t+this.name.length},t.prototype.copyBytesInto=function(t,e){for(var r=e,n=0,o=this.args.length;n<o;n++){var i=this.args[n];i instanceof eI?e+=i.copyBytesInto(t,e):e+=Z(i,t,e),t[e++]=eM.Space}return(e+=Z(this.name,t,e))-r},t.of=function(e,r){return new t(e,r)},t}();!function(t){t.NonStrokingColor="sc",t.NonStrokingColorN="scn",t.NonStrokingColorRgb="rg",t.NonStrokingColorGray="g",t.NonStrokingColorCmyk="k",t.NonStrokingColorspace="cs",t.StrokingColor="SC",t.StrokingColorN="SCN",t.StrokingColorRgb="RG",t.StrokingColorGray="G",t.StrokingColorCmyk="K",t.StrokingColorspace="CS",t.BeginMarkedContentSequence="BDC",t.BeginMarkedContent="BMC",t.EndMarkedContent="EMC",t.MarkedContentPointWithProps="DP",t.MarkedContentPoint="MP",t.DrawObject="Do",t.ConcatTransformationMatrix="cm",t.PopGraphicsState="Q",t.PushGraphicsState="q",t.SetFlatness="i",t.SetGraphicsStateParams="gs",t.SetLineCapStyle="J",t.SetLineDashPattern="d",t.SetLineJoinStyle="j",t.SetLineMiterLimit="M",t.SetLineWidth="w",t.SetTextMatrix="Tm",t.SetRenderingIntent="ri",t.AppendRectangle="re",t.BeginInlineImage="BI",t.BeginInlineImageData="ID",t.EndInlineImage="EI",t.ClipEvenOdd="W*",t.ClipNonZero="W",t.CloseAndStroke="s",t.CloseFillEvenOddAndStroke="b*",t.CloseFillNonZeroAndStroke="b",t.ClosePath="h",t.AppendBezierCurve="c",t.CurveToReplicateFinalPoint="y",t.CurveToReplicateInitialPoint="v",t.EndPath="n",t.FillEvenOddAndStroke="B*",t.FillEvenOdd="f*",t.FillNonZeroAndStroke="B",t.FillNonZero="f",t.LegacyFillNonZero="F",t.LineTo="l",t.MoveTo="m",t.ShadingFill="sh",t.StrokePath="S",t.BeginText="BT",t.EndText="ET",t.MoveText="Td",t.MoveTextSetLeading="TD",t.NextLine="T*",t.SetCharacterSpacing="Tc",t.SetFontAndSize="Tf",t.SetTextHorizontalScaling="Tz",t.SetTextLineHeight="TL",t.SetTextRenderingMode="Tr",t.SetTextRise="Ts",t.SetWordSpacing="Tw",t.ShowText="Tj",t.ShowTextAdjusted="TJ",t.ShowTextLine="'",t.ShowTextLineAndSpace='"',t.Type3D0="d0",t.Type3D1="d1",t.BeginCompatibilitySection="BX",t.EndCompatibilitySection="EX"}(a||(a={}));let e3=a;var e4=function(t){function e(e,r){var n=t.call(this,e)||this;return n.computeContents=function(){var t=n.getUnencodedContents();return n.encode?tM().deflate(t):t},n.encode=r,r&&e.set(eY.of("Filter"),eY.of("FlateDecode")),n.contentsCache=eu.populatedBy(n.computeContents),n}return N(e,t),e.prototype.getContents=function(){return this.contentsCache.access()},e.prototype.getContentsSize=function(){return this.contentsCache.access().length},e.prototype.getUnencodedContents=function(){throw new ec(this.constructor.name,"getUnencodedContents")},e}(e_),e6=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e,n)||this;return o.operators=r,o}return N(e,t),e.prototype.push=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this.operators).push.apply(t,e)},e.prototype.clone=function(t){for(var r=Array(this.operators.length),n=0,o=this.operators.length;n<o;n++)r[n]=this.operators[n].clone(t);var i=this.dict,a=this.encode;return e.of(i.clone(t),r,a)},e.prototype.getContentsString=function(){for(var t="",e=0,r=this.operators.length;e<r;e++)t+=this.operators[e]+"\n";return t},e.prototype.getUnencodedContents=function(){for(var t=new Uint8Array(this.getUnencodedContentsSize()),e=0,r=0,n=this.operators.length;r<n;r++)e+=this.operators[r].copyBytesInto(t,e),t[e++]=eM.Newline;return t},e.prototype.getUnencodedContentsSize=function(){for(var t=0,e=0,r=this.operators.length;e<r;e++)t+=this.operators[e].sizeInBytes()+1;return t},e.of=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(e4),e8=function(){function t(t){this.seed=t}return t.prototype.nextInt=function(){var t=1e4*Math.sin(this.seed++);return t-Math.floor(t)},t.withSeed=function(e){return new t(e)},t}(),e9=function(t,e){var r=t[0],n=e[0];return r.objectNumber-n.objectNumber},e7=function(){function t(){this.largestObjectNumber=0,this.header=eV.forVersion(1,7),this.trailerInfo={},this.indirectObjects=new Map,this.rng=e8.withSeed(1)}return t.prototype.assign=function(t,e){this.indirectObjects.set(t,e),t.objectNumber>this.largestObjectNumber&&(this.largestObjectNumber=t.objectNumber)},t.prototype.nextRef=function(){return this.largestObjectNumber+=1,e2.of(this.largestObjectNumber)},t.prototype.register=function(t){var e=this.nextRef();return this.assign(e,t),e},t.prototype.delete=function(t){return this.indirectObjects.delete(t)},t.prototype.lookupMaybe=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=e.includes(eJ),o=t instanceof e2?this.indirectObjects.get(t):t;if(o&&(o!==eJ||n)){for(var i=0,a=e.length;i<a;i++){var s=e[i];if(s===eJ){if(o===eJ)return o}else if(o instanceof s)return o}throw new el(e,o)}},t.prototype.lookup=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=t instanceof e2?this.indirectObjects.get(t):t;if(0===e.length)return n;for(var o=0,i=e.length;o<i;o++){var a=e[o];if(a===eJ){if(n===eJ)return n}else if(n instanceof a)return n}throw new el(e,n)},t.prototype.getObjectRef=function(t){for(var e=Array.from(this.indirectObjects.entries()),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0];if(o[1]===t)return i}},t.prototype.enumerateIndirectObjects=function(){return Array.from(this.indirectObjects.entries()).sort(e9)},t.prototype.obj=function(t){if(t instanceof eI)return t;if(null==t)return eJ;if("string"==typeof t)return eY.of(t);if("number"==typeof t)return eU.of(t);if("boolean"==typeof t)return t?eq.True:eq.False;else if(Array.isArray(t)){for(var e=eE.withContext(this),r=0,n=t.length;r<n;r++)e.push(this.obj(t[r]));return e}else{for(var o=eQ.withContext(this),i=Object.keys(t),r=0,n=i.length;r<n;r++){var a=i[r],s=t[a];void 0!==s&&o.set(eY.of(a),this.obj(s))}return o}},t.prototype.stream=function(t,e){return void 0===e&&(e={}),e$.of(this.obj(e),tu(t))},t.prototype.flateStream=function(t,e){return void 0===e&&(e={}),this.stream(tM().deflate(tu(t)),D(D({},e),{Filter:"FlateDecode"}))},t.prototype.contentStream=function(t,e){return void 0===e&&(e={}),e6.of(this.obj(e),t)},t.prototype.formXObject=function(t,e){return void 0===e&&(e={}),this.contentStream(t,D(D({BBox:this.obj([0,0,0,0]),Matrix:this.obj([1,0,0,1,0,0])},e),{Type:"XObject",Subtype:"Form"}))},t.prototype.getPushGraphicsStateContentStream=function(){if(this.pushGraphicsStateContentStreamRef)return this.pushGraphicsStateContentStreamRef;var t=this.obj({}),e=e5.of(e3.PushGraphicsState),r=e6.of(t,[e]);return this.pushGraphicsStateContentStreamRef=this.register(r),this.pushGraphicsStateContentStreamRef},t.prototype.getPopGraphicsStateContentStream=function(){if(this.popGraphicsStateContentStreamRef)return this.popGraphicsStateContentStreamRef;var t=this.obj({}),e=e5.of(e3.PopGraphicsState),r=e6.of(t,[e]);return this.popGraphicsStateContentStreamRef=this.register(r),this.popGraphicsStateContentStreamRef},t.prototype.addRandomSuffix=function(t,e){return void 0===e&&(e=4),t+"-"+Math.floor(this.rng.nextInt()*Math.pow(10,e))},t.create=function(){return new t},t}(),rt=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e,r)||this;return o.normalized=!1,o.autoNormalizeCTM=n,o}return N(e,t),e.prototype.clone=function(t){for(var r=e.fromMapWithContext(new Map,t||this.context,this.autoNormalizeCTM),n=this.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];r.set(s,u)}return r},e.prototype.Parent=function(){return this.lookupMaybe(eY.Parent,eQ)},e.prototype.Contents=function(){return this.lookup(eY.of("Contents"))},e.prototype.Annots=function(){return this.lookupMaybe(eY.Annots,eE)},e.prototype.BleedBox=function(){return this.lookupMaybe(eY.BleedBox,eE)},e.prototype.TrimBox=function(){return this.lookupMaybe(eY.TrimBox,eE)},e.prototype.ArtBox=function(){return this.lookupMaybe(eY.ArtBox,eE)},e.prototype.Resources=function(){var t=this.getInheritableAttribute(eY.Resources);return this.context.lookupMaybe(t,eQ)},e.prototype.MediaBox=function(){var t=this.getInheritableAttribute(eY.MediaBox);return this.context.lookup(t,eE)},e.prototype.CropBox=function(){var t=this.getInheritableAttribute(eY.CropBox);return this.context.lookupMaybe(t,eE)},e.prototype.Rotate=function(){var t=this.getInheritableAttribute(eY.Rotate);return this.context.lookupMaybe(t,eU)},e.prototype.getInheritableAttribute=function(t){var e;return this.ascend(function(r){e||(e=r.get(t))}),e},e.prototype.setParent=function(t){this.set(eY.Parent,t)},e.prototype.addContentStream=function(t){var e=this.normalizedEntries().Contents||this.context.obj([]);this.set(eY.Contents,e),e.push(t)},e.prototype.wrapContentStreams=function(t,e){var r=this.Contents();return r instanceof eE&&(r.insert(0,t),r.push(e),!0)},e.prototype.addAnnot=function(t){this.normalizedEntries().Annots.push(t)},e.prototype.removeAnnot=function(t){var e=this.normalizedEntries().Annots,r=e.indexOf(t);void 0!==r&&e.remove(r)},e.prototype.setFontDictionary=function(t,e){this.normalizedEntries().Font.set(t,e)},e.prototype.newFontDictionaryKey=function(t){return this.normalizedEntries().Font.uniqueKey(t)},e.prototype.newFontDictionary=function(t,e){var r=this.newFontDictionaryKey(t);return this.setFontDictionary(r,e),r},e.prototype.setXObject=function(t,e){this.normalizedEntries().XObject.set(t,e)},e.prototype.newXObjectKey=function(t){return this.normalizedEntries().XObject.uniqueKey(t)},e.prototype.newXObject=function(t,e){var r=this.newXObjectKey(t);return this.setXObject(r,e),r},e.prototype.setExtGState=function(t,e){this.normalizedEntries().ExtGState.set(t,e)},e.prototype.newExtGStateKey=function(t){return this.normalizedEntries().ExtGState.uniqueKey(t)},e.prototype.newExtGState=function(t,e){var r=this.newExtGStateKey(t);return this.setExtGState(r,e),r},e.prototype.ascend=function(t){t(this);var e=this.Parent();e&&e.ascend(t)},e.prototype.normalize=function(){if(!this.normalized){var t=this.context,e=this.get(eY.Contents);this.context.lookup(e)instanceof e_&&this.set(eY.Contents,t.obj([e])),this.autoNormalizeCTM&&this.wrapContentStreams(this.context.getPushGraphicsStateContentStream(),this.context.getPopGraphicsStateContentStream());var r=this.getInheritableAttribute(eY.Resources),n=t.lookupMaybe(r,eQ)||t.obj({});this.set(eY.Resources,n);var o=n.lookupMaybe(eY.Font,eQ)||t.obj({});n.set(eY.Font,o);var i=n.lookupMaybe(eY.XObject,eQ)||t.obj({});n.set(eY.XObject,i);var a=n.lookupMaybe(eY.ExtGState,eQ)||t.obj({});n.set(eY.ExtGState,a);var s=this.Annots()||t.obj([]);this.set(eY.Annots,s),this.normalized=!0}},e.prototype.normalizedEntries=function(){this.normalize();var t=this.Annots(),e=this.Resources();return{Annots:t,Resources:e,Contents:this.Contents(),Font:e.lookup(eY.Font,eQ),XObject:e.lookup(eY.XObject,eQ),ExtGState:e.lookup(eY.ExtGState,eQ)}},e.InheritableEntries=["Resources","MediaBox","CropBox","Rotate"],e.withContextAndParent=function(t,r){var n=new Map;return n.set(eY.Type,eY.Page),n.set(eY.Parent,r),n.set(eY.Resources,t.obj({})),n.set(eY.MediaBox,t.obj([0,0,612,792])),new e(n,t,!1)},e.fromMapWithContext=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(eQ),re=function(){function t(t,e){var r=this;this.traversedObjects=new Map,this.copy=function(t){return t instanceof rt?r.copyPDFPage(t):t instanceof eQ?r.copyPDFDict(t):t instanceof eE?r.copyPDFArray(t):t instanceof e_?r.copyPDFStream(t):t instanceof e2?r.copyPDFIndirectObject(t):t.clone()},this.copyPDFPage=function(t){for(var e=t.clone(),n=rt.InheritableEntries,o=0,i=n.length;o<i;o++){var a=eY.of(n[o]),s=e.getInheritableAttribute(a);!e.get(a)&&s&&e.set(a,s)}return e.delete(eY.of("Parent")),r.copyPDFDict(e)},this.copyPDFDict=function(t){if(r.traversedObjects.has(t))return r.traversedObjects.get(t);var e=t.clone(r.dest);r.traversedObjects.set(t,e);for(var n=t.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];e.set(s,r.copy(u))}return e},this.copyPDFArray=function(t){if(r.traversedObjects.has(t))return r.traversedObjects.get(t);var e=t.clone(r.dest);r.traversedObjects.set(t,e);for(var n=0,o=t.size();n<o;n++){var i=t.get(n);e.set(n,r.copy(i))}return e},this.copyPDFStream=function(t){if(r.traversedObjects.has(t))return r.traversedObjects.get(t);var e=t.clone(r.dest);r.traversedObjects.set(t,e);for(var n=t.dict.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];e.dict.set(s,r.copy(u))}return e},this.copyPDFIndirectObject=function(t){if(!r.traversedObjects.has(t)){var e=r.dest.nextRef();r.traversedObjects.set(t,e);var n=r.src.lookup(t);if(n){var o=r.copy(n);r.dest.assign(e,o)}}return r.traversedObjects.get(t)},this.src=t,this.dest=e}return t.for=function(e,r){return new t(e,r)},t}(),rr=function(){function t(t){this.subsections=t?[[t]]:[],this.chunkIdx=0,this.chunkLength=+!!t}return t.prototype.addEntry=function(t,e){this.append({ref:t,offset:e,deleted:!1})},t.prototype.addDeletedEntry=function(t,e){this.append({ref:t,offset:e,deleted:!0})},t.prototype.toString=function(){for(var t="xref\n",e=0,r=this.subsections.length;e<r;e++){var n=this.subsections[e];t+=n[0].ref.objectNumber+" "+n.length+"\n";for(var o=0,i=n.length;o<i;o++){var a=n[o];t+=H(String(a.offset),10,"0"),t+=" ",t+=H(String(a.ref.generationNumber),5,"0"),t+=" ",t+=a.deleted?"f":"n",t+=" \n"}}return t},t.prototype.sizeInBytes=function(){for(var t=5,e=0,r=this.subsections.length;e<r;e++){var n=this.subsections[e],o=n.length;t+=2,t+=String(n[0].ref.objectNumber).length,t+=String(o).length,t+=20*o}return t},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=eM.x,t[e++]=eM.r,t[e++]=eM.e,t[e++]=eM.f,t[e++]=eM.Newline,(e+=this.copySubsectionsIntoBuffer(this.subsections,t,e))-r},t.prototype.copySubsectionsIntoBuffer=function(t,e,r){for(var n=r,o=t.length,i=0;i<o;i++){var a=this.subsections[i],s=String(a[0].ref.objectNumber);r+=Z(s,e,r),e[r++]=eM.Space;var u=String(a.length);r+=Z(u,e,r),e[r++]=eM.Newline,r+=this.copyEntriesIntoBuffer(a,e,r)}return r-n},t.prototype.copyEntriesIntoBuffer=function(t,e,r){for(var n=t.length,o=0;o<n;o++){var i=t[o],a=H(String(i.offset),10,"0");r+=Z(a,e,r),e[r++]=eM.Space;var s=H(String(i.ref.generationNumber),5,"0");r+=Z(s,e,r),e[r++]=eM.Space,e[r++]=i.deleted?eM.f:eM.n,e[r++]=eM.Space,e[r++]=eM.Newline}return 20*n},t.prototype.append=function(t){if(0===this.chunkLength){this.subsections.push([t]),this.chunkIdx=0,this.chunkLength=1;return}var e=this.subsections[this.chunkIdx],r=e[this.chunkLength-1];t.ref.objectNumber-r.ref.objectNumber>1?(this.subsections.push([t]),this.chunkIdx+=1,this.chunkLength=1):(e.push(t),this.chunkLength+=1)},t.create=function(){return new t({ref:e2.of(0,65535),offset:0,deleted:!0})},t.createEmpty=function(){return new t},t}(),rn=function(){function t(t){this.lastXRefOffset=String(t)}return t.prototype.toString=function(){return"startxref\n"+this.lastXRefOffset+"\n%%EOF"},t.prototype.sizeInBytes=function(){return 16+this.lastXRefOffset.length},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=eM.s,t[e++]=eM.t,t[e++]=eM.a,t[e++]=eM.r,t[e++]=eM.t,t[e++]=eM.x,t[e++]=eM.r,t[e++]=eM.e,t[e++]=eM.f,t[e++]=eM.Newline,e+=Z(this.lastXRefOffset,t,e),t[e++]=eM.Newline,t[e++]=eM.Percent,t[e++]=eM.Percent,t[e++]=eM.E,t[e++]=eM.O,t[e++]=eM.F,e-r},t.forLastCrossRefSectionOffset=function(e){return new t(e)},t}(),ro=function(){function t(t){this.dict=t}return t.prototype.toString=function(){return"trailer\n"+this.dict.toString()},t.prototype.sizeInBytes=function(){return 8+this.dict.sizeInBytes()},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=eM.t,t[e++]=eM.r,t[e++]=eM.a,t[e++]=eM.i,t[e++]=eM.l,t[e++]=eM.e,t[e++]=eM.r,t[e++]=eM.Newline,(e+=this.dict.copyBytesInto(t,e))-r},t.of=function(e){return new t(e)},t}(),ri=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e.obj({}),n)||this;return o.objects=r,o.offsets=o.computeObjectOffsets(),o.offsetsString=o.computeOffsetsString(),o.dict.set(eY.of("Type"),eY.of("ObjStm")),o.dict.set(eY.of("N"),eU.of(o.objects.length)),o.dict.set(eY.of("First"),eU.of(o.offsetsString.length)),o}return N(e,t),e.prototype.getObjectsCount=function(){return this.objects.length},e.prototype.clone=function(t){return e.withContextAndObjects(t||this.dict.context,this.objects.slice(),this.encode)},e.prototype.getContentsString=function(){for(var t=this.offsetsString,e=0,r=this.objects.length;e<r;e++)t+=this.objects[e][1]+"\n";return t},e.prototype.getUnencodedContents=function(){for(var t=new Uint8Array(this.getUnencodedContentsSize()),e=Z(this.offsetsString,t,0),r=0,n=this.objects.length;r<n;r++){var o=this.objects[r][1];e+=o.copyBytesInto(t,e),t[e++]=eM.Newline}return t},e.prototype.getUnencodedContentsSize=function(){return this.offsetsString.length+ts(this.offsets)[1]+ts(this.objects)[1].sizeInBytes()+1},e.prototype.computeOffsetsString=function(){for(var t="",e=0,r=this.offsets.length;e<r;e++){var n=this.offsets[e];t+=n[0]+" "+n[1]+" "}return t},e.prototype.computeObjectOffsets=function(){for(var t=0,e=Array(this.objects.length),r=0,n=this.objects.length;r<n;r++){var o=this.objects[r],i=o[0],a=o[1];e[r]=[i.objectNumber,t],t+=a.sizeInBytes()+1}return e},e.withContextAndObjects=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(e4),ra=function(){function t(t,e){var r=this;this.parsedObjects=0,this.shouldWaitForTick=function(t){return r.parsedObjects+=t,r.parsedObjects%r.objectsPerTick==0},this.context=t,this.objectsPerTick=e}return t.prototype.serializeToBuffer=function(){return j(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s,u,c,h,l,f,d,p,g,y;return z(this,function(v){switch(v.label){case 0:return[4,this.computeBufferSize()];case 1:e=(t=v.sent()).size,r=t.header,n=t.indirectObjects,o=t.xref,i=t.trailerDict,a=t.trailer,s=0,u=new Uint8Array(e),s+=r.copyBytesInto(u,s),u[s++]=eM.Newline,u[s++]=eM.Newline,c=0,h=n.length,v.label=2;case 2:if(!(c<h))return[3,5];if(f=(l=n[c])[0],d=l[1],p=String(f.objectNumber),s+=Z(p,u,s),u[s++]=eM.Space,g=String(f.generationNumber),s+=Z(g,u,s),u[s++]=eM.Space,u[s++]=eM.o,u[s++]=eM.b,u[s++]=eM.j,u[s++]=eM.Newline,s+=d.copyBytesInto(u,s),u[s++]=eM.Newline,u[s++]=eM.e,u[s++]=eM.n,u[s++]=eM.d,u[s++]=eM.o,u[s++]=eM.b,u[s++]=eM.j,u[s++]=eM.Newline,u[s++]=eM.Newline,y=d instanceof ri?d.getObjectsCount():1,!this.shouldWaitForTick(y))return[3,4];return[4,tb()];case 3:v.sent(),v.label=4;case 4:return c++,[3,2];case 5:return o&&(s+=o.copyBytesInto(u,s),u[s++]=eM.Newline),i&&(s+=i.copyBytesInto(u,s),u[s++]=eM.Newline,u[s++]=eM.Newline),s+=a.copyBytesInto(u,s),[2,u]}})})},t.prototype.computeIndirectObjectSize=function(t){var e=t[0],r=t[1];return e.sizeInBytes()+3+(r.sizeInBytes()+9)},t.prototype.createTrailerDict=function(){return this.context.obj({Size:this.context.largestObjectNumber+1,Root:this.context.trailerInfo.Root,Encrypt:this.context.trailerInfo.Encrypt,Info:this.context.trailerInfo.Info,ID:this.context.trailerInfo.ID})},t.prototype.computeBufferSize=function(){return j(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s,u,c,h;return z(this,function(l){switch(l.label){case 0:e=(t=eV.forVersion(1,7)).sizeInBytes()+2,r=rr.create(),n=this.context.enumerateIndirectObjects(),o=0,i=n.length,l.label=1;case 1:if(!(o<i))return[3,4];if(s=(a=n[o])[0],r.addEntry(s,e),e+=this.computeIndirectObjectSize(a),!this.shouldWaitForTick(1))return[3,3];return[4,tb()];case 2:l.sent(),l.label=3;case 3:return o++,[3,1];case 4:return u=e,e+=r.sizeInBytes()+1,c=ro.of(this.createTrailerDict()),e+=c.sizeInBytes()+2,h=rn.forLastCrossRefSectionOffset(u),[2,{size:e+=h.sizeInBytes(),header:t,indirectObjects:n,xref:r,trailerDict:c,trailer:h}]}})})},t.forContext=function(e,r){return new t(e,r)},t}(),rs=function(t){function e(e){var r=t.call(this)||this;return r.data=e,r}return N(e,t),e.prototype.clone=function(){return e.of(this.data.slice())},e.prototype.toString=function(){return"PDFInvalidObject("+this.data.length+" bytes)"},e.prototype.sizeInBytes=function(){return this.data.length},e.prototype.copyBytesInto=function(t,e){for(var r=this.data.length,n=0;n<r;n++)t[e++]=this.data[n];return r},e.of=function(t){return new e(t)},e}(eI);!function(t){t[t.Deleted=0]="Deleted",t[t.Uncompressed=1]="Uncompressed",t[t.Compressed=2]="Compressed"}(s||(s={}));var ru=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e,n)||this;return o.computeIndex=function(){for(var t=[],e=0,r=0,n=o.entries.length;r<n;r++){var i=o.entries[r],a=o.entries[r-1];0===r?t.push(i.ref.objectNumber):i.ref.objectNumber-a.ref.objectNumber>1&&(t.push(e),t.push(i.ref.objectNumber),e=0),e+=1}return t.push(e),t},o.computeEntryTuples=function(){for(var t=Array(o.entries.length),e=0,r=o.entries.length;e<r;e++){var n=o.entries[e];if(n.type===s.Deleted){var i=n.type,a=n.nextFreeObjectNumber,u=n.ref;t[e]=[i,a,u.generationNumber]}if(n.type===s.Uncompressed){var i=n.type,c=n.offset,u=n.ref;t[e]=[i,c,u.generationNumber]}if(n.type===s.Compressed){var i=n.type,h=n.objectStreamRef,l=n.index;t[e]=[i,h.objectNumber,l]}}return t},o.computeMaxEntryByteWidths=function(){for(var t=o.entryTuplesCache.access(),e=[0,0,0],r=0,n=t.length;r<n;r++){var i=t[r],a=i[0],s=i[1],u=i[2],c=tD(a),h=tD(s),l=tD(u);c>e[0]&&(e[0]=c),h>e[1]&&(e[1]=h),l>e[2]&&(e[2]=l)}return e},o.entries=r||[],o.entryTuplesCache=eu.populatedBy(o.computeEntryTuples),o.maxByteWidthsCache=eu.populatedBy(o.computeMaxEntryByteWidths),o.indexCache=eu.populatedBy(o.computeIndex),e.set(eY.of("Type"),eY.of("XRef")),o}return N(e,t),e.prototype.addDeletedEntry=function(t,e){var r=s.Deleted;this.entries.push({type:r,ref:t,nextFreeObjectNumber:e}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.addUncompressedEntry=function(t,e){var r=s.Uncompressed;this.entries.push({type:r,ref:t,offset:e}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.addCompressedEntry=function(t,e,r){var n=s.Compressed;this.entries.push({type:n,ref:t,objectStreamRef:e,index:r}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.clone=function(t){var r=this.dict,n=this.entries,o=this.encode;return e.of(r.clone(t),n.slice(),o)},e.prototype.getContentsString=function(){for(var t=this.entryTuplesCache.access(),e=this.maxByteWidthsCache.access(),r="",n=0,o=t.length;n<o;n++){for(var i=t[n],a=i[0],s=i[1],u=i[2],c=tp(tj(a)),h=tp(tj(s)),l=tp(tj(u)),f=e[0]-1;f>=0;f--)r+=(c[f]||0).toString(2);for(var f=e[1]-1;f>=0;f--)r+=(h[f]||0).toString(2);for(var f=e[2]-1;f>=0;f--)r+=(l[f]||0).toString(2)}return r},e.prototype.getUnencodedContents=function(){for(var t=this.entryTuplesCache.access(),e=this.maxByteWidthsCache.access(),r=new Uint8Array(this.getUnencodedContentsSize()),n=0,o=0,i=t.length;o<i;o++){for(var a=t[o],s=a[0],u=a[1],c=a[2],h=tp(tj(s)),l=tp(tj(u)),f=tp(tj(c)),d=e[0]-1;d>=0;d--)r[n++]=h[d]||0;for(var d=e[1]-1;d>=0;d--)r[n++]=l[d]||0;for(var d=e[2]-1;d>=0;d--)r[n++]=f[d]||0}return r},e.prototype.getUnencodedContentsSize=function(){return tg(this.maxByteWidthsCache.access())*this.entries.length},e.prototype.updateDict=function(){t.prototype.updateDict.call(this);var e=this.maxByteWidthsCache.access(),r=this.indexCache.access(),n=this.dict.context;this.dict.set(eY.of("W"),n.obj(e)),this.dict.set(eY.of("Index"),n.obj(r))},e.create=function(t,r){void 0===r&&(r=!0);var n=new e(t,[],r);return n.addDeletedEntry(e2.of(0,65535),0),n},e.of=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(e4),rc=function(t){function e(e,r,n,o){var i=t.call(this,e,r)||this;return i.encodeStreams=n,i.objectsPerStream=o,i}return N(e,t),e.prototype.computeBufferSize=function(){return j(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s,u,c,h,l,f,d,p,g,y,v,m;return z(this,function(b){switch(b.label){case 0:t=this.context.largestObjectNumber+1,r=(e=eV.forVersion(1,7)).sizeInBytes()+2,n=ru.create(this.createTrailerDict(),this.encodeStreams),o=[],i=[],a=[],s=this.context.enumerateIndirectObjects(),u=0,c=s.length,b.label=1;case 1:if(!(u<c))return[3,6];if(l=(h=s[u])[0],f=h[1],!(l===this.context.trailerInfo.Encrypt||f instanceof e_||f instanceof rs||0!==l.generationNumber))return[3,4];if(o.push(h),n.addUncompressedEntry(l,r),r+=this.computeIndirectObjectSize(h),!this.shouldWaitForTick(1))return[3,3];return[4,tb()];case 2:b.sent(),b.label=3;case 3:return[3,5];case 4:d=ts(i),p=ts(a),d&&d.length%this.objectsPerStream!=0||(d=[],i.push(d),p=e2.of(t++),a.push(p)),n.addCompressedEntry(l,p,d.length),d.push(h),b.label=5;case 5:return u++,[3,1];case 6:u=0,c=i.length,b.label=7;case 7:if(!(u<c))return[3,10];if(d=i[u],l=a[u],g=ri.withContextAndObjects(this.context,d,this.encodeStreams),n.addUncompressedEntry(l,r),r+=this.computeIndirectObjectSize([l,g]),o.push([l,g]),!this.shouldWaitForTick(d.length))return[3,9];return[4,tb()];case 8:b.sent(),b.label=9;case 9:return u++,[3,7];case 10:return y=e2.of(t++),n.dict.set(eY.of("Size"),eU.of(t)),n.addUncompressedEntry(y,r),v=r,r+=this.computeIndirectObjectSize([y,n]),o.push([y,n]),m=rn.forLastCrossRefSectionOffset(v),[2,{size:r+=m.sizeInBytes(),header:e,indirectObjects:o,trailer:m}]}})})},e.forContext=function(t,r,n,o){return void 0===n&&(n=!0),void 0===o&&(o=50),new e(t,r,n,o)},e}(ra),rh=function(t){function e(e){var r=t.call(this)||this;return r.value=e,r}return N(e,t),e.prototype.asBytes=function(){for(var t=this.value+(this.value.length%2==1?"0":""),e=t.length,r=new Uint8Array(t.length/2),n=0,o=0;n<e;){var i=parseInt(t.substring(n,n+2),16);r[o]=i,n+=2,o+=1}return r},e.prototype.decodeText=function(){var t=this.asBytes();return tR(t)?tS(t):es(t)},e.prototype.decodeDate=function(){var t=this.decodeText(),e=ti(t);if(!e)throw new em(t);return e},e.prototype.asString=function(){return this.value},e.prototype.clone=function(){return e.of(this.value)},e.prototype.toString=function(){return"<"+this.value+">"},e.prototype.sizeInBytes=function(){return this.value.length+2},e.prototype.copyBytesInto=function(t,e){return t[e++]=eM.LessThan,e+=Z(this.value,t,e),t[e++]=eM.GreaterThan,this.value.length+2},e.of=function(t){return new e(t)},e.fromText=function(t){for(var r=tx(t),n="",o=0,i=r.length;o<i;o++)n+=G(r[o],4);return new e(n)},e}(eI),rl=function(){function t(t,e){this.encoding=t===o.ZapfDingbats?tY.ZapfDingbats:t===o.Symbol?tY.Symbol:tY.WinAnsi,this.font=tX.load(t),this.fontName=this.font.FontName,this.customName=e}return t.prototype.encodeText=function(t){for(var e=this.encodeTextAsGlyphs(t),r=Array(e.length),n=0,o=e.length;n<o;n++)r[n]=L(e[n].code);return rh.of(r.join(""))},t.prototype.widthOfTextAtSize=function(t,e){for(var r=this.encodeTextAsGlyphs(t),n=0,o=0,i=r.length;o<i;o++){var a=r[o].name,s=(r[o+1]||{}).name,u=this.font.getXAxisKerningForPair(a,s)||0;n+=this.widthOfGlyph(a)+u}return e/1e3*n},t.prototype.heightOfFontAtSize=function(t,e){void 0===e&&(e={});var r=e.descender,n=this.font,o=n.Ascender,i=n.Descender,a=n.FontBBox,s=(o||a[3])-(i||a[1]);return void 0===r||r||(s+=i||0),s/1e3*t},t.prototype.sizeOfFontAtHeight=function(t){var e=this.font,r=e.Ascender,n=e.Descender,o=e.FontBBox;return 1e3*t/((r||o[3])-(n||o[1]))},t.prototype.embedIntoContext=function(t,e){var r=t.obj({Type:"Font",Subtype:"Type1",BaseFont:this.customName||this.fontName,Encoding:this.encoding===tY.WinAnsi?"WinAnsiEncoding":void 0});return e?(t.assign(e,r),e):t.register(r)},t.prototype.widthOfGlyph=function(t){return this.font.getWidthOfGlyph(t)||250},t.prototype.encodeTextAsGlyphs=function(t){for(var e=Array.from(t),r=Array(e.length),n=0,o=e.length;n<o;n++){var i=e[n].codePointAt(0);r[n]=this.encoding.encodeUnicodeCodePoint(i)}return r},t.for=function(e,r){return new t(e,r)},t}(),rf=function(t,e){for(var r=Array(t.length),n=0,o=t.length;n<o;n++){var i=t[n],a=rp(rg(e(i))),s=rp.apply(void 0,i.codePoints.map(ry));r[n]=[a,s]}return rd(r)},rd=function(t){return"/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange\n"+t.length+" beginbfchar\n"+t.map(function(t){return t[0]+" "+t[1]}).join("\n")+"\nendbfchar\nendcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"},rp=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return"<"+t.join("")+">"},rg=function(t){return G(t,4)},ry=function(t){if(t>=0&&t<=65535)return rg(t);if(t>=65536&&t<=1114111){var e=tw(t),r=tF(t);return""+rg(e)+rg(r)}throw Error("0x"+L(t)+" is not a valid UTF-8 or UTF-16 codepoint.")},rv=function(t){var e=0,r=function(t){e|=1<<t-1};return t.fixedPitch&&r(1),t.serif&&r(2),t.symbolic&&r(3),t.script&&r(4),t.nonsymbolic&&r(6),t.italic&&r(7),t.allCap&&r(17),t.smallCap&&r(18),t.forceBold&&r(19),e},rm=function(t){var e=t["OS/2"]?t["OS/2"].sFamilyClass:0;return rv({fixedPitch:t.post.isFixedPitch,serif:1<=e&&e<=7,symbolic:!0,script:10===e,italic:t.head.macStyle.italic})},rb=function(t){function e(e){var r=t.call(this)||this;return r.value=e,r}return N(e,t),e.prototype.asBytes=function(){for(var t=[],e="",r=!1,n=function(e){void 0!==e&&t.push(e),r=!1},o=0,i=this.value.length;o<i;o++){var a=this.value[o],s=K(a),u=this.value[o+1];r?s===eM.Newline||s===eM.CarriageReturn?n():s===eM.n?n(eM.Newline):s===eM.r?n(eM.CarriageReturn):s===eM.t?n(eM.Tab):s===eM.b?n(eM.Backspace):s===eM.f?n(eM.FormFeed):s===eM.LeftParen?n(eM.LeftParen):s===eM.RightParen?n(eM.RightParen):s===eM.Backspace?n(eM.BackSlash):s>=eM.Zero&&s<=eM.Seven?3!==(e+=a).length&&u>="0"&&u<="7"||(n(parseInt(e,8)),e=""):n(s):s===eM.BackSlash?r=!0:n(s)}return new Uint8Array(t)},e.prototype.decodeText=function(){var t=this.asBytes();return tR(t)?tS(t):es(t)},e.prototype.decodeDate=function(){var t=this.decodeText(),e=ti(t);if(!e)throw new em(t);return e},e.prototype.asString=function(){return this.value},e.prototype.clone=function(){return e.of(this.value)},e.prototype.toString=function(){return"("+this.value+")"},e.prototype.sizeInBytes=function(){return this.value.length+2},e.prototype.copyBytesInto=function(t,e){return t[e++]=eM.LeftParen,e+=Z(this.value,t,e),t[e++]=eM.RightParen,this.value.length+2},e.of=function(t){return new e(t)},e.fromDate=function(t){return new e("D:"+H(String(t.getUTCFullYear()),4,"0")+H(String(t.getUTCMonth()+1),2,"0")+H(String(t.getUTCDate()),2,"0")+H(String(t.getUTCHours()),2,"0")+H(String(t.getUTCMinutes()),2,"0")+H(String(t.getUTCSeconds()),2,"0")+"Z")},e}(eI),rx=function(){function t(t,e,r,n){var o=this;this.allGlyphsInFontSortedById=function(){for(var t=Array(o.font.characterSet.length),e=0,r=t.length;e<r;e++){var n=o.font.characterSet[e];t[e]=o.font.glyphForCodePoint(n)}return td(t.sort(tf),function(t){return t.id})},this.font=t,this.scale=1e3/this.font.unitsPerEm,this.fontData=e,this.fontName=this.font.postscriptName||"Font",this.customName=r,this.fontFeatures=n,this.baseFontName="",this.glyphCache=eu.populatedBy(this.allGlyphsInFontSortedById)}return t.for=function(e,r,n,o){return j(this,void 0,void 0,function(){return z(this,function(i){switch(i.label){case 0:return[4,e.create(r)];case 1:return[2,new t(i.sent(),r,n,o)]}})})},t.prototype.encodeText=function(t){for(var e=this.font.layout(t,this.fontFeatures).glyphs,r=Array(e.length),n=0,o=e.length;n<o;n++)r[n]=G(e[n].id,4);return rh.of(r.join(""))},t.prototype.widthOfTextAtSize=function(t,e){for(var r=this.font.layout(t,this.fontFeatures).glyphs,n=0,o=0,i=r.length;o<i;o++)n+=r[o].advanceWidth*this.scale;return e/1e3*n},t.prototype.heightOfFontAtSize=function(t,e){void 0===e&&(e={});var r=e.descender,n=this.font,o=n.ascent,i=n.descent,a=n.bbox,s=(o||a.maxY)*this.scale-(i||a.minY)*this.scale;return void 0===r||r||(s-=Math.abs(i)||0),s/1e3*t},t.prototype.sizeOfFontAtHeight=function(t){var e=this.font,r=e.ascent,n=e.descent,o=e.bbox;return 1e3*t/((r||o.maxY)*this.scale-(n||o.minY)*this.scale)},t.prototype.embedIntoContext=function(t,e){return this.baseFontName=this.customName||t.addRandomSuffix(this.fontName),this.embedFontDict(t,e)},t.prototype.embedFontDict=function(t,e){return j(this,void 0,void 0,function(){var r,n,o;return z(this,function(i){switch(i.label){case 0:return[4,this.embedCIDFontDict(t)];case 1:if(r=i.sent(),n=this.embedUnicodeCmap(t),o=t.obj({Type:"Font",Subtype:"Type0",BaseFont:this.baseFontName,Encoding:"Identity-H",DescendantFonts:[r],ToUnicode:n}),e)return t.assign(e,o),[2,e];return[2,t.register(o)]}})})},t.prototype.isCFF=function(){return this.font.cff},t.prototype.embedCIDFontDict=function(t){return j(this,void 0,void 0,function(){var e,r;return z(this,function(n){switch(n.label){case 0:return[4,this.embedFontDescriptor(t)];case 1:return e=n.sent(),r=t.obj({Type:"Font",Subtype:this.isCFF()?"CIDFontType0":"CIDFontType2",CIDToGIDMap:"Identity",BaseFont:this.baseFontName,CIDSystemInfo:{Registry:rb.of("Adobe"),Ordering:rb.of("Identity"),Supplement:0},FontDescriptor:e,W:this.computeWidths()}),[2,t.register(r)]}})})},t.prototype.embedFontDescriptor=function(t){return j(this,void 0,void 0,function(){var e,r,n,o,i,a,s,u,c,h,l,f,d,p,g;return z(this,function(y){switch(y.label){case 0:return[4,this.embedFontStream(t)];case 1:return e=y.sent(),r=this.scale,o=(n=this.font).italicAngle,i=n.ascent,a=n.descent,s=n.capHeight,u=n.xHeight,h=(c=this.font.bbox).minX,l=c.minY,f=c.maxX,d=c.maxY,p=t.obj(((g={Type:"FontDescriptor",FontName:this.baseFontName,Flags:rm(this.font),FontBBox:[h*r,l*r,f*r,d*r],ItalicAngle:o,Ascent:i*r,Descent:a*r,CapHeight:(s||i)*r,XHeight:(u||0)*r,StemV:0})[this.isCFF()?"FontFile3":"FontFile2"]=e,g)),[2,t.register(p)]}})})},t.prototype.serializeFont=function(){return j(this,void 0,void 0,function(){return z(this,function(t){return[2,this.fontData]})})},t.prototype.embedFontStream=function(t){return j(this,void 0,void 0,function(){var e,r,n;return z(this,function(o){switch(o.label){case 0:return n=(r=t).flateStream,[4,this.serializeFont()];case 1:return e=n.apply(r,[o.sent(),{Subtype:this.isCFF()?"CIDFontType0C":void 0}]),[2,t.register(e)]}})})},t.prototype.embedUnicodeCmap=function(t){var e=rf(this.glyphCache.access(),this.glyphId.bind(this)),r=t.flateStream(e);return t.register(r)},t.prototype.glyphId=function(t){return t?t.id:-1},t.prototype.computeWidths=function(){for(var t=this.glyphCache.access(),e=[],r=[],n=0,o=t.length;n<o;n++){var i=t[n],a=t[n-1],s=this.glyphId(i),u=this.glyphId(a);0===n?e.push(s):s-u!=1&&(e.push(r),e.push(s),r=[]),r.push(i.advanceWidth*this.scale)}return e.push(r),e},t}(),rw=function(t){function e(e,r,n,o){var i=t.call(this,e,r,n,o)||this;return i.subset=i.font.createSubset(),i.glyphs=[],i.glyphCache=eu.populatedBy(function(){return i.glyphs}),i.glyphIdMap=new Map,i}return N(e,t),e.for=function(t,r,n,o){return j(this,void 0,void 0,function(){return z(this,function(i){switch(i.label){case 0:return[4,t.create(r)];case 1:return[2,new e(i.sent(),r,n,o)]}})})},e.prototype.encodeText=function(t){for(var e=this.font.layout(t,this.fontFeatures).glyphs,r=Array(e.length),n=0,o=e.length;n<o;n++){var i=e[n],a=this.subset.includeGlyph(i);this.glyphs[a-1]=i,this.glyphIdMap.set(i.id,a),r[n]=G(a,4)}return this.glyphCache.invalidate(),rh.of(r.join(""))},e.prototype.isCFF=function(){return this.subset.cff},e.prototype.glyphId=function(t){return t?this.glyphIdMap.get(t.id):-1},e.prototype.serializeFont=function(){var t=this;return new Promise(function(e,r){var n=[];t.subset.encodeStream().on("data",function(t){return n.push(t)}).on("end",function(){return e(th(n))}).on("error",function(t){return r(t)})})},e}(rx);!function(t){t.Source="Source",t.Data="Data",t.Alternative="Alternative",t.Supplement="Supplement",t.EncryptedPayload="EncryptedPayload",t.FormData="EncryptedPayload",t.Schema="Schema",t.Unspecified="Unspecified"}(u||(u={}));var rF=function(){function t(t,e,r){void 0===r&&(r={}),this.fileData=t,this.fileName=e,this.options=r}return t.for=function(e,r,n){return void 0===n&&(n={}),new t(e,r,n)},t.prototype.embedIntoContext=function(t,e){return j(this,void 0,void 0,function(){var r,n,o,i,a,s,u,c,h;return z(this,function(l){return(n=(r=this.options).mimeType,o=r.description,i=r.creationDate,a=r.modificationDate,s=r.afRelationship,u=t.flateStream(this.fileData,{Type:"EmbeddedFile",Subtype:null!=n?n:void 0,Params:{Size:this.fileData.length,CreationDate:i?rb.fromDate(i):void 0,ModDate:a?rb.fromDate(a):void 0}}),c=t.register(u),h=t.obj({Type:"Filespec",F:rb.of(this.fileName),UF:rh.fromText(this.fileName),EF:{F:c},Desc:o?rh.fromText(o):void 0,AFRelationship:null!=s?s:void 0}),e)?(t.assign(e,h),[2,e]):[2,t.register(h)]})})},t}(),rS=[65472,65473,65474,65475,65477,65478,65479,65480,65481,65482,65483,65484,65485,65486,65487];!function(t){t.DeviceGray="DeviceGray",t.DeviceRGB="DeviceRGB",t.DeviceCMYK="DeviceCMYK"}(c||(c={}));var rC={1:c.DeviceGray,3:c.DeviceRGB,4:c.DeviceCMYK},rk=function(){function t(t,e,r,n,o){this.imageData=t,this.bitsPerComponent=e,this.width=r,this.height=n,this.colorSpace=o}return t.for=function(e){return j(this,void 0,void 0,function(){var r,n,o,i,a,s,u;return z(this,function(c){if(65496!==(r=new DataView(e.buffer)).getUint16(0))throw Error("SOI not found in JPEG");for(n=2;n<r.byteLength&&(o=r.getUint16(n),n+=2,!rS.includes(o));)n+=r.getUint16(n);if(!rS.includes(o))throw Error("Invalid JPEG");if(n+=2,i=r.getUint8(n++),a=r.getUint16(n),n+=2,s=r.getUint16(n),n+=2,!(u=rC[r.getUint8(n++)]))throw Error("Unknown JPEG channel.");return[2,new t(e,i,s,a,u)]})})},t.prototype.embedIntoContext=function(t,e){return j(this,void 0,void 0,function(){var r;return z(this,function(n){return(r=t.stream(this.imageData,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.bitsPerComponent,Width:this.width,Height:this.height,ColorSpace:this.colorSpace,Filter:"DCTDecode",Decode:this.colorSpace===c.DeviceCMYK?[1,0,1,0,1,0,1,0]:void 0}),e)?(t.assign(e,r),[2,e]):[2,t.register(r)]})})},t}(),rT={};rT.toRGBA8=function(t){var e=t.width,r=t.height;if(null==t.tabs.acTL)return[rT.toRGBA8.decodeImage(t.data,e,r,t).buffer];var n=[];null==t.frames[0].data&&(t.frames[0].data=t.data);for(var o=e*r*4,i=new Uint8Array(o),a=new Uint8Array(o),s=new Uint8Array(o),u=0;u<t.frames.length;u++){var c=t.frames[u],h=c.rect.x,l=c.rect.y,f=c.rect.width,d=c.rect.height,p=rT.toRGBA8.decodeImage(c.data,f,d,t);if(0!=u)for(var g=0;g<o;g++)s[g]=i[g];if(0==c.blend?rT._copyTile(p,f,d,i,e,r,h,l,0):1==c.blend&&rT._copyTile(p,f,d,i,e,r,h,l,1),n.push(i.buffer.slice(0)),0==c.dispose);else if(1==c.dispose)rT._copyTile(a,f,d,i,e,r,h,l,0);else if(2==c.dispose)for(var g=0;g<o;g++)i[g]=s[g]}return n},rT.toRGBA8.decodeImage=function(t,e,r,n){var o=e*r,i=Math.ceil(e*rT.decode._getBPP(n)/8),a=new Uint8Array(4*o),s=new Uint32Array(a.buffer),u=n.ctype,c=n.depth,h=rT._bin.readUshort;if(Date.now(),6==u){var l=o<<2;if(8==c)for(var f=0;f<l;f+=4)a[f]=t[f],a[f+1]=t[f+1],a[f+2]=t[f+2],a[f+3]=t[f+3];if(16==c)for(var f=0;f<l;f++)a[f]=t[f<<1]}else if(2==u){var d=n.tabs.tRNS;if(null==d){if(8==c)for(var f=0;f<o;f++){var p=3*f;s[f]=-0x1000000|t[p+2]<<16|t[p+1]<<8|t[p]}if(16==c)for(var f=0;f<o;f++){var p=6*f;s[f]=-0x1000000|t[p+4]<<16|t[p+2]<<8|t[p]}}else{var g=d[0],y=d[1],v=d[2];if(8==c)for(var f=0;f<o;f++){var m=f<<2,p=3*f;s[f]=-0x1000000|t[p+2]<<16|t[p+1]<<8|t[p],t[p]==g&&t[p+1]==y&&t[p+2]==v&&(a[m+3]=0)}if(16==c)for(var f=0;f<o;f++){var m=f<<2,p=6*f;s[f]=-0x1000000|t[p+4]<<16|t[p+2]<<8|t[p],h(t,p)==g&&h(t,p+2)==y&&h(t,p+4)==v&&(a[m+3]=0)}}}else if(3==u){var b=n.tabs.PLTE,x=n.tabs.tRNS,w=x?x.length:0;if(1==c)for(var F=0;F<r;F++)for(var S=F*i,C=F*e,f=0;f<e;f++){var m=C+f<<2,k=t[S+(f>>3)]>>7-(7&f)&1,T=3*k;a[m]=b[T],a[m+1]=b[T+1],a[m+2]=b[T+2],a[m+3]=k<w?x[k]:255}if(2==c)for(var F=0;F<r;F++)for(var S=F*i,C=F*e,f=0;f<e;f++){var m=C+f<<2,k=t[S+(f>>2)]>>6-((3&f)<<1)&3,T=3*k;a[m]=b[T],a[m+1]=b[T+1],a[m+2]=b[T+2],a[m+3]=k<w?x[k]:255}if(4==c)for(var F=0;F<r;F++)for(var S=F*i,C=F*e,f=0;f<e;f++){var m=C+f<<2,k=t[S+(f>>1)]>>4-((1&f)<<2)&15,T=3*k;a[m]=b[T],a[m+1]=b[T+1],a[m+2]=b[T+2],a[m+3]=k<w?x[k]:255}if(8==c)for(var f=0;f<o;f++){var m=f<<2,k=t[f],T=3*k;a[m]=b[T],a[m+1]=b[T+1],a[m+2]=b[T+2],a[m+3]=k<w?x[k]:255}}else if(4==u){if(8==c)for(var f=0;f<o;f++){var m=f<<2,O=f<<1,A=t[O];a[m]=A,a[m+1]=A,a[m+2]=A,a[m+3]=t[O+1]}if(16==c)for(var f=0;f<o;f++){var m=f<<2,O=f<<2,A=t[O];a[m]=A,a[m+1]=A,a[m+2]=A,a[m+3]=t[O+2]}}else if(0==u)for(var g=n.tabs.tRNS?n.tabs.tRNS:-1,F=0;F<r;F++){var P=F*i,R=F*e;if(1==c)for(var N=0;N<e;N++){var A=255*(t[P+(N>>>3)]>>>7-(7&N)&1),D=255*(A!=255*g);s[R+N]=D<<24|A<<16|A<<8|A}else if(2==c)for(var N=0;N<e;N++){var A=85*(t[P+(N>>>2)]>>>6-((3&N)<<1)&3),D=255*(A!=85*g);s[R+N]=D<<24|A<<16|A<<8|A}else if(4==c)for(var N=0;N<e;N++){var A=17*(t[P+(N>>>1)]>>>4-((1&N)<<2)&15),D=255*(A!=17*g);s[R+N]=D<<24|A<<16|A<<8|A}else if(8==c)for(var N=0;N<e;N++){var A=t[P+N],D=255*(A!=g);s[R+N]=D<<24|A<<16|A<<8|A}else if(16==c)for(var N=0;N<e;N++){var A=t[P+(N<<1)],D=255*(h(t,P+(N<<f))!=g);s[R+N]=D<<24|A<<16|A<<8|A}}return a},rT.decode=function(t){for(var e,r=new Uint8Array(t),n=8,o=rT._bin,i=o.readUshort,a=o.readUint,s={tabs:{},frames:[]},u=new Uint8Array(r.length),c=0,h=0,l=[137,80,78,71,13,10,26,10],f=0;f<8;f++)if(r[f]!=l[f])throw"The input is not a PNG file!";for(;n<r.length;){var d=o.readUint(r,n);n+=4;var p=o.readASCII(r,n,4);if(n+=4,"IHDR"==p)rT.decode._IHDR(r,n,s);else if("IDAT"==p){for(var f=0;f<d;f++)u[c+f]=r[n+f];c+=d}else if("acTL"==p)s.tabs[p]={num_frames:a(r,n),num_plays:a(r,n+4)},e=new Uint8Array(r.length);else if("fcTL"==p){if(0!=h){var g=s.frames[s.frames.length-1];g.data=rT.decode._decompress(s,e.slice(0,h),g.rect.width,g.rect.height),h=0}var y={x:a(r,n+12),y:a(r,n+16),width:a(r,n+4),height:a(r,n+8)},v=i(r,n+22),m={rect:y,delay:Math.round(1e3*(v=i(r,n+20)/(0==v?100:v))),dispose:r[n+24],blend:r[n+25]};s.frames.push(m)}else if("fdAT"==p){for(var f=0;f<d-4;f++)e[h+f]=r[n+f+4];h+=d-4}else if("pHYs"==p)s.tabs[p]=[o.readUint(r,n),o.readUint(r,n+4),r[n+8]];else if("cHRM"==p){s.tabs[p]=[];for(var f=0;f<8;f++)s.tabs[p].push(o.readUint(r,n+4*f))}else if("tEXt"==p){null==s.tabs[p]&&(s.tabs[p]={});var b=o.nextZero(r,n),x=o.readASCII(r,n,b-n),w=o.readASCII(r,b+1,n+d-b-1);s.tabs[p][x]=w}else if("iTXt"==p){null==s.tabs[p]&&(s.tabs[p]={});var b=0,F=n;b=o.nextZero(r,F);var x=o.readASCII(r,F,b-F);r[F=b+1],r[F+1],F+=2,b=o.nextZero(r,F),o.readASCII(r,F,b-F),F=b+1,b=o.nextZero(r,F),o.readUTF8(r,F,b-F),F=b+1;var w=o.readUTF8(r,F,d-(F-n));s.tabs[p][x]=w}else if("PLTE"==p)s.tabs[p]=o.readBytes(r,n,d);else if("hIST"==p){var S=s.tabs.PLTE.length/3;s.tabs[p]=[];for(var f=0;f<S;f++)s.tabs[p].push(i(r,n+2*f))}else if("tRNS"==p)3==s.ctype?s.tabs[p]=o.readBytes(r,n,d):0==s.ctype?s.tabs[p]=i(r,n):2==s.ctype&&(s.tabs[p]=[i(r,n),i(r,n+2),i(r,n+4)]);else if("gAMA"==p)s.tabs[p]=o.readUint(r,n)/1e5;else if("sRGB"==p)s.tabs[p]=r[n];else if("bKGD"==p)0==s.ctype||4==s.ctype?s.tabs[p]=[i(r,n)]:2==s.ctype||6==s.ctype?s.tabs[p]=[i(r,n),i(r,n+2),i(r,n+4)]:3==s.ctype&&(s.tabs[p]=r[n]);else if("IEND"==p)break;n+=d,o.readUint(r,n),n+=4}if(0!=h){var g=s.frames[s.frames.length-1];g.data=rT.decode._decompress(s,e.slice(0,h),g.rect.width,g.rect.height),h=0}return s.data=rT.decode._decompress(s,u,s.width,s.height),delete s.compress,delete s.interlace,delete s.filter,s},rT.decode._decompress=function(t,e,r,n){Date.now();var o=new Uint8Array((Math.ceil(r*rT.decode._getBPP(t)/8)+1+t.interlace)*n);return e=rT.decode._inflate(e,o),Date.now(),0==t.interlace?e=rT.decode._filterZero(e,t,0,r,n):1==t.interlace&&(e=rT.decode._readInterlace(e,t)),e},rT.decode._inflate=function(t,e){return rT.inflateRaw(new Uint8Array(t.buffer,2,t.length-6),e)},rT.inflateRaw=function(){var t,e,r={};return r.H={},r.H.N=function(t,e){var n,o,i=Uint8Array,a=0,s=0,u=0,c=0,h=0,l=0,f=0,d=0,p=0;if(3==t[0]&&0==t[1])return e||new i(0);var g=r.H,y=g.b,v=g.e,m=g.R,b=g.n,x=g.A,w=g.Z,F=g.m,S=null==e;for(S&&(e=new i(t.length>>>2<<3));0==a;){if(a=y(t,p,1),s=y(t,p+1,2),p+=3,0==s){(7&p)!=0&&(p+=8-(7&p));var C=(p>>>3)+4,k=t[C-4]|t[C-3]<<8;S&&(e=r.H.W(e,d+k)),e.set(new i(t.buffer,t.byteOffset+C,k),d),p=C+k<<3,d+=k;continue}if(S&&(e=r.H.W(e,d+131072)),1==s&&(n=F.J,o=F.h,l=511,f=31),2==s){u=v(t,p,5)+257,c=v(t,p+5,5)+1,h=v(t,p+10,4)+4,p+=14;for(var T=1,O=0;O<38;O+=2)F.Q[O]=0,F.Q[O+1]=0;for(var O=0;O<h;O++){var A=v(t,p+3*O,3);F.Q[(F.X[O]<<1)+1]=A,A>T&&(T=A)}p+=3*h,b(F.Q,T),x(F.Q,T,F.u),n=F.w,o=F.d,p=m(F.u,(1<<T)-1,u+c,t,p,F.v);var P=g.V(F.v,0,u,F.C);l=(1<<P)-1;var R=g.V(F.v,u,c,F.D);f=(1<<R)-1,b(F.C,P),x(F.C,P,n),b(F.D,R),x(F.D,R,o)}for(;;){var N=n[w(t,p)&l];p+=15&N;var D=N>>>4;if(D>>>8==0)e[d++]=D;else if(256==D)break;else{var j=d+D-254;if(D>264){var z=F.q[D-257];j=d+(z>>>3)+v(t,p,7&z),p+=7&z}var B=o[w(t,p)&f];p+=15&B;var M=B>>>4,V=F.c[M],I=(V>>>4)+y(t,p,15&V);for(p+=15&V;d<j;)e[d]=e[d++-I],e[d]=e[d++-I],e[d]=e[d++-I],e[d]=e[d++-I];d=j}}}return e.length==d?e:e.slice(0,d)},r.H.W=function(t,e){var r=t.length;if(e<=r)return t;var n=new Uint8Array(r<<1);return n.set(t,0),n},r.H.R=function(t,e,n,o,i,a){for(var s=r.H.e,u=r.H.Z,c=0;c<n;){var h=t[u(o,i)&e];i+=15&h;var l=h>>>4;if(l<=15)a[c]=l,c++;else{var f=0,d=0;16==l?(d=3+s(o,i,2),i+=2,f=a[c-1]):17==l?(d=3+s(o,i,3),i+=3):18==l&&(d=11+s(o,i,7),i+=7);for(var p=c+d;c<p;)a[c]=f,c++}}return i},r.H.V=function(t,e,r,n){for(var o=0,i=0,a=n.length>>>1;i<r;){var s=t[i+e];n[i<<1]=0,n[(i<<1)+1]=s,s>o&&(o=s),i++}for(;i<a;)n[i<<1]=0,n[(i<<1)+1]=0,i++;return o},r.H.n=function(t,e){for(var n,o,i,a,s,u=r.H.m,c=t.length,h=u.j,a=0;a<=e;a++)h[a]=0;for(a=1;a<c;a+=2)h[t[a]]++;var l=u.K;for(o=1,n=0,h[0]=0;o<=e;o++)n=n+h[o-1]<<1,l[o]=n;for(i=0;i<c;i+=2)0!=(s=t[i+1])&&(t[i]=l[s],l[s]++)},r.H.A=function(t,e,n){for(var o=t.length,i=r.H.m.r,a=0;a<o;a+=2)if(0!=t[a+1])for(var s=a>>1,u=t[a+1],c=s<<4|u,h=e-u,l=t[a]<<h,f=l+(1<<h);l!=f;)n[i[l]>>>15-e]=c,l++},r.H.l=function(t,e){for(var n=r.H.m.r,o=15-e,i=0;i<t.length;i+=2){var a=t[i]<<e-t[i+1];t[i]=n[a]>>>o}},r.H.M=function(t,e,r){r<<=7&e;var n=e>>>3;t[n]|=r,t[n+1]|=r>>>8},r.H.I=function(t,e,r){r<<=7&e;var n=e>>>3;t[n]|=r,t[n+1]|=r>>>8,t[n+2]|=r>>>16},r.H.e=function(t,e,r){return(t[e>>>3]|t[(e>>>3)+1]<<8)>>>(7&e)&(1<<r)-1},r.H.b=function(t,e,r){return(t[e>>>3]|t[(e>>>3)+1]<<8|t[(e>>>3)+2]<<16)>>>(7&e)&(1<<r)-1},r.H.Z=function(t,e){return(t[e>>>3]|t[(e>>>3)+1]<<8|t[(e>>>3)+2]<<16)>>>(7&e)},r.H.i=function(t,e){return(t[e>>>3]|t[(e>>>3)+1]<<8|t[(e>>>3)+2]<<16|t[(e>>>3)+3]<<24)>>>(7&e)},r.H.m=(t=Uint16Array,e=Uint32Array,{K:new t(16),j:new t(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new t(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new e(32),J:new t(512),_:[],h:new t(32),$:[],w:new t(32768),C:[],v:[],d:new t(32768),D:[],u:new t(512),Q:[],r:new t(32768),s:new e(286),Y:new e(30),a:new e(19),t:new e(15e3),k:new t(65536),g:new t(32768)}),!function(){for(var t=r.H.m,e=0;e<32768;e++){var n=e;n=(0xff00ff00&(n=(0xf0f0f0f0&(n=(0xcccccccc&(n=(0xaaaaaaaa&n)>>>1|(0x55555555&n)<<1))>>>2|(0x33333333&n)<<2))>>>4|(0xf0f0f0f&n)<<4))>>>8|(0xff00ff&n)<<8,t.r[e]=(n>>>16|n<<16)>>>17}function o(t,e,r){for(;0!=e--;)t.push(0,r)}for(var e=0;e<32;e++)t.q[e]=t.S[e]<<3|t.T[e],t.c[e]=t.p[e]<<4|t.z[e];o(t._,144,8),o(t._,112,9),o(t._,24,7),o(t._,8,8),r.H.n(t._,9),r.H.A(t._,9,t.J),r.H.l(t._,9),o(t.$,32,5),r.H.n(t.$,5),r.H.A(t.$,5,t.h),r.H.l(t.$,5),o(t.Q,19,0),o(t.C,286,0),o(t.D,30,0),o(t.v,320,0)}(),r.H.N}(),rT.decode._readInterlace=function(t,e){for(var r=e.width,n=e.height,o=rT.decode._getBPP(e),i=o>>3,a=Math.ceil(r*o/8),s=new Uint8Array(n*a),u=0,c=[0,0,4,0,2,0,1],h=[0,4,0,2,0,1,0],l=[8,8,8,4,4,2,2],f=[8,8,4,4,2,2,1],d=0;d<7;){for(var p=l[d],g=f[d],y=0,v=0,m=c[d];m<n;)m+=p,v++;for(var b=h[d];b<r;)b+=g,y++;var x=Math.ceil(y*o/8);rT.decode._filterZero(t,e,u,y,v);for(var w=0,F=c[d];F<n;){for(var S=h[d],C=u+w*x<<3;S<r;){if(1==o){var k=t[C>>3];k=k>>7-(7&C)&1,s[F*a+(S>>3)]|=k<<7-(7&S)}if(2==o){var k=t[C>>3];k=k>>6-(7&C)&3,s[F*a+(S>>2)]|=k<<6-((3&S)<<1)}if(4==o){var k=t[C>>3];k=k>>4-(7&C)&15,s[F*a+(S>>1)]|=k<<4-((1&S)<<2)}if(o>=8)for(var T=F*a+S*i,O=0;O<i;O++)s[T+O]=t[(C>>3)+O];C+=o,S+=g}w++,F+=p}y*v!=0&&(u+=v*(1+x)),d+=1}return s},rT.decode._getBPP=function(t){return[1,null,3,1,2,null,4][t.ctype]*t.depth},rT.decode._filterZero=function(t,e,r,n,o){var i=rT.decode._getBPP(e),a=Math.ceil(n*i/8),s=rT.decode._paeth;i=Math.ceil(i/8);var u=0,c=1,h=t[r],l=0;if(h>1&&(t[r]=[0,0,1][h-2]),3==h)for(l=i;l<a;l++)t[l+1]=t[l+1]+(t[l+1-i]>>>1)&255;for(var f=0;f<o;f++)if(h=t[(c=(u=r+f*a)+f+1)-1],l=0,0==h)for(;l<a;l++)t[u+l]=t[c+l];else if(1==h){for(;l<i;l++)t[u+l]=t[c+l];for(;l<a;l++)t[u+l]=t[c+l]+t[u+l-i]}else if(2==h)for(;l<a;l++)t[u+l]=t[c+l]+t[u+l-a];else if(3==h){for(;l<i;l++)t[u+l]=t[c+l]+(t[u+l-a]>>>1);for(;l<a;l++)t[u+l]=t[c+l]+(t[u+l-a]+t[u+l-i]>>>1)}else{for(;l<i;l++)t[u+l]=t[c+l]+s(0,t[u+l-a],0);for(;l<a;l++)t[u+l]=t[c+l]+s(t[u+l-i],t[u+l-a],t[u+l-i-a])}return t},rT.decode._paeth=function(t,e,r){var n=t+e-r,o=n-t,i=n-e,a=n-r;return o*o<=i*i&&o*o<=a*a?t:i*i<=a*a?e:r},rT.decode._IHDR=function(t,e,r){var n=rT._bin;r.width=n.readUint(t,e),e+=4,r.height=n.readUint(t,e),r.depth=t[e+=4],r.ctype=t[++e],r.compress=t[++e],r.filter=t[++e],r.interlace=t[++e],e++},rT._bin={nextZero:function(t,e){for(;0!=t[e];)e++;return e},readUshort:function(t,e){return t[e]<<8|t[e+1]},writeUshort:function(t,e,r){t[e]=r>>8&255,t[e+1]=255&r},readUint:function(t,e){return 0x1000000*t[e]+(t[e+1]<<16|t[e+2]<<8|t[e+3])},writeUint:function(t,e,r){t[e]=r>>24&255,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=255&r},readASCII:function(t,e,r){for(var n="",o=0;o<r;o++)n+=String.fromCharCode(t[e+o]);return n},writeASCII:function(t,e,r){for(var n=0;n<r.length;n++)t[e+n]=r.charCodeAt(n)},readBytes:function(t,e,r){for(var n=[],o=0;o<r;o++)n.push(t[e+o]);return n},pad:function(t){return t.length<2?"0"+t:t},readUTF8:function(t,e,r){for(var n,o="",i=0;i<r;i++)o+="%"+rT._bin.pad(t[e+i].toString(16));try{n=decodeURIComponent(o)}catch(n){return rT._bin.readASCII(t,e,r)}return n}},rT._copyTile=function(t,e,r,n,o,i,a,s,u){for(var c=Math.min(e,o),h=Math.min(r,i),l=0,f=0,d=0;d<h;d++)for(var p=0;p<c;p++)if(a>=0&&s>=0?(l=d*e+p<<2,f=(s+d)*o+a+p<<2):(l=(-s+d)*e-a+p<<2,f=d*o+p<<2),0==u)n[f]=t[l],n[f+1]=t[l+1],n[f+2]=t[l+2],n[f+3]=t[l+3];else if(1==u){var g=t[l+3]*(1/255),y=t[l]*g,v=t[l+1]*g,m=t[l+2]*g,b=n[f+3]*(1/255),x=n[f]*b,w=n[f+1]*b,F=n[f+2]*b,S=1-g,C=g+b*S,k=0==C?0:1/C;n[f+3]=255*C,n[f+0]=(y+x*S)*k,n[f+1]=(v+w*S)*k,n[f+2]=(m+F*S)*k}else if(2==u){var g=t[l+3],y=t[l],v=t[l+1],m=t[l+2],b=n[f+3],x=n[f],w=n[f+1],F=n[f+2];g==b&&y==x&&v==w&&m==F?(n[f]=0,n[f+1]=0,n[f+2]=0,n[f+3]=0):(n[f]=y,n[f+1]=v,n[f+2]=m,n[f+3]=g)}else if(3==u){var g=t[l+3],y=t[l],v=t[l+1],m=t[l+2],b=n[f+3],x=n[f],w=n[f+1],F=n[f+2];if(g==b&&y==x&&v==w&&m==F)continue;if(g<220&&b>20)return!1}return!0},rT.encode=function(t,e,r,n,o,i,a){null==n&&(n=0),null==a&&(a=!1);var s=rT.encode.compress(t,e,r,n,[!1,!1,!1,0,a]);return rT.encode.compressPNG(s,-1),rT.encode._main(s,e,r,o,i)},rT.encodeLL=function(t,e,r,n,o,i,a,s){var u={ctype:0+2*(1!=n)+4*(0!=o),depth:i,frames:[]};Date.now();for(var c=(n+o)*i,h=c*e,l=0;l<t.length;l++)u.frames.push({rect:{x:0,y:0,width:e,height:r},img:new Uint8Array(t[l]),blend:0,dispose:1,bpp:Math.ceil(c/8),bpl:Math.ceil(h/8)});return rT.encode.compressPNG(u,0,!0),rT.encode._main(u,e,r,a,s)},rT.encode._main=function(t,e,r,n,o){null==o&&(o={});var i=rT.crc.crc,a=rT._bin.writeUint,s=rT._bin.writeUshort,u=rT._bin.writeASCII,c=8,h=t.frames.length>1,l=!1,f=33+20*!!h;if(null!=o.sRGB&&(f+=13),null!=o.pHYs&&(f+=21),3==t.ctype){for(var d=t.plte.length,p=0;p<d;p++)t.plte[p]>>>24!=255&&(l=!0);f+=8+3*d+4+(l?8+ +d+4:0)}for(var g=0;g<t.frames.length;g++){var y=t.frames[g];h&&(f+=38),f+=y.cimg.length+12,0!=g&&(f+=4)}for(var v=new Uint8Array(f+=12),m=[137,80,78,71,13,10,26,10],p=0;p<8;p++)v[p]=m[p];if(a(v,c,13),u(v,c+=4,"IHDR"),a(v,c+=4,e),a(v,c+=4,r),v[c+=4]=t.depth,v[++c]=t.ctype,v[++c]=0,v[++c]=0,v[++c]=0,a(v,++c,i(v,c-17,17)),c+=4,null!=o.sRGB&&(a(v,c,1),u(v,c+=4,"sRGB"),v[c+=4]=o.sRGB,a(v,++c,i(v,c-5,5)),c+=4),null!=o.pHYs&&(a(v,c,9),u(v,c+=4,"pHYs"),a(v,c+=4,o.pHYs[0]),a(v,c+=4,o.pHYs[1]),v[c+=4]=o.pHYs[2],a(v,++c,i(v,c-13,13)),c+=4),h&&(a(v,c,8),u(v,c+=4,"acTL"),a(v,c+=4,t.frames.length),a(v,c+=4,null!=o.loop?o.loop:0),a(v,c+=4,i(v,c-12,12)),c+=4),3==t.ctype){var d=t.plte.length;a(v,c,3*d),u(v,c+=4,"PLTE"),c+=4;for(var p=0;p<d;p++){var b=3*p,x=t.plte[p],w=255&x,F=x>>>8&255,S=x>>>16&255;v[c+b+0]=w,v[c+b+1]=F,v[c+b+2]=S}if(a(v,c+=3*d,i(v,c-3*d-4,3*d+4)),c+=4,l){a(v,c,d),u(v,c+=4,"tRNS"),c+=4;for(var p=0;p<d;p++)v[c+p]=t.plte[p]>>>24&255;a(v,c+=d,i(v,c-d-4,d+4)),c+=4}}for(var C=0,g=0;g<t.frames.length;g++){var y=t.frames[g];h&&(a(v,c,26),u(v,c+=4,"fcTL"),a(v,c+=4,C++),a(v,c+=4,y.rect.width),a(v,c+=4,y.rect.height),a(v,c+=4,y.rect.x),a(v,c+=4,y.rect.y),s(v,c+=4,n[g]),s(v,c+=2,1e3),v[c+=2]=y.dispose,v[++c]=y.blend,a(v,++c,i(v,c-30,30)),c+=4);var k=y.cimg,d=k.length;a(v,c,d+4*(0!=g));var T=c+=4;u(v,c,0==g?"IDAT":"fdAT"),c+=4,0!=g&&(a(v,c,C++),c+=4),v.set(k,c),a(v,c+=d,i(v,T,c-T)),c+=4}return a(v,c,0),u(v,c+=4,"IEND"),a(v,c+=4,i(v,c-4,4)),c+=4,v.buffer},rT.encode.compressPNG=function(t,e,r){for(var n=0;n<t.frames.length;n++){var o=t.frames[n],i=(o.rect.width,o.rect.height),a=new Uint8Array(i*o.bpl+i);o.cimg=rT.encode._filterZero(o.img,i,o.bpp,o.bpl,a,e,r)}},rT.encode.compress=function(t,e,r,n,o){for(var i=o[0],a=o[1],s=o[2],u=o[3],c=o[4],h=6,l=8,f=255,d=0;d<t.length;d++)for(var p=new Uint8Array(t[d]),g=p.length,y=0;y<g;y+=4)f&=p[y+3];var v=255!=f,m=rT.encode.framize(t,e,r,i,a,s),b={},x=[],w=[];if(0!=n){for(var F=[],y=0;y<m.length;y++)F.push(m[y].img.buffer);for(var S=rT.encode.concatRGBA(F),C=rT.quantize(S,n),k=0,T=new Uint8Array(C.abuf),y=0;y<m.length;y++){var O=m[y].img,A=O.length;w.push(new Uint8Array(C.inds.buffer,k>>2,A>>2));for(var d=0;d<A;d+=4)O[d]=T[k+d],O[d+1]=T[k+d+1],O[d+2]=T[k+d+2],O[d+3]=T[k+d+3];k+=A}for(var y=0;y<C.plte.length;y++)x.push(C.plte[y].est.rgba)}else for(var d=0;d<m.length;d++){var P=m[d],R=new Uint32Array(P.img.buffer),N=P.rect.width,g=R.length,D=new Uint8Array(g);w.push(D);for(var y=0;y<g;y++){var j=R[y];if(0!=y&&j==R[y-1])D[y]=D[y-1];else if(y>N&&j==R[y-N])D[y]=D[y-N];else{var z=b[j];if(null==z&&(b[j]=z=x.length,x.push(j),x.length>=300))break;D[y]=z}}}var B=x.length;B<=256&&!1==c&&(l=Math.max(l=B<=2?1:B<=4?2:B<=16?4:8,u));for(var d=0;d<m.length;d++){var P=m[d],N=(P.rect.x,P.rect.y,P.rect.width),M=P.rect.height,V=P.img;new Uint32Array(V.buffer);var I=4*N,U=4;if(B<=256&&!1==c){for(var E=new Uint8Array((I=Math.ceil(l*N/8))*M),W=w[d],q=0;q<M;q++){var y=q*I,K=q*N;if(8==l)for(var G=0;G<N;G++)E[y+G]=W[K+G];else if(4==l)for(var G=0;G<N;G++)E[y+(G>>1)]|=W[K+G]<<4-(1&G)*4;else if(2==l)for(var G=0;G<N;G++)E[y+(G>>2)]|=W[K+G]<<6-(3&G)*2;else if(1==l)for(var G=0;G<N;G++)E[y+(G>>3)]|=W[K+G]<<7-(7&G)*1}V=E,h=3,U=1}else if(!1==v&&1==m.length){for(var E=new Uint8Array(N*M*3),L=N*M,y=0;y<L;y++){var O=3*y,X=4*y;E[O]=V[X],E[O+1]=V[X+1],E[O+2]=V[X+2]}V=E,h=2,U=3,I=3*N}P.img=V,P.bpl=I,P.bpp=U}return{ctype:h,depth:l,plte:x,frames:m}},rT.encode.framize=function(t,e,r,n,o,i){for(var a=[],s=0;s<t.length;s++){var u,c=new Uint8Array(t[s]),h=new Uint32Array(c.buffer),l=0,f=0,d=e,p=r,g=+!!n;if(0!=s){for(var y=i||n||1==s||0!=a[s-2].dispose?1:2,v=0,m=1e9,b=0;b<y;b++){for(var x=new Uint8Array(t[s-1-b]),w=new Uint32Array(t[s-1-b]),F=e,S=r,C=-1,k=-1,T=0;T<r;T++)for(var O=0;O<e;O++){var A=T*e+O;h[A]!=w[A]&&(O<F&&(F=O),O>C&&(C=O),T<S&&(S=T),T>k&&(k=T))}-1==C&&(F=S=C=k=0),o&&((1&F)==1&&F--,(1&S)==1&&S--);var P=(C-F+1)*(k-S+1);P<m&&(m=P,v=b,l=F,f=S,d=C-F+1,p=k-S+1)}var x=new Uint8Array(t[s-1-v]);1==v&&(a[s-1].dispose=2),u=new Uint8Array(d*p*4),rT._copyTile(x,e,r,u,d,p,-l,-f,0),1==(g=+!!rT._copyTile(c,e,r,u,d,p,-l,-f,3))?rT.encode._prepareDiff(c,e,r,u,{x:l,y:f,width:d,height:p}):rT._copyTile(c,e,r,u,d,p,-l,-f,0)}else u=c.slice(0);a.push({rect:{x:l,y:f,width:d,height:p},img:u,blend:g,dispose:0})}if(n)for(var s=0;s<a.length;s++){var R=a[s];if(1!=R.blend){var N=R.rect,D=a[s-1].rect,j=Math.min(N.x,D.x),z=Math.min(N.y,D.y),B={x:j,y:z,width:Math.max(N.x+N.width,D.x+D.width)-j,height:Math.max(N.y+N.height,D.y+D.height)-z};a[s-1].dispose=1,s-1!=0&&rT.encode._updateFrame(t,e,r,a,s-1,B,o),rT.encode._updateFrame(t,e,r,a,s,B,o)}}if(1!=t.length)for(var A=0;A<a.length;A++){var R=a[A];R.rect.width,R.rect.height}return a},rT.encode._updateFrame=function(t,e,r,n,o,i,a){for(var s=Uint8Array,u=Uint32Array,c=new s(t[o-1]),h=new u(t[o-1]),l=o+1<t.length?new s(t[o+1]):null,f=new s(t[o]),d=new u(f.buffer),p=e,g=r,y=-1,v=-1,m=0;m<i.height;m++)for(var b=0;b<i.width;b++){var x=i.x+b,w=i.y+m,F=w*e+x,S=d[F];0==S||0==n[o-1].dispose&&h[F]==S&&(null==l||0!=l[4*F+3])||(x<p&&(p=x),x>y&&(y=x),w<g&&(g=w),w>v&&(v=w))}-1==y&&(p=g=y=v=0),a&&((1&p)==1&&p--,(1&g)==1&&g--),i={x:p,y:g,width:y-p+1,height:v-g+1};var C=n[o];C.rect=i,C.blend=1,C.img=new Uint8Array(i.width*i.height*4),0==n[o-1].dispose?(rT._copyTile(c,e,r,C.img,i.width,i.height,-i.x,-i.y,0),rT.encode._prepareDiff(f,e,r,C.img,i)):rT._copyTile(f,e,r,C.img,i.width,i.height,-i.x,-i.y,0)},rT.encode._prepareDiff=function(t,e,r,n,o){rT._copyTile(t,e,r,n,o.width,o.height,-o.x,-o.y,2)},rT.encode._filterZero=function(t,e,r,n,o,i,a){var s=[],u=[0,1,2,3,4];-1!=i?u=[i]:(e*n>5e5||1==r)&&(u=[0]),a&&(f={level:0});for(var c=a&&null!=UZIP?UZIP:tM(),h=0;h<u.length;h++){for(var l=0;l<e;l++)rT.encode._filterLine(o,t,l,n,r,u[h]);s.push(c.deflate(o,f))}for(var f,d,p=1e9,h=0;h<s.length;h++)s[h].length<p&&(d=h,p=s[h].length);return s[d]},rT.encode._filterLine=function(t,e,r,n,o,i){var a=r*n,s=a+r,u=rT.decode._paeth;if(t[s]=i,s++,0==i)if(n<500)for(var c=0;c<n;c++)t[s+c]=e[a+c];else t.set(new Uint8Array(e.buffer,a,n),s);else if(1==i){for(var c=0;c<o;c++)t[s+c]=e[a+c];for(var c=o;c<n;c++)t[s+c]=e[a+c]-e[a+c-o]+256&255}else if(0==r){for(var c=0;c<o;c++)t[s+c]=e[a+c];if(2==i)for(var c=o;c<n;c++)t[s+c]=e[a+c];if(3==i)for(var c=o;c<n;c++)t[s+c]=e[a+c]-(e[a+c-o]>>1)+256&255;if(4==i)for(var c=o;c<n;c++)t[s+c]=e[a+c]-u(e[a+c-o],0,0)+256&255}else{if(2==i)for(var c=0;c<n;c++)t[s+c]=e[a+c]+256-e[a+c-n]&255;if(3==i){for(var c=0;c<o;c++)t[s+c]=e[a+c]+256-(e[a+c-n]>>1)&255;for(var c=o;c<n;c++)t[s+c]=e[a+c]+256-(e[a+c-n]+e[a+c-o]>>1)&255}if(4==i){for(var c=0;c<o;c++)t[s+c]=e[a+c]+256-u(0,e[a+c-n],0)&255;for(var c=o;c<n;c++)t[s+c]=e[a+c]+256-u(e[a+c-o],e[a+c-n],e[a+c-o-n])&255}}},rT.crc={table:function(){for(var t=new Uint32Array(256),e=0;e<256;e++){for(var r=e,n=0;n<8;n++)1&r?r=0xedb88320^r>>>1:r>>>=1;t[e]=r}return t}(),update:function(t,e,r,n){for(var o=0;o<n;o++)t=rT.crc.table[(t^e[r+o])&255]^t>>>8;return t},crc:function(t,e,r){return 0xffffffff^rT.crc.update(0xffffffff,t,e,r)}},rT.quantize=function(t,e){var r=new Uint8Array(t),n=r.slice(0),o=new Uint32Array(n.buffer),i=rT.quantize.getKDtree(n,e),a=i[0],s=i[1];rT.quantize.planeDst;for(var u=r.length,c=new Uint8Array(r.length>>2),h=0;h<u;h+=4){var l=r[h]*(1/255),f=r[h+1]*(1/255),d=r[h+2]*(1/255),p=r[h+3]*(1/255),g=rT.quantize.getNearest(a,l,f,d,p);c[h>>2]=g.ind,o[h>>2]=g.est.rgba}return{abuf:n.buffer,inds:c,plte:s}},rT.quantize.getKDtree=function(t,e,r){null==r&&(r=1e-4);var n=new Uint32Array(t.buffer),o={i0:0,i1:t.length,bst:null,est:null,tdst:0,left:null,right:null};o.bst=rT.quantize.stats(t,o.i0,o.i1),o.est=rT.quantize.estats(o.bst);for(var i=[o];i.length<e;){for(var a=0,s=0,u=0;u<i.length;u++)i[u].est.L>a&&(a=i[u].est.L,s=u);if(a<r)break;var c=i[s],h=rT.quantize.splitPixels(t,n,c.i0,c.i1,c.est.e,c.est.eMq255);if(c.i0>=h||c.i1<=h){c.est.L=0;continue}var l={i0:c.i0,i1:h,bst:null,est:null,tdst:0,left:null,right:null};l.bst=rT.quantize.stats(t,l.i0,l.i1),l.est=rT.quantize.estats(l.bst);var f={i0:h,i1:c.i1,bst:null,est:null,tdst:0,left:null,right:null};f.bst={R:[],m:[],N:c.bst.N-l.bst.N};for(var u=0;u<16;u++)f.bst.R[u]=c.bst.R[u]-l.bst.R[u];for(var u=0;u<4;u++)f.bst.m[u]=c.bst.m[u]-l.bst.m[u];f.est=rT.quantize.estats(f.bst),c.left=l,c.right=f,i[s]=l,i.push(f)}i.sort(function(t,e){return e.bst.N-t.bst.N});for(var u=0;u<i.length;u++)i[u].ind=u;return[o,i]},rT.quantize.getNearest=function(t,e,r,n,o){if(null==t.left)return t.tdst=rT.quantize.dist(t.est.q,e,r,n,o),t;var i=rT.quantize.planeDst(t.est,e,r,n,o),a=t.left,s=t.right;i>0&&(a=t.right,s=t.left);var u=rT.quantize.getNearest(a,e,r,n,o);if(u.tdst<=i*i)return u;var c=rT.quantize.getNearest(s,e,r,n,o);return c.tdst<u.tdst?c:u},rT.quantize.planeDst=function(t,e,r,n,o){var i=t.e;return i[0]*e+i[1]*r+i[2]*n+i[3]*o-t.eMq},rT.quantize.dist=function(t,e,r,n,o){var i=e-t[0],a=r-t[1],s=n-t[2],u=o-t[3];return i*i+a*a+s*s+u*u},rT.quantize.splitPixels=function(t,e,r,n,o,i){var a=rT.quantize.vecDot;for(n-=4;r<n;){for(;a(t,r,o)<=i;)r+=4;for(;a(t,n,o)>i;)n-=4;if(r>=n)break;var s=e[r>>2];e[r>>2]=e[n>>2],e[n>>2]=s,r+=4,n-=4}for(;a(t,r,o)>i;)r-=4;return r+4},rT.quantize.vecDot=function(t,e,r){return t[e]*r[0]+t[e+1]*r[1]+t[e+2]*r[2]+t[e+3]*r[3]},rT.quantize.stats=function(t,e,r){for(var n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o=[0,0,0,0],i=e;i<r;i+=4){var a=t[i]*(1/255),s=t[i+1]*(1/255),u=t[i+2]*(1/255),c=t[i+3]*(1/255);o[0]+=a,o[1]+=s,o[2]+=u,o[3]+=c,n[0]+=a*a,n[1]+=a*s,n[2]+=a*u,n[3]+=a*c,n[5]+=s*s,n[6]+=s*u,n[7]+=s*c,n[10]+=u*u,n[11]+=u*c,n[15]+=c*c}return n[4]=n[1],n[8]=n[2],n[9]=n[6],n[12]=n[3],n[13]=n[7],n[14]=n[11],{R:n,m:o,N:r-e>>2}},rT.quantize.estats=function(t){var e=t.R,r=t.m,n=t.N,o=r[0],i=r[1],a=r[2],s=r[3],u=0==n?0:1/n,c=[e[0]-o*o*u,e[1]-o*i*u,e[2]-o*a*u,e[3]-o*s*u,e[4]-i*o*u,e[5]-i*i*u,e[6]-i*a*u,e[7]-i*s*u,e[8]-a*o*u,e[9]-a*i*u,e[10]-a*a*u,e[11]-a*s*u,e[12]-s*o*u,e[13]-s*i*u,e[14]-s*a*u,e[15]-s*s*u],h=rT.M4,l=[.5,.5,.5,.5],f=0,d=0;if(0!=n)for(var p=0;p<10&&(l=h.multVec(c,l),d=Math.sqrt(h.dot(l,l)),l=h.sml(1/d,l),!(1e-9>Math.abs(d-f)));p++)f=d;var g=[o*u,i*u,a*u,s*u],y=h.dot(h.sml(255,g),l);return{Cov:c,q:g,e:l,L:f,eMq255:y,eMq:h.dot(l,g),rgba:(Math.round(255*g[3])<<24|Math.round(255*g[2])<<16|Math.round(255*g[1])<<8|(0|Math.round(255*g[0])))>>>0}},rT.M4={multVec:function(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3],t[4]*e[0]+t[5]*e[1]+t[6]*e[2]+t[7]*e[3],t[8]*e[0]+t[9]*e[1]+t[10]*e[2]+t[11]*e[3],t[12]*e[0]+t[13]*e[1]+t[14]*e[2]+t[15]*e[3]]},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3]},sml:function(t,e){return[t*e[0],t*e[1],t*e[2],t*e[3]]}},rT.encode.concatRGBA=function(t){for(var e=0,r=0;r<t.length;r++)e+=t[r].byteLength;for(var n=new Uint8Array(e),o=0,r=0;r<t.length;r++){for(var i=new Uint8Array(t[r]),a=i.length,s=0;s<a;s+=4){var u=i[s],c=i[s+1],h=i[s+2],l=i[s+3];0==l&&(u=c=h=0),n[o+s]=u,n[o+s+1]=c,n[o+s+2]=h,n[o+s+3]=l}o+=a}return n.buffer};var rO=function(t){if(0===t)return h.Greyscale;if(2===t)return h.Truecolour;if(3===t)return h.IndexedColour;if(4===t)return h.GreyscaleWithAlpha;if(6===t)return h.TruecolourWithAlpha;throw Error("Unknown color type: "+t)},rA=function(t){for(var e=Math.floor(t.length/4),r=new Uint8Array(3*e),n=new Uint8Array(+e),o=0,i=0,a=0;o<t.length;)r[i++]=t[o++],r[i++]=t[o++],r[i++]=t[o++],n[a++]=t[o++];return{rgbChannel:r,alphaChannel:n}};!function(t){t.Greyscale="Greyscale",t.Truecolour="Truecolour",t.IndexedColour="IndexedColour",t.GreyscaleWithAlpha="GreyscaleWithAlpha",t.TruecolourWithAlpha="TruecolourWithAlpha"}(h||(h={}));var rP=function(){function t(t){var e=rT.decode(t),r=rT.toRGBA8(e);if(r.length>1)throw Error("Animated PNGs are not supported");var n=rA(new Uint8Array(r[0])),o=n.rgbChannel,i=n.alphaChannel;this.rgbChannel=o,i.some(function(t){return t<255})&&(this.alphaChannel=i),this.type=rO(e.ctype),this.width=e.width,this.height=e.height,this.bitsPerComponent=8}return t.load=function(e){return new t(e)},t}(),rR=function(){function t(t){this.image=t,this.bitsPerComponent=t.bitsPerComponent,this.width=t.width,this.height=t.height,this.colorSpace="DeviceRGB"}return t.for=function(e){return j(this,void 0,void 0,function(){return z(this,function(r){return[2,new t(rP.load(e))]})})},t.prototype.embedIntoContext=function(t,e){return j(this,void 0,void 0,function(){var r,n;return z(this,function(o){return(r=this.embedAlphaChannel(t),n=t.flateStream(this.image.rgbChannel,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.image.bitsPerComponent,Width:this.image.width,Height:this.image.height,ColorSpace:this.colorSpace,SMask:r}),e)?(t.assign(e,n),[2,e]):[2,t.register(n)]})})},t.prototype.embedAlphaChannel=function(t){if(this.image.alphaChannel){var e=t.flateStream(this.image.alphaChannel,{Type:"XObject",Subtype:"Image",Height:this.image.height,Width:this.image.width,BitsPerComponent:this.image.bitsPerComponent,ColorSpace:"DeviceGray",Decode:[0,1]});return t.register(e)}},t}(),rN=function(){function t(t,e,r){this.bytes=t,this.start=e||0,this.pos=this.start,this.end=e&&r?e+r:this.bytes.length}return Object.defineProperty(t.prototype,"length",{get:function(){return this.end-this.start},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEmpty",{get:function(){return 0===this.length},enumerable:!1,configurable:!0}),t.prototype.getByte=function(){return this.pos>=this.end?-1:this.bytes[this.pos++]},t.prototype.getUint16=function(){var t=this.getByte(),e=this.getByte();return -1===t||-1===e?-1:(t<<8)+e},t.prototype.getInt32=function(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()},t.prototype.getBytes=function(t,e){void 0===e&&(e=!1);var r=this.bytes,n=this.pos,o=this.end;if(t){var i=n+t;i>o&&(i=o),this.pos=i;var a=r.subarray(n,i);return e?new Uint8ClampedArray(a):a}var a=r.subarray(n,o);return e?new Uint8ClampedArray(a):a},t.prototype.peekByte=function(){var t=this.getByte();return this.pos--,t},t.prototype.peekBytes=function(t,e){void 0===e&&(e=!1);var r=this.getBytes(t,e);return this.pos-=r.length,r},t.prototype.skip=function(t){t||(t=1),this.pos+=t},t.prototype.reset=function(){this.pos=this.start},t.prototype.moveStart=function(){this.start=this.pos},t.prototype.makeSubStream=function(e,r){return new t(this.bytes,e,r)},t.prototype.decode=function(){return this.bytes},t}(),rD=new Uint8Array(0),rj=function(){function t(t){if(this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=rD,this.minBufferLength=512,t)for(;this.minBufferLength<t;)this.minBufferLength*=2}return Object.defineProperty(t.prototype,"isEmpty",{get:function(){for(;!this.eof&&0===this.bufferLength;)this.readBlock();return 0===this.bufferLength},enumerable:!1,configurable:!0}),t.prototype.getByte=function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return -1;this.readBlock()}return this.buffer[this.pos++]},t.prototype.getUint16=function(){var t=this.getByte(),e=this.getByte();return -1===t||-1===e?-1:(t<<8)+e},t.prototype.getInt32=function(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()},t.prototype.getBytes=function(t,e){void 0===e&&(e=!1);var r,n=this.pos;if(t){for(this.ensureBuffer(n+t),r=n+t;!this.eof&&this.bufferLength<r;)this.readBlock();var o=this.bufferLength;r>o&&(r=o)}else{for(;!this.eof;)this.readBlock();r=this.bufferLength}this.pos=r;var i=this.buffer.subarray(n,r);return!e||i instanceof Uint8ClampedArray?i:new Uint8ClampedArray(i)},t.prototype.peekByte=function(){var t=this.getByte();return this.pos--,t},t.prototype.peekBytes=function(t,e){void 0===e&&(e=!1);var r=this.getBytes(t,e);return this.pos-=r.length,r},t.prototype.skip=function(t){t||(t=1),this.pos+=t},t.prototype.reset=function(){this.pos=0},t.prototype.makeSubStream=function(t,e){for(var r=t+e;this.bufferLength<=r&&!this.eof;)this.readBlock();return new rN(this.buffer,t,e)},t.prototype.decode=function(){for(;!this.eof;)this.readBlock();return this.buffer.subarray(0,this.bufferLength)},t.prototype.readBlock=function(){throw new ec(this.constructor.name,"readBlock")},t.prototype.ensureBuffer=function(t){var e=this.buffer;if(t<=e.byteLength)return e;for(var r=this.minBufferLength;r<t;)r*=2;var n=new Uint8Array(r);return n.set(e),this.buffer=n},t}(),rz=function(t){return 32===t||9===t||13===t||10===t},rB=function(t){function e(e,r){var n=t.call(this,r)||this;return n.stream=e,n.input=new Uint8Array(5),r&&(r*=.8),n}return N(e,t),e.prototype.readBlock=function(){for(var t,e,r=this.stream,n=r.getByte();rz(n);)n=r.getByte();if(-1===n||126===n){this.eof=!0;return}var o=this.bufferLength;if(122===n){for(e=0,t=this.ensureBuffer(o+4);e<4;++e)t[o+e]=0;this.bufferLength+=4}else{var i=this.input;for(e=1,i[0]=n;e<5;++e){for(n=r.getByte();rz(n);)n=r.getByte();if(i[e]=n,-1===n||126===n)break}if(t=this.ensureBuffer(o+e-1),this.bufferLength+=e-1,e<5){for(;e<5;++e)i[e]=117;this.eof=!0}var a=0;for(e=0;e<5;++e)a=85*a+(i[e]-33);for(e=3;e>=0;--e)t[o+e]=255&a,a>>=8}},e}(rj),rM=function(t){function e(e,r){var n=t.call(this,r)||this;return n.stream=e,n.firstDigit=-1,r&&(r*=.5),n}return N(e,t),e.prototype.readBlock=function(){var t=this.stream.getBytes(8e3);if(!t.length){this.eof=!0;return}for(var e=t.length+1>>1,r=this.ensureBuffer(this.bufferLength+e),n=this.bufferLength,o=this.firstDigit,i=0,a=t.length;i<a;i++){var s=t[i],u=void 0;if(s>=48&&s<=57)u=15&s;else if(s>=65&&s<=70||s>=97&&s<=102)u=(15&s)+9;else{if(62!==s)continue;this.eof=!0;break}o<0?o=u:(r[n++]=o<<4|u,o=-1)}o>=0&&this.eof&&(r[n++]=o<<4,o=-1),this.firstDigit=o,this.bufferLength=n},e}(rj),rV=new Int32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),rI=new Int32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),rU=new Int32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),rE=[new Int32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],rW=[new Int32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5],rq=function(t){function e(e,r){var n=t.call(this,r)||this;n.stream=e;var o=e.getByte(),i=e.getByte();if(-1===o||-1===i)throw Error("Invalid header in flate stream: "+o+", "+i);if((15&o)!=8)throw Error("Unknown compression method in flate stream: "+o+", "+i);if(((o<<8)+i)%31!=0)throw Error("Bad FCHECK in flate stream: "+o+", "+i);if(32&i)throw Error("FDICT bit set in flate stream: "+o+", "+i);return n.codeSize=0,n.codeBuf=0,n}return N(e,t),e.prototype.readBlock=function(){var t,e,r,n,o=this.stream,i=this.getBits(3);if(1&i&&(this.eof=!0),0==(i>>=1)){var a=void 0;if(-1===(a=o.getByte()))throw Error("Bad block header in flate stream");var s=a;if(-1===(a=o.getByte())||(s|=a<<8,-1===(a=o.getByte())))throw Error("Bad block header in flate stream");var u=a;if(-1===(a=o.getByte()))throw Error("Bad block header in flate stream");if((u|=a<<8)!=(65535&~s)&&(0!==s||0!==u))throw Error("Bad uncompressed block length in flate stream");this.codeBuf=0,this.codeSize=0;var c=this.bufferLength;t=this.ensureBuffer(c+s);var h=c+s;if(this.bufferLength=h,0===s)-1===o.peekByte()&&(this.eof=!0);else for(var l=c;l<h;++l){if(-1===(a=o.getByte())){this.eof=!0;break}t[l]=a}return}if(1===i)r=rE,n=rW;else if(2===i){var f=this.getBits(5)+257,d=this.getBits(5)+1,p=this.getBits(4)+4,g=new Uint8Array(rV.length),y=void 0;for(y=0;y<p;++y)g[rV[y]]=this.getBits(3);var v=this.generateHuffmanTable(g);e=0,y=0;for(var m=f+d,b=new Uint8Array(m),x=void 0,w=void 0,F=void 0;y<m;){var S=this.getCode(v);if(16===S)x=2,w=3,F=e;else if(17===S)x=3,w=3,F=e=0;else if(18===S)x=7,w=11,F=e=0;else{b[y++]=e=S;continue}for(var C=this.getBits(x)+w;C-- >0;)b[y++]=F}r=this.generateHuffmanTable(b.subarray(0,f)),n=this.generateHuffmanTable(b.subarray(f,m))}else throw Error("Unknown block type in flate stream");for(var k=(t=this.buffer)?t.length:0,T=this.bufferLength;;){var O=this.getCode(r);if(O<256){T+1>=k&&(k=(t=this.ensureBuffer(T+1)).length),t[T++]=O;continue}if(256===O){this.bufferLength=T;return}O-=257;var A=(O=rI[O])>>16;A>0&&(A=this.getBits(A)),e=(65535&O)+A,(A=(O=rU[O=this.getCode(n)])>>16)>0&&(A=this.getBits(A));var P=(65535&O)+A;T+e>=k&&(k=(t=this.ensureBuffer(T+e)).length);for(var R=0;R<e;++R,++T)t[T]=t[T-P]}},e.prototype.getBits=function(t){for(var e,r=this.stream,n=this.codeSize,o=this.codeBuf;n<t;){if(-1===(e=r.getByte()))throw Error("Bad encoding in flate stream");o|=e<<n,n+=8}return e=o&(1<<t)-1,this.codeBuf=o>>t,this.codeSize=n-=t,e},e.prototype.getCode=function(t){for(var e,r=this.stream,n=t[0],o=t[1],i=this.codeSize,a=this.codeBuf;i<o&&-1!==(e=r.getByte());)a|=e<<i,i+=8;var s=n[a&(1<<o)-1];"number"==typeof n&&console.log("FLATE:",s);var u=s>>16;if(u<1||i<u)throw Error("Bad encoding in flate stream");return this.codeBuf=a>>u,this.codeSize=i-u,65535&s},e.prototype.generateHuffmanTable=function(t){var e,r=t.length,n=0;for(e=0;e<r;++e)t[e]>n&&(n=t[e]);for(var o=1<<n,i=new Int32Array(o),a=1,s=0,u=2;a<=n;++a,s<<=1,u<<=1)for(var c=0;c<r;++c)if(t[c]===a){var h=0,l=s;for(e=0;e<a;++e)h=h<<1|1&l,l>>=1;for(e=h;e<o;e+=u)i[e]=a<<16|c;++s}return[i,n]},e}(rj),rK=function(t){function e(e,r,n){var o=t.call(this,r)||this;o.stream=e,o.cachedData=0,o.bitsCached=0;for(var i={earlyChange:n,codeLength:9,nextCode:258,dictionaryValues:new Uint8Array(4096),dictionaryLengths:new Uint16Array(4096),dictionaryPrevCodes:new Uint16Array(4096),currentSequence:new Uint8Array(4096),currentSequenceLength:0},a=0;a<256;++a)i.dictionaryValues[a]=a,i.dictionaryLengths[a]=1;return o.lzwState=i,o}return N(e,t),e.prototype.readBlock=function(){var t,e,r,n=1024,o=this.lzwState;if(o){var i=o.earlyChange,a=o.nextCode,s=o.dictionaryValues,u=o.dictionaryLengths,c=o.dictionaryPrevCodes,h=o.codeLength,l=o.prevCode,f=o.currentSequence,d=o.currentSequenceLength,p=0,g=this.bufferLength,y=this.ensureBuffer(this.bufferLength+n);for(t=0;t<512;t++){var v=this.readBits(h),m=d>0;if(!v||v<256)f[0]=v,d=1;else if(v>=258)if(v<a)for(e=(d=u[v])-1,r=v;e>=0;e--)f[e]=s[r],r=c[r];else f[d++]=f[0];else if(256===v){h=9,a=258,d=0;continue}else{this.eof=!0,delete this.lzwState;break}if(m&&(c[a]=l,u[a]=u[l]+1,s[a]=f[0],h=++a+i&a+i-1?h:0|Math.min(Math.log(a+i)/.6931471805599453+1,12)),l=v,n<(p+=d)){do n+=512;while(n<p);y=this.ensureBuffer(this.bufferLength+n)}for(e=0;e<d;e++)y[g++]=f[e]}o.nextCode=a,o.codeLength=h,o.prevCode=l,o.currentSequenceLength=d,this.bufferLength=g}},e.prototype.readBits=function(t){for(var e=this.bitsCached,r=this.cachedData;e<t;){var n=this.stream.getByte();if(-1===n)return this.eof=!0,null;r=r<<8|n,e+=8}return this.bitsCached=e-=t,this.cachedData=r,r>>>e&(1<<t)-1},e}(rj),rG=function(t){function e(e,r){var n=t.call(this,r)||this;return n.stream=e,n}return N(e,t),e.prototype.readBlock=function(){var t,e=this.stream.getBytes(2);if(!e||e.length<2||128===e[0]){this.eof=!0;return}var r=this.bufferLength,n=e[0];if(n<128){if((t=this.ensureBuffer(r+n+1))[r++]=e[1],n>0){var o=this.stream.getBytes(n);t.set(o,r),r+=n}}else{n=257-n;var i=e[1];t=this.ensureBuffer(r+n+1);for(var a=0;a<n;a++)t[r++]=i}this.bufferLength=r},e}(rj),rL=function(t,e,r){if(e===eY.of("FlateDecode"))return new rq(t);if(e===eY.of("LZWDecode")){var n=1;if(r instanceof eQ){var o=r.lookup(eY.of("EarlyChange"));o instanceof eU&&(n=o.asNumber())}return new rK(t,void 0,n)}if(e===eY.of("ASCII85Decode"))return new rB(t);if(e===eY.of("ASCIIHexDecode"))return new rM(t);if(e===eY.of("RunLengthDecode"))return new rG(t);throw new ef(e.asString())},rX=function(t){var e=t.dict,r=new rN(t.contents),n=e.lookup(eY.of("Filter")),o=e.lookup(eY.of("DecodeParms"));if(n instanceof eY)r=rL(r,n,o);else if(n instanceof eE)for(var i=0,a=n.size();i<a;i++)r=rL(r,n.lookup(i,eY),o&&o.lookupMaybe(i,eQ));else if(n)throw new el([eY,eE],n);return r},rH=function(t){var e=t.MediaBox();return{left:0,bottom:0,right:e.lookup(2,eU).asNumber()-e.lookup(0,eU).asNumber(),top:e.lookup(3,eU).asNumber()-e.lookup(1,eU).asNumber()}},rZ=function(){function t(t,e,r){this.page=t;var n=null!=e?e:rH(t);this.width=n.right-n.left,this.height=n.top-n.bottom,this.boundingBox=n,this.transformationMatrix=null!=r?r:[1,0,0,1,-n.left,-n.bottom]}return t.for=function(e,r,n){return j(this,void 0,void 0,function(){return z(this,function(o){return[2,new t(e,r,n)]})})},t.prototype.embedIntoContext=function(t,e){return j(this,void 0,void 0,function(){var r,n,o,i,a,s,u,c,h,l;return z(this,function(f){if(n=(r=this.page.normalizedEntries()).Contents,o=r.Resources,!n)throw new ep;return(i=this.decodeContents(n),s=(a=this.boundingBox).left,u=a.bottom,c=a.right,h=a.top,l=t.flateStream(i,{Type:"XObject",Subtype:"Form",FormType:1,BBox:[s,u,c,h],Matrix:this.transformationMatrix,Resources:o}),e)?(t.assign(e,l),[2,e]):[2,t.register(l)]})})},t.prototype.decodeContents=function(t){for(var e=Uint8Array.of(eM.Newline),r=[],n=0,o=t.size();n<o;n++){var i=t.lookup(n,e_),a=void 0;if(i instanceof e$)a=rX(i).decode();else if(i instanceof e6)a=i.getUnencodedContents();else throw new eg(i);r.push(a,e)}return tc.apply(void 0,r)},t}(),rY=function(t,e){if(void 0!==t)return e[t]};!function(t){t.UseNone="UseNone",t.UseOutlines="UseOutlines",t.UseThumbs="UseThumbs",t.UseOC="UseOC"}(l||(l={})),function(t){t.L2R="L2R",t.R2L="R2L"}(f||(f={})),function(t){t.None="None",t.AppDefault="AppDefault"}(d||(d={})),function(t){t.Simplex="Simplex",t.DuplexFlipShortEdge="DuplexFlipShortEdge",t.DuplexFlipLongEdge="DuplexFlipLongEdge"}(p||(p={}));var rJ=function(){function t(t){this.dict=t}return t.prototype.lookupBool=function(t){var e=this.dict.lookup(eY.of(t));if(e instanceof eq)return e},t.prototype.lookupName=function(t){var e=this.dict.lookup(eY.of(t));if(e instanceof eY)return e},t.prototype.HideToolbar=function(){return this.lookupBool("HideToolbar")},t.prototype.HideMenubar=function(){return this.lookupBool("HideMenubar")},t.prototype.HideWindowUI=function(){return this.lookupBool("HideWindowUI")},t.prototype.FitWindow=function(){return this.lookupBool("FitWindow")},t.prototype.CenterWindow=function(){return this.lookupBool("CenterWindow")},t.prototype.DisplayDocTitle=function(){return this.lookupBool("DisplayDocTitle")},t.prototype.NonFullScreenPageMode=function(){return this.lookupName("NonFullScreenPageMode")},t.prototype.Direction=function(){return this.lookupName("Direction")},t.prototype.PrintScaling=function(){return this.lookupName("PrintScaling")},t.prototype.Duplex=function(){return this.lookupName("Duplex")},t.prototype.PickTrayByPDFSize=function(){return this.lookupBool("PickTrayByPDFSize")},t.prototype.PrintPageRange=function(){var t=this.dict.lookup(eY.of("PrintPageRange"));if(t instanceof eE)return t},t.prototype.NumCopies=function(){var t=this.dict.lookup(eY.of("NumCopies"));if(t instanceof eU)return t},t.prototype.getHideToolbar=function(){var t,e;return null!=(e=null==(t=this.HideToolbar())?void 0:t.asBoolean())&&e},t.prototype.getHideMenubar=function(){var t,e;return null!=(e=null==(t=this.HideMenubar())?void 0:t.asBoolean())&&e},t.prototype.getHideWindowUI=function(){var t,e;return null!=(e=null==(t=this.HideWindowUI())?void 0:t.asBoolean())&&e},t.prototype.getFitWindow=function(){var t,e;return null!=(e=null==(t=this.FitWindow())?void 0:t.asBoolean())&&e},t.prototype.getCenterWindow=function(){var t,e;return null!=(e=null==(t=this.CenterWindow())?void 0:t.asBoolean())&&e},t.prototype.getDisplayDocTitle=function(){var t,e;return null!=(e=null==(t=this.DisplayDocTitle())?void 0:t.asBoolean())&&e},t.prototype.getNonFullScreenPageMode=function(){var t,e;return null!=(e=rY(null==(t=this.NonFullScreenPageMode())?void 0:t.decodeText(),l))?e:l.UseNone},t.prototype.getReadingDirection=function(){var t,e;return null!=(e=rY(null==(t=this.Direction())?void 0:t.decodeText(),f))?e:f.L2R},t.prototype.getPrintScaling=function(){var t,e;return null!=(e=rY(null==(t=this.PrintScaling())?void 0:t.decodeText(),d))?e:d.AppDefault},t.prototype.getDuplex=function(){var t;return rY(null==(t=this.Duplex())?void 0:t.decodeText(),p)},t.prototype.getPickTrayByPDFSize=function(){var t;return null==(t=this.PickTrayByPDFSize())?void 0:t.asBoolean()},t.prototype.getPrintPageRange=function(){var t=this.PrintPageRange();if(!t)return[];for(var e=[],r=0;r<t.size();r+=2){var n=t.lookup(r,eU).asNumber(),o=t.lookup(r+1,eU).asNumber();e.push({start:n,end:o})}return e},t.prototype.getNumCopies=function(){var t,e;return null!=(e=null==(t=this.NumCopies())?void 0:t.asNumber())?e:1},t.prototype.setHideToolbar=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("HideToolbar"),e)},t.prototype.setHideMenubar=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("HideMenubar"),e)},t.prototype.setHideWindowUI=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("HideWindowUI"),e)},t.prototype.setFitWindow=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("FitWindow"),e)},t.prototype.setCenterWindow=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("CenterWindow"),e)},t.prototype.setDisplayDocTitle=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("DisplayDocTitle"),e)},t.prototype.setNonFullScreenPageMode=function(t){t5(t,"nonFullScreenPageMode",l);var e=eY.of(t);this.dict.set(eY.of("NonFullScreenPageMode"),e)},t.prototype.setReadingDirection=function(t){t5(t,"readingDirection",f);var e=eY.of(t);this.dict.set(eY.of("Direction"),e)},t.prototype.setPrintScaling=function(t){t5(t,"printScaling",d);var e=eY.of(t);this.dict.set(eY.of("PrintScaling"),e)},t.prototype.setDuplex=function(t){t5(t,"duplex",p);var e=eY.of(t);this.dict.set(eY.of("Duplex"),e)},t.prototype.setPickTrayByPDFSize=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("PickTrayByPDFSize"),e)},t.prototype.setPrintPageRange=function(t){Array.isArray(t)||(t=[t]);for(var e=[],r=0,n=t.length;r<n;r++)e.push(t[r].start),e.push(t[r].end);t7(e,"printPageRange",["number"]);var o=this.dict.context.obj(e);this.dict.set(eY.of("PrintPageRange"),o)},t.prototype.setNumCopies=function(t){et(t,"numCopies",1,Number.MAX_VALUE),en(t,"numCopies");var e=this.dict.context.obj(t);this.dict.set(eY.of("NumCopies"),e)},t.fromDict=function(e){return new t(e)},t.create=function(e){return new t(e.obj({}))},t}(),rQ=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+Tf/,r_=function(){function t(t,e){this.dict=t,this.ref=e}return t.prototype.T=function(){return this.dict.lookupMaybe(eY.of("T"),rb,rh)},t.prototype.Ff=function(){var t=this.getInheritableAttribute(eY.of("Ff"));return this.dict.context.lookupMaybe(t,eU)},t.prototype.V=function(){var t=this.getInheritableAttribute(eY.of("V"));return this.dict.context.lookup(t)},t.prototype.Kids=function(){return this.dict.lookupMaybe(eY.of("Kids"),eE)},t.prototype.DA=function(){var t=this.dict.lookup(eY.of("DA"));if(t instanceof rb||t instanceof rh)return t},t.prototype.setKids=function(t){this.dict.set(eY.of("Kids"),this.dict.context.obj(t))},t.prototype.getParent=function(){var e=this.dict.get(eY.of("Parent"));if(e instanceof e2)return new t(this.dict.lookup(eY.of("Parent"),eQ),e)},t.prototype.setParent=function(t){t?this.dict.set(eY.of("Parent"),t):this.dict.delete(eY.of("Parent"))},t.prototype.getFullyQualifiedName=function(){var t=this.getParent();return t?t.getFullyQualifiedName()+"."+this.getPartialName():this.getPartialName()},t.prototype.getPartialName=function(){var t;return null==(t=this.T())?void 0:t.decodeText()},t.prototype.setPartialName=function(t){t?this.dict.set(eY.of("T"),rh.fromText(t)):this.dict.delete(eY.of("T"))},t.prototype.setDefaultAppearance=function(t){this.dict.set(eY.of("DA"),rb.of(t))},t.prototype.getDefaultAppearance=function(){var t=this.DA();return t instanceof rh?t.decodeText():null==t?void 0:t.asString()},t.prototype.setFontSize=function(t){var e,r=null!=(e=this.getFullyQualifiedName())?e:"",n=this.getDefaultAppearance();if(!n)throw new eC(r);var o=ta(n,rQ);if(!o.match)throw new ek(r);var i=n.slice(0,o.pos-o.match[0].length),a=o.pos<=n.length?n.slice(o.pos):"",s=i+" /"+o.match[1]+" "+t+" Tf "+a;this.setDefaultAppearance(s)},t.prototype.getFlags=function(){var t,e;return null!=(e=null==(t=this.Ff())?void 0:t.asNumber())?e:0},t.prototype.setFlags=function(t){this.dict.set(eY.of("Ff"),eU.of(t))},t.prototype.hasFlag=function(t){return(this.getFlags()&t)!=0},t.prototype.setFlag=function(t){var e=this.getFlags();this.setFlags(e|t)},t.prototype.clearFlag=function(t){var e=this.getFlags();this.setFlags(e&~t)},t.prototype.setFlagTo=function(t,e){e?this.setFlag(t):this.clearFlag(t)},t.prototype.getInheritableAttribute=function(t){var e;return this.ascend(function(r){e||(e=r.dict.get(t))}),e},t.prototype.ascend=function(t){t(this);var e=this.getParent();e&&e.ascend(t)},t}(),r$=function(){function t(t){this.dict=t}return t.prototype.W=function(){var t=this.dict.lookup(eY.of("W"));if(t instanceof eU)return t},t.prototype.getWidth=function(){var t,e;return null!=(e=null==(t=this.W())?void 0:t.asNumber())?e:1},t.prototype.setWidth=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("W"),e)},t.fromDict=function(e){return new t(e)},t}(),r0=function(){function t(t){this.dict=t}return t.prototype.Rect=function(){return this.dict.lookup(eY.of("Rect"),eE)},t.prototype.AP=function(){return this.dict.lookupMaybe(eY.of("AP"),eQ)},t.prototype.F=function(){var t=this.dict.lookup(eY.of("F"));return this.dict.context.lookupMaybe(t,eU)},t.prototype.getRectangle=function(){var t,e=this.Rect();return null!=(t=null==e?void 0:e.asRectangle())?t:{x:0,y:0,width:0,height:0}},t.prototype.setRectangle=function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=this.dict.context.obj([e,r,e+n,r+o]);this.dict.set(eY.of("Rect"),i)},t.prototype.getAppearanceState=function(){var t=this.dict.lookup(eY.of("AS"));if(t instanceof eY)return t},t.prototype.setAppearanceState=function(t){this.dict.set(eY.of("AS"),t)},t.prototype.setAppearances=function(t){this.dict.set(eY.of("AP"),t)},t.prototype.ensureAP=function(){var t=this.AP();return t||(t=this.dict.context.obj({}),this.dict.set(eY.of("AP"),t)),t},t.prototype.getNormalAppearance=function(){var t=this.ensureAP().get(eY.of("N"));if(t instanceof e2||t instanceof eQ)return t;throw Error("Unexpected N type: "+(null==t?void 0:t.constructor.name))},t.prototype.setNormalAppearance=function(t){this.ensureAP().set(eY.of("N"),t)},t.prototype.setRolloverAppearance=function(t){this.ensureAP().set(eY.of("R"),t)},t.prototype.setDownAppearance=function(t){this.ensureAP().set(eY.of("D"),t)},t.prototype.removeRolloverAppearance=function(){var t=this.AP();null==t||t.delete(eY.of("R"))},t.prototype.removeDownAppearance=function(){var t=this.AP();null==t||t.delete(eY.of("D"))},t.prototype.getAppearances=function(){var t=this.AP();if(t)return{normal:t.lookup(eY.of("N"),eQ,e_),rollover:t.lookupMaybe(eY.of("R"),eQ,e_),down:t.lookupMaybe(eY.of("D"),eQ,e_)}},t.prototype.getFlags=function(){var t,e;return null!=(e=null==(t=this.F())?void 0:t.asNumber())?e:0},t.prototype.setFlags=function(t){this.dict.set(eY.of("F"),eU.of(t))},t.prototype.hasFlag=function(t){return(this.getFlags()&t)!=0},t.prototype.setFlag=function(t){var e=this.getFlags();this.setFlags(e|t)},t.prototype.clearFlag=function(t){var e=this.getFlags();this.setFlags(e&~t)},t.prototype.setFlagTo=function(t,e){e?this.setFlag(t):this.clearFlag(t)},t.fromDict=function(e){return new t(e)},t}(),r1=function(){function t(t){this.dict=t}return t.prototype.R=function(){var t=this.dict.lookup(eY.of("R"));if(t instanceof eU)return t},t.prototype.BC=function(){var t=this.dict.lookup(eY.of("BC"));if(t instanceof eE)return t},t.prototype.BG=function(){var t=this.dict.lookup(eY.of("BG"));if(t instanceof eE)return t},t.prototype.CA=function(){var t=this.dict.lookup(eY.of("CA"));if(t instanceof rh||t instanceof rb)return t},t.prototype.RC=function(){var t=this.dict.lookup(eY.of("RC"));if(t instanceof rh||t instanceof rb)return t},t.prototype.AC=function(){var t=this.dict.lookup(eY.of("AC"));if(t instanceof rh||t instanceof rb)return t},t.prototype.getRotation=function(){var t;return null==(t=this.R())?void 0:t.asNumber()},t.prototype.getBorderColor=function(){var t=this.BC();if(t){for(var e=[],r=0,n=null==t?void 0:t.size();r<n;r++){var o=t.get(r);o instanceof eU&&e.push(o.asNumber())}return e}},t.prototype.getBackgroundColor=function(){var t=this.BG();if(t){for(var e=[],r=0,n=null==t?void 0:t.size();r<n;r++){var o=t.get(r);o instanceof eU&&e.push(o.asNumber())}return e}},t.prototype.getCaptions=function(){var t=this.CA(),e=this.RC(),r=this.AC();return{normal:null==t?void 0:t.decodeText(),rollover:null==e?void 0:e.decodeText(),down:null==r?void 0:r.decodeText()}},t.prototype.setRotation=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("R"),e)},t.prototype.setBorderColor=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("BC"),e)},t.prototype.setBackgroundColor=function(t){var e=this.dict.context.obj(t);this.dict.set(eY.of("BG"),e)},t.prototype.setCaptions=function(t){var e=rh.fromText(t.normal);if(this.dict.set(eY.of("CA"),e),t.rollover){var r=rh.fromText(t.rollover);this.dict.set(eY.of("RC"),r)}else this.dict.delete(eY.of("RC"));if(t.down){var n=rh.fromText(t.down);this.dict.set(eY.of("AC"),n)}else this.dict.delete(eY.of("AC"))},t.fromDict=function(e){return new t(e)},t}(),r2=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.MK=function(){var t=this.dict.lookup(eY.of("MK"));if(t instanceof eQ)return t},e.prototype.BS=function(){var t=this.dict.lookup(eY.of("BS"));if(t instanceof eQ)return t},e.prototype.DA=function(){var t=this.dict.lookup(eY.of("DA"));if(t instanceof rb||t instanceof rh)return t},e.prototype.P=function(){var t=this.dict.get(eY.of("P"));if(t instanceof e2)return t},e.prototype.setP=function(t){this.dict.set(eY.of("P"),t)},e.prototype.setDefaultAppearance=function(t){this.dict.set(eY.of("DA"),rb.of(t))},e.prototype.getDefaultAppearance=function(){var t=this.DA();return t instanceof rh?t.decodeText():null==t?void 0:t.asString()},e.prototype.getAppearanceCharacteristics=function(){var t=this.MK();if(t)return r1.fromDict(t)},e.prototype.getOrCreateAppearanceCharacteristics=function(){var t=this.MK();if(t)return r1.fromDict(t);var e=r1.fromDict(this.dict.context.obj({}));return this.dict.set(eY.of("MK"),e.dict),e},e.prototype.getBorderStyle=function(){var t=this.BS();if(t)return r$.fromDict(t)},e.prototype.getOrCreateBorderStyle=function(){var t=this.BS();if(t)return r$.fromDict(t);var e=r$.fromDict(this.dict.context.obj({}));return this.dict.set(eY.of("BS"),e.dict),e},e.prototype.getOnValue=function(){var t,e=null==(t=this.getAppearances())?void 0:t.normal;if(e instanceof eQ)for(var r=e.keys(),n=0,o=r.length;n<o;n++){var i=r[n];if(i!==eY.of("Off"))return i}},e.fromDict=function(t){return new e(t)},e.create=function(t,r){return new e(t.obj({Type:"Annot",Subtype:"Widget",Rect:[0,0,0,0],Parent:r}))},e}(r0),r5=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.FT=function(){var t=this.getInheritableAttribute(eY.of("FT"));return this.dict.context.lookup(t,eY)},e.prototype.getWidgets=function(){var t=this.Kids();if(!t)return[r2.fromDict(this.dict)];for(var e=Array(t.size()),r=0,n=t.size();r<n;r++){var o=t.lookup(r,eQ);e[r]=r2.fromDict(o)}return e},e.prototype.addWidget=function(t){this.normalizedEntries().Kids.push(t)},e.prototype.removeWidget=function(t){var e=this.Kids();if(e){if(t<0||t>e.size())throw new ew(t,0,e.size());e.remove(t)}else{if(0!==t)throw new ew(t,0,0);this.setKids([])}},e.prototype.normalizedEntries=function(){var t=this.Kids();return t||(t=this.dict.context.obj([this.ref]),this.dict.set(eY.of("Kids"),t)),{Kids:t}},e.fromDict=function(t,r){return new e(t,r)},e}(r_),r3=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.Opt=function(){return this.dict.lookupMaybe(eY.of("Opt"),rb,rh,eE)},e.prototype.setOpt=function(t){this.dict.set(eY.of("Opt"),this.dict.context.obj(t))},e.prototype.getExportValues=function(){var t=this.Opt();if(t){if(t instanceof rb||t instanceof rh)return[t];for(var e=[],r=0,n=t.size();r<n;r++){var o=t.lookup(r);(o instanceof rb||o instanceof rh)&&e.push(o)}return e}},e.prototype.removeExportValue=function(t){var e=this.Opt();if(e)if(e instanceof rb||e instanceof rh){if(0!==t)throw new ew(t,0,0);this.setOpt([])}else{if(t<0||t>e.size())throw new ew(t,0,e.size());e.remove(t)}},e.prototype.normalizeExportValues=function(){for(var t,e,r,n,o=null!=(t=this.getExportValues())?t:[],i=[],a=this.getWidgets(),s=0,u=a.length;s<u;s++){var c=a[s],h=null!=(e=o[s])?e:rh.fromText(null!=(n=null==(r=c.getOnValue())?void 0:r.decodeText())?n:"");i.push(h)}this.setOpt(i)},e.prototype.addOpt=function(t,e){this.normalizeExportValues();var r,n,o=t.decodeText();if(e)for(var i=null!=(r=this.getExportValues())?r:[],a=0,s=i.length;a<s;a++)i[a].decodeText()===o&&(n=a);var u=this.Opt();return u.push(t),null!=n?n:u.size()-1},e.prototype.addWidgetWithOpt=function(t,e,r){var n=this.addOpt(e,r),o=eY.of(String(n));return this.addWidget(t),o},e}(r5),r4=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.setValue=function(t){if(t!==(null!=(e=this.getOnValue())?e:eY.of("Yes"))&&t!==eY.of("Off"))throw new eF;this.dict.set(eY.of("V"),t);for(var e,r=this.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n],a=i.getOnValue()===t?t:eY.of("Off");i.setAppearanceState(a)}},e.prototype.getValue=function(){var t=this.V();return t instanceof eY?t:eY.of("Off")},e.prototype.getOnValue=function(){var t=this.getWidgets()[0];return null==t?void 0:t.getOnValue()},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Btn",Kids:[]}),n=t.register(r);return new e(r,n)},e}(r3),r6=function(t){return 1<<t};!function(t){t[t.ReadOnly=r6(0)]="ReadOnly",t[t.Required=r6(1)]="Required",t[t.NoExport=r6(2)]="NoExport"}(g||(g={})),function(t){t[t.NoToggleToOff=r6(14)]="NoToggleToOff",t[t.Radio=r6(15)]="Radio",t[t.PushButton=r6(16)]="PushButton",t[t.RadiosInUnison=r6(25)]="RadiosInUnison"}(y||(y={})),function(t){t[t.Multiline=r6(12)]="Multiline",t[t.Password=r6(13)]="Password",t[t.FileSelect=r6(20)]="FileSelect",t[t.DoNotSpellCheck=r6(22)]="DoNotSpellCheck",t[t.DoNotScroll=r6(23)]="DoNotScroll",t[t.Comb=r6(24)]="Comb",t[t.RichText=r6(25)]="RichText"}(v||(v={})),function(t){t[t.Combo=r6(17)]="Combo",t[t.Edit=r6(18)]="Edit",t[t.Sort=r6(19)]="Sort",t[t.MultiSelect=r6(21)]="MultiSelect",t[t.DoNotSpellCheck=r6(22)]="DoNotSpellCheck",t[t.CommitOnSelChange=r6(26)]="CommitOnSelChange"}(m||(m={}));var r8=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.setValues=function(t){if(this.hasFlag(m.Combo)&&!this.hasFlag(m.Edit)&&!this.valuesAreValid(t))throw new eF;if(0===t.length&&this.dict.delete(eY.of("V")),1===t.length&&this.dict.set(eY.of("V"),t[0]),t.length>1){if(!this.hasFlag(m.MultiSelect))throw new eS;this.dict.set(eY.of("V"),this.dict.context.obj(t))}this.updateSelectedIndices(t)},e.prototype.valuesAreValid=function(t){for(var e=this.getOptions(),r=0,n=t.length;r<n;r++){var o=function(r,n){var o=t[r].decodeText();if(!e.find(function(t){return o===(t.display||t.value).decodeText()}))return{value:!1}}(r,0);if("object"==typeof o)return o.value}return!0},e.prototype.updateSelectedIndices=function(t){if(t.length>1){for(var e=Array(t.length),r=this.getOptions(),n=0,o=t.length;n<o;n++)!function(n,o){var i=t[n].decodeText();e[n]=r.findIndex(function(t){return i===(t.display||t.value).decodeText()})}(n,0);this.dict.set(eY.of("I"),this.dict.context.obj(e.sort()))}else this.dict.delete(eY.of("I"))},e.prototype.getValues=function(){var t=this.V();if(t instanceof rb||t instanceof rh)return[t];if(t instanceof eE){for(var e=[],r=0,n=t.size();r<n;r++){var o=t.lookup(r);(o instanceof rb||o instanceof rh)&&e.push(o)}return e}return[]},e.prototype.Opt=function(){return this.dict.lookupMaybe(eY.of("Opt"),rb,rh,eE)},e.prototype.setOptions=function(t){for(var e=Array(t.length),r=0,n=t.length;r<n;r++){var o=t[r],i=o.value,a=o.display;e[r]=this.dict.context.obj([i,a||i])}this.dict.set(eY.of("Opt"),this.dict.context.obj(e))},e.prototype.getOptions=function(){var t=this.Opt();if(t instanceof rb||t instanceof rh)return[{value:t,display:t}];if(t instanceof eE){for(var e=[],r=0,n=t.size();r<n;r++){var o=t.lookup(r);if((o instanceof rb||o instanceof rh)&&e.push({value:o,display:o}),o instanceof eE&&o.size()>0){var i=o.lookup(0,rb,rh),a=o.lookupMaybe(1,rb,rh);e.push({value:i,display:a||i})}}return e}return[]},e}(r5),r9=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Ch",Ff:m.Combo,Kids:[]}),n=t.register(r);return new e(r,n)},e}(r8),r7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.addField=function(t){var e=this.normalizedEntries().Kids;null==e||e.push(t)},e.prototype.normalizedEntries=function(){var t=this.Kids();return t||(t=this.dict.context.obj([]),this.dict.set(eY.of("Kids"),t)),{Kids:t}},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({}),n=t.register(r);return new e(r,n)},e}(r_),nt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.fromDict=function(t,r){return new e(t,r)},e}(r5),ne=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.MaxLen=function(){var t=this.dict.lookup(eY.of("MaxLen"));if(t instanceof eU)return t},e.prototype.Q=function(){var t=this.dict.lookup(eY.of("Q"));if(t instanceof eU)return t},e.prototype.setMaxLength=function(t){this.dict.set(eY.of("MaxLen"),eU.of(t))},e.prototype.removeMaxLength=function(){this.dict.delete(eY.of("MaxLen"))},e.prototype.getMaxLength=function(){var t;return null==(t=this.MaxLen())?void 0:t.asNumber()},e.prototype.setQuadding=function(t){this.dict.set(eY.of("Q"),eU.of(t))},e.prototype.getQuadding=function(){var t;return null==(t=this.Q())?void 0:t.asNumber()},e.prototype.setValue=function(t){this.dict.set(eY.of("V"),t)},e.prototype.removeValue=function(){this.dict.delete(eY.of("V"))},e.prototype.getValue=function(){var t=this.V();if(t instanceof rb||t instanceof rh)return t},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Tx",Kids:[]}),n=t.register(r);return new e(r,n)},e}(r5),nr=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Btn",Ff:y.PushButton,Kids:[]}),n=t.register(r);return new e(r,n)},e}(r3),nn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.setValue=function(t){if(!this.getOnValues().includes(t)&&t!==eY.of("Off"))throw new eF;this.dict.set(eY.of("V"),t);for(var e=this.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getOnValue()===t?t:eY.of("Off");o.setAppearanceState(i)}},e.prototype.getValue=function(){var t=this.V();return t instanceof eY?t:eY.of("Off")},e.prototype.getOnValues=function(){for(var t=this.getWidgets(),e=[],r=0,n=t.length;r<n;r++){var o=t[r].getOnValue();o&&e.push(o)}return e},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Btn",Ff:y.Radio,Kids:[]}),n=t.register(r);return new e(r,n)},e}(r3),no=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Ch",Kids:[]}),n=t.register(r);return new e(r,n)},e}(r8),ni=function(t){if(!t)return[];for(var e=[],r=0,n=t.size();r<n;r++){var o=t.get(r),i=t.lookup(r);o instanceof e2&&i instanceof eQ&&e.push([na(i,o),o])}return e},na=function(t,e){return ns(t)?r7.fromDict(t,e):nu(t,e)},ns=function(t){var e=t.lookup(eY.of("Kids"));if(e instanceof eE)for(var r=0,n=e.size();r<n;r++){var o=e.lookup(r);if(o instanceof eQ&&o.has(eY.of("T")))return!0}return!1},nu=function(t,e){var r=nf(t,eY.of("FT")),n=t.context.lookup(r,eY);return n===eY.of("Btn")?nc(t,e):n===eY.of("Ch")?nh(t,e):n===eY.of("Tx")?ne.fromDict(t,e):n===eY.of("Sig")?nt.fromDict(t,e):r5.fromDict(t,e)},nc=function(t,e){var r,n=nf(t,eY.of("Ff")),o=t.context.lookupMaybe(n,eU),i=null!=(r=null==o?void 0:o.asNumber())?r:0;return nl(i,y.PushButton)?nr.fromDict(t,e):nl(i,y.Radio)?nn.fromDict(t,e):r4.fromDict(t,e)},nh=function(t,e){var r,n=nf(t,eY.of("Ff")),o=t.context.lookupMaybe(n,eU);return nl(null!=(r=null==o?void 0:o.asNumber())?r:0,m.Combo)?r9.fromDict(t,e):no.fromDict(t,e)},nl=function(t,e){return(t&e)!=0},nf=function(t,e){var r;return nd(t,function(t){r||(r=t.get(e))}),r},nd=function(t,e){e(t);var r=t.lookupMaybe(eY.of("Parent"),eQ);r&&nd(r,e)},np=function(){function t(t){this.dict=t}return t.prototype.Fields=function(){var t=this.dict.lookup(eY.of("Fields"));if(t instanceof eE)return t},t.prototype.getFields=function(){for(var t=this.normalizedEntries().Fields,e=Array(t.size()),r=0,n=t.size();r<n;r++){var o=t.get(r),i=t.lookup(r,eQ);e[r]=[na(i,o),o]}return e},t.prototype.getAllFields=function(){var t=[],e=function(r){if(r)for(var n=0,o=r.length;n<o;n++){var i=r[n];t.push(i);var a=i[0];a instanceof r7&&e(ni(a.Kids()))}};return e(this.getFields()),t},t.prototype.addField=function(t){var e=this.normalizedEntries().Fields;null==e||e.push(t)},t.prototype.removeField=function(t){var e=t.getParent(),r=void 0===e?this.normalizedEntries().Fields:e.Kids(),n=null==r?void 0:r.indexOf(t.ref);if(void 0===r||void 0===n)throw Error("Tried to remove inexistent field "+t.getFullyQualifiedName());r.remove(n),void 0!==e&&0===r.size()&&this.removeField(e)},t.prototype.normalizedEntries=function(){var t=this.Fields();return t||(t=this.dict.context.obj([]),this.dict.set(eY.of("Fields"),t)),{Fields:t}},t.fromDict=function(e){return new t(e)},t.create=function(e){return new t(e.obj({Fields:[]}))},t}(),ng=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.Pages=function(){return this.lookup(eY.of("Pages"),eQ)},e.prototype.AcroForm=function(){return this.lookupMaybe(eY.of("AcroForm"),eQ)},e.prototype.getAcroForm=function(){var t=this.AcroForm();if(t)return np.fromDict(t)},e.prototype.getOrCreateAcroForm=function(){var t=this.getAcroForm();if(!t){t=np.create(this.context);var e=this.context.register(t.dict);this.set(eY.of("AcroForm"),e)}return t},e.prototype.ViewerPreferences=function(){return this.lookupMaybe(eY.of("ViewerPreferences"),eQ)},e.prototype.getViewerPreferences=function(){var t=this.ViewerPreferences();if(t)return rJ.fromDict(t)},e.prototype.getOrCreateViewerPreferences=function(){var t=this.getViewerPreferences();if(!t){t=rJ.create(this.context);var e=this.context.register(t.dict);this.set(eY.of("ViewerPreferences"),e)}return t},e.prototype.insertLeafNode=function(t,e){var r=this.get(eY.of("Pages"));return this.Pages().insertLeafNode(t,e)||r},e.prototype.removeLeafNode=function(t){this.Pages().removeLeafNode(t)},e.withContextAndPages=function(t,r){var n=new Map;return n.set(eY.of("Type"),eY.of("Catalog")),n.set(eY.of("Pages"),r),new e(n,t)},e.fromMapWithContext=function(t,r){return new e(t,r)},e}(eQ),ny=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return N(e,t),e.prototype.Parent=function(){return this.lookup(eY.of("Parent"))},e.prototype.Kids=function(){return this.lookup(eY.of("Kids"),eE)},e.prototype.Count=function(){return this.lookup(eY.of("Count"),eU)},e.prototype.pushTreeNode=function(t){this.Kids().push(t)},e.prototype.pushLeafNode=function(t){var e=this.Kids();this.insertLeafKid(e.size(),t)},e.prototype.insertLeafNode=function(t,r){var n=this.Kids(),o=this.Count().asNumber();if(r>o)throw new eb(r,o);for(var i=r,a=0,s=n.size();a<s;a++){if(0===i)return void this.insertLeafKid(a,t);var u=n.get(a),c=this.context.lookup(u);if(c instanceof e)if(c.Count().asNumber()>i)return c.insertLeafNode(t,i)||u;else i-=c.Count().asNumber();c instanceof rt&&(i-=1)}if(0===i)return void this.insertLeafKid(n.size(),t);throw new ex(r,"insertLeafNode")},e.prototype.removeLeafNode=function(t,r){void 0===r&&(r=!0);var n=this.Kids(),o=this.Count().asNumber();if(t>=o)throw new eb(t,o);for(var i=t,a=0,s=n.size();a<s;a++){var u=n.get(a),c=this.context.lookup(u);if(c instanceof e)if(c.Count().asNumber()>i){c.removeLeafNode(i,r),r&&0===c.Kids().size()&&n.remove(a);return}else i-=c.Count().asNumber();if(c instanceof rt)if(0===i)return void this.removeKid(a);else i-=1}throw new ex(t,"removeLeafNode")},e.prototype.ascend=function(t){t(this);var e=this.Parent();e&&e.ascend(t)},e.prototype.traverse=function(t){for(var r=this.Kids(),n=0,o=r.size();n<o;n++){var i=r.get(n),a=this.context.lookup(i);a instanceof e&&a.traverse(t),t(a,i)}},e.prototype.insertLeafKid=function(t,e){var r=this.Kids();this.ascend(function(t){var e=t.Count().asNumber()+1;t.set(eY.of("Count"),eU.of(e))}),r.insert(t,e)},e.prototype.removeKid=function(t){var e=this.Kids();e.lookup(t)instanceof rt&&this.ascend(function(t){var e=t.Count().asNumber()-1;t.set(eY.of("Count"),eU.of(e))}),e.remove(t)},e.withContext=function(t,r){var n=new Map;return n.set(eY.of("Type"),eY.of("Pages")),n.set(eY.of("Kids"),t.obj([])),n.set(eY.of("Count"),t.obj(0)),r&&n.set(eY.of("Parent"),r),new e(n,t)},e.fromMapWithContext=function(t,r){return new e(t,r)},e}(eQ),nv=new Uint8Array(256);nv[eM.Zero]=1,nv[eM.One]=1,nv[eM.Two]=1,nv[eM.Three]=1,nv[eM.Four]=1,nv[eM.Five]=1,nv[eM.Six]=1,nv[eM.Seven]=1,nv[eM.Eight]=1,nv[eM.Nine]=1;var nm=new Uint8Array(256);nm[eM.Period]=1,nm[eM.Plus]=1,nm[eM.Minus]=1;for(var nb=new Uint8Array(256),nx=0;nx<256;nx++)nb[nx]=nv[nx]||nm[nx]?1:0;var nw=eM.Newline,nF=eM.CarriageReturn,nS=function(){function t(t,e){void 0===e&&(e=!1),this.bytes=t,this.capNumbers=e}return t.prototype.parseRawInt=function(){for(var t="";!this.bytes.done()&&nv[this.bytes.peek()];)t+=X(this.bytes.next());var e=Number(t);if(!t||!isFinite(e))throw new eT(this.bytes.position(),t);return e},t.prototype.parseRawNumber=function(){for(var t="";!this.bytes.done();){var e=this.bytes.peek();if(!nb[e]||(t+=X(this.bytes.next()),e===eM.Period))break}for(;!this.bytes.done();){var e=this.bytes.peek();if(!nv[e])break;t+=X(this.bytes.next())}var r=Number(t);if(!t||!isFinite(r))throw new eT(this.bytes.position(),t);if(r>Number.MAX_SAFE_INTEGER)if(this.capNumbers){var n="Parsed number that is too large for some PDF readers: "+t+", using Number.MAX_SAFE_INTEGER instead.";return console.warn(n),Number.MAX_SAFE_INTEGER}else{var n="Parsed number that is too large for some PDF readers: "+t+", not capping.";console.warn(n)}return r},t.prototype.skipWhitespace=function(){for(;!this.bytes.done()&&eG[this.bytes.peek()];)this.bytes.next()},t.prototype.skipLine=function(){for(;!this.bytes.done();){var t=this.bytes.peek();if(t===nw||t===nF)return;this.bytes.next()}},t.prototype.skipComment=function(){if(this.bytes.peek()!==eM.Percent)return!1;for(;!this.bytes.done();){var t=this.bytes.peek();if(t===nw||t===nF)break;this.bytes.next()}return!0},t.prototype.skipWhitespaceAndComments=function(){for(this.skipWhitespace();this.skipComment();)this.skipWhitespace()},t.prototype.matchKeyword=function(t){for(var e=this.bytes.offset(),r=0,n=t.length;r<n;r++)if(this.bytes.done()||this.bytes.next()!==t[r])return this.bytes.moveTo(e),!1;return!0},t}(),nC=function(){function t(t){this.idx=0,this.line=0,this.column=0,this.bytes=t,this.length=this.bytes.length}return t.prototype.moveTo=function(t){this.idx=t},t.prototype.next=function(){var t=this.bytes[this.idx++];return t===eM.Newline?(this.line+=1,this.column=0):this.column+=1,t},t.prototype.assertNext=function(t){if(this.peek()!==t)throw new eA(this.position(),t,this.peek());return this.next()},t.prototype.peek=function(){return this.bytes[this.idx]},t.prototype.peekAhead=function(t){return this.bytes[this.idx+t]},t.prototype.peekAt=function(t){return this.bytes[t]},t.prototype.done=function(){return this.idx>=this.length},t.prototype.offset=function(){return this.idx},t.prototype.slice=function(t,e){return this.bytes.slice(t,e)},t.prototype.position=function(){return{line:this.line,column:this.column,offset:this.idx}},t.of=function(e){return new t(e)},t.fromPDFRawStream=function(e){return t.of(rX(e).decode())},t}(),nk=eM.Space,nT=eM.CarriageReturn,nO=eM.Newline,nA=[eM.s,eM.t,eM.r,eM.e,eM.a,eM.m],nP=[eM.e,eM.n,eM.d,eM.s,eM.t,eM.r,eM.e,eM.a,eM.m],nR={header:[eM.Percent,eM.P,eM.D,eM.F,eM.Dash],eof:[eM.Percent,eM.Percent,eM.E,eM.O,eM.F],obj:[eM.o,eM.b,eM.j],endobj:[eM.e,eM.n,eM.d,eM.o,eM.b,eM.j],xref:[eM.x,eM.r,eM.e,eM.f],trailer:[eM.t,eM.r,eM.a,eM.i,eM.l,eM.e,eM.r],startxref:[eM.s,eM.t,eM.a,eM.r,eM.t,eM.x,eM.r,eM.e,eM.f],true:[eM.t,eM.r,eM.u,eM.e],false:[eM.f,eM.a,eM.l,eM.s,eM.e],null:[eM.n,eM.u,eM.l,eM.l],stream:nA,streamEOF1:B(nA,[nk,nT,nO]),streamEOF2:B(nA,[nT,nO]),streamEOF3:B(nA,[nT]),streamEOF4:B(nA,[nO]),endstream:nP,EOF1endstream:B([nT,nO],nP),EOF2endstream:B([nT],nP),EOF3endstream:B([nO],nP)},nN=function(t){function e(e,r,n){void 0===n&&(n=!1);var o=t.call(this,e,n)||this;return o.context=r,o}return N(e,t),e.prototype.parseObject=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(nR.true))return eq.True;if(this.matchKeyword(nR.false))return eq.False;if(this.matchKeyword(nR.null))return eJ;var t=this.bytes.peek();if(t===eM.LessThan&&this.bytes.peekAhead(1)===eM.LessThan)return this.parseDictOrStream();if(t===eM.LessThan)return this.parseHexString();if(t===eM.LeftParen)return this.parseString();if(t===eM.ForwardSlash)return this.parseName();if(t===eM.LeftSquareBracket)return this.parseArray();if(nb[t])return this.parseNumberOrRef();throw new eP(this.bytes.position(),t)},e.prototype.parseNumberOrRef=function(){var t=this.parseRawNumber();this.skipWhitespaceAndComments();var e=this.bytes.offset();if(nv[this.bytes.peek()]){var r=this.parseRawNumber();if(this.skipWhitespaceAndComments(),this.bytes.peek()===eM.R)return this.bytes.assertNext(eM.R),e2.of(t,r)}return this.bytes.moveTo(e),eU.of(t)},e.prototype.parseHexString=function(){var t="";for(this.bytes.assertNext(eM.LessThan);!this.bytes.done()&&this.bytes.peek()!==eM.GreaterThan;)t+=X(this.bytes.next());return this.bytes.assertNext(eM.GreaterThan),rh.of(t)},e.prototype.parseString=function(){for(var t=0,e=!1,r="";!this.bytes.done();){var n=this.bytes.next();if(r+=X(n),e||(n===eM.LeftParen&&(t+=1),n===eM.RightParen&&(t-=1)),n===eM.BackSlash?e=!e:e&&(e=!1),0===t)return rb.of(r.substring(1,r.length-1))}throw new eD(this.bytes.position())},e.prototype.parseName=function(){this.bytes.assertNext(eM.ForwardSlash);for(var t="";!this.bytes.done();){var e=this.bytes.peek();if(eG[e]||eK[e])break;t+=X(e),this.bytes.next()}return eY.of(t)},e.prototype.parseArray=function(){this.bytes.assertNext(eM.LeftSquareBracket),this.skipWhitespaceAndComments();for(var t=eE.withContext(this.context);this.bytes.peek()!==eM.RightSquareBracket;){var e=this.parseObject();t.push(e),this.skipWhitespaceAndComments()}return this.bytes.assertNext(eM.RightSquareBracket),t},e.prototype.parseDict=function(){this.bytes.assertNext(eM.LessThan),this.bytes.assertNext(eM.LessThan),this.skipWhitespaceAndComments();for(var t=new Map;!this.bytes.done()&&this.bytes.peek()!==eM.GreaterThan&&this.bytes.peekAhead(1)!==eM.GreaterThan;){var e=this.parseName(),r=this.parseObject();t.set(e,r),this.skipWhitespaceAndComments()}this.skipWhitespaceAndComments(),this.bytes.assertNext(eM.GreaterThan),this.bytes.assertNext(eM.GreaterThan);var n=t.get(eY.of("Type"));return n===eY.of("Catalog")?ng.fromMapWithContext(t,this.context):n===eY.of("Pages")?ny.fromMapWithContext(t,this.context):n===eY.of("Page")?rt.fromMapWithContext(t,this.context):eQ.fromMapWithContext(t,this.context)},e.prototype.parseDictOrStream=function(){var t,e=this.bytes.position(),r=this.parseDict();if(this.skipWhitespaceAndComments(),!this.matchKeyword(nR.streamEOF1)&&!this.matchKeyword(nR.streamEOF2)&&!this.matchKeyword(nR.streamEOF3)&&!this.matchKeyword(nR.streamEOF4)&&!this.matchKeyword(nR.stream))return r;var n=this.bytes.offset(),o=r.get(eY.of("Length"));o instanceof eU?(t=n+o.asNumber(),this.bytes.moveTo(t),this.skipWhitespaceAndComments(),this.matchKeyword(nR.endstream)||(this.bytes.moveTo(n),t=this.findEndOfStreamFallback(e))):t=this.findEndOfStreamFallback(e);var i=this.bytes.slice(n,t);return e$.of(r,i)},e.prototype.findEndOfStreamFallback=function(t){for(var e=1,r=this.bytes.offset();!this.bytes.done()&&(r=this.bytes.offset(),this.matchKeyword(nR.stream)?e+=1:this.matchKeyword(nR.EOF1endstream)||this.matchKeyword(nR.EOF2endstream)||this.matchKeyword(nR.EOF3endstream)||this.matchKeyword(nR.endstream)?e-=1:this.bytes.next(),0!==e););if(0!==e)throw new eN(t);return r},e.forBytes=function(t,r,n){return new e(nC.of(t),r,n)},e.forByteStream=function(t,r,n){return void 0===n&&(n=!1),new e(t,r,n)},e}(nS),nD=function(t){function e(e,r){var n=t.call(this,nC.fromPDFRawStream(e),e.dict.context)||this,o=e.dict;return n.alreadyParsed=!1,n.shouldWaitForTick=r||function(){return!1},n.firstOffset=o.lookup(eY.of("First"),eU).asNumber(),n.objectCount=o.lookup(eY.of("N"),eU).asNumber(),n}return N(e,t),e.prototype.parseIntoContext=function(){return j(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s;return z(this,function(u){switch(u.label){case 0:if(this.alreadyParsed)throw new ed("PDFObjectStreamParser","parseIntoContext");this.alreadyParsed=!0,t=this.parseOffsetsAndObjectNumbers(),e=0,r=t.length,u.label=1;case 1:if(!(e<r))return[3,4];if(o=(n=t[e]).objectNumber,i=n.offset,this.bytes.moveTo(this.firstOffset+i),a=this.parseObject(),s=e2.of(o,0),this.context.assign(s,a),!this.shouldWaitForTick())return[3,3];return[4,tb()];case 2:u.sent(),u.label=3;case 3:return e++,[3,1];case 4:return[2]}})})},e.prototype.parseOffsetsAndObjectNumbers=function(){for(var t=[],e=0,r=this.objectCount;e<r;e++){this.skipWhitespaceAndComments();var n=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.parseRawInt();t.push({objectNumber:n,offset:o})}return t},e.forStream=function(t,r){return new e(t,r)},e}(nN),nj=function(){function t(t){this.alreadyParsed=!1,this.dict=t.dict,this.bytes=nC.fromPDFRawStream(t),this.context=this.dict.context;var e=this.dict.lookup(eY.of("Size"),eU),r=this.dict.lookup(eY.of("Index"));if(r instanceof eE){this.subsections=[];for(var n=0,o=r.size();n<o;n+=2){var i=r.lookup(n+0,eU).asNumber(),a=r.lookup(n+1,eU).asNumber();this.subsections.push({firstObjectNumber:i,length:a})}}else this.subsections=[{firstObjectNumber:0,length:e.asNumber()}];var s=this.dict.lookup(eY.of("W"),eE);this.byteWidths=[-1,-1,-1];for(var n=0,o=s.size();n<o;n++)this.byteWidths[n]=s.lookup(n,eU).asNumber()}return t.prototype.parseIntoContext=function(){if(this.alreadyParsed)throw new ed("PDFXRefStreamParser","parseIntoContext");return this.alreadyParsed=!0,this.context.trailerInfo={Root:this.dict.get(eY.of("Root")),Encrypt:this.dict.get(eY.of("Encrypt")),Info:this.dict.get(eY.of("Info")),ID:this.dict.get(eY.of("ID"))},this.parseEntries()},t.prototype.parseEntries=function(){for(var t=[],e=this.byteWidths,r=e[0],n=e[1],o=e[2],i=0,a=this.subsections.length;i<a;i++)for(var s=this.subsections[i],u=s.firstObjectNumber,c=s.length,h=0;h<c;h++){for(var l=0,f=0,d=r;f<d;f++)l=l<<8|this.bytes.next();for(var p=0,f=0,d=n;f<d;f++)p=p<<8|this.bytes.next();for(var g=0,f=0,d=o;f<d;f++)g=g<<8|this.bytes.next();0===r&&(l=1);var y=u+h,v={ref:e2.of(y,g),offset:p,deleted:0===l,inObjectStream:2===l};t.push(v)}return t},t.forStream=function(e){return new t(e)},t}(),nz=function(t){function e(e,r,n,o){void 0===r&&(r=1/0),void 0===n&&(n=!1),void 0===o&&(o=!1);var i=t.call(this,nC.of(e),e7.create(),o)||this;return i.alreadyParsed=!1,i.parsedObjects=0,i.shouldWaitForTick=function(){return i.parsedObjects+=1,i.parsedObjects%i.objectsPerTick==0},i.objectsPerTick=r,i.throwOnInvalidObject=n,i}return N(e,t),e.prototype.parseDocument=function(){return j(this,void 0,void 0,function(){var t,e;return z(this,function(r){switch(r.label){case 0:if(this.alreadyParsed)throw new ed("PDFParser","parseDocument");this.alreadyParsed=!0,this.context.header=this.parseHeader(),r.label=1;case 1:if(this.bytes.done())return[3,3];return[4,this.parseDocumentSection()];case 2:if(r.sent(),(e=this.bytes.offset())===t)throw new ej(this.bytes.position());return t=e,[3,1];case 3:return this.maybeRecoverRoot(),this.context.lookup(e2.of(0))&&(console.warn("Removing parsed object: 0 0 R"),this.context.delete(e2.of(0))),[2,this.context]}})})},e.prototype.maybeRecoverRoot=function(){var t=function(t){return t instanceof eQ&&t.lookup(eY.of("Type"))===eY.of("Catalog")};if(!t(this.context.lookup(this.context.trailerInfo.Root)))for(var e=this.context.enumerateIndirectObjects(),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0];t(o[1])&&(this.context.trailerInfo.Root=i)}},e.prototype.parseHeader=function(){for(;!this.bytes.done();){if(this.matchKeyword(nR.header)){var t=this.parseRawInt();this.bytes.assertNext(eM.Period);var e=this.parseRawInt(),r=eV.forVersion(t,e);return this.skipBinaryHeaderComment(),r}this.bytes.next()}throw new ez(this.bytes.position())},e.prototype.parseIndirectObjectHeader=function(){this.skipWhitespaceAndComments();var t=this.parseRawInt();this.skipWhitespaceAndComments();var e=this.parseRawInt();if(this.skipWhitespaceAndComments(),!this.matchKeyword(nR.obj))throw new eB(this.bytes.position(),nR.obj);return e2.of(t,e)},e.prototype.matchIndirectObjectHeader=function(){var t=this.bytes.offset();try{return this.parseIndirectObjectHeader(),!0}catch(e){return this.bytes.moveTo(t),!1}},e.prototype.parseIndirectObject=function(){return j(this,void 0,void 0,function(){var t,e;return z(this,function(r){switch(r.label){case 0:if(t=this.parseIndirectObjectHeader(),this.skipWhitespaceAndComments(),e=this.parseObject(),this.skipWhitespaceAndComments(),this.matchKeyword(nR.endobj),!(e instanceof e$&&e.dict.lookup(eY.of("Type"))===eY.of("ObjStm")))return[3,2];return[4,nD.forStream(e,this.shouldWaitForTick).parseIntoContext()];case 1:return r.sent(),[3,3];case 2:e instanceof e$&&e.dict.lookup(eY.of("Type"))===eY.of("XRef")?nj.forStream(e).parseIntoContext():this.context.assign(t,e),r.label=3;case 3:return[2,t]}})})},e.prototype.tryToParseInvalidIndirectObject=function(){var t=this.bytes.position(),e="Trying to parse invalid object: "+JSON.stringify(t)+")";if(this.throwOnInvalidObject)throw Error(e);console.warn(e);var r=this.parseIndirectObjectHeader();console.warn("Invalid object ref: "+r),this.skipWhitespaceAndComments();for(var n=this.bytes.offset(),o=!0;!this.bytes.done()&&(this.matchKeyword(nR.endobj)&&(o=!1),o);)this.bytes.next();if(o)throw new eR(t);var i=this.bytes.offset()-nR.endobj.length,a=rs.of(this.bytes.slice(n,i));return this.context.assign(r,a),r},e.prototype.parseIndirectObjects=function(){return j(this,void 0,void 0,function(){var t;return z(this,function(e){switch(e.label){case 0:this.skipWhitespaceAndComments(),e.label=1;case 1:if(!(!this.bytes.done()&&nv[this.bytes.peek()]))return[3,8];t=this.bytes.offset(),e.label=2;case 2:return e.trys.push([2,4,,5]),[4,this.parseIndirectObject()];case 3:return e.sent(),[3,5];case 4:return e.sent(),this.bytes.moveTo(t),this.tryToParseInvalidIndirectObject(),[3,5];case 5:if(this.skipWhitespaceAndComments(),this.skipJibberish(),!this.shouldWaitForTick())return[3,7];return[4,tb()];case 6:e.sent(),e.label=7;case 7:return[3,1];case 8:return[2]}})})},e.prototype.maybeParseCrossRefSection=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(nR.xref)){this.skipWhitespaceAndComments();for(var t=-1,e=rr.createEmpty();!this.bytes.done()&&nv[this.bytes.peek()];){var r=this.parseRawInt();this.skipWhitespaceAndComments();var n=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.bytes.peek();if(o===eM.n||o===eM.f){var i=e2.of(t,n);this.bytes.next()===eM.n?e.addEntry(i,r):e.addDeletedEntry(i,r),t+=1}else t=r;this.skipWhitespaceAndComments()}return e}},e.prototype.maybeParseTrailerDict=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(nR.trailer)){this.skipWhitespaceAndComments();var t=this.parseDict(),e=this.context;e.trailerInfo={Root:t.get(eY.of("Root"))||e.trailerInfo.Root,Encrypt:t.get(eY.of("Encrypt"))||e.trailerInfo.Encrypt,Info:t.get(eY.of("Info"))||e.trailerInfo.Info,ID:t.get(eY.of("ID"))||e.trailerInfo.ID}}},e.prototype.maybeParseTrailer=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(nR.startxref)){this.skipWhitespaceAndComments();var t=this.parseRawInt();return this.skipWhitespace(),this.matchKeyword(nR.eof),this.skipWhitespaceAndComments(),this.matchKeyword(nR.eof),this.skipWhitespaceAndComments(),rn.forLastCrossRefSectionOffset(t)}},e.prototype.parseDocumentSection=function(){return j(this,void 0,void 0,function(){return z(this,function(t){switch(t.label){case 0:return[4,this.parseIndirectObjects()];case 1:return t.sent(),this.maybeParseCrossRefSection(),this.maybeParseTrailerDict(),this.maybeParseTrailer(),this.skipJibberish(),[2]}})})},e.prototype.skipJibberish=function(){for(this.skipWhitespaceAndComments();!this.bytes.done();){var t=this.bytes.offset(),e=this.bytes.peek();if(e>=eM.Space&&e<=eM.Tilde&&(this.matchKeyword(nR.xref)||this.matchKeyword(nR.trailer)||this.matchKeyword(nR.startxref)||this.matchIndirectObjectHeader())){this.bytes.moveTo(t);break}this.bytes.next()}},e.prototype.skipBinaryHeaderComment=function(){this.skipWhitespaceAndComments();try{var t=this.bytes.offset();this.parseIndirectObjectHeader(),this.bytes.moveTo(t)}catch(t){this.bytes.next(),this.skipWhitespaceAndComments()}},e.forBytesWithOptions=function(t,r,n,o){return new e(t,r,n,o)},e}(nN),nB=function(t){return 1<<t};!function(t){t[t.Invisible=nB(0)]="Invisible",t[t.Hidden=nB(1)]="Hidden",t[t.Print=nB(2)]="Print",t[t.NoZoom=nB(3)]="NoZoom",t[t.NoRotate=nB(4)]="NoRotate",t[t.NoView=nB(5)]="NoView",t[t.ReadOnly=nB(6)]="ReadOnly",t[t.Locked=nB(7)]="Locked",t[t.ToggleNoView=nB(8)]="ToggleNoView",t[t.LockedContents=nB(9)]="LockedContents"}(b||(b={}));var nM=function(t){return t instanceof eY?t:eY.of(t)},nV=function(t){return t instanceof eU?t:eU.of(t)},nI=function(t){return t instanceof eU?t.asNumber():t};!function(t){t.Degrees="degrees",t.Radians="radians"}(x||(x={}));var nU=function(t){return t8(t,"degreeAngle",["number"]),{type:x.Degrees,angle:t}},nE=x.Radians,nW=x.Degrees,nq=function(t){return t*Math.PI/180},nK=function(t){return t.type===nE?t.angle:t.type===nW?nq(t.angle):tz("Invalid rotation: "+JSON.stringify(t))},nG=function(t){return t.type===nE?180*t.angle/Math.PI:t.type===nW?t.angle:tz("Invalid rotation: "+JSON.stringify(t))},nL=function(t){void 0===t&&(t=0);var e=t/90%4;return 0===e?0:1===e?90:2===e?180:270*(3===e)},nX=function(t,e){void 0===e&&(e=0);var r=nL(e);return 90===r||270===r?{width:t.height,height:t.width}:{width:t.width,height:t.height}},nH=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0);var n=t.x,o=t.y,i=t.width,a=t.height,s=nL(r),u=e/2;return 0===s?{x:n-u,y:o-u,width:i,height:a}:90===s?{x:n-a+u,y:o-u,width:a,height:i}:180===s?{x:n-i+u,y:o-a+u,width:i,height:a}:270===s?{x:n-u,y:o-i+u,width:a,height:i}:{x:n-u,y:o-u,width:i,height:a}},nZ=function(){return e5.of(e3.ClipNonZero)},nY=Math.cos,nJ=Math.sin,nQ=Math.tan,n_=function(t,e,r,n,o,i){return e5.of(e3.ConcatTransformationMatrix,[nV(t),nV(e),nV(r),nV(n),nV(o),nV(i)])},n$=function(t,e){return n_(1,0,0,1,t,e)},n0=function(t,e){return n_(t,0,0,e,0,0)},n1=function(t){return n_(nY(nI(t)),nJ(nI(t)),-nJ(nI(t)),nY(nI(t)),0,0)},n2=function(t){return n1(nq(nI(t)))},n5=function(t,e){return n_(1,nQ(nI(t)),nQ(nI(e)),1,0,0)},n3=function(t,e){return e5.of(e3.SetLineDashPattern,["["+t.map(nV).join(" ")+"]",nV(e)])};!function(t){t[t.Butt=0]="Butt",t[t.Round=1]="Round",t[t.Projecting=2]="Projecting"}(w||(w={}));var n4=function(t){return e5.of(e3.SetLineCapStyle,[nV(t)])};!function(t){t[t.Miter=0]="Miter",t[t.Round=1]="Round",t[t.Bevel=2]="Bevel"}(F||(F={}));var n6=function(t){return e5.of(e3.SetGraphicsStateParams,[nM(t)])},n8=function(){return e5.of(e3.PushGraphicsState)},n9=function(){return e5.of(e3.PopGraphicsState)},n7=function(t){return e5.of(e3.SetLineWidth,[nV(t)])},ot=function(t,e,r,n,o,i){return e5.of(e3.AppendBezierCurve,[nV(t),nV(e),nV(r),nV(n),nV(o),nV(i)])},oe=function(t,e,r,n){return e5.of(e3.CurveToReplicateInitialPoint,[nV(t),nV(e),nV(r),nV(n)])},or=function(){return e5.of(e3.ClosePath)},on=function(t,e){return e5.of(e3.MoveTo,[nV(t),nV(e)])},oo=function(t,e){return e5.of(e3.LineTo,[nV(t),nV(e)])},oi=function(){return e5.of(e3.StrokePath)},oa=function(){return e5.of(e3.FillNonZero)},os=function(){return e5.of(e3.FillNonZeroAndStroke)},ou=function(){return e5.of(e3.EndPath)},oc=function(t){return e5.of(e3.ShowText,[t])},oh=function(){return e5.of(e3.BeginText)},ol=function(){return e5.of(e3.EndText)},of=function(t,e){return e5.of(e3.SetFontAndSize,[nM(t),nV(e)])};!function(t){t[t.Fill=0]="Fill",t[t.Outline=1]="Outline",t[t.FillAndOutline=2]="FillAndOutline",t[t.Invisible=3]="Invisible",t[t.FillAndClip=4]="FillAndClip",t[t.OutlineAndClip=5]="OutlineAndClip",t[t.FillAndOutlineAndClip=6]="FillAndOutlineAndClip",t[t.Clip=7]="Clip"}(S||(S={}));var od=function(t,e,r,n,o){var i,a,s,u;return i=nY(nI(t)),a=nJ(nI(t))+nQ(nI(e)),s=-nJ(nI(t))+nQ(nI(r)),u=nY(nI(t)),e5.of(e3.SetTextMatrix,[nV(i),nV(a),nV(s),nV(u),nV(n),nV(o)])},op=function(t){return e5.of(e3.DrawObject,[nM(t)])},og=function(t){return e5.of(e3.BeginMarkedContent,[nM(t)])},oy=function(){return e5.of(e3.EndMarkedContent)};!function(t){t.Grayscale="Grayscale",t.RGB="RGB",t.CMYK="CMYK"}(C||(C={}));var ov=function(t){return et(t,"gray",0,1),{type:C.Grayscale,gray:t}},om=function(t,e,r){return et(t,"red",0,1),et(e,"green",0,1),et(r,"blue",0,1),{type:C.RGB,red:t,green:e,blue:r}},ob=function(t,e,r,n){return et(t,"cyan",0,1),et(e,"magenta",0,1),et(r,"yellow",0,1),et(n,"key",0,1),{type:C.CMYK,cyan:t,magenta:e,yellow:r,key:n}},ox=C.Grayscale,ow=C.RGB,oF=C.CMYK,oS=function(t){var e,r,n,o,i,a,s,u;return t.type===ox?(e=t.gray,e5.of(e3.NonStrokingColorGray,[nV(e)])):t.type===ow?(r=t.red,n=t.green,o=t.blue,e5.of(e3.NonStrokingColorRgb,[nV(r),nV(n),nV(o)])):t.type===oF?(i=t.cyan,a=t.magenta,s=t.yellow,u=t.key,e5.of(e3.NonStrokingColorCmyk,[nV(i),nV(a),nV(s),nV(u)])):tz("Invalid color: "+JSON.stringify(t))},oC=function(t){var e,r,n,o,i,a,s,u;return t.type===ox?(e=t.gray,e5.of(e3.StrokingColorGray,[nV(e)])):t.type===ow?(r=t.red,n=t.green,o=t.blue,e5.of(e3.StrokingColorRgb,[nV(r),nV(n),nV(o)])):t.type===oF?(i=t.cyan,a=t.magenta,s=t.yellow,u=t.key,e5.of(e3.StrokingColorCmyk,[nV(i),nV(a),nV(s),nV(u)])):tz("Invalid color: "+JSON.stringify(t))},ok=function(t,e){return void 0===e&&(e=1),(null==t?void 0:t.length)===1?ov(t[0]*e):(null==t?void 0:t.length)===3?om(t[0]*e,t[1]*e,t[2]*e):(null==t?void 0:t.length)===4?ob(t[0]*e,t[1]*e,t[2]*e,t[3]*e):void 0},oT=function(t){return t.type===ox?[t.gray]:t.type===ow?[t.red,t.green,t.blue]:t.type===oF?[t.cyan,t.magenta,t.yellow,t.key]:tz("Invalid color: "+JSON.stringify(t))},oO=0,oA=0,oP=0,oR=0,oN=0,oD=0,oj=new Map([["A",7],["a",7],["C",6],["c",6],["H",1],["h",1],["L",2],["l",2],["M",2],["m",2],["Q",4],["q",4],["S",4],["s",4],["T",2],["t",2],["V",1],["v",1],["Z",0],["z",0]]),oz=function(t){for(var e,r=[],n=[],o="",i=!1,a=0,s=0;s<t.length;s++){var u=t[s];if(oj.has(u))a=oj.get(u),e&&(o.length>0&&(n[n.length]=+o),r[r.length]={cmd:e,args:n},n=[],o="",i=!1),e=u;else if([" ",","].includes(u)||"-"===u&&o.length>0&&"e"!==o[o.length-1]||"."===u&&i){if(0===o.length)continue;n.length===a?(r[r.length]={cmd:e,args:n},n=[+o],"M"===e&&(e="L"),"m"===e&&(e="l")):n[n.length]=+o,i="."===u,o=["-","."].includes(u)?u:""}else o+=u,"."===u&&(i=!0)}return o.length>0&&(n.length===a?(r[r.length]={cmd:e,args:n},n=[+o],"M"===e&&(e="L"),"m"===e&&(e="l")):n[n.length]=+o),r[r.length]={cmd:e,args:n},r},oB=function(t){oO=oA=oP=oR=oN=oD=0;for(var e=[],r=0;r<t.length;r++){var n=t[r];if(n.cmd&&"function"==typeof oM[n.cmd]){var o=oM[n.cmd](n.args);Array.isArray(o)?e=e.concat(o):e.push(o)}}return e},oM={M:function(t){return oO=t[0],oA=t[1],oP=oR=null,oN=oO,oD=oA,on(oO,oA)},m:function(t){return oO+=t[0],oA+=t[1],oP=oR=null,oN=oO,oD=oA,on(oO,oA)},C:function(t){return oO=t[4],oA=t[5],oP=t[2],oR=t[3],ot(t[0],t[1],t[2],t[3],t[4],t[5])},c:function(t){var e=ot(t[0]+oO,t[1]+oA,t[2]+oO,t[3]+oA,t[4]+oO,t[5]+oA);return oP=oO+t[2],oR=oA+t[3],oO+=t[4],oA+=t[5],e},S:function(t){(null===oP||null===oR)&&(oP=oO,oR=oA);var e=ot(oO-(oP-oO),oA-(oR-oA),t[0],t[1],t[2],t[3]);return oP=t[0],oR=t[1],oO=t[2],oA=t[3],e},s:function(t){(null===oP||null===oR)&&(oP=oO,oR=oA);var e=ot(oO-(oP-oO),oA-(oR-oA),oO+t[0],oA+t[1],oO+t[2],oA+t[3]);return oP=oO+t[0],oR=oA+t[1],oO+=t[2],oA+=t[3],e},Q:function(t){return oP=t[0],oR=t[1],oO=t[2],oA=t[3],oe(t[0],t[1],oO,oA)},q:function(t){var e=oe(t[0]+oO,t[1]+oA,t[2]+oO,t[3]+oA);return oP=oO+t[0],oR=oA+t[1],oO+=t[2],oA+=t[3],e},T:function(t){null===oP||null===oR?(oP=oO,oR=oA):(oP=oO-(oP-oO),oR=oA-(oR-oA));var e=oe(oP,oR,t[0],t[1]);return oP=oO-(oP-oO),oR=oA-(oR-oA),oO=t[0],oA=t[1],e},t:function(t){null===oP||null===oR?(oP=oO,oR=oA):(oP=oO-(oP-oO),oR=oA-(oR-oA));var e=oe(oP,oR,oO+t[0],oA+t[1]);return oO+=t[0],oA+=t[1],e},A:function(t){var e=oV(oO,oA,t);return oO=t[5],oA=t[6],e},a:function(t){t[5]+=oO,t[6]+=oA;var e=oV(oO,oA,t);return oO=t[5],oA=t[6],e},L:function(t){return oO=t[0],oA=t[1],oP=oR=null,oo(oO,oA)},l:function(t){return oO+=t[0],oA+=t[1],oP=oR=null,oo(oO,oA)},H:function(t){return oO=t[0],oP=oR=null,oo(oO,oA)},h:function(t){return oO+=t[0],oP=oR=null,oo(oO,oA)},V:function(t){return oA=t[0],oP=oR=null,oo(oO,oA)},v:function(t){return oA+=t[0],oP=oR=null,oo(oO,oA)},Z:function(){var t=or();return oO=oN,oA=oD,t},z:function(){var t=or();return oO=oN,oA=oD,t}},oV=function(t,e,r){for(var n=r[0],o=r[1],i=r[2],a=r[3],s=r[4],u=oI(r[5],r[6],n,o,a,s,i,t,e),c=[],h=0;h<u.length;h++){var l=oU.apply(void 0,u[h]);c.push(ot.apply(void 0,l))}return c},oI=function(t,e,r,n,o,i,a,s,u){var c=Math.PI/180*a,h=Math.sin(c),l=Math.cos(c);r=Math.abs(r),n=Math.abs(n);var f=(oP=l*(s-t)*.5+h*(u-e)*.5)*oP/(r*r)+(oR=l*(u-e)*.5-h*(s-t)*.5)*oR/(n*n);f>1&&(r*=f=Math.sqrt(f),n*=f);var d=l/r,p=h/r,g=-h/n,y=l/n,v=d*s+p*u,m=g*s+y*u,b=d*t+p*e,x=g*t+y*e,w=1/((b-v)*(b-v)+(x-m)*(x-m))-.25;w<0&&(w=0);var F=Math.sqrt(w);i===o&&(F=-F);var S=.5*(v+b)-F*(x-m),C=.5*(m+x)+F*(b-v),k=Math.atan2(m-C,v-S),T=Math.atan2(x-C,b-S)-k;T<0&&1===i?T+=2*Math.PI:T>0&&0===i&&(T-=2*Math.PI);for(var O=Math.ceil(Math.abs(T/(.5*Math.PI+.001))),A=[],P=0;P<O;P++){var R=k+P*T/O,N=k+(P+1)*T/O;A[P]=[S,C,R,N,r,n,h,l]}return A},oU=function(t,e,r,n,o,i,a,s){var u=s*o,c=-a*i,h=a*o,l=s*i,f=.5*(n-r),d=8/3*Math.sin(.5*f)*Math.sin(.5*f)/Math.sin(f),p=t+Math.cos(r)-d*Math.sin(r),g=e+Math.sin(r)+d*Math.cos(r),y=t+Math.cos(n),v=e+Math.sin(n),m=y+d*Math.sin(n),b=v-d*Math.cos(n);return[u*p+c*g,h*p+l*g,u*m+c*b,h*m+l*b,u*y+c*v,h*y+l*v]},oE=function(t,e){for(var r,n=[n8(),e.graphicsState&&n6(e.graphicsState),oh(),oS(e.color),of(e.font,e.size),(r=e.lineHeight,e5.of(e3.SetTextLineHeight,[nV(r)])),od(nK(e.rotate),nK(e.xSkew),nK(e.ySkew),e.x,e.y)].filter(Boolean),o=0,i=t.length;o<i;o++)n.push(oc(t[o]),e5.of(e3.NextLine));return n.push(ol(),n9()),n},oW=function(t,e){return[n8(),e.graphicsState&&n6(e.graphicsState),n$(e.x,e.y),n1(nK(e.rotate)),n0(e.width,e.height),n5(nK(e.xSkew),nK(e.ySkew)),op(t),n9()].filter(Boolean)},oq=function(t){var e,r;return[n8(),t.graphicsState&&n6(t.graphicsState),t.color&&oC(t.color),n7(t.thickness),n3(null!=(e=t.dashArray)?e:[],null!=(r=t.dashPhase)?r:0),on(t.start.x,t.start.y),t.lineCap&&n4(t.lineCap),on(t.start.x,t.start.y),oo(t.end.x,t.end.y),oi(),n9()].filter(Boolean)},oK=function(t){var e,r;return[n8(),t.graphicsState&&n6(t.graphicsState),t.color&&oS(t.color),t.borderColor&&oC(t.borderColor),n7(t.borderWidth),t.borderLineCap&&n4(t.borderLineCap),n3(null!=(e=t.borderDashArray)?e:[],null!=(r=t.borderDashPhase)?r:0),n$(t.x,t.y),n1(nK(t.rotate)),n5(nK(t.xSkew),nK(t.ySkew)),on(0,0),oo(0,t.height),oo(t.width,t.height),oo(t.width,0),or(),t.color&&t.borderWidth?os():t.color?oa():t.borderColor?oi():or(),n9()].filter(Boolean)},oG=(Math.sqrt(2)-1)/3*4,oL=function(t){var e=nI(t.x),r=nI(t.y),n=nI(t.xScale),o=nI(t.yScale);e-=n,r-=o;var i=n*oG,a=o*oG,s=e+2*n,u=r+2*o,c=e+n,h=r+o;return[n8(),on(e,h),ot(e,h-a,c-i,r,c,r),ot(c+i,r,s,h-a,s,h),ot(s,h+a,c+i,u,c,u),ot(c-i,u,e,h+a,e,h),n9()]},oX=function(t){var e=nI(t.x),r=nI(t.y),n=nI(t.xScale),o=nI(t.yScale),i=-n,a=-o,s=n*oG,u=o*oG,c=i+2*n,h=a+2*o,l=i+n,f=a+o;return[n$(e,r),n1(nK(t.rotate)),on(i,f),ot(i,f-u,l-s,a,l,a),ot(l+s,a,c,f-u,c,f),ot(c,f+u,l+s,h,l,h),ot(l-s,h,i,f+u,i,f)]},oH=function(t){var e,r,n;return B([n8(),t.graphicsState&&n6(t.graphicsState),t.color&&oS(t.color),t.borderColor&&oC(t.borderColor),n7(t.borderWidth),t.borderLineCap&&n4(t.borderLineCap),n3(null!=(e=t.borderDashArray)?e:[],null!=(r=t.borderDashPhase)?r:0)],void 0===t.rotate?oL({x:t.x,y:t.y,xScale:t.xScale,yScale:t.yScale}):oX({x:t.x,y:t.y,xScale:t.xScale,yScale:t.yScale,rotate:null!=(n=t.rotate)?n:nU(0)}),[t.color&&t.borderWidth?os():t.color?oa():t.borderColor?oi():or(),n9()]).filter(Boolean)},oZ=function(t,e){var r,n,o;return B([n8(),e.graphicsState&&n6(e.graphicsState),n$(e.x,e.y),n1(nK(null!=(r=e.rotate)?r:nU(0))),e.scale?n0(e.scale,-e.scale):n0(1,-1),e.color&&oS(e.color),e.borderColor&&oC(e.borderColor),e.borderWidth&&n7(e.borderWidth),e.borderLineCap&&n4(e.borderLineCap),n3(null!=(n=e.borderDashArray)?n:[],null!=(o=e.borderDashPhase)?o:0)],oB(oz(t)),[e.color&&e.borderWidth?os():e.color?oa():e.borderColor?oi():or(),n9()]).filter(Boolean)},oY=function(t){var e=nI(t.size);return[n8(),t.color&&oC(t.color),n7(t.thickness),n$(t.x,t.y),on(-.675*e,-.07601036269430045*e),oo(-.25*e,-.49*e),oo(.69*e,.475*e),oi(),n9()].filter(Boolean)},oJ=function(t){return 0===t.rotation?[n$(0,0),n2(0)]:90===t.rotation?[n$(t.width,0),n2(90)]:180===t.rotation?[n$(t.width,t.height),n2(180)]:270===t.rotation?[n$(0,t.height),n2(270)]:[]},oQ=function(t){var e=oK({x:t.x,y:t.y,width:t.width,height:t.height,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)});if(!t.filled)return e;var r=nI(t.width),n=nI(t.height),o=Math.min(r,n)/2,i=oY({x:r/2,y:n/2,size:o,thickness:t.thickness,color:t.markColor});return B([n8()],e,i,[n9()])},o_=function(t){var e=Math.min(nI(t.width),nI(t.height))/2,r=oH({x:t.x,y:t.y,xScale:e,yScale:e,color:t.color,borderColor:t.borderColor,borderWidth:t.borderWidth});if(!t.filled)return r;var n=oH({x:t.x,y:t.y,xScale:.45*e,yScale:.45*e,color:t.dotColor,borderColor:void 0,borderWidth:0});return B([n8()],r,n,[n9()])},o$=function(t){var e=oK({x:nI(t.x),y:nI(t.y),width:nI(t.width),height:nI(t.height),borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)}),r=o0(t.textLines,{color:t.textColor,font:t.font,size:t.fontSize,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)});return B([n8()],e,r,[n9()])},o0=function(t,e){for(var r=[oh(),oS(e.color),of(e.font,e.size)],n=0,o=t.length;n<o;n++){var i=t[n],a=i.encoded,s=i.x,u=i.y;r.push(od(nK(e.rotate),nK(e.xSkew),nK(e.ySkew),s,u),oc(a))}return r.push(ol()),r},o1=function(t){var e=nI(t.x),r=nI(t.y),n=nI(t.width),o=nI(t.height),i=nI(t.borderWidth),a=nI(t.padding),s=e+i/2+a,u=r+i/2+a,c=n-(i/2+a)*2,h=o-(i/2+a)*2,l=[on(s,u),oo(s,u+h),oo(s+c,u+h),oo(s+c,u),or(),nZ(),ou()],f=oK({x:e,y:r,width:n,height:o,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)}),d=o0(t.textLines,{color:t.textColor,font:t.font,size:t.fontSize,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)}),p=B([og("Tx"),n8()],d,[n9(),oy()]);return B([n8()],f,l,p,[n9()])},o2=function(t){for(var e=nI(t.x),r=nI(t.y),n=nI(t.width),o=nI(t.height),i=nI(t.lineHeight),a=nI(t.borderWidth),s=nI(t.padding),u=e+a/2+s,c=r+a/2+s,h=n-(a/2+s)*2,l=o-(a/2+s)*2,f=[on(u,c),oo(u,c+l),oo(u+h,c+l),oo(u+h,c),or(),nZ(),ou()],d=oK({x:e,y:r,width:n,height:o,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)}),p=[],g=0,y=t.selectedLines.length;g<y;g++){var v=t.textLines[t.selectedLines[g]];p.push.apply(p,oK({x:v.x-s,y:v.y-(i-v.height)/2,width:n-a,height:v.height+(i-v.height)/2,borderWidth:0,color:t.selectedColor,borderColor:void 0,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)}))}var m=o0(t.textLines,{color:t.textColor,font:t.font,size:t.fontSize,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)}),b=B([og("Tx"),n8()],m,[n9(),oy()]);return B([n8()],d,p,f,b,[n9()])},o5=function(t){function e(){var e=this;return t.call(this,"Input document to `PDFDocument.load` is encrypted. You can use `PDFDocument.load(..., { ignoreEncryption: true })` if you wish to load the document anyways.")||this}return N(e,t),e}(Error),o3=function(t){function e(){var e=this;return t.call(this,"Input to `PDFDocument.embedFont` was a custom font, but no `fontkit` instance was found. You must register a `fontkit` instance with `PDFDocument.registerFontkit(...)` before embedding custom fonts.")||this}return N(e,t),e}(Error),o4=function(t){function e(){var e=this;return t.call(this,"A `page` passed to `PDFDocument.addPage` or `PDFDocument.insertPage` was from a different (foreign) PDF document. If you want to copy pages from one PDFDocument to another, you must use `PDFDocument.copyPages(...)` to copy the pages before adding or inserting them.")||this}return N(e,t),e}(Error),o6=function(t){function e(){var e=this;return t.call(this,"PDFDocument has no pages so `PDFDocument.removePage` cannot be called")||this}return N(e,t),e}(Error),o8=function(t){function e(e){var r=this;return t.call(this,'PDFDocument has no form field with the name "'+e+'"')||this}return N(e,t),e}(Error),o9=function(t){function e(e,r,n){var o,i,a=this,s='Expected field "'+e+'" to be of type '+(null==r?void 0:r.name)+", but it is actually of type "+(null!=(i=null==(o=null==n?void 0:n.constructor)?void 0:o.name)?i:n);return t.call(this,s)||this}return N(e,t),e}(Error);!function(t){N(function(e){return t.call(this,'Failed to select check box due to missing onValue: "'+e+'"')||this},t)}(Error);var o7=function(t){function e(e){var r=this;return t.call(this,'A field already exists with the specified name: "'+e+'"')||this}return N(e,t),e}(Error),it=function(t){function e(e){var r=this;return t.call(this,'Field name contains invalid component: "'+e+'"')||this}return N(e,t),e}(Error);!function(t){N(function(e){return t.call(this,'A non-terminal field already exists with the specified name: "'+e+'"')||this},t)}(Error);var ie=function(t){function e(e){var r=this;return t.call(this,"Reading rich text fields is not supported: Attempted to read rich text field: "+e)||this}return N(e,t),e}(Error),ir=function(t){function e(e,r){var n=this;return t.call(this,"Failed to layout combed text as lineLength="+e+" is greater than cellCount="+r)||this}return N(e,t),e}(Error),io=function(t){function e(e,r,n){var o=this;return t.call(this,"Attempted to set text with length="+e+" for TextField with maxLength="+r+" and name="+n)||this}return N(e,t),e}(Error),ii=function(t){function e(e,r,n){var o=this,i="Attempted to set maxLength="+r+", which is less than "+e+", the length of this field's current value (name="+n+")";return t.call(this,i)||this}return N(e,t),e}(Error);!function(t){t[t.Left=0]="Left",t[t.Center=1]="Center",t[t.Right=2]="Right"}(k||(k={}));var ia=function(t,e,r,n){void 0===n&&(n=!1);for(var o=4;o<500;){for(var i=0,a=0,s=t.length;a<s;a++){i+=1;for(var u=t[a].split(" "),c=r.width,h=0,l=u.length;h<l;h++){var f=h===l-1?u[h]:u[h]+" ",d=e.widthOfTextAtSize(f,o);(c-=d)<=0&&(i+=1,c=r.width-d)}}if(!n&&i>t.length)return o-1;var p=e.heightAtSize(o);if((p+.2*p)*i>Math.abs(r.height))return o-1;o+=1}return o},is=function(t,e,r,n){for(var o=r.width/n,i=r.height,a=4,s=te(t);a<500;){for(var u=0,c=s.length;u<c;u++){var h=s[u];if(e.widthOfTextAtSize(h,a)>.75*o)return a-1}if(e.heightAtSize(a,{descender:!1})>i)return a-1;a+=1}return a},iu=function(t){for(var e=t.length;e>0;e--)if(/\s/.test(t[e]))return e},ic=function(t,e,r,n){for(var o,i=t.length;i>0;){var a=t.substring(0,i),s=r.encodeText(a),u=r.widthOfTextAtSize(a,n);if(u<e)return{line:a,encoded:s,width:u,remainder:t.substring(i)||void 0};i=null!=(o=iu(a))?o:0}return{line:t,encoded:r.encodeText(t),width:r.widthOfTextAtSize(t,n),remainder:void 0}},ih=function(t,e){var r=e.alignment,n=e.fontSize,o=e.font,i=e.bounds,a=_(Y(t));(void 0===n||0===n)&&(n=ia(a,o,i,!0));for(var s=o.heightAtSize(n),u=s+.2*s,c=[],h=i.x,l=i.y,f=i.x+i.width,d=i.y+i.height,p=i.y+i.height,g=0,y=a.length;g<y;g++)for(var v=a[g];void 0!==v;){var m=ic(v,i.width,o,n),b=m.line,x=m.encoded,w=m.width,F=m.remainder,S=r===k.Left?i.x:r===k.Center?i.x+i.width/2-w/2:r===k.Right?i.x+i.width-w:i.x;p-=u,S<h&&(h=S),p<l&&(l=p),S+w>f&&(f=S+w),p+s>d&&(d=p+s),c.push({text:b,encoded:x,width:w,height:s,x:S,y:p}),v=null==F?void 0:F.trim()}return{fontSize:n,lineHeight:u,lines:c,bounds:{x:h,y:l,width:f-h,height:d-l}}},il=function(t,e){var r=e.fontSize,n=e.font,o=e.bounds,i=e.cellCount,a=$(Y(t));if(a.length>i)throw new ir(a.length,i);(void 0===r||0===r)&&(r=is(a,n,o,i));for(var s=o.width/i,u=n.heightAtSize(r,{descender:!1}),c=o.y+(o.height/2-u/2),h=[],l=o.x,f=o.y,d=o.x+o.width,p=o.y+o.height,g=0,y=0;g<i;){var v=tt(a,y),m=v[0],b=v[1],x=n.encodeText(m),w=n.widthOfTextAtSize(m,r),F=o.x+(s*g+s/2)-w/2;F<l&&(l=F),c<f&&(f=c),F+w>d&&(d=F+w),c+u>p&&(p=c+u),h.push({text:a,encoded:x,width:w,height:u,x:F,y:c}),g+=1,y+=b}return{fontSize:r,cells:h,bounds:{x:l,y:f,width:d-l,height:p-f}}},id=function(t,e){var r=e.alignment,n=e.fontSize,o=e.font,i=e.bounds,a=$(Y(t));(void 0===n||0===n)&&(n=ia([a],o,i));var s=o.encodeText(a),u=o.widthOfTextAtSize(a,n),c=o.heightAtSize(n,{descender:!1}),h=r===k.Left?i.x:r===k.Center?i.x+i.width/2-u/2:r===k.Right?i.x+i.width-u:i.x,l=i.y+(i.height/2-c/2);return{fontSize:n,line:{text:a,encoded:s,width:u,height:c,x:h,y:l},bounds:{x:h,y:l,width:u,height:c}}},ip=function(t){return"normal"in t?t:{normal:t}},ig=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]+(\d*\.\d+|\d+)[\0\t\n\f\r\ ]+Tf/,iy=function(t){var e,r,n=Number((null!=(r=ta(null!=(e=t.getDefaultAppearance())?e:"",ig).match)?r:[])[2]);return isFinite(n)?n:void 0},iv=/(\d*\.\d+|\d+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+(g|rg|k)/,im=function(t){var e,r=ta(null!=(e=t.getDefaultAppearance())?e:"",iv).match,n=null!=r?r:[],o=n[1],i=n[2],a=n[3],s=n[4],u=n[5];return"g"===u&&o?ov(Number(o)):"rg"===u&&o&&i&&a?om(Number(o),Number(i),Number(a)):"k"===u&&o&&i&&a&&s?ob(Number(o),Number(i),Number(a),Number(s)):void 0},ib=function(t,e,r,n){void 0===n&&(n=0);var o,i=[oS(e).toString(),of(null!=(o=null==r?void 0:r.name)?o:"dummy__noop",n).toString()].join("\n");t.setDefaultAppearance(i)},ix=function(t,e){var r,n,o,i=im(e),a=im(t.acroField),s=e.getRectangle(),u=e.getAppearanceCharacteristics(),c=e.getBorderStyle(),h=null!=(r=null==c?void 0:c.getWidth())?r:0,l=nL(null==u?void 0:u.getRotation()),f=nX(s,l),d=f.width,p=f.height,g=oJ(D(D({},s),{rotation:l})),y=om(0,0,0),v=null!=(n=ok(null==u?void 0:u.getBorderColor()))?n:y,m=ok(null==u?void 0:u.getBackgroundColor()),b=ok(null==u?void 0:u.getBackgroundColor(),.8),x=null!=(o=null!=i?i:a)?o:y;i?ib(e,x):ib(t.acroField,x);var w={x:0+h/2,y:0+h/2,width:d-h,height:p-h,thickness:1.5,borderWidth:h,borderColor:v,markColor:x};return{normal:{on:B(g,oQ(D(D({},w),{color:m,filled:!0}))),off:B(g,oQ(D(D({},w),{color:m,filled:!1})))},down:{on:B(g,oQ(D(D({},w),{color:b,filled:!0}))),off:B(g,oQ(D(D({},w),{color:b,filled:!1})))}}},iw=function(t,e){var r,n,o,i=im(e),a=im(t.acroField),s=e.getRectangle(),u=e.getAppearanceCharacteristics(),c=e.getBorderStyle(),h=null!=(r=null==c?void 0:c.getWidth())?r:0,l=nL(null==u?void 0:u.getRotation()),f=nX(s,l),d=f.width,p=f.height,g=oJ(D(D({},s),{rotation:l})),y=om(0,0,0),v=null!=(n=ok(null==u?void 0:u.getBorderColor()))?n:y,m=ok(null==u?void 0:u.getBackgroundColor()),b=ok(null==u?void 0:u.getBackgroundColor(),.8),x=null!=(o=null!=i?i:a)?o:y;i?ib(e,x):ib(t.acroField,x);var w={x:d/2,y:p/2,width:d-h,height:p-h,borderWidth:h,borderColor:v,dotColor:x};return{normal:{on:B(g,o_(D(D({},w),{color:m,filled:!0}))),off:B(g,o_(D(D({},w),{color:m,filled:!1})))},down:{on:B(g,o_(D(D({},w),{color:b,filled:!0}))),off:B(g,o_(D(D({},w),{color:b,filled:!1})))}}},iF=function(t,e,r){var n,o,i,a,s,u=im(e),c=im(t.acroField),h=iy(e),l=iy(t.acroField),f=e.getRectangle(),d=e.getAppearanceCharacteristics(),p=e.getBorderStyle(),g=null==d?void 0:d.getCaptions(),y=null!=(n=null==g?void 0:g.normal)?n:"",v=null!=(i=null!=(o=null==g?void 0:g.down)?o:y)?i:"",m=null!=(a=null==p?void 0:p.getWidth())?a:0,b=nL(null==d?void 0:d.getRotation()),x=nX(f,b),w=x.width,F=x.height,S=oJ(D(D({},f),{rotation:b})),C=om(0,0,0),T=ok(null==d?void 0:d.getBorderColor()),O=ok(null==d?void 0:d.getBackgroundColor()),A=ok(null==d?void 0:d.getBackgroundColor(),.8),P={x:m,y:m,width:w-2*m,height:F-2*m},R=id(y,{alignment:k.Center,fontSize:null!=h?h:l,font:r,bounds:P}),N=id(v,{alignment:k.Center,fontSize:null!=h?h:l,font:r,bounds:P}),j=Math.min(R.fontSize,N.fontSize),z=null!=(s=null!=u?u:c)?s:C;u||void 0!==h?ib(e,z,r,j):ib(t.acroField,z,r,j);var M={x:0+m/2,y:0+m/2,width:w-m,height:F-m,borderWidth:m,borderColor:T,textColor:z,font:r.name,fontSize:j};return{normal:B(S,o$(D(D({},M),{color:O,textLines:[R.line]}))),down:B(S,o$(D(D({},M),{color:A,textLines:[N.line]})))}},iS=function(t,e,r){var n,o,i,a,s,u,c=im(e),h=im(t.acroField),l=iy(e),f=iy(t.acroField),d=e.getRectangle(),p=e.getAppearanceCharacteristics(),g=e.getBorderStyle(),y=null!=(n=t.getText())?n:"",v=null!=(o=null==g?void 0:g.getWidth())?o:0,m=nL(null==p?void 0:p.getRotation()),b=nX(d,m),x=b.width,w=b.height,F=oJ(D(D({},d),{rotation:m})),S=om(0,0,0),C=ok(null==p?void 0:p.getBorderColor()),k=ok(null==p?void 0:p.getBackgroundColor()),T=+!t.isCombed(),O={x:v+T,y:v+T,width:x-(v+T)*2,height:w-(v+T)*2};if(t.isMultiline()){var A=ih(y,{alignment:t.getAlignment(),fontSize:null!=l?l:f,font:r,bounds:O});s=A.lines,u=A.fontSize}else if(t.isCombed()){var A=il(y,{fontSize:null!=l?l:f,font:r,bounds:O,cellCount:null!=(i=t.getMaxLength())?i:0});s=A.cells,u=A.fontSize}else{var A=id(y,{alignment:t.getAlignment(),fontSize:null!=l?l:f,font:r,bounds:O});s=[A.line],u=A.fontSize}var P=null!=(a=null!=c?c:h)?a:S;return c||void 0!==l?ib(e,P,r,u):ib(t.acroField,P,r,u),B(F,o1({x:0+v/2,y:0+v/2,width:x-v,height:w-v,borderWidth:null!=v?v:0,borderColor:C,textColor:P,font:r.name,fontSize:u,color:k,textLines:s,padding:T}))},iC=function(t,e,r){var n,o,i,a=im(e),s=im(t.acroField),u=iy(e),c=iy(t.acroField),h=e.getRectangle(),l=e.getAppearanceCharacteristics(),f=e.getBorderStyle(),d=null!=(n=t.getSelected()[0])?n:"",p=null!=(o=null==f?void 0:f.getWidth())?o:0,g=nL(null==l?void 0:l.getRotation()),y=nX(h,g),v=y.width,m=y.height,b=oJ(D(D({},h),{rotation:g})),x=om(0,0,0),w=ok(null==l?void 0:l.getBorderColor()),F=ok(null==l?void 0:l.getBackgroundColor()),S=id(d,{alignment:k.Left,fontSize:null!=u?u:c,font:r,bounds:{x:p+1,y:p+1,width:v-(p+1)*2,height:m-(p+1)*2}}),C=S.line,T=S.fontSize,O=null!=(i=null!=a?a:s)?i:x;return a||void 0!==u?ib(e,O,r,T):ib(t.acroField,O,r,T),B(b,o1({x:0+p/2,y:0+p/2,width:v-p,height:m-p,borderWidth:null!=p?p:0,borderColor:w,textColor:O,font:r.name,fontSize:T,color:F,textLines:[C],padding:1}))},ik=function(t,e,r){var n,o,i=im(e),a=im(t.acroField),s=iy(e),u=iy(t.acroField),c=e.getRectangle(),h=e.getAppearanceCharacteristics(),l=e.getBorderStyle(),f=null!=(n=null==l?void 0:l.getWidth())?n:0,d=nL(null==h?void 0:h.getRotation()),p=nX(c,d),g=p.width,y=p.height,v=oJ(D(D({},c),{rotation:d})),m=om(0,0,0),b=ok(null==h?void 0:h.getBorderColor()),x=ok(null==h?void 0:h.getBackgroundColor()),w=t.getOptions(),F=t.getSelected();t.isSorted()&&w.sort();for(var S="",C=0,T=w.length;C<T;C++)S+=w[C],C<T-1&&(S+="\n");for(var O=ih(S,{alignment:k.Left,fontSize:null!=s?s:u,font:r,bounds:{x:f+1,y:f+1,width:g-(f+1)*2,height:y-(f+1)*2}}),A=O.lines,P=O.fontSize,R=O.lineHeight,N=[],C=0,T=A.length;C<T;C++){var j=A[C];F.includes(j.text)&&N.push(C)}var z=om(.6,193/255,218/255),M=null!=(o=null!=i?i:a)?o:m;return i||void 0!==s?ib(e,M,r,P):ib(t.acroField,M,r,P),B(v,o2({x:0+f/2,y:0+f/2,width:g-f,height:y-f,borderWidth:null!=f?f:0,borderColor:b,textColor:M,font:r.name,fontSize:P,color:x,textLines:A,lineHeight:R,selectedColor:z,selectedLines:N,padding:1}))},iT=function(){function t(t,e,r){this.alreadyEmbedded=!1,t8(t,"ref",[[e2,"PDFRef"]]),t8(e,"doc",[[iL,"PDFDocument"]]),t8(r,"embedder",[[rZ,"PDFPageEmbedder"]]),this.ref=t,this.doc=e,this.width=r.width,this.height=r.height,this.embedder=r}return t.prototype.scale=function(t){return t8(t,"factor",["number"]),{width:this.width*t,height:this.height*t}},t.prototype.size=function(){return this.scale(1)},t.prototype.embed=function(){return j(this,void 0,void 0,function(){return z(this,function(t){switch(t.label){case 0:if(this.alreadyEmbedded)return[3,2];return[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t.sent(),this.alreadyEmbedded=!0,t.label=2;case 2:return[2]}})})},t.of=function(e,r,n){return new t(e,r,n)},t}(),iO=function(){function t(t,e,r){this.modified=!0,t8(t,"ref",[[e2,"PDFRef"]]),t8(e,"doc",[[iL,"PDFDocument"]]),t8(r,"embedder",[[rx,"CustomFontEmbedder"],[rl,"StandardFontEmbedder"]]),this.ref=t,this.doc=e,this.name=r.fontName,this.embedder=r}return t.prototype.encodeText=function(t){return t8(t,"text",["string"]),this.modified=!0,this.embedder.encodeText(t)},t.prototype.widthOfTextAtSize=function(t,e){return t8(t,"text",["string"]),t8(e,"size",["number"]),this.embedder.widthOfTextAtSize(t,e)},t.prototype.heightAtSize=function(t,e){var r;return t8(t,"size",["number"]),t9(null==e?void 0:e.descender,"options.descender",["boolean"]),this.embedder.heightOfFontAtSize(t,{descender:null==(r=null==e?void 0:e.descender)||r})},t.prototype.sizeAtHeight=function(t){return t8(t,"height",["number"]),this.embedder.sizeOfFontAtHeight(t)},t.prototype.getCharacterSet=function(){return this.embedder instanceof rl?this.embedder.encoding.supportedCodePoints:this.embedder.font.characterSet},t.prototype.embed=function(){return j(this,void 0,void 0,function(){return z(this,function(t){switch(t.label){case 0:if(!this.modified)return[3,2];return[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t.sent(),this.modified=!1,t.label=2;case 2:return[2]}})})},t.of=function(e,r,n){return new t(e,r,n)},t}(),iA=function(){function t(t,e,r){t8(t,"ref",[[e2,"PDFRef"]]),t8(e,"doc",[[iL,"PDFDocument"]]),t8(r,"embedder",[[rk,"JpegEmbedder"],[rR,"PngEmbedder"]]),this.ref=t,this.doc=e,this.width=r.width,this.height=r.height,this.embedder=r}return t.prototype.scale=function(t){return t8(t,"factor",["number"]),{width:this.width*t,height:this.height*t}},t.prototype.scaleToFit=function(t,e){t8(t,"width",["number"]),t8(e,"height",["number"]);var r=Math.min(t/this.width,e/this.height);return this.scale(r)},t.prototype.size=function(){return this.scale(1)},t.prototype.embed=function(){return j(this,void 0,void 0,function(){var t,e,r;return z(this,function(n){switch(n.label){case 0:if(!this.embedder)return[2];return this.embedTask||(t=this,e=t.doc,r=t.ref,this.embedTask=this.embedder.embedIntoContext(e.context,r)),[4,this.embedTask];case 1:return n.sent(),this.embedder=void 0,[2]}})})},t.of=function(e,r,n){return new t(e,r,n)},t}();!function(t){t[t.Left=0]="Left",t[t.Center=1]="Center",t[t.Right=2]="Right"}(T||(T={}));var iP=function(t){t9(null==t?void 0:t.x,"options.x",["number"]),t9(null==t?void 0:t.y,"options.y",["number"]),t9(null==t?void 0:t.width,"options.width",["number"]),t9(null==t?void 0:t.height,"options.height",["number"]),t9(null==t?void 0:t.textColor,"options.textColor",[[Object,"Color"]]),t9(null==t?void 0:t.backgroundColor,"options.backgroundColor",[[Object,"Color"]]),t9(null==t?void 0:t.borderColor,"options.borderColor",[[Object,"Color"]]),t9(null==t?void 0:t.borderWidth,"options.borderWidth",["number"]),t9(null==t?void 0:t.rotate,"options.rotate",[[Object,"Rotation"]])},iR=function(){function t(t,e,r){t8(t,"acroField",[[r5,"PDFAcroTerminal"]]),t8(e,"ref",[[e2,"PDFRef"]]),t8(r,"doc",[[iL,"PDFDocument"]]),this.acroField=t,this.ref=e,this.doc=r}return t.prototype.getName=function(){var t;return null!=(t=this.acroField.getFullyQualifiedName())?t:""},t.prototype.isReadOnly=function(){return this.acroField.hasFlag(g.ReadOnly)},t.prototype.enableReadOnly=function(){this.acroField.setFlagTo(g.ReadOnly,!0)},t.prototype.disableReadOnly=function(){this.acroField.setFlagTo(g.ReadOnly,!1)},t.prototype.isRequired=function(){return this.acroField.hasFlag(g.Required)},t.prototype.enableRequired=function(){this.acroField.setFlagTo(g.Required,!0)},t.prototype.disableRequired=function(){this.acroField.setFlagTo(g.Required,!1)},t.prototype.isExported=function(){return!this.acroField.hasFlag(g.NoExport)},t.prototype.enableExporting=function(){this.acroField.setFlagTo(g.NoExport,!1)},t.prototype.disableExporting=function(){this.acroField.setFlagTo(g.NoExport,!0)},t.prototype.needsAppearancesUpdate=function(){throw new ec(this.constructor.name,"needsAppearancesUpdate")},t.prototype.defaultUpdateAppearances=function(t){throw new ec(this.constructor.name,"defaultUpdateAppearances")},t.prototype.markAsDirty=function(){this.doc.getForm().markFieldAsDirty(this.ref)},t.prototype.markAsClean=function(){this.doc.getForm().markFieldAsClean(this.ref)},t.prototype.isDirty=function(){return this.doc.getForm().fieldIsDirty(this.ref)},t.prototype.createWidget=function(t){var e,r=t.textColor,n=t.backgroundColor,o=t.borderColor,i=t.borderWidth,a=nG(t.rotate),s=t.caption,u=t.x,c=t.y,h=t.width+i,l=t.height+i,f=!!t.hidden,d=t.page;er(a,"degreesAngle",90);var p=r2.create(this.doc.context,this.ref),g=nH({x:u,y:c,width:h,height:l},i,a);p.setRectangle(g),d&&p.setP(d);var y=p.getOrCreateAppearanceCharacteristics();n&&y.setBackgroundColor(oT(n)),y.setRotation(a),s&&y.setCaptions({normal:s}),o&&y.setBorderColor(oT(o));var v=p.getOrCreateBorderStyle();if(void 0!==i&&v.setWidth(i),p.setFlagTo(b.Print,!0),p.setFlagTo(b.Hidden,f),p.setFlagTo(b.Invisible,!1),r){var m=(null!=(e=this.acroField.getDefaultAppearance())?e:"")+"\n"+oS(r).toString();this.acroField.setDefaultAppearance(m)}return p},t.prototype.updateWidgetAppearanceWithFont=function(t,e,r){var n=r.normal,o=r.rollover,i=r.down;this.updateWidgetAppearances(t,{normal:this.createAppearanceStream(t,n,e),rollover:o&&this.createAppearanceStream(t,o,e),down:i&&this.createAppearanceStream(t,i,e)})},t.prototype.updateOnOffWidgetAppearance=function(t,e,r){var n=r.normal,o=r.rollover,i=r.down;this.updateWidgetAppearances(t,{normal:this.createAppearanceDict(t,n,e),rollover:o&&this.createAppearanceDict(t,o,e),down:i&&this.createAppearanceDict(t,i,e)})},t.prototype.updateWidgetAppearances=function(t,e){var r=e.normal,n=e.rollover,o=e.down;t.setNormalAppearance(r),n?t.setRolloverAppearance(n):t.removeRolloverAppearance(),o?t.setDownAppearance(o):t.removeDownAppearance()},t.prototype.createAppearanceStream=function(t,e,r){var n,o=this.acroField.dict.context,i=t.getRectangle(),a=i.width,s=i.height,u=r&&{Font:((n={})[r.name]=r.ref,n)},c=o.formXObject(e,{Resources:u,BBox:o.obj([0,0,a,s]),Matrix:o.obj([1,0,0,1,0,0])});return o.register(c)},t.prototype.createImageAppearanceStream=function(t,e,r){var n,o,i=this.acroField.dict.context,a=t.getRectangle(),s=t.getAppearanceCharacteristics(),u=t.getBorderStyle(),c=null!=(o=null==u?void 0:u.getWidth())?o:0,h=nL(null==s?void 0:s.getRotation()),l=oJ(D(D({},a),{rotation:h})),f=nX(a,h),d=e.scaleToFit(f.width-2*c,f.height-2*c),p={x:c,y:c,width:d.width,height:d.height,rotate:nU(0),xSkew:nU(0),ySkew:nU(0)};r===T.Center?(p.x+=(f.width-2*c)/2-d.width/2,p.y+=(f.height-2*c)/2-d.height/2):r===T.Right&&(p.x=f.width-c-d.width,p.y=f.height-c-d.height);var g=this.doc.context.addRandomSuffix("Image",10),y=B(l,oW(g,p)),v={XObject:((n={})[g]=e.ref,n)},m=i.formXObject(y,{Resources:v,BBox:i.obj([0,0,a.width,a.height]),Matrix:i.obj([1,0,0,1,0,0])});return i.register(m)},t.prototype.createAppearanceDict=function(t,e,r){var n=this.acroField.dict.context,o=this.createAppearanceStream(t,e.on),i=this.createAppearanceStream(t,e.off),a=n.obj({});return a.set(r,o),a.set(eY.of("Off"),i),a},t}(),iN=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroCheckBox",[[r4,"PDFAcroCheckBox"]]),o.acroField=e,o}return N(e,t),e.prototype.check=function(){var t,e=null!=(t=this.acroField.getOnValue())?t:eY.of("Yes");this.markAsDirty(),this.acroField.setValue(e)},e.prototype.uncheck=function(){this.markAsDirty(),this.acroField.setValue(eY.of("Off"))},e.prototype.isChecked=function(){var t=this.acroField.getOnValue();return!!t&&t===this.acroField.getValue()},e.prototype.addToPage=function(t,e){t8(t,"page",[[iH,"PDFPage"]]),iP(e),e||(e={}),"textColor"in e||(e.textColor=om(0,0,0)),"backgroundColor"in e||(e.backgroundColor=om(1,1,1)),"borderColor"in e||(e.borderColor=om(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var r,n,o,i,a,s,u=this.createWidget({x:null!=(r=e.x)?r:0,y:null!=(n=e.y)?n:0,width:null!=(o=e.width)?o:50,height:null!=(i=e.height)?i:50,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!=(a=e.borderWidth)?a:0,rotate:null!=(s=e.rotate)?s:nU(0),hidden:e.hidden,page:t.ref}),c=this.doc.context.register(u.dict);this.acroField.addWidget(c),u.setAppearanceState(eY.of("Off")),this.updateWidgetAppearance(u,eY.of("Yes")),t.node.addAnnot(c)},e.prototype.needsAppearancesUpdate=function(){for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getAppearanceState(),a=null==(t=o.getAppearances())?void 0:t.normal;if(!(a instanceof eQ)||i&&!a.has(i))return!0}return!1},e.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},e.prototype.updateAppearances=function(t){t9(t,"provider",[Function]);for(var e,r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n],a=null!=(e=i.getOnValue())?e:eY.of("Yes");a&&this.updateWidgetAppearance(i,a,t)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=ip((null!=r?r:ix)(this,t));this.updateOnOffWidgetAppearance(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(iR),iD=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroComboBox",[[r9,"PDFAcroComboBox"]]),o.acroField=e,o}return N(e,t),e.prototype.getOptions=function(){for(var t=this.acroField.getOptions(),e=Array(t.length),r=0,n=e.length;r<n;r++){var o=t[r],i=o.display,a=o.value;e[r]=(null!=i?i:a).decodeText()}return e},e.prototype.getSelected=function(){for(var t=this.acroField.getValues(),e=Array(t.length),r=0,n=t.length;r<n;r++)e[r]=t[r].decodeText();return e},e.prototype.setOptions=function(t){t8(t,"options",[Array]);for(var e=Array(t.length),r=0,n=t.length;r<n;r++)e[r]={value:rh.fromText(t[r])};this.acroField.setOptions(e)},e.prototype.addOptions=function(t){t8(t,"options",["string",Array]);for(var e=Array.isArray(t)?t:[t],r=this.acroField.getOptions(),n=Array(e.length),o=0,i=e.length;o<i;o++)n[o]={value:rh.fromText(e[o])};this.acroField.setOptions(r.concat(n))},e.prototype.select=function(t,e){void 0===e&&(e=!1),t8(t,"options",["string",Array]),t8(e,"merge",["boolean"]);var r=Array.isArray(t)?t:[t],n=this.getOptions();r.find(function(t){return!n.includes(t)})&&this.enableEditing(),this.markAsDirty(),(r.length>1||1===r.length&&e)&&this.enableMultiselect();for(var o=Array(r.length),i=0,a=r.length;i<a;i++)o[i]=rh.fromText(r[i]);if(e){var s=this.acroField.getValues();this.acroField.setValues(s.concat(o))}else this.acroField.setValues(o)},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},e.prototype.setFontSize=function(t){eo(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isEditable=function(){return this.acroField.hasFlag(m.Edit)},e.prototype.enableEditing=function(){this.acroField.setFlagTo(m.Edit,!0)},e.prototype.disableEditing=function(){this.acroField.setFlagTo(m.Edit,!1)},e.prototype.isSorted=function(){return this.acroField.hasFlag(m.Sort)},e.prototype.enableSorting=function(){this.acroField.setFlagTo(m.Sort,!0)},e.prototype.disableSorting=function(){this.acroField.setFlagTo(m.Sort,!1)},e.prototype.isMultiselect=function(){return this.acroField.hasFlag(m.MultiSelect)},e.prototype.enableMultiselect=function(){this.acroField.setFlagTo(m.MultiSelect,!0)},e.prototype.disableMultiselect=function(){this.acroField.setFlagTo(m.MultiSelect,!1)},e.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(m.DoNotSpellCheck)},e.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(m.DoNotSpellCheck,!1)},e.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(m.DoNotSpellCheck,!0)},e.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(m.CommitOnSelChange)},e.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(m.CommitOnSelChange,!0)},e.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(m.CommitOnSelChange,!1)},e.prototype.addToPage=function(t,e){t8(t,"page",[[iH,"PDFPage"]]),iP(e),e||(e={}),"textColor"in e||(e.textColor=om(0,0,0)),"backgroundColor"in e||(e.backgroundColor=om(1,1,1)),"borderColor"in e||(e.borderColor=om(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var r,n,o,i,a,s,u,c=this.createWidget({x:null!=(r=e.x)?r:0,y:null!=(n=e.y)?n:0,width:null!=(o=e.width)?o:200,height:null!=(i=e.height)?i:50,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!=(a=e.borderWidth)?a:0,rotate:null!=(s=e.rotate)?s:nU(0),hidden:e.hidden,page:t.ref}),h=this.doc.context.register(c.dict);this.acroField.addWidget(h);var l=null!=(u=e.font)?u:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(c,l),t.node.addAnnot(h)},e.prototype.needsAppearancesUpdate=function(){if(this.isDirty())return!0;for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++)if(!((null==(t=e[r].getAppearances())?void 0:t.normal)instanceof e_))return!0;return!1},e.prototype.defaultUpdateAppearances=function(t){t8(t,"font",[[iO,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){t8(t,"font",[[iO,"PDFFont"]]),t9(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=ip((null!=r?r:iC)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(iR),ij=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroListBox",[[no,"PDFAcroListBox"]]),o.acroField=e,o}return N(e,t),e.prototype.getOptions=function(){for(var t=this.acroField.getOptions(),e=Array(t.length),r=0,n=e.length;r<n;r++){var o=t[r],i=o.display,a=o.value;e[r]=(null!=i?i:a).decodeText()}return e},e.prototype.getSelected=function(){for(var t=this.acroField.getValues(),e=Array(t.length),r=0,n=t.length;r<n;r++)e[r]=t[r].decodeText();return e},e.prototype.setOptions=function(t){t8(t,"options",[Array]),this.markAsDirty();for(var e=Array(t.length),r=0,n=t.length;r<n;r++)e[r]={value:rh.fromText(t[r])};this.acroField.setOptions(e)},e.prototype.addOptions=function(t){t8(t,"options",["string",Array]),this.markAsDirty();for(var e=Array.isArray(t)?t:[t],r=this.acroField.getOptions(),n=Array(e.length),o=0,i=e.length;o<i;o++)n[o]={value:rh.fromText(e[o])};this.acroField.setOptions(r.concat(n))},e.prototype.select=function(t,e){void 0===e&&(e=!1),t8(t,"options",["string",Array]),t8(e,"merge",["boolean"]);var r=Array.isArray(t)?t:[t];t4(r,"option",this.getOptions()),this.markAsDirty(),(r.length>1||1===r.length&&e)&&this.enableMultiselect();for(var n=Array(r.length),o=0,i=r.length;o<i;o++)n[o]=rh.fromText(r[o]);if(e){var a=this.acroField.getValues();this.acroField.setValues(a.concat(n))}else this.acroField.setValues(n)},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},e.prototype.setFontSize=function(t){eo(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isSorted=function(){return this.acroField.hasFlag(m.Sort)},e.prototype.enableSorting=function(){this.acroField.setFlagTo(m.Sort,!0)},e.prototype.disableSorting=function(){this.acroField.setFlagTo(m.Sort,!1)},e.prototype.isMultiselect=function(){return this.acroField.hasFlag(m.MultiSelect)},e.prototype.enableMultiselect=function(){this.acroField.setFlagTo(m.MultiSelect,!0)},e.prototype.disableMultiselect=function(){this.acroField.setFlagTo(m.MultiSelect,!1)},e.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(m.CommitOnSelChange)},e.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(m.CommitOnSelChange,!0)},e.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(m.CommitOnSelChange,!1)},e.prototype.addToPage=function(t,e){t8(t,"page",[[iH,"PDFPage"]]),iP(e),e||(e={}),"textColor"in e||(e.textColor=om(0,0,0)),"backgroundColor"in e||(e.backgroundColor=om(1,1,1)),"borderColor"in e||(e.borderColor=om(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var r,n,o,i,a,s,u,c=this.createWidget({x:null!=(r=e.x)?r:0,y:null!=(n=e.y)?n:0,width:null!=(o=e.width)?o:200,height:null!=(i=e.height)?i:100,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!=(a=e.borderWidth)?a:0,rotate:null!=(s=e.rotate)?s:nU(0),hidden:e.hidden,page:t.ref}),h=this.doc.context.register(c.dict);this.acroField.addWidget(h);var l=null!=(u=e.font)?u:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(c,l),t.node.addAnnot(h)},e.prototype.needsAppearancesUpdate=function(){if(this.isDirty())return!0;for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++)if(!((null==(t=e[r].getAppearances())?void 0:t.normal)instanceof e_))return!0;return!1},e.prototype.defaultUpdateAppearances=function(t){t8(t,"font",[[iO,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){t8(t,"font",[[iO,"PDFFont"]]),t9(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=ip((null!=r?r:ik)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(iR),iz=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroRadioButton",[[nn,"PDFAcroRadioButton"]]),o.acroField=e,o}return N(e,t),e.prototype.getOptions=function(){var t=this.acroField.getExportValues();if(t){for(var e=Array(t.length),r=0,n=t.length;r<n;r++)e[r]=t[r].decodeText();return e}for(var o=this.acroField.getOnValues(),i=Array(o.length),r=0,n=i.length;r<n;r++)i[r]=o[r].decodeText();return i},e.prototype.getSelected=function(){var t=this.acroField.getValue();if(t!==eY.of("Off")){var e=this.acroField.getExportValues();if(e){for(var r=this.acroField.getOnValues(),n=0,o=r.length;n<o;n++)if(r[n]===t)return e[n].decodeText()}return t.decodeText()}},e.prototype.select=function(t){t8(t,"option",["string"]),t5(t,"option",this.getOptions()),this.markAsDirty();var e=this.acroField.getOnValues(),r=this.acroField.getExportValues();if(r)for(var n=0,o=r.length;n<o;n++)r[n].decodeText()===t&&this.acroField.setValue(e[n]);else for(var n=0,o=e.length;n<o;n++){var i=e[n];i.decodeText()===t&&this.acroField.setValue(i)}},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValue(eY.of("Off"))},e.prototype.isOffToggleable=function(){return!this.acroField.hasFlag(y.NoToggleToOff)},e.prototype.enableOffToggling=function(){this.acroField.setFlagTo(y.NoToggleToOff,!1)},e.prototype.disableOffToggling=function(){this.acroField.setFlagTo(y.NoToggleToOff,!0)},e.prototype.isMutuallyExclusive=function(){return!this.acroField.hasFlag(y.RadiosInUnison)},e.prototype.enableMutualExclusion=function(){this.acroField.setFlagTo(y.RadiosInUnison,!1)},e.prototype.disableMutualExclusion=function(){this.acroField.setFlagTo(y.RadiosInUnison,!0)},e.prototype.addOptionToPage=function(t,e,r){t8(t,"option",["string"]),t8(e,"page",[[iH,"PDFPage"]]),iP(r);var n,o,i,a,s,u,c,h,l,f=this.createWidget({x:null!=(n=null==r?void 0:r.x)?n:0,y:null!=(o=null==r?void 0:r.y)?o:0,width:null!=(i=null==r?void 0:r.width)?i:50,height:null!=(a=null==r?void 0:r.height)?a:50,textColor:null!=(s=null==r?void 0:r.textColor)?s:om(0,0,0),backgroundColor:null!=(u=null==r?void 0:r.backgroundColor)?u:om(1,1,1),borderColor:null!=(c=null==r?void 0:r.borderColor)?c:om(0,0,0),borderWidth:null!=(h=null==r?void 0:r.borderWidth)?h:1,rotate:null!=(l=null==r?void 0:r.rotate)?l:nU(0),hidden:null==r?void 0:r.hidden,page:e.ref}),d=this.doc.context.register(f.dict),p=this.acroField.addWidgetWithOpt(d,rh.fromText(t),!this.isMutuallyExclusive());f.setAppearanceState(eY.of("Off")),this.updateWidgetAppearance(f,p),e.node.addAnnot(d)},e.prototype.needsAppearancesUpdate=function(){for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getAppearanceState(),a=null==(t=o.getAppearances())?void 0:t.normal;if(!(a instanceof eQ)||i&&!a.has(i))return!0}return!1},e.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},e.prototype.updateAppearances=function(t){t9(t,"provider",[Function]);for(var e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getOnValue();i&&this.updateWidgetAppearance(o,i,t)}},e.prototype.updateWidgetAppearance=function(t,e,r){var n=ip((null!=r?r:iw)(this,t));this.updateOnOffWidgetAppearance(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(iR),iB=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroSignature",[[nt,"PDFAcroSignature"]]),o.acroField=e,o}return N(e,t),e.prototype.needsAppearancesUpdate=function(){return!1},e.of=function(t,r,n){return new e(t,r,n)},e}(iR),iM=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroText",[[ne,"PDFAcroText"]]),o.acroField=e,o}return N(e,t),e.prototype.getText=function(){var t=this.acroField.getValue();if(!t&&this.isRichFormatted())throw new ie(this.getName());return null==t?void 0:t.decodeText()},e.prototype.setText=function(t){t9(t,"text",["string"]);var e=this.getMaxLength();if(void 0!==e&&t&&t.length>e)throw new io(t.length,e,this.getName());this.markAsDirty(),this.disableRichFormatting(),t?this.acroField.setValue(rh.fromText(t)):this.acroField.removeValue()},e.prototype.getAlignment=function(){var t=this.acroField.getQuadding();return 0===t?k.Left:1===t?k.Center:2===t?k.Right:k.Left},e.prototype.setAlignment=function(t){t5(t,"alignment",k),this.markAsDirty(),this.acroField.setQuadding(t)},e.prototype.getMaxLength=function(){return this.acroField.getMaxLength()},e.prototype.setMaxLength=function(t){if(ee(t,"maxLength",0,Number.MAX_SAFE_INTEGER),this.markAsDirty(),void 0===t)this.acroField.removeMaxLength();else{var e=this.getText();if(e&&e.length>t)throw new ii(e.length,t,this.getName());this.acroField.setMaxLength(t)}},e.prototype.removeMaxLength=function(){this.markAsDirty(),this.acroField.removeMaxLength()},e.prototype.setImage=function(t){for(var e=this.getAlignment(),r=e===k.Center?T.Center:e===k.Right?T.Right:T.Left,n=this.acroField.getWidgets(),o=0,i=n.length;o<i;o++){var a=n[o],s=this.createImageAppearanceStream(a,t,r);this.updateWidgetAppearances(a,{normal:s})}this.markAsClean()},e.prototype.setFontSize=function(t){eo(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isMultiline=function(){return this.acroField.hasFlag(v.Multiline)},e.prototype.enableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(v.Multiline,!0)},e.prototype.disableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(v.Multiline,!1)},e.prototype.isPassword=function(){return this.acroField.hasFlag(v.Password)},e.prototype.enablePassword=function(){this.acroField.setFlagTo(v.Password,!0)},e.prototype.disablePassword=function(){this.acroField.setFlagTo(v.Password,!1)},e.prototype.isFileSelector=function(){return this.acroField.hasFlag(v.FileSelect)},e.prototype.enableFileSelection=function(){this.acroField.setFlagTo(v.FileSelect,!0)},e.prototype.disableFileSelection=function(){this.acroField.setFlagTo(v.FileSelect,!1)},e.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(v.DoNotSpellCheck)},e.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(v.DoNotSpellCheck,!1)},e.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(v.DoNotSpellCheck,!0)},e.prototype.isScrollable=function(){return!this.acroField.hasFlag(v.DoNotScroll)},e.prototype.enableScrolling=function(){this.acroField.setFlagTo(v.DoNotScroll,!1)},e.prototype.disableScrolling=function(){this.acroField.setFlagTo(v.DoNotScroll,!0)},e.prototype.isCombed=function(){return this.acroField.hasFlag(v.Comb)&&!this.isMultiline()&&!this.isPassword()&&!this.isFileSelector()&&void 0!==this.getMaxLength()},e.prototype.enableCombing=function(){void 0===this.getMaxLength()&&console.warn("PDFTextFields must have a max length in order to be combed"),this.markAsDirty(),this.disableMultiline(),this.disablePassword(),this.disableFileSelection(),this.acroField.setFlagTo(v.Comb,!0)},e.prototype.disableCombing=function(){this.markAsDirty(),this.acroField.setFlagTo(v.Comb,!1)},e.prototype.isRichFormatted=function(){return this.acroField.hasFlag(v.RichText)},e.prototype.enableRichFormatting=function(){this.acroField.setFlagTo(v.RichText,!0)},e.prototype.disableRichFormatting=function(){this.acroField.setFlagTo(v.RichText,!1)},e.prototype.addToPage=function(t,e){t8(t,"page",[[iH,"PDFPage"]]),iP(e),e||(e={}),"textColor"in e||(e.textColor=om(0,0,0)),"backgroundColor"in e||(e.backgroundColor=om(1,1,1)),"borderColor"in e||(e.borderColor=om(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var r,n,o,i,a,s,u,c=this.createWidget({x:null!=(r=e.x)?r:0,y:null!=(n=e.y)?n:0,width:null!=(o=e.width)?o:200,height:null!=(i=e.height)?i:50,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!=(a=e.borderWidth)?a:0,rotate:null!=(s=e.rotate)?s:nU(0),hidden:e.hidden,page:t.ref}),h=this.doc.context.register(c.dict);this.acroField.addWidget(h);var l=null!=(u=e.font)?u:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(c,l),t.node.addAnnot(h)},e.prototype.needsAppearancesUpdate=function(){if(this.isDirty())return!0;for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++)if(!((null==(t=e[r].getAppearances())?void 0:t.normal)instanceof e_))return!0;return!1},e.prototype.defaultUpdateAppearances=function(t){t8(t,"font",[[iO,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){t8(t,"font",[[iO,"PDFFont"]]),t9(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=ip((null!=r?r:iS)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(iR);!function(t){t.Courier="Courier",t.CourierBold="Courier-Bold",t.CourierOblique="Courier-Oblique",t.CourierBoldOblique="Courier-BoldOblique",t.Helvetica="Helvetica",t.HelveticaBold="Helvetica-Bold",t.HelveticaOblique="Helvetica-Oblique",t.HelveticaBoldOblique="Helvetica-BoldOblique",t.TimesRoman="Times-Roman",t.TimesRomanBold="Times-Bold",t.TimesRomanItalic="Times-Italic",t.TimesRomanBoldItalic="Times-BoldItalic",t.Symbol="Symbol",t.ZapfDingbats="ZapfDingbats"}(O||(O={}));var iV=function(){function t(t,e){var r=this;this.embedDefaultFont=function(){return r.doc.embedStandardFont(O.Helvetica)},t8(t,"acroForm",[[np,"PDFAcroForm"]]),t8(e,"doc",[[iL,"PDFDocument"]]),this.acroForm=t,this.doc=e,this.dirtyFields=new Set,this.defaultFontCache=eu.populatedBy(this.embedDefaultFont)}return t.prototype.hasXFA=function(){return this.acroForm.dict.has(eY.of("XFA"))},t.prototype.deleteXFA=function(){this.acroForm.dict.delete(eY.of("XFA"))},t.prototype.getFields=function(){for(var t=this.acroForm.getAllFields(),e=[],r=0,n=t.length;r<n;r++){var o=t[r],i=iI(o[0],o[1],this.doc);i&&e.push(i)}return e},t.prototype.getFieldMaybe=function(t){t8(t,"name",["string"]);for(var e=this.getFields(),r=0,n=e.length;r<n;r++){var o=e[r];if(o.getName()===t)return o}},t.prototype.getField=function(t){t8(t,"name",["string"]);var e=this.getFieldMaybe(t);if(e)return e;throw new o8(t)},t.prototype.getButton=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof iZ)return e;throw new o9(t,iZ,e)},t.prototype.getCheckBox=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof iN)return e;throw new o9(t,iN,e)},t.prototype.getDropdown=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof iD)return e;throw new o9(t,iD,e)},t.prototype.getOptionList=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof ij)return e;throw new o9(t,ij,e)},t.prototype.getRadioGroup=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof iz)return e;throw new o9(t,iz,e)},t.prototype.getSignature=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof iB)return e;throw new o9(t,iB,e)},t.prototype.getTextField=function(t){t8(t,"name",["string"]);var e=this.getField(t);if(e instanceof iM)return e;throw new o9(t,iM,e)},t.prototype.createButton=function(t){t8(t,"name",["string"]);var e=iU(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=nr.create(this.doc.context);return n.setPartialName(e.terminal),iE(r,[n,n.ref],e.terminal),iZ.of(n,n.ref,this.doc)},t.prototype.createCheckBox=function(t){t8(t,"name",["string"]);var e=iU(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=r4.create(this.doc.context);return n.setPartialName(e.terminal),iE(r,[n,n.ref],e.terminal),iN.of(n,n.ref,this.doc)},t.prototype.createDropdown=function(t){t8(t,"name",["string"]);var e=iU(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=r9.create(this.doc.context);return n.setPartialName(e.terminal),iE(r,[n,n.ref],e.terminal),iD.of(n,n.ref,this.doc)},t.prototype.createOptionList=function(t){t8(t,"name",["string"]);var e=iU(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=no.create(this.doc.context);return n.setPartialName(e.terminal),iE(r,[n,n.ref],e.terminal),ij.of(n,n.ref,this.doc)},t.prototype.createRadioGroup=function(t){t8(t,"name",["string"]);var e=iU(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=nn.create(this.doc.context);return n.setPartialName(e.terminal),iE(r,[n,n.ref],e.terminal),iz.of(n,n.ref,this.doc)},t.prototype.createTextField=function(t){t8(t,"name",["string"]);var e=iU(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=ne.create(this.doc.context);return n.setPartialName(e.terminal),iE(r,[n,n.ref],e.terminal),iM.of(n,n.ref,this.doc)},t.prototype.flatten=function(t){void 0===t&&(t={updateFieldAppearances:!0}),t.updateFieldAppearances&&this.updateFieldAppearances();for(var e=this.getFields(),r=0,n=e.length;r<n;r++){for(var o=e[r],i=o.acroField.getWidgets(),a=0,s=i.length;a<s;a++){var u=i[a],c=this.findWidgetPage(u),h=this.findWidgetAppearanceRef(o,u),l=c.node.newXObject("FlatWidget",h),f=u.getRectangle(),d=B([n8(),n$(f.x,f.y)],oJ(D(D({},f),{rotation:0})),[op(l),n9()]).filter(Boolean);c.pushOperators.apply(c,d)}this.removeField(o)}},t.prototype.removeField=function(t){for(var e=t.acroField.getWidgets(),r=new Set,n=0,o=e.length;n<o;n++){var i=e[n],a=this.findWidgetAppearanceRef(t,i),s=this.findWidgetPage(i);r.add(s),s.node.removeAnnot(a)}r.forEach(function(e){return e.node.removeAnnot(t.ref)}),this.acroForm.removeField(t.acroField);for(var u=t.acroField.normalizedEntries().Kids,c=u.size(),h=0;h<c;h++){var l=u.get(h);l instanceof e2&&this.doc.context.delete(l)}this.doc.context.delete(t.ref)},t.prototype.updateFieldAppearances=function(t){t9(t,"font",[[iO,"PDFFont"]]),t=null!=t?t:this.getDefaultFont();for(var e=this.getFields(),r=0,n=e.length;r<n;r++){var o=e[r];o.needsAppearancesUpdate()&&o.defaultUpdateAppearances(t)}},t.prototype.markFieldAsDirty=function(t){t9(t,"fieldRef",[[e2,"PDFRef"]]),this.dirtyFields.add(t)},t.prototype.markFieldAsClean=function(t){t9(t,"fieldRef",[[e2,"PDFRef"]]),this.dirtyFields.delete(t)},t.prototype.fieldIsDirty=function(t){return t9(t,"fieldRef",[[e2,"PDFRef"]]),this.dirtyFields.has(t)},t.prototype.getDefaultFont=function(){return this.defaultFontCache.access()},t.prototype.findWidgetPage=function(t){var e=t.P(),r=this.doc.getPages().find(function(t){return t.ref===e});if(void 0===r){var n=this.doc.context.getObjectRef(t.dict);if(void 0===n)throw Error("Could not find PDFRef for PDFObject");if(void 0===(r=this.doc.findPageForAnnotationRef(n)))throw Error("Could not find page for PDFRef "+n)}return r},t.prototype.findWidgetAppearanceRef=function(t,e){var r,n=e.getNormalAppearance();if(n instanceof eQ&&(t instanceof iN||t instanceof iz)){var o=t.acroField.getValue(),i=null!=(r=n.get(o))?r:n.get(eY.of("Off"));i instanceof e2&&(n=i)}if(!(n instanceof e2))throw Error("Failed to extract appearance ref for: "+t.getName());return n},t.prototype.findOrCreateNonTerminals=function(t){for(var e=[this.acroForm],r=0,n=t.length;r<n;r++){var o=t[r];if(!o)throw new it(o);var i=e[0],a=e[1],s=this.findNonTerminal(o,i);if(s)e=s;else{var u=r7.create(this.doc.context);u.setPartialName(o),u.setParent(a);var c=this.doc.context.register(u.dict);i.addField(c),e=[u,c]}}return e},t.prototype.findNonTerminal=function(t,e){for(var r=e instanceof np?this.acroForm.getFields():ni(e.Kids()),n=0,o=r.length;n<o;n++){var i=r[n],a=i[0],s=i[1];if(a.getPartialName()===t){if(a instanceof r7)return[a,s];throw new o7(t)}}},t.of=function(e,r){return new t(e,r)},t}(),iI=function(t,e,r){return t instanceof nr?iZ.of(t,e,r):t instanceof r4?iN.of(t,e,r):t instanceof r9?iD.of(t,e,r):t instanceof no?ij.of(t,e,r):t instanceof ne?iM.of(t,e,r):t instanceof nn?iz.of(t,e,r):t instanceof nt?iB.of(t,e,r):void 0},iU=function(t){if(0===t.length)throw Error("PDF field names must not be empty strings");for(var e=t.split("."),r=0,n=e.length;r<n;r++)if(""===e[r])throw Error('Periods in PDF field names must be separated by at least one character: "'+t+'"');return 1===e.length?{nonTerminal:[],terminal:e[0]}:{nonTerminal:e.slice(0,e.length-1),terminal:e[e.length-1]}},iE=function(t,e,r){for(var n=t[0],o=t[1],i=e[0],a=e[1],s=n.normalizedEntries(),u=ni("Kids"in s?s.Kids:s.Fields),c=0,h=u.length;c<h;c++)if(u[c][0].getPartialName()===r)throw new o7(r);n.addField(a),i.setParent(o)},iW={A4:[595.28,841.89]};!function(t){t[t.Fastest=1/0]="Fastest",t[t.Fast=1500]="Fast",t[t.Medium=500]="Medium",t[t.Slow=100]="Slow"}(A||(A={}));var iq=function(){function t(t,e,r){this.alreadyEmbedded=!1,this.ref=t,this.doc=e,this.embedder=r}return t.prototype.embed=function(){return j(this,void 0,void 0,function(){var t,e,r,n;return z(this,function(o){switch(o.label){case 0:if(this.alreadyEmbedded)return[3,2];return[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t=o.sent(),this.doc.catalog.has(eY.of("Names"))||this.doc.catalog.set(eY.of("Names"),this.doc.context.obj({})),(e=this.doc.catalog.lookup(eY.of("Names"),eQ)).has(eY.of("EmbeddedFiles"))||e.set(eY.of("EmbeddedFiles"),this.doc.context.obj({})),(r=e.lookup(eY.of("EmbeddedFiles"),eQ)).has(eY.of("Names"))||r.set(eY.of("Names"),this.doc.context.obj([])),(n=r.lookup(eY.of("Names"),eE)).push(rh.fromText(this.embedder.fileName)),n.push(t),this.doc.catalog.has(eY.of("AF"))||this.doc.catalog.set(eY.of("AF"),this.doc.context.obj([])),this.doc.catalog.lookup(eY.of("AF"),eE).push(t),this.alreadyEmbedded=!0,o.label=2;case 2:return[2]}})})},t.of=function(e,r,n){return new t(e,r,n)},t}(),iK=function(){function t(t,e,r){this.alreadyEmbedded=!1,this.ref=t,this.doc=e,this.embedder=r}return t.prototype.embed=function(){return j(this,void 0,void 0,function(){var t,e,r,n,o,i,a;return z(this,function(s){switch(s.label){case 0:if(this.alreadyEmbedded)return[3,2];return e=(t=this.doc).catalog,r=t.context,[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:n=s.sent(),e.has(eY.of("Names"))||e.set(eY.of("Names"),r.obj({})),(o=e.lookup(eY.of("Names"),eQ)).has(eY.of("JavaScript"))||o.set(eY.of("JavaScript"),r.obj({})),(i=o.lookup(eY.of("JavaScript"),eQ)).has(eY.of("Names"))||i.set(eY.of("Names"),r.obj([])),(a=i.lookup(eY.of("Names"),eE)).push(rh.fromText(this.embedder.scriptName)),a.push(n),this.alreadyEmbedded=!0,s.label=2;case 2:return[2]}})})},t.of=function(e,r,n){return new t(e,r,n)},t}(),iG=function(){function t(t,e){this.script=t,this.scriptName=e}return t.for=function(e,r){return new t(e,r)},t.prototype.embedIntoContext=function(t,e){return j(this,void 0,void 0,function(){var r;return z(this,function(n){return(r=t.obj({Type:"Action",S:"JavaScript",JS:rh.fromText(this.script)}),e)?(t.assign(e,r),[2,e]):[2,t.register(r)]})})},t}();let iL=function(){function t(t,e,r){var n=this;if(this.defaultWordBreaks=[" "],this.computePages=function(){var t=[];return n.catalog.Pages().traverse(function(e,r){if(e instanceof rt){var o=n.pageMap.get(e);o||(o=iH.of(e,r,n),n.pageMap.set(e,o)),t.push(o)}}),t},this.getOrCreateForm=function(){var t=n.catalog.getOrCreateAcroForm();return iV.of(t,n)},t8(t,"context",[[e7,"PDFContext"]]),t8(e,"ignoreEncryption",["boolean"]),this.context=t,this.catalog=t.lookup(t.trailerInfo.Root),this.isEncrypted=!!t.lookup(t.trailerInfo.Encrypt),this.pageCache=eu.populatedBy(this.computePages),this.pageMap=new Map,this.formCache=eu.populatedBy(this.getOrCreateForm),this.fonts=[],this.images=[],this.embeddedPages=[],this.embeddedFiles=[],this.javaScripts=[],!e&&this.isEncrypted)throw new o5;r&&this.updateInfoDict()}return t.load=function(e,r){return void 0===r&&(r={}),j(this,void 0,void 0,function(){var n,o,i,a,s,u,c,h,l,f,d;return z(this,function(p){switch(p.label){case 0:return o=void 0!==(n=r.ignoreEncryption)&&n,a=void 0===(i=r.parseSpeed)?A.Slow:i,u=void 0!==(s=r.throwOnInvalidObject)&&s,h=void 0===(c=r.updateMetadata)||c,f=void 0!==(l=r.capNumbers)&&l,t8(e,"pdf",["string",Uint8Array,ArrayBuffer]),t8(o,"ignoreEncryption",["boolean"]),t8(a,"parseSpeed",["number"]),t8(u,"throwOnInvalidObject",["boolean"]),d=tm(e),[4,nz.forBytesWithOptions(d,a,u,f).parseDocument()];case 1:return[2,new t(p.sent(),o,h)]}})})},t.create=function(e){return void 0===e&&(e={}),j(this,void 0,void 0,function(){var r,n,o,i,a,s;return z(this,function(u){return n=void 0===(r=e.updateMetadata)||r,o=e7.create(),i=ny.withContext(o),a=o.register(i),s=ng.withContextAndPages(o,a),o.trailerInfo.Root=o.register(s),[2,new t(o,!1,n)]})})},t.prototype.registerFontkit=function(t){this.fontkit=t},t.prototype.getForm=function(){var t=this.formCache.access();return t.hasXFA()&&(console.warn("Removing XFA form data as pdf-lib does not support reading or writing XFA"),t.deleteXFA()),t},t.prototype.getTitle=function(){var t=this.getInfoDict().lookup(eY.Title);if(t)return iX(t),t.decodeText()},t.prototype.getAuthor=function(){var t=this.getInfoDict().lookup(eY.Author);if(t)return iX(t),t.decodeText()},t.prototype.getSubject=function(){var t=this.getInfoDict().lookup(eY.Subject);if(t)return iX(t),t.decodeText()},t.prototype.getKeywords=function(){var t=this.getInfoDict().lookup(eY.Keywords);if(t)return iX(t),t.decodeText()},t.prototype.getCreator=function(){var t=this.getInfoDict().lookup(eY.Creator);if(t)return iX(t),t.decodeText()},t.prototype.getProducer=function(){var t=this.getInfoDict().lookup(eY.Producer);if(t)return iX(t),t.decodeText()},t.prototype.getCreationDate=function(){var t=this.getInfoDict().lookup(eY.CreationDate);if(t)return iX(t),t.decodeDate()},t.prototype.getModificationDate=function(){var t=this.getInfoDict().lookup(eY.ModDate);if(t)return iX(t),t.decodeDate()},t.prototype.setTitle=function(t,e){t8(t,"title",["string"]);var r=eY.of("Title");this.getInfoDict().set(r,rh.fromText(t)),(null==e?void 0:e.showInWindowTitleBar)&&this.catalog.getOrCreateViewerPreferences().setDisplayDocTitle(!0)},t.prototype.setAuthor=function(t){t8(t,"author",["string"]);var e=eY.of("Author");this.getInfoDict().set(e,rh.fromText(t))},t.prototype.setSubject=function(t){t8(t,"author",["string"]);var e=eY.of("Subject");this.getInfoDict().set(e,rh.fromText(t))},t.prototype.setKeywords=function(t){t8(t,"keywords",[Array]);var e=eY.of("Keywords");this.getInfoDict().set(e,rh.fromText(t.join(" ")))},t.prototype.setCreator=function(t){t8(t,"creator",["string"]);var e=eY.of("Creator");this.getInfoDict().set(e,rh.fromText(t))},t.prototype.setProducer=function(t){t8(t,"creator",["string"]);var e=eY.of("Producer");this.getInfoDict().set(e,rh.fromText(t))},t.prototype.setLanguage=function(t){t8(t,"language",["string"]);var e=eY.of("Lang");this.catalog.set(e,rb.of(t))},t.prototype.setCreationDate=function(t){t8(t,"creationDate",[[Date,"Date"]]);var e=eY.of("CreationDate");this.getInfoDict().set(e,rb.fromDate(t))},t.prototype.setModificationDate=function(t){t8(t,"modificationDate",[[Date,"Date"]]);var e=eY.of("ModDate");this.getInfoDict().set(e,rb.fromDate(t))},t.prototype.getPageCount=function(){return void 0===this.pageCount&&(this.pageCount=this.getPages().length),this.pageCount},t.prototype.getPages=function(){return this.pageCache.access()},t.prototype.getPage=function(t){var e=this.getPages();return et(t,"index",0,e.length-1),e[t]},t.prototype.getPageIndices=function(){return ty(0,this.getPageCount())},t.prototype.removePage=function(t){var e=this.getPageCount();if(0===this.pageCount)throw new o6;et(t,"index",0,e-1),this.catalog.removeLeafNode(t),this.pageCount=e-1},t.prototype.addPage=function(t){return t8(t,"page",["undefined",[iH,"PDFPage"],Array]),this.insertPage(this.getPageCount(),t)},t.prototype.insertPage=function(t,e){var r=this.getPageCount();if(et(t,"index",0,r),t8(e,"page",["undefined",[iH,"PDFPage"],Array]),!e||Array.isArray(e)){var n=Array.isArray(e)?e:iW.A4;(e=iH.create(this)).setSize.apply(e,n)}else if(e.doc!==this)throw new o4;var o=this.catalog.insertLeafNode(e.ref,t);return e.node.setParent(o),this.pageMap.set(e.node,e),this.pageCache.invalidate(),this.pageCount=r+1,e},t.prototype.copyPages=function(e,r){return j(this,void 0,void 0,function(){var n,o,i,a,s,u,c,h;return z(this,function(l){switch(l.label){case 0:return t8(e,"srcDoc",[[t,"PDFDocument"]]),t8(r,"indices",[Array]),[4,e.flush()];case 1:for(l.sent(),n=re.for(e.context,this.context),o=e.getPages(),i=Array(r.length),a=0,s=r.length;a<s;a++)u=o[r[a]],c=n.copy(u.node),h=this.context.register(c),i[a]=iH.of(c,h,this);return[2,i]}})})},t.prototype.copy=function(){return j(this,void 0,void 0,function(){var e,r,n,o;return z(this,function(i){switch(i.label){case 0:return[4,t.create()];case 1:return[4,(e=i.sent()).copyPages(this,this.getPageIndices())];case 2:for(n=0,o=(r=i.sent()).length;n<o;n++)e.addPage(r[n]);return void 0!==this.getAuthor()&&e.setAuthor(this.getAuthor()),void 0!==this.getCreationDate()&&e.setCreationDate(this.getCreationDate()),void 0!==this.getCreator()&&e.setCreator(this.getCreator()),void 0!==this.getModificationDate()&&e.setModificationDate(this.getModificationDate()),void 0!==this.getProducer()&&e.setProducer(this.getProducer()),void 0!==this.getSubject()&&e.setSubject(this.getSubject()),void 0!==this.getTitle()&&e.setTitle(this.getTitle()),e.defaultWordBreaks=this.defaultWordBreaks,[2,e]}})})},t.prototype.addJavaScript=function(t,e){t8(t,"name",["string"]),t8(e,"script",["string"]);var r=iG.for(e,t),n=this.context.nextRef(),o=iK.of(n,this,r);this.javaScripts.push(o)},t.prototype.attach=function(t,e,r){return void 0===r&&(r={}),j(this,void 0,void 0,function(){var n,o,i,a;return z(this,function(s){return t8(t,"attachment",["string",Uint8Array,ArrayBuffer]),t8(e,"name",["string"]),t9(r.mimeType,"mimeType",["string"]),t9(r.description,"description",["string"]),t9(r.creationDate,"options.creationDate",[Date]),t9(r.modificationDate,"options.modificationDate",[Date]),t3(r.afRelationship,"options.afRelationship",u),n=tm(t),o=rF.for(n,e,r),i=this.context.nextRef(),a=iq.of(i,this,o),this.embeddedFiles.push(a),[2]})})},t.prototype.embedFont=function(t,e){return void 0===e&&(e={}),j(this,void 0,void 0,function(){var r,n,o,i,a,s,u,c,h,l;return z(this,function(f){switch(f.label){case 0:if(n=void 0!==(r=e.subset)&&r,o=e.customName,i=e.features,t8(t,"font",["string",Uint8Array,ArrayBuffer]),t8(n,"subset",["boolean"]),!t_(t))return[3,1];return a=rl.for(t,o),[3,7];case 1:if(!(t instanceof Uint8Array||t instanceof ArrayBuffer||"string"==typeof t))return[3,6];if(s=tm(t),u=this.assertFontkit(),!n)return[3,3];return[4,rw.for(u,s,o,i)];case 2:return c=f.sent(),[3,5];case 3:return[4,rx.for(u,s,o,i)];case 4:c=f.sent(),f.label=5;case 5:return a=c,[3,7];case 6:throw TypeError("`font` must be one of `StandardFonts | string | Uint8Array | ArrayBuffer`");case 7:return h=this.context.nextRef(),l=iO.of(h,this,a),this.fonts.push(l),[2,l]}})})},t.prototype.embedStandardFont=function(t,e){if(t8(t,"font",["string"]),!t_(t))throw TypeError("`font` must be one of type `StandardFonts`");var r=rl.for(t,e),n=this.context.nextRef(),o=iO.of(n,this,r);return this.fonts.push(o),o},t.prototype.embedJpg=function(t){return j(this,void 0,void 0,function(){var e,r,n,o;return z(this,function(i){switch(i.label){case 0:return t8(t,"jpg",["string",Uint8Array,ArrayBuffer]),e=tm(t),[4,rk.for(e)];case 1:return r=i.sent(),n=this.context.nextRef(),o=iA.of(n,this,r),this.images.push(o),[2,o]}})})},t.prototype.embedPng=function(t){return j(this,void 0,void 0,function(){var e,r,n,o;return z(this,function(i){switch(i.label){case 0:return t8(t,"png",["string",Uint8Array,ArrayBuffer]),e=tm(t),[4,rR.for(e)];case 1:return r=i.sent(),n=this.context.nextRef(),o=iA.of(n,this,r),this.images.push(o),[2,o]}})})},t.prototype.embedPdf=function(e,r){return void 0===r&&(r=[0]),j(this,void 0,void 0,function(){var n,o;return z(this,function(i){switch(i.label){case 0:if(t8(e,"pdf",["string",Uint8Array,ArrayBuffer,[t,"PDFDocument"]]),t8(r,"indices",[Array]),!(e instanceof t))return[3,1];return n=e,[3,3];case 1:return[4,t.load(e)];case 2:n=i.sent(),i.label=3;case 3:return o=tv(n.getPages(),r),[2,this.embedPages(o)]}})})},t.prototype.embedPage=function(t,e,r){return j(this,void 0,void 0,function(){return z(this,function(n){switch(n.label){case 0:return t8(t,"page",[[iH,"PDFPage"]]),[4,this.embedPages([t],[e],[r])];case 1:return[2,n.sent()[0]]}})})},t.prototype.embedPages=function(t,e,r){return void 0===e&&(e=[]),void 0===r&&(r=[]),j(this,void 0,void 0,function(){var n,o,i,a,s,u,c,h,l,f,d,p,g;return z(this,function(y){switch(y.label){case 0:if(0===t.length)return[2,[]];for(n=0,o=t.length-1;n<o;n++)if(i=t[n],a=t[n+1],i.node.context!==a.node.context)throw new ey;u=(s=t[0].node.context)===this.context?function(t){return t}:re.for(s,this.context).copy,c=Array(t.length),n=0,o=t.length,y.label=1;case 1:if(!(n<o))return[3,4];return h=u(t[n].node),l=e[n],f=r[n],[4,rZ.for(h,l,f)];case 2:d=y.sent(),p=this.context.nextRef(),c[n]=iT.of(p,this,d),y.label=3;case 3:return n++,[3,1];case 4:return(g=this.embeddedPages).push.apply(g,c),[2,c]}})})},t.prototype.flush=function(){return j(this,void 0,void 0,function(){return z(this,function(t){switch(t.label){case 0:return[4,this.embedAll(this.fonts)];case 1:return t.sent(),[4,this.embedAll(this.images)];case 2:return t.sent(),[4,this.embedAll(this.embeddedPages)];case 3:return t.sent(),[4,this.embedAll(this.embeddedFiles)];case 4:return t.sent(),[4,this.embedAll(this.javaScripts)];case 5:return t.sent(),[2]}})})},t.prototype.save=function(t){return void 0===t&&(t={}),j(this,void 0,void 0,function(){var e,r,n,o,i,a,s,u,c;return z(this,function(h){switch(h.label){case 0:return r=void 0===(e=t.useObjectStreams)||e,o=void 0===(n=t.addDefaultPage)||n,a=void 0===(i=t.objectsPerTick)?50:i,u=void 0===(s=t.updateFieldAppearances)||s,t8(r,"useObjectStreams",["boolean"]),t8(o,"addDefaultPage",["boolean"]),t8(a,"objectsPerTick",["number"]),t8(u,"updateFieldAppearances",["boolean"]),o&&0===this.getPageCount()&&this.addPage(),u&&(c=this.formCache.getValue())&&c.updateFieldAppearances(),[4,this.flush()];case 1:return h.sent(),[2,(r?rc:ra).forContext(this.context,a).serializeToBuffer()]}})})},t.prototype.saveAsBase64=function(t){return void 0===t&&(t={}),j(this,void 0,void 0,function(){var e,r,n,o;return z(this,function(i){switch(i.label){case 0:return r=void 0!==(e=t.dataUri)&&e,n=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)0>e.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]]);return r}(t,["dataUri"]),t8(r,"dataUri",["boolean"]),[4,this.save(n)];case 1:return o=U(i.sent()),[2,r?"data:application/pdf;base64,"+o:o]}})})},t.prototype.findPageForAnnotationRef=function(t){for(var e=this.getPages(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.node.Annots();if((null==i?void 0:i.indexOf(t))!==void 0)return o}},t.prototype.embedAll=function(t){return j(this,void 0,void 0,function(){var e,r;return z(this,function(n){switch(n.label){case 0:e=0,r=t.length,n.label=1;case 1:if(!(e<r))return[3,4];return[4,t[e].embed()];case 2:n.sent(),n.label=3;case 3:return e++,[3,1];case 4:return[2]}})})},t.prototype.updateInfoDict=function(){var t="pdf-lib (https://github.com/Hopding/pdf-lib)",e=new Date,r=this.getInfoDict();this.setProducer(t),this.setModificationDate(e),r.get(eY.of("Creator"))||this.setCreator(t),r.get(eY.of("CreationDate"))||this.setCreationDate(e)},t.prototype.getInfoDict=function(){var t=this.context.lookup(this.context.trailerInfo.Info);if(t instanceof eQ)return t;var e=this.context.obj({});return this.context.trailerInfo.Info=this.context.register(e),e},t.prototype.assertFontkit=function(){if(!this.fontkit)throw new o3;return this.fontkit},t}();function iX(t){if(!(t instanceof rh)&&!(t instanceof rb))throw new el([rh,rb],t)}!function(t){t.Normal="Normal",t.Multiply="Multiply",t.Screen="Screen",t.Overlay="Overlay",t.Darken="Darken",t.Lighten="Lighten",t.ColorDodge="ColorDodge",t.ColorBurn="ColorBurn",t.HardLight="HardLight",t.SoftLight="SoftLight",t.Difference="Difference",t.Exclusion="Exclusion"}(P||(P={}));let iH=function(){function t(t,e,r){this.fontSize=24,this.fontColor=om(0,0,0),this.lineHeight=24,this.x=0,this.y=0,t8(t,"leafNode",[[rt,"PDFPageLeaf"]]),t8(e,"ref",[[e2,"PDFRef"]]),t8(r,"doc",[[iL,"PDFDocument"]]),this.node=t,this.ref=e,this.doc=r}return t.prototype.setRotation=function(t){var e=nG(t);er(e,"degreesAngle",90),this.node.set(eY.of("Rotate"),this.doc.context.obj(e))},t.prototype.getRotation=function(){var t=this.node.Rotate();return nU(t?t.asNumber():0)},t.prototype.setSize=function(t,e){t8(t,"width",["number"]),t8(e,"height",["number"]);var r=this.getMediaBox();this.setMediaBox(r.x,r.y,t,e);var n=this.getCropBox(),o=this.getBleedBox(),i=this.getTrimBox(),a=this.getArtBox(),s=this.node.CropBox(),u=this.node.BleedBox(),c=this.node.TrimBox(),h=this.node.ArtBox();s&&t$(n,r)&&this.setCropBox(r.x,r.y,t,e),u&&t$(o,r)&&this.setBleedBox(r.x,r.y,t,e),c&&t$(i,r)&&this.setTrimBox(r.x,r.y,t,e),h&&t$(a,r)&&this.setArtBox(r.x,r.y,t,e)},t.prototype.setWidth=function(t){t8(t,"width",["number"]),this.setSize(t,this.getSize().height)},t.prototype.setHeight=function(t){t8(t,"height",["number"]),this.setSize(this.getSize().width,t)},t.prototype.setMediaBox=function(t,e,r,n){t8(t,"x",["number"]),t8(e,"y",["number"]),t8(r,"width",["number"]),t8(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(eY.MediaBox,o)},t.prototype.setCropBox=function(t,e,r,n){t8(t,"x",["number"]),t8(e,"y",["number"]),t8(r,"width",["number"]),t8(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(eY.CropBox,o)},t.prototype.setBleedBox=function(t,e,r,n){t8(t,"x",["number"]),t8(e,"y",["number"]),t8(r,"width",["number"]),t8(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(eY.BleedBox,o)},t.prototype.setTrimBox=function(t,e,r,n){t8(t,"x",["number"]),t8(e,"y",["number"]),t8(r,"width",["number"]),t8(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(eY.TrimBox,o)},t.prototype.setArtBox=function(t,e,r,n){t8(t,"x",["number"]),t8(e,"y",["number"]),t8(r,"width",["number"]),t8(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(eY.ArtBox,o)},t.prototype.getSize=function(){var t=this.getMediaBox();return{width:t.width,height:t.height}},t.prototype.getWidth=function(){return this.getSize().width},t.prototype.getHeight=function(){return this.getSize().height},t.prototype.getMediaBox=function(){return this.node.MediaBox().asRectangle()},t.prototype.getCropBox=function(){var t,e=this.node.CropBox();return null!=(t=null==e?void 0:e.asRectangle())?t:this.getMediaBox()},t.prototype.getBleedBox=function(){var t,e=this.node.BleedBox();return null!=(t=null==e?void 0:e.asRectangle())?t:this.getCropBox()},t.prototype.getTrimBox=function(){var t,e=this.node.TrimBox();return null!=(t=null==e?void 0:e.asRectangle())?t:this.getCropBox()},t.prototype.getArtBox=function(){var t,e=this.node.ArtBox();return null!=(t=null==e?void 0:e.asRectangle())?t:this.getCropBox()},t.prototype.translateContent=function(t,e){t8(t,"x",["number"]),t8(e,"y",["number"]),this.node.normalize(),this.getContentStream();var r=this.createContentStream(n8(),n$(t,e)),n=this.doc.context.register(r),o=this.createContentStream(n9()),i=this.doc.context.register(o);this.node.wrapContentStreams(n,i)},t.prototype.scale=function(t,e){t8(t,"x",["number"]),t8(e,"y",["number"]),this.setSize(this.getWidth()*t,this.getHeight()*e),this.scaleContent(t,e),this.scaleAnnotations(t,e)},t.prototype.scaleContent=function(t,e){t8(t,"x",["number"]),t8(e,"y",["number"]),this.node.normalize(),this.getContentStream();var r=this.createContentStream(n8(),n0(t,e)),n=this.doc.context.register(r),o=this.createContentStream(n9()),i=this.doc.context.register(o);this.node.wrapContentStreams(n,i)},t.prototype.scaleAnnotations=function(t,e){t8(t,"x",["number"]),t8(e,"y",["number"]);var r=this.node.Annots();if(r)for(var n=0;n<r.size();n++){var o=r.lookup(n);o instanceof eQ&&this.scaleAnnot(o,t,e)}},t.prototype.resetPosition=function(){this.getContentStream(!1),this.x=0,this.y=0},t.prototype.setFont=function(t){t8(t,"font",[[iO,"PDFFont"]]),this.font=t,this.fontKey=this.node.newFontDictionary(this.font.name,this.font.ref)},t.prototype.setFontSize=function(t){t8(t,"fontSize",["number"]),this.fontSize=t},t.prototype.setFontColor=function(t){t8(t,"fontColor",[[Object,"Color"]]),this.fontColor=t},t.prototype.setLineHeight=function(t){t8(t,"lineHeight",["number"]),this.lineHeight=t},t.prototype.getPosition=function(){return{x:this.x,y:this.y}},t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t.prototype.moveTo=function(t,e){t8(t,"x",["number"]),t8(e,"y",["number"]),this.x=t,this.y=e},t.prototype.moveDown=function(t){t8(t,"yDecrease",["number"]),this.y-=t},t.prototype.moveUp=function(t){t8(t,"yIncrease",["number"]),this.y+=t},t.prototype.moveLeft=function(t){t8(t,"xDecrease",["number"]),this.x-=t},t.prototype.moveRight=function(t){t8(t,"xIncrease",["number"]),this.x+=t},t.prototype.pushOperators=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];t7(t,"operator",[[e5,"PDFOperator"]]);var r=this.getContentStream();r.push.apply(r,t)},t.prototype.drawText=function(t,e){void 0===e&&(e={}),t8(t,"text",["string"]),t9(e.color,"options.color",[[Object,"Color"]]),ee(e.opacity,"opacity.opacity",0,1),t9(e.font,"options.font",[[iO,"PDFFont"]]),t9(e.size,"options.size",["number"]),t9(e.rotate,"options.rotate",[[Object,"Rotation"]]),t9(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),t9(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),t9(e.x,"options.x",["number"]),t9(e.y,"options.y",["number"]),t9(e.lineHeight,"options.lineHeight",["number"]),t9(e.maxWidth,"options.maxWidth",["number"]),t9(e.wordBreaks,"options.wordBreaks",[Array]),t3(e.blendMode,"options.blendMode",P);for(var r,n,o,i,a,s,u,c=this.setOrEmbedFont(e.font),h=c.oldFont,l=c.newFont,f=c.newFontKey,d=e.size||this.fontSize,p=e.wordBreaks||this.doc.defaultWordBreaks,g=void 0===e.maxWidth?_(Y(t)):tn(t,p,e.maxWidth,function(t){return l.widthOfTextAtSize(t,d)}),y=Array(g.length),v=0,m=g.length;v<m;v++)y[v]=l.encodeText(g[v]);var b=this.maybeEmbedGraphicsState({opacity:e.opacity,blendMode:e.blendMode}),x=this.getContentStream();x.push.apply(x,oE(y,{color:null!=(r=e.color)?r:this.fontColor,font:f,size:d,rotate:null!=(n=e.rotate)?n:nU(0),xSkew:null!=(o=e.xSkew)?o:nU(0),ySkew:null!=(i=e.ySkew)?i:nU(0),x:null!=(a=e.x)?a:this.x,y:null!=(s=e.y)?s:this.y,lineHeight:null!=(u=e.lineHeight)?u:this.lineHeight,graphicsState:b})),e.font&&(h?this.setFont(h):this.resetFont())},t.prototype.drawImage=function(t,e){void 0===e&&(e={}),t8(t,"image",[[iA,"PDFImage"]]),t9(e.x,"options.x",["number"]),t9(e.y,"options.y",["number"]),t9(e.width,"options.width",["number"]),t9(e.height,"options.height",["number"]),t9(e.rotate,"options.rotate",[[Object,"Rotation"]]),t9(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),t9(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),ee(e.opacity,"opacity.opacity",0,1),t3(e.blendMode,"options.blendMode",P);var r,n,o,i,a,s,u,c=this.node.newXObject("Image",t.ref),h=this.maybeEmbedGraphicsState({opacity:e.opacity,blendMode:e.blendMode}),l=this.getContentStream();l.push.apply(l,oW(c,{x:null!=(r=e.x)?r:this.x,y:null!=(n=e.y)?n:this.y,width:null!=(o=e.width)?o:t.size().width,height:null!=(i=e.height)?i:t.size().height,rotate:null!=(a=e.rotate)?a:nU(0),xSkew:null!=(s=e.xSkew)?s:nU(0),ySkew:null!=(u=e.ySkew)?u:nU(0),graphicsState:h}))},t.prototype.drawPage=function(t,e){void 0===e&&(e={}),t8(t,"embeddedPage",[[iT,"PDFEmbeddedPage"]]),t9(e.x,"options.x",["number"]),t9(e.y,"options.y",["number"]),t9(e.xScale,"options.xScale",["number"]),t9(e.yScale,"options.yScale",["number"]),t9(e.width,"options.width",["number"]),t9(e.height,"options.height",["number"]),t9(e.rotate,"options.rotate",[[Object,"Rotation"]]),t9(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),t9(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),ee(e.opacity,"opacity.opacity",0,1),t3(e.blendMode,"options.blendMode",P);var r,n,o,i,a,s,u=this.node.newXObject("EmbeddedPdfPage",t.ref),c=this.maybeEmbedGraphicsState({opacity:e.opacity,blendMode:e.blendMode}),h=void 0!==e.width?e.width/t.width:void 0!==e.xScale?e.xScale:1,l=void 0!==e.height?e.height/t.height:void 0!==e.yScale?e.yScale:1,f=this.getContentStream();f.push.apply(f,(r={x:null!=(n=e.x)?n:this.x,y:null!=(o=e.y)?o:this.y,xScale:h,yScale:l,rotate:null!=(i=e.rotate)?i:nU(0),xSkew:null!=(a=e.xSkew)?a:nU(0),ySkew:null!=(s=e.ySkew)?s:nU(0),graphicsState:c},[n8(),r.graphicsState&&n6(r.graphicsState),n$(r.x,r.y),n1(nK(r.rotate)),n0(r.xScale,r.yScale),n5(nK(r.xSkew),nK(r.ySkew)),op(u),n9()].filter(Boolean)))},t.prototype.drawSvgPath=function(t,e){void 0===e&&(e={}),t8(t,"path",["string"]),t9(e.x,"options.x",["number"]),t9(e.y,"options.y",["number"]),t9(e.scale,"options.scale",["number"]),t9(e.rotate,"options.rotate",[[Object,"Rotation"]]),t9(e.borderWidth,"options.borderWidth",["number"]),t9(e.color,"options.color",[[Object,"Color"]]),ee(e.opacity,"opacity.opacity",0,1),t9(e.borderColor,"options.borderColor",[[Object,"Color"]]),t9(e.borderDashArray,"options.borderDashArray",[Array]),t9(e.borderDashPhase,"options.borderDashPhase",["number"]),t3(e.borderLineCap,"options.borderLineCap",w),ee(e.borderOpacity,"options.borderOpacity",0,1),t3(e.blendMode,"options.blendMode",P);var r,n,o,i,a,s,u,c,h,l=this.maybeEmbedGraphicsState({opacity:e.opacity,borderOpacity:e.borderOpacity,blendMode:e.blendMode});"color"in e||"borderColor"in e||(e.borderColor=om(0,0,0));var f=this.getContentStream();f.push.apply(f,oZ(t,{x:null!=(r=e.x)?r:this.x,y:null!=(n=e.y)?n:this.y,scale:e.scale,rotate:null!=(o=e.rotate)?o:nU(0),color:null!=(i=e.color)?i:void 0,borderColor:null!=(a=e.borderColor)?a:void 0,borderWidth:null!=(s=e.borderWidth)?s:0,borderDashArray:null!=(u=e.borderDashArray)?u:void 0,borderDashPhase:null!=(c=e.borderDashPhase)?c:void 0,borderLineCap:null!=(h=e.borderLineCap)?h:void 0,graphicsState:l}))},t.prototype.drawLine=function(t){t8(t.start,"options.start",[[Object,"{ x: number, y: number }"]]),t8(t.end,"options.end",[[Object,"{ x: number, y: number }"]]),t8(t.start.x,"options.start.x",["number"]),t8(t.start.y,"options.start.y",["number"]),t8(t.end.x,"options.end.x",["number"]),t8(t.end.y,"options.end.y",["number"]),t9(t.thickness,"options.thickness",["number"]),t9(t.color,"options.color",[[Object,"Color"]]),t9(t.dashArray,"options.dashArray",[Array]),t9(t.dashPhase,"options.dashPhase",["number"]),t3(t.lineCap,"options.lineCap",w),ee(t.opacity,"opacity.opacity",0,1),t3(t.blendMode,"options.blendMode",P);var e,r,n,o,i,a=this.maybeEmbedGraphicsState({borderOpacity:t.opacity,blendMode:t.blendMode});"color"in t||(t.color=om(0,0,0));var s=this.getContentStream();s.push.apply(s,oq({start:t.start,end:t.end,thickness:null!=(e=t.thickness)?e:1,color:null!=(r=t.color)?r:void 0,dashArray:null!=(n=t.dashArray)?n:void 0,dashPhase:null!=(o=t.dashPhase)?o:void 0,lineCap:null!=(i=t.lineCap)?i:void 0,graphicsState:a}))},t.prototype.drawRectangle=function(t){void 0===t&&(t={}),t9(t.x,"options.x",["number"]),t9(t.y,"options.y",["number"]),t9(t.width,"options.width",["number"]),t9(t.height,"options.height",["number"]),t9(t.rotate,"options.rotate",[[Object,"Rotation"]]),t9(t.xSkew,"options.xSkew",[[Object,"Rotation"]]),t9(t.ySkew,"options.ySkew",[[Object,"Rotation"]]),t9(t.borderWidth,"options.borderWidth",["number"]),t9(t.color,"options.color",[[Object,"Color"]]),ee(t.opacity,"opacity.opacity",0,1),t9(t.borderColor,"options.borderColor",[[Object,"Color"]]),t9(t.borderDashArray,"options.borderDashArray",[Array]),t9(t.borderDashPhase,"options.borderDashPhase",["number"]),t3(t.borderLineCap,"options.borderLineCap",w),ee(t.borderOpacity,"options.borderOpacity",0,1),t3(t.blendMode,"options.blendMode",P);var e,r,n,o,i,a,s,u,c,h,l,f,d,p=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});"color"in t||"borderColor"in t||(t.color=om(0,0,0));var g=this.getContentStream();g.push.apply(g,oK({x:null!=(e=t.x)?e:this.x,y:null!=(r=t.y)?r:this.y,width:null!=(n=t.width)?n:150,height:null!=(o=t.height)?o:100,rotate:null!=(i=t.rotate)?i:nU(0),xSkew:null!=(a=t.xSkew)?a:nU(0),ySkew:null!=(s=t.ySkew)?s:nU(0),borderWidth:null!=(u=t.borderWidth)?u:0,color:null!=(c=t.color)?c:void 0,borderColor:null!=(h=t.borderColor)?h:void 0,borderDashArray:null!=(l=t.borderDashArray)?l:void 0,borderDashPhase:null!=(f=t.borderDashPhase)?f:void 0,graphicsState:p,borderLineCap:null!=(d=t.borderLineCap)?d:void 0}))},t.prototype.drawSquare=function(t){void 0===t&&(t={});var e=t.size;t9(e,"size",["number"]),this.drawRectangle(D(D({},t),{width:e,height:e}))},t.prototype.drawEllipse=function(t){void 0===t&&(t={}),t9(t.x,"options.x",["number"]),t9(t.y,"options.y",["number"]),t9(t.xScale,"options.xScale",["number"]),t9(t.yScale,"options.yScale",["number"]),t9(t.rotate,"options.rotate",[[Object,"Rotation"]]),t9(t.color,"options.color",[[Object,"Color"]]),ee(t.opacity,"opacity.opacity",0,1),t9(t.borderColor,"options.borderColor",[[Object,"Color"]]),ee(t.borderOpacity,"options.borderOpacity",0,1),t9(t.borderWidth,"options.borderWidth",["number"]),t9(t.borderDashArray,"options.borderDashArray",[Array]),t9(t.borderDashPhase,"options.borderDashPhase",["number"]),t3(t.borderLineCap,"options.borderLineCap",w),t3(t.blendMode,"options.blendMode",P);var e,r,n,o,i,a,s,u,c,h,l,f=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});"color"in t||"borderColor"in t||(t.color=om(0,0,0));var d=this.getContentStream();d.push.apply(d,oH({x:null!=(e=t.x)?e:this.x,y:null!=(r=t.y)?r:this.y,xScale:null!=(n=t.xScale)?n:100,yScale:null!=(o=t.yScale)?o:100,rotate:null!=(i=t.rotate)?i:void 0,color:null!=(a=t.color)?a:void 0,borderColor:null!=(s=t.borderColor)?s:void 0,borderWidth:null!=(u=t.borderWidth)?u:0,borderDashArray:null!=(c=t.borderDashArray)?c:void 0,borderDashPhase:null!=(h=t.borderDashPhase)?h:void 0,borderLineCap:null!=(l=t.borderLineCap)?l:void 0,graphicsState:f}))},t.prototype.drawCircle=function(t){void 0===t&&(t={});var e=t.size,r=void 0===e?100:e;t9(r,"size",["number"]),this.drawEllipse(D(D({},t),{xScale:r,yScale:r}))},t.prototype.setOrEmbedFont=function(t){var e=this.font,r=this.fontKey;return t?this.setFont(t):this.getFont(),{oldFont:e,oldFontKey:r,newFont:this.font,newFontKey:this.fontKey}},t.prototype.getFont=function(){if(!this.font||!this.fontKey){var t=this.doc.embedStandardFont(O.Helvetica);this.setFont(t)}return[this.font,this.fontKey]},t.prototype.resetFont=function(){this.font=void 0,this.fontKey=void 0},t.prototype.getContentStream=function(t){return void 0===t&&(t=!0),t&&this.contentStream||(this.contentStream=this.createContentStream(),this.contentStreamRef=this.doc.context.register(this.contentStream),this.node.addContentStream(this.contentStreamRef)),this.contentStream},t.prototype.createContentStream=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=this.doc.context.obj({});return e6.of(r,t)},t.prototype.maybeEmbedGraphicsState=function(t){var e=t.opacity,r=t.borderOpacity,n=t.blendMode;if(void 0!==e||void 0!==r||void 0!==n){var o=this.doc.context.obj({Type:"ExtGState",ca:e,CA:r,BM:n});return this.node.newExtGState("GS",o)}},t.prototype.scaleAnnot=function(t,e,r){for(var n=["RD","CL","Vertices","QuadPoints","L","Rect"],o=0,i=n.length;o<i;o++){var a=t.lookup(eY.of(n[o]));a instanceof eE&&a.scalePDFNumbers(e,r)}var s=t.lookup(eY.of("InkList"));if(s instanceof eE)for(var o=0,i=s.size();o<i;o++){var u=s.lookup(o);u instanceof eE&&u.scalePDFNumbers(e,r)}},t.of=function(e,r,n){return new t(e,r,n)},t.create=function(e){t8(e,"doc",[[iL,"PDFDocument"]]);var r=e2.of(-1),n=rt.withContextAndParent(e.context,r),o=e.context.register(n);return new t(n,o,e)},t}(),iZ=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return t8(e,"acroButton",[[nr,"PDFAcroPushButton"]]),o.acroField=e,o}return N(e,t),e.prototype.setImage=function(t,e){void 0===e&&(e=T.Center);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n],a=this.createImageAppearanceStream(i,t,e);this.updateWidgetAppearances(i,{normal:a})}this.markAsClean()},e.prototype.setFontSize=function(t){eo(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.addToPage=function(t,e,r){t9(t,"text",["string"]),t9(e,"page",[[iH,"PDFPage"]]),iP(r);var n,o,i,a,s,u,c,h,l,f,d,p=this.createWidget({x:(null!=(n=null==r?void 0:r.x)?n:0)-(null!=(o=null==r?void 0:r.borderWidth)?o:0)/2,y:(null!=(i=null==r?void 0:r.y)?i:0)-(null!=(a=null==r?void 0:r.borderWidth)?a:0)/2,width:null!=(s=null==r?void 0:r.width)?s:100,height:null!=(u=null==r?void 0:r.height)?u:50,textColor:null!=(c=null==r?void 0:r.textColor)?c:om(0,0,0),backgroundColor:null!=(h=null==r?void 0:r.backgroundColor)?h:om(.75,.75,.75),borderColor:null==r?void 0:r.borderColor,borderWidth:null!=(l=null==r?void 0:r.borderWidth)?l:0,rotate:null!=(f=null==r?void 0:r.rotate)?f:nU(0),caption:t,hidden:null==r?void 0:r.hidden,page:e.ref}),g=this.doc.context.register(p.dict);this.acroField.addWidget(g);var y=null!=(d=null==r?void 0:r.font)?d:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(p,y),e.node.addAnnot(g)},e.prototype.needsAppearancesUpdate=function(){if(this.isDirty())return!0;for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++)if(!((null==(t=e[r].getAppearances())?void 0:t.normal)instanceof e_))return!0;return!1},e.prototype.defaultUpdateAppearances=function(t){t8(t,"font",[[iO,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){t8(t,"font",[[iO,"PDFFont"]]),t9(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}},e.prototype.updateWidgetAppearance=function(t,e,r){var n=ip((null!=r?r:iF)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(iR)},97411:(t,e,r)=>{var n=r(144).assign,o=r(2980),i=r(23408),a=r(76672),s={};n(s,o,i,a),t.exports=s}};
exports.id=8444,exports.ids=[8444],exports.modules={28319:(e,t,r)=>{"use strict";function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var a,i,o;a=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in a?Object.defineProperty(a,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{E:()=>o,SupabaseAdminService:()=>n});let o=(0,r(41370).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class n{static async createStripeTransaction(e){let{data:t,error:r}=await o.from("stripe_transactions").insert([e]).select().single();if(r)throw console.error("Error creating stripe transaction:",r),Error(`Failed to create transaction: ${r.message}`);return t}static async getTransactionBySessionId(e){let{data:t,error:r}=await o.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(r&&"PGRST116"!==r.code)throw console.error("Error fetching transaction:",r),Error(`Failed to fetch transaction: ${r.message}`);return t}static async createUserWithInvitation(e,t){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:t,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:r,error:a}=await o.auth.admin.inviteUserByEmail(e,{data:t,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!r,hasUser:!!r?.user,userId:r?.user?.id,userEmail:r?.user?.email,userAud:r?.user?.aud,userRole:r?.user?.role,emailConfirmed:r?.user?.email_confirmed_at,userMetadata:r?.user?.user_metadata,appMetadata:r?.user?.app_metadata,error:a?.message,errorCode:a?.status,fullError:a}),a)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:a.message,status:a.status,details:a}),Error(`Failed to create user invitation: ${a.message}`);return r}static async createUserWithPassword(e,t,r,a=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:r,sendConfirmationEmail:a,timestamp:new Date().toISOString()});let{data:i,error:n}=await o.auth.admin.createUser({email:e,password:t,user_metadata:r,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!i,hasUser:!!i?.user,userId:i?.user?.id,userEmail:i?.user?.email,emailConfirmed:i?.user?.email_confirmed_at,userMetadata:i?.user?.user_metadata,error:n?.message,errorCode:n?.status}),n)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:n.message,status:n.status,details:n}),{data:null,error:n};if(i?.user&&a){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:r}=await o.auth.admin.generateLink({type:"signup",email:e,password:t,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});r?console.error("⚠️ Error enviando email de confirmaci\xf3n:",r):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else i?.user&&!a&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:i,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:t,error:r}=await o.auth.admin.getUserById(e);if(r||!t?.user)return console.error("Error obteniendo datos del usuario:",r),{success:!1,error:"Usuario no encontrado"};let a=t.user,{error:n}=await o.auth.admin.updateUserById(a.id,{email_confirm:!0,user_metadata:i(i({},a.user_metadata),{},{payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()})});if(n)return console.error("⚠️ Error confirmando email del usuario:",n),{success:!1,error:n.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,t){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:r}=await o.auth.admin.generateLink({type:"signup",email:e,password:t,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return r?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",r),{success:!1,error:r.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:t,error:r}=await o.from("user_profiles").insert([e]).select().single();if(r)throw console.error("Error creating user profile:",r),Error(`Failed to create user profile: ${r.message}`);return t}static async upsertUserProfile(e){let{data:t,error:r}=await o.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(r)throw console.error("Error upserting user profile:",r),Error(`Failed to upsert user profile: ${r.message}`);return t}static async logPlanChange(e){let{data:t,error:r}=await o.from("user_plan_history").insert([e]).select().single();if(r)throw console.error("Error logging plan change:",r),Error(`Failed to log plan change: ${r.message}`);return t}static async logFeatureAccess(e){let{data:t,error:r}=await o.from("feature_access_log").insert([e]).select().single();if(r)throw console.error("Error logging feature access:",r),Error(`Failed to log feature access: ${r.message}`);return t}static async getUserProfile(e){let{data:t,error:r}=await o.from("user_profiles").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)throw console.error("Error fetching user profile:",r),Error(`Failed to fetch user profile: ${r.message}`);return t}static async updateTransactionWithUser(e,t){let{error:r}=await o.from("stripe_transactions").update({user_id:t,updated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error updating transaction with user_id:",r),Error(`Failed to update transaction: ${r.message}`)}static async activateTransaction(e){let{error:t}=await o.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error activating transaction:",t),Error(`Failed to activate transaction: ${t.message}`)}static async getDocumentsCount(e){let{count:t,error:r}=await o.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return r?(console.error("Error getting documents count:",r),0):t||0}static async getUserByEmail(e){try{let{data:{users:t},error:r}=await o.auth.admin.listUsers();if(r)throw console.error("Error getting user by email:",r),Error(`Failed to get user by email: ${r.message}`);if(!t||0===t.length)return null;let a=t.find(t=>t.email===e);if(!a)return null;return{id:a.id,email:a.email,email_confirmed_at:a.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},72280:(e,t,r)=>{"use strict";r.d(t,{X:()=>u});class a{static generateSubscriptionCancelledEmail(e,t,r){let a=new Date(r),i=a.toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),o=Math.ceil((a.getTime()-new Date().getTime())/864e5),n=`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Suscripci\xf3n Cancelada - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">Suscripci\xf3n Cancelada</h1>
          
          <p>Hola ${e},</p>
          
          <p>Hemos recibido tu solicitud de cancelaci\xf3n de la suscripci\xf3n al <strong>Plan ${t}</strong>.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #059669;">📅 Per\xedodo de Gracia Activo</h3>
            <p><strong>Mantienes acceso completo hasta:</strong> ${i}</p>
            <p><strong>D\xedas restantes:</strong> ${o} d\xedas</p>
            <p>Durante este per\xedodo, puedes seguir usando todas las funciones de tu plan actual.</p>
          </div>
          
          <h3>\xbfQu\xe9 sucede despu\xe9s?</h3>
          <ul>
            <li>Tu acceso a las funciones premium finalizar\xe1 el ${i}</li>
            <li>Tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito</li>
            <li>Conservar\xe1s acceso a las funciones b\xe1sicas de OposI</li>
            <li>Tus documentos y progreso se mantendr\xe1n guardados</li>
          </ul>
          
          <h3>\xbfCambiaste de opini\xf3n?</h3>
          <p>Si deseas reactivar tu suscripci\xf3n, puedes hacerlo en cualquier momento desde tu panel de control:</p>
          <p style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000/upgrade-plan" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Reactivar Suscripci\xf3n
            </a>
          </p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          
          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>
            Equipo de OposI
          </p>
        </div>
      </body>
      </html>
    `;return{htmlContent:n,textContent:`
Suscripci\xf3n Cancelada - OposI

Hola ${e},

Hemos recibido tu solicitud de cancelaci\xf3n de la suscripci\xf3n al Plan ${t}.

PER\xcdODO DE GRACIA ACTIVO:
- Mantienes acceso completo hasta: ${i}
- D\xedas restantes: ${o} d\xedas

\xbfQu\xe9 sucede despu\xe9s?
- Tu acceso a las funciones premium finalizar\xe1 el ${i}
- Tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito
- Conservar\xe1s acceso a las funciones b\xe1sicas de OposI
- Tus documentos y progreso se mantendr\xe1n guardados

\xbfCambiaste de opini\xf3n?
Puedes reactivar tu suscripci\xf3n en cualquier momento desde: http://localhost:3000/upgrade-plan

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
    `,subject:`Suscripci\xf3n cancelada - Acceso hasta el ${a.toLocaleDateString("es-ES")}`}}static generateGracePeriodEndingEmail(e,t,r){let a=new Date(r),i=a.toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric"}),o=Math.ceil((a.getTime()-new Date().getTime())/36e5),n=`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Tu acceso premium termina pronto - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #dc2626;">⏰ Tu acceso premium termina pronto</h1>
          
          <p>Hola ${e},</p>
          
          <p>Te recordamos que tu acceso al <strong>Plan ${t}</strong> terminar\xe1 el <strong>${i}</strong> (en aproximadamente ${o} horas).</p>
          
          <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <h3 style="margin-top: 0; color: #92400e;">\xbfQuieres continuar con tu plan premium?</h3>
            <p>Reactivar tu suscripci\xf3n es f\xe1cil y r\xe1pido. Mant\xe9n acceso a todas las funciones avanzadas de OposI.</p>
          </div>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="http://localhost:3000/upgrade-plan" 
               style="background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Reactivar Mi Suscripci\xf3n
            </a>
          </p>
          
          <p style="font-size: 14px; color: #6b7280;">
            Si no reactivas tu suscripci\xf3n, tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito el ${i}.
          </p>
        </div>
      </body>
      </html>
    `;return{htmlContent:n,textContent:`
Tu acceso premium termina pronto - OposI

Hola ${e},

Te recordamos que tu acceso al Plan ${t} terminar\xe1 el ${i} (en aproximadamente ${o} horas).

\xbfQuieres continuar con tu plan premium?
Reactivar tu suscripci\xf3n: http://localhost:3000/upgrade-plan

Si no reactivas tu suscripci\xf3n, tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito el ${i}.

Equipo de OposI
    `,subject:`⏰ Tu Plan ${t} termina en ${o} horas`}}static generateGenericEmail(e,t,r,a,i){return{htmlContent:`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${t} - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">${t}</h1>
          
          <p>Hola ${e},</p>
          
          <p>${r}</p>
          
          ${a&&i?`
          <p style="text-align: center; margin: 30px 0;">
            <a href="${i}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              ${a}
            </a>
          </p>
          `:""}
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          
          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta, no dudes en contactarnos.<br>
            Equipo de OposI
          </p>
        </div>
      </body>
      </html>
    `,textContent:`
${t} - OposI

Hola ${e},

${r}

${a&&i?`${a}: ${i}`:""}

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
    `,subject:t}}}var i=r(28319);class o{static async logEmailNotification(e,t="sent"){try{let r={recipient_email:e.to,subject:e.subject,type:e.type,sent_at:new Date().toISOString(),status:t};e.userId&&(r.user_id=e.userId),e.metadata&&(r.metadata=e.metadata);let{data:a,error:o}=await i.E.from("email_notifications").insert(r).select("id").single();if(o)throw o;return console.log("\uD83D\uDCDD Email notification logged:",{id:a.id,type:e.type,recipient:e.to,status:t,userId:e.userId||"N/A"}),a.id}catch(e){return console.error("Error logging email notification:",e),null}}static async updateEmailNotificationStatus(e,t,r){try{let a={status:t,updated_at:new Date().toISOString()};"failed"===t&&r&&(a.metadata={error_message:r,failed_at:new Date().toISOString()}),"sent"===t&&(a.delivered_at=new Date().toISOString());let{error:o}=await i.E.from("email_notifications").update(a).eq("id",e);if(o)throw o;console.log("\uD83D\uDCDD Email notification status updated:",{id:e,status:t,error:r||"N/A"})}catch(e){console.error("Error updating email notification status:",e)}}static async getUserNotifications(e,t=50,r){try{let a=i.E.from("email_notifications").select("*").eq("user_id",e).order("sent_at",{ascending:!1});r&&(a=a.eq("type",r)),t&&(a=a.limit(t));let{data:o,error:n}=await a;if(n)throw n;let s=i.E.from("email_notifications").select("*",{count:"exact",head:!0}).eq("user_id",e);r&&(s=s.eq("type",r));let{count:c}=await s;return{notifications:o||[],total:c||0}}catch(e){return console.error("Error obteniendo notificaciones del usuario:",e),{notifications:[],total:0}}}static async getFailedNotifications(e=24,t=10){try{let r=new Date(Date.now()-60*e*6e4).toISOString(),{data:a,error:o}=await i.E.from("email_notifications").select("*").eq("status","failed").gte("sent_at",r).limit(t);if(o)throw o;return a||[]}catch(e){return console.error("Error obteniendo notificaciones fallidas:",e),[]}}static async markAsRetried(e,t,r){try{await this.updateEmailNotificationStatus(e,t?"retried_successfully":"failed",t?"Successfully retried":r||"Retry failed")}catch(e){console.error("Error marcando notificaci\xf3n como reintentada:",e)}}static async cleanupOldNotifications(e=90){try{let t=new Date(Date.now()-24*e*36e5).toISOString();console.log(`🧹 Limpiando notificaciones anteriores a: ${t}`);let{data:r,error:a}=await i.E.from("email_notifications").delete().lt("sent_at",t).select("id");if(a)throw a;let o=r?.length||0;return console.log(`✅ Limpieza completada: ${o} notificaciones eliminadas`),{deleted:o}}catch(e){return console.error("Error en limpieza de notificaciones:",e),{deleted:0,error:e instanceof Error?e.message:"Unknown error"}}}}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var a,i,o;a=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(i))in a?Object.defineProperty(a,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class c{static async sendEmail(e,t=0){let r=1e3*Math.pow(2,t),a=null;try{if(console.log(`📧 Enviando email (intento ${t+1}/4):`,{to:e.to,subject:e.subject,type:e.type}),a=await o.logEmailNotification(e,"pending"),await new Promise(e=>setTimeout(e,100)),.1>Math.random()&&0===t)throw Error("Simulated email provider error");return a&&await o.updateEmailNotificationStatus(a,"sent"),console.log("✅ Email enviado exitosamente"),!0}catch(n){let i=n instanceof Error?n.message:"Unknown error";if(console.error(`❌ Error enviando email (intento ${t+1}):`,i),a&&await o.updateEmailNotificationStatus(a,"failed",i),t<3)return console.log(`🔄 Reintentando env\xedo en ${r}ms...`),await new Promise(e=>setTimeout(e,r)),this.sendEmail(e,t+1);return console.error(`💥 Fallo definitivo despu\xe9s de 4 intentos`),!1}}static async retryFailedNotifications(e=24,t=10){try{console.log(`🔄 Buscando notificaciones fallidas para reintentar (m\xe1ximo ${e} horas)...`);let r=await o.getFailedNotifications(e,t);if(0===r.length)return console.log("✅ No se encontraron notificaciones fallidas para reintentar"),{attempted:0,successful:0,failed:0,errors:[]};console.log(`📋 Encontradas ${r.length} notificaciones para reintentar`);let a=0,i=0,n=[];for(let e of r)try{let t={to:e.recipient_email,subject:e.subject,htmlContent:"",textContent:"",type:e.type,userId:e.user_id,metadata:e.metadata};t.metadata=s(s({},t.metadata),{},{retry_attempt:!0,original_notification_id:e.id,retry_at:new Date().toISOString()}),await this.sendEmail(t)?(a++,await o.markAsRetried(e.id,!0)):(i++,await o.markAsRetried(e.id,!1,"Retry failed"),n.push(`Failed to retry notification ${e.id}`))}catch(r){i++;let t=`Error retrying notification ${e.id}: ${r instanceof Error?r.message:"Unknown error"}`;console.error(t),n.push(t),await o.markAsRetried(e.id,!1,t)}return console.log(`🎯 Reintentos completados: ${a} exitosos, ${i} fallidos`),{attempted:r.length,successful:a,failed:i,errors:n}}catch(e){throw console.error("❌ Error en retryFailedNotifications:",e),e}}static async sendTestEmail(e,t){try{let t={to:e,subject:"Test Email - OposI",htmlContent:`
          <h1>Email de Prueba</h1>
          <p>Este es un email de prueba para verificar la configuraci\xf3n del sistema de notificaciones.</p>
          <p>Enviado el: ${new Date().toLocaleString("es-ES")}</p>
        `,textContent:`
          Email de Prueba
          
          Este es un email de prueba para verificar la configuraci\xf3n del sistema de notificaciones.
          Enviado el: ${new Date().toLocaleString("es-ES")}
        `,type:"other",metadata:{test_email:!0,sent_at:new Date().toISOString()}},r=await this.sendEmail(t);return{success:r,message:r?"Email de prueba enviado exitosamente":"Fallo al enviar email de prueba",details:{to:e,timestamp:new Date().toISOString()}}}catch(e){return console.error("Error enviando email de prueba:",e),{success:!1,message:"Error enviando email de prueba",details:{error:e instanceof Error?e.message:"Unknown error"}}}}static async validateEmailProvider(){try{return{isValid:!0,provider:"Simulado",message:"Proveedor de email configurado correctamente"}}catch(e){return{isValid:!1,provider:"Unknown",message:e instanceof Error?e.message:"Error validando proveedor"}}}}class l{static async getNotificationStats(e,t){try{let r=i.E.from("email_notifications").select("*");e&&(r=r.gte("sent_at",e)),t&&(r=r.lte("sent_at",t));let{data:a,error:o}=await r;if(o)throw o;let n=(a||[]).reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{}),s=(a||[]).reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),c=(a||[]).sort((e,t)=>new Date(t.sent_at).getTime()-new Date(e.sent_at).getTime()).slice(0,10);return{byType:n,byStatus:s,total:a?.length||0,recentNotifications:c}}catch(e){return console.error("Error obteniendo estad\xedsticas de notificaciones:",e),{byType:{},byStatus:{},total:0,recentNotifications:[]}}}static async getFailureStats(e,t){try{let r=i.E.from("email_notifications").select("*").eq("status","failed");e&&(r=r.gte("sent_at",e)),t&&(r=r.lte("sent_at",t));let{data:a,error:o}=await r;if(o)throw o;let n=i.E.from("email_notifications").select("*",{count:"exact",head:!0});e&&(n=n.gte("sent_at",e)),t&&(n=n.lte("sent_at",t));let{count:s}=await n,c=(a||[]).reduce((e,t)=>{let r=t.metadata?.error_message||"Unknown error",a=this.categorizeError(r);return e[a]=(e[a]||0)+1,e},{}),l=a?.length||0;return{totalFailures:l,failureRate:Math.round(100*(s&&s>0?l/s*100:0))/100,errorsByType:c,recentFailures:(a||[]).sort((e,t)=>new Date(t.sent_at).getTime()-new Date(e.sent_at).getTime()).slice(0,10).map(e=>({id:e.id,type:e.type,recipient:e.recipient_email,error:e.metadata?.error_message||"Unknown error",failedAt:e.metadata?.failed_at||e.sent_at}))}}catch(e){return console.error("Error obteniendo estad\xedsticas de fallos:",e),{totalFailures:0,failureRate:0,errorsByType:{},recentFailures:[]}}}static categorizeError(e){let t=e.toLowerCase();return t.includes("network")||t.includes("timeout")||t.includes("connection")?"Network Error":t.includes("invalid")||t.includes("malformed")||t.includes("email")?"Invalid Email":t.includes("rate limit")||t.includes("quota")||t.includes("limit")?"Rate Limit":t.includes("auth")||t.includes("key")||t.includes("permission")?"Authentication Error":t.includes("bounce")||t.includes("reject")?"Email Bounced":"Other Error"}static async getPerformanceMetrics(e,t){try{let r=i.E.from("email_notifications").select("*");e&&(r=r.gte("sent_at",e)),t&&(r=r.lte("sent_at",t));let{data:a,error:o}=await r;if(o)throw o;let n=a?.length||0,s=a?.filter(e=>"sent"===e.status).length||0,c=(a||[]).reduce((e,t)=>{let r=new Date(t.sent_at).getHours();return e[r]=(e[r]||0)+1,e},{}),l=(a||[]).reduce((e,t)=>{let r=t.sent_at.split("T")[0];return e[r]=(e[r]||0)+1,e},{});return{totalSent:n,successRate:Math.round(100*(n>0?s/n*100:0))/100,avgResponseTime:0,peakHours:c,dailyVolume:l}}catch(e){return console.error("Error obteniendo m\xe9tricas de rendimiento:",e),{totalSent:0,successRate:0,avgResponseTime:0,peakHours:{},dailyVolume:{}}}}static async getTopUsersByVolume(e=10,t,r){try{let a=i.E.from("email_notifications").select("user_id, recipient_email, sent_at").not("user_id","is",null);t&&(a=a.gte("sent_at",t)),r&&(a=a.lte("sent_at",r));let{data:o,error:n}=await a;if(n)throw n;let s=(o||[]).reduce((e,t)=>{let r=t.user_id;return e[r]||(e[r]={userId:r,email:t.recipient_email,count:0,lastNotification:t.sent_at}),e[r].count++,new Date(t.sent_at)>new Date(e[r].lastNotification)&&(e[r].lastNotification=t.sent_at),e},{});return Object.values(s).sort((e,t)=>t.count-e.count).slice(0,e)}catch(e){return console.error("Error obteniendo top usuarios:",e),[]}}}class u{static async sendSubscriptionCancelledNotification(e,t,r,i,o){try{let n=a.generateSubscriptionCancelledEmail(t,r,i),s={to:e,subject:n.subject,htmlContent:n.htmlContent,textContent:n.textContent,type:"subscription_cancelled",userId:o,metadata:{planName:r,gracePeriodEnd:i,userName:t,daysRemaining:Math.ceil((new Date(i).getTime()-new Date().getTime())/864e5)}};return await c.sendEmail(s)}catch(e){return console.error("Error enviando notificaci\xf3n de cancelaci\xf3n:",e),!1}}static async sendGracePeriodEndingNotification(e,t,r,i,o){try{let n=a.generateGracePeriodEndingEmail(t,r,i),s={to:e,subject:n.subject,htmlContent:n.htmlContent,textContent:n.textContent,type:"grace_period_ending",userId:o,metadata:{planName:r,gracePeriodEnd:i,userName:t,hoursRemaining:Math.ceil((new Date(i).getTime()-new Date().getTime())/36e5)}};return await c.sendEmail(s)}catch(e){return console.error("Error enviando recordatorio de per\xedodo de gracia:",e),!1}}static async sendGenericNotification(e,t,r,i,o="other",n,s,l){try{let u=a.generateGenericEmail(t,r,i,s,l),d={to:e,subject:u.subject,htmlContent:u.htmlContent,textContent:u.textContent,type:o,userId:n,metadata:{userName:t,title:r,message:i,ctaText:s,ctaUrl:l}};return await c.sendEmail(d)}catch(e){return console.error("Error enviando notificaci\xf3n gen\xe9rica:",e),!1}}static async getUserNotifications(e,t=50,r){return await o.getUserNotifications(e,t,r)}static async getNotificationStats(e,t){return await l.getNotificationStats(e,t)}static async getFailureStats(e,t){return await l.getFailureStats(e,t)}static async retryFailedNotifications(e=24,t=10){return await c.retryFailedNotifications(e,t)}static async getPerformanceMetrics(e,t){return await l.getPerformanceMetrics(e,t)}static async getTopUsersByVolume(e=10,t,r){return await l.getTopUsersByVolume(e,t,r)}static async sendTestEmail(e,t){return await c.sendTestEmail(e,t)}static async validateEmailProvider(){return await c.validateEmailProvider()}static async cleanupOldNotifications(e=90){return await o.cleanupOldNotifications(e)}static async getSystemSummary(){try{let[e,t,r,a]=await Promise.all([this.validateEmailProvider(),this.getNotificationStats(new Date(Date.now()-6048e5).toISOString(),new Date().toISOString()),this.getFailureStats(new Date(Date.now()-6048e5).toISOString(),new Date().toISOString()),this.getPerformanceMetrics(new Date(Date.now()-6048e5).toISOString(),new Date().toISOString())]);return{providerStatus:e,recentStats:t,failureStats:r,performanceMetrics:a}}catch(e){throw console.error("Error obteniendo resumen del sistema:",e),e}}}},78335:()=>{},96487:()=>{}};
"use strict";exports.id=9844,exports.ids=[9844],exports.modules={19844:(e,r,i)=>{i.d(r,{guardarPlanEstudiosServidor:()=>s}),i(29389);async function s(e,r,s,o){try{let{createServerSupabaseClient:t}=await Promise.resolve().then(i.bind(i,11345)),n=await t(),{data:a,error:d}=await n.from("planes_estudios").insert([{user_id:s.id,temario_id:e,titulo:o||"Plan de Estudios",plan_data:r,activo:!0,version:1}]).select().single();if(d)return console.error("Error al guardar plan de estudios (servidor):",d),null;return console.log("✅ Plan de estudios guardado exitosamente (servidor):",a.id),a.id}catch(e){return console.error("Error al guardar plan de estudios (servidor):",e),null}}},29389:(e,r,i)=>{i.d(r,{U:()=>o});var s=i(83760);function o(){return(0,s.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}o()}};
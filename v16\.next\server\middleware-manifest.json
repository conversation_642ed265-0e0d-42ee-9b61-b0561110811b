{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "6BJKT_u_M-uVE4xMb_3o1", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "204RUUtRLXHBYXmVyktehpqhhmihkVwQj1t8QzAwKtg=", "__NEXT_PREVIEW_MODE_ID": "1fca39fd25c9cc2fa048919b097a1b85", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9aba26f597d8ff1abd0e95d283eb66630d80b0551005d890f80404be915a96f7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b34ffa136703b3fee799ce1bb64c7190667ed46884035cfccaf4ac45991d35d7"}}}, "functions": {}, "sortedMiddleware": ["/"]}
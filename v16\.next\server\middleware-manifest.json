{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "z6x8yp7Nhe4mvhQGcViY2", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "204RUUtRLXHBYXmVyktehpqhhmihkVwQj1t8QzAwKtg=", "__NEXT_PREVIEW_MODE_ID": "f0cf362b214c22321db2691f55e464bd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "56ed7ca65bc861bb6c0a2ee782bf8347c981aea7e650aed4cf564015b9121c6e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "739d20fb874f0796d4093c25edde9dc0186847da72af7ce42df3344290148fc3"}}}, "functions": {}, "sortedMiddleware": ["/"]}
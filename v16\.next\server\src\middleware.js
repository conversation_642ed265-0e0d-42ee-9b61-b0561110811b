(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{82:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},86:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let n=i(r(312));t.PostgrestClient=n.default;let s=i(r(556));t.PostgrestQueryBuilder=s.default;let a=i(r(950));t.PostgrestFilterBuilder=a.default;let o=i(r(852));t.PostgrestTransformBuilder=o.default;let l=i(r(488));t.PostgrestBuilder=l.default;let c=i(r(351));t.PostgrestError=c.default,t.default={PostgrestClient:n.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:c.default}},93:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function i(){}function n(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,i,s,a){if("function"!=typeof i)throw TypeError("The listener must be a function");var o=new n(i,s||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new i:delete e._events[t]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(r=!1)),o.prototype.eventNames=function(){var e,i,n=[];if(0===this._eventsCount)return n;for(i in e=this._events)t.call(e,i)&&n.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(e)):n},o.prototype.listeners=function(e){var t=r?r+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var n=0,s=i.length,a=Array(s);n<s;n++)a[n]=i[n].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,i=this._events[t];return i?i.fn?1:i.length:0},o.prototype.emit=function(e,t,i,n,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,c,u=this._events[o],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,i),!0;case 4:return u.fn.call(u.context,t,i,n),!0;case 5:return u.fn.call(u.context,t,i,n,s),!0;case 6:return u.fn.call(u.context,t,i,n,s,a),!0}for(c=1,l=Array(h-1);c<h;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var d,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,i);break;case 4:u[c].fn.call(u[c].context,t,i,n);break;default:if(!l)for(d=1,l=Array(h-1);d<h;d++)l[d-1]=arguments[d];u[c].fn.apply(u[c].context,l)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,i,n){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||n&&!o.once||i&&o.context!==i||a(this,s);else{for(var l=0,c=[],u=o.length;l<u;l++)(o[l].fn!==t||n&&!o[l].once||i&&o[l].context!==i)&&c.push(o[l]);c.length?this._events[s]=1===c.length?c[0]:c:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let i=0,n=e.length;for(;n>0;){let s=n/2|0,a=i+s;0>=r(e[a],t)?(i=++a,n-=s+1):n=s}return i}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let i=r(574);class n{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let n=i.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(n,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=n},816:(e,t,r)=>{let i=r(213);class n extends Error{constructor(e){super(e),this.name="TimeoutError"}}let s=(e,t,r)=>new Promise((s,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void s(e);let o=setTimeout(()=>{if("function"==typeof r){try{s(r())}catch(e){a(e)}return}let i="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new n(i);"function"==typeof e.cancel&&e.cancel(),a(o)},t);i(e.then(s,a),()=>{clearTimeout(o)})});e.exports=s,e.exports.default=s,e.exports.TimeoutError=n}},r={};function i(e){var n=r[e];if(void 0!==n)return n.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,i),a=!1}finally{a&&delete r[e]}return s.exports}i.ab="//";var n={};(()=>{Object.defineProperty(n,"__esModule",{value:!0});let e=i(993),t=i(816),r=i(821),s=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,i,n,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(i=null==(t=e.intervalCap)?void 0:t.toString())?i:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(n=e.interval)?void 0:n.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((i,n)=>{let s=async()=>{this._pendingCount++,this._intervalCount++;try{let s=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&n(a)});i(await s)}catch(e){n(e)}this._next()};this._queue.enqueue(s,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}n.default=o})(),e.exports=n})()},103:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},s=t.split(i),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),h=l.substr(++c,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==n[u]&&(n[u]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return n},t.serialize=function(e,t,i){var s=i||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var c=s.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},112:(e,t)=>{"use strict";var r=Array.isArray,i=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),s=(Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy")),a=Symbol.iterator;Object.prototype.hasOwnProperty,Object.assign;var o=/\/+/g;function l(e,t){var r,i;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,i={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return i[e]})):t.toString(36)}function c(){}},312:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(556)),s=i(r(950)),a=r(457);class o{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new n.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:n}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);r||i?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let c=Object.assign({},this.headers);return n&&(c.Prefer=`count=${n}`),new s.default({method:a,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},351:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},356:e=>{"use strict";e.exports=require("node:buffer")},457:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(82);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},465:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let i=r(223),n=r(172),s=r(930),a="context",o=new i.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,n.registerGlobal)(a,e,s.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...i){return this._getContextManager().with(e,t,r,...i)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,n.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,n.unregisterGlobal)(a,s.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let i=r(56),n=r(912),s=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:s.DiagLogLevel.INFO})=>{var i,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=e.stack)?i:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,a.getGlobal)("diag"),u=(0,n.createLogLevelDiagLogger)(null!=(o=r.logLevel)?o:s.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new i.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let i=r(660),n=r(172),s=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,n.registerGlobal)(a,e,s.DiagAPI.instance())}getMeterProvider(){return(0,n.getGlobal)(a)||i.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,n.unregisterGlobal)(a,s.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let i=r(172),n=r(874),s=r(194),a=r(277),o=r(369),l=r(930),c="propagation",u=new n.NoopTextMapPropagator;class h{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalPropagator(e){return(0,i.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=s.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=s.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,i.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,i.getGlobal)(c)||u}}t.PropagationAPI=h},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let i=r(172),n=r(846),s=r(139),a=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new n.ProxyTracerProvider,this.wrapSpanContext=s.wrapSpanContext,this.isSpanContextValid=s.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,i.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,i.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,i.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new n.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let i=r(491),n=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function s(e){return e.getValue(n)||void 0}t.getBaggage=s,t.getActiveBaggage=function(){return s(i.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(n,t)},t.deleteBaggage=function(e){return e.deleteValue(n)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let i=new r(this._entries);return i._entries.set(e,t),i}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let i=r(930),n=r(993),s=r(830),a=i.DiagAPI.instance();t.createBaggage=function(e={}){return new n.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:s.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let i=r(780);class n{active(){return i.ROOT_CONTEXT}with(e,t,r,...i){return t.call(r,...i)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=n},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,i)=>{let n=new r(t._currentContext);return n._currentContext.set(e,i),n},t.deleteValue=e=>{let i=new r(t._currentContext);return i._currentContext.delete(e),i}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let i=r(172);class n{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return s("debug",this._namespace,e)}error(...e){return s("error",this._namespace,e)}info(...e){return s("info",this._namespace,e)}warn(...e){return s("warn",this._namespace,e)}verbose(...e){return s("verbose",this._namespace,e)}}function s(e,t,r){let n=(0,i.getGlobal)("diag");if(n)return r.unshift(t),n[e](...r)}t.DiagComponentLogger=n},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class i{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=i},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let i=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,i){let n=t[r];return"function"==typeof n&&e>=i?n.bind(t):function(){}}return e<i.DiagLogLevel.NONE?e=i.DiagLogLevel.NONE:e>i.DiagLogLevel.ALL&&(e=i.DiagLogLevel.ALL),t=t||{},{error:r("error",i.DiagLogLevel.ERROR),warn:r("warn",i.DiagLogLevel.WARN),info:r("info",i.DiagLogLevel.INFO),debug:r("debug",i.DiagLogLevel.DEBUG),verbose:r("verbose",i.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let i=r(200),n=r(521),s=r(130),a=n.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=i._globalThis;t.registerGlobal=function(e,t,r,i=!1){var s;let a=l[o]=null!=(s=l[o])?s:{version:n.VERSION};if(!i&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==n.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${n.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${n.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let i=null==(t=l[o])?void 0:t.version;if(i&&(0,s.isCompatible)(i))return null==(r=l[o])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${n.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let i=r(521),n=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function s(e){let t=new Set([e]),r=new Set,i=e.match(n);if(!i)return()=>!1;let s={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=s.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let i=e.match(n);if(!i)return a(e);let o={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=o.prerelease||s.major!==o.major)return a(e);if(0===s.major)return s.minor===o.minor&&s.patch<=o.patch?(t.add(e),!0):a(e);return s.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=s,t.isCompatible=s(i.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class i{}t.NoopMetric=i;class n extends i{add(e,t){}}t.NoopCounterMetric=n;class s extends i{add(e,t){}}t.NoopUpDownCounterMetric=s;class a extends i{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new n,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new s,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let i=r(102);class n{getMeter(e,t,r){return i.NOOP_METER}}t.NoopMeterProvider=n,t.NOOP_METER_PROVIDER=new n},200:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let i=r(476);class n{constructor(e=i.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=n},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let i=r(491),n=r(607),s=r(403),a=r(139),o=i.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){var i;if(null==t?void 0:t.root)return new s.NonRecordingSpan;let l=r&&(0,n.getSpanContext)(r);return"object"==typeof(i=l)&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&(0,a.isSpanContextValid)(l)?new s.NonRecordingSpan(l):new s.NonRecordingSpan}startActiveSpan(e,t,r,i){let s,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(s=t,l=r):(s=t,a=r,l=i);let c=null!=a?a:o.active(),u=this.startSpan(e,s,c),h=(0,n.setSpan)(c,u);return o.with(h,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let i=r(614);class n{getTracer(e,t,r){return new i.NoopTracer}}t.NoopTracerProvider=n},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let i=new(r(614)).NoopTracer;class n{constructor(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,i){let n=this._getTracer();return Reflect.apply(n.startActiveSpan,n,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):i}}t.ProxyTracer=n},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let i=r(125),n=new(r(124)).NoopTracerProvider;class s{getTracer(e,t,r){var n;return null!=(n=this.getDelegateTracer(e,t,r))?n:new i.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:n}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var i;return null==(i=this._delegate)?void 0:i.getTracer(e,t,r)}}t.ProxyTracerProvider=s},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let i=r(780),n=r(403),s=r(491),a=(0,i.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(s.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new n.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let i=r(564);class n{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),n=r.indexOf("=");if(-1!==n){let s=r.slice(0,n),a=r.slice(n+1,t.length);(0,i.validateKey)(s)&&(0,i.validateValue)(a)&&e.set(s,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new n;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=n},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",i=`[a-z]${r}{0,255}`,n=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,s=RegExp(`^(?:${i}|${n})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return s.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let i=r(325);t.createTraceState=function(e){return new i.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let i=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:i.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let i=r(476),n=r(403),s=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return s.test(e)&&e!==i.INVALID_TRACEID}function l(e){return a.test(e)&&e!==i.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new n.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function n(e){var r=i[e];if(void 0!==r)return r.exports;var s=i[e]={exports:{}},a=!0;try{t[e].call(s.exports,s,s.exports,n),a=!1}finally{a&&delete i[e]}return s.exports}n.ab="//";var s={};(()=>{Object.defineProperty(s,"__esModule",{value:!0}),s.trace=s.propagation=s.metrics=s.diag=s.context=s.INVALID_SPAN_CONTEXT=s.INVALID_TRACEID=s.INVALID_SPANID=s.isValidSpanId=s.isValidTraceId=s.isSpanContextValid=s.createTraceState=s.TraceFlags=s.SpanStatusCode=s.SpanKind=s.SamplingDecision=s.ProxyTracerProvider=s.ProxyTracer=s.defaultTextMapSetter=s.defaultTextMapGetter=s.ValueType=s.createNoopMeter=s.DiagLogLevel=s.DiagConsoleLogger=s.ROOT_CONTEXT=s.createContextKey=s.baggageEntryMetadataFromString=void 0;var e=n(369);Object.defineProperty(s,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=n(780);Object.defineProperty(s,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(s,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=n(972);Object.defineProperty(s,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=n(957);Object.defineProperty(s,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var a=n(102);Object.defineProperty(s,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=n(901);Object.defineProperty(s,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=n(194);Object.defineProperty(s,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(s,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=n(125);Object.defineProperty(s,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=n(846);Object.defineProperty(s,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var h=n(996);Object.defineProperty(s,"SamplingDecision",{enumerable:!0,get:function(){return h.SamplingDecision}});var d=n(357);Object.defineProperty(s,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var p=n(847);Object.defineProperty(s,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=n(475);Object.defineProperty(s,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=n(98);Object.defineProperty(s,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=n(139);Object.defineProperty(s,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(s,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(s,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var b=n(476);Object.defineProperty(s,"INVALID_SPANID",{enumerable:!0,get:function(){return b.INVALID_SPANID}}),Object.defineProperty(s,"INVALID_TRACEID",{enumerable:!0,get:function(){return b.INVALID_TRACEID}}),Object.defineProperty(s,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return b.INVALID_SPAN_CONTEXT}});let v=n(67);Object.defineProperty(s,"context",{enumerable:!0,get:function(){return v.context}});let y=n(506);Object.defineProperty(s,"diag",{enumerable:!0,get:function(){return y.diag}});let w=n(886);Object.defineProperty(s,"metrics",{enumerable:!0,get:function(){return w.metrics}});let _=n(939);Object.defineProperty(s,"propagation",{enumerable:!0,get:function(){return _.propagation}});let O=n(845);Object.defineProperty(s,"trace",{enumerable:!0,get:function(){return O.trace}}),s.default={context:v.context,diag:y.diag,metrics:w.metrics,propagation:_.propagation,trace:O.trace}})(),e.exports=s})()},488:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(504)),s=i(r(351));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=n.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let n=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),s=null==(r=e.headers.get("content-range"))?void 0:r.split("/");i&&s&&s.length>1&&(o=parseInt(s[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(n={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(a=[],n=null,l=200,c="OK")}catch(r){404===e.status&&""===t?(l=204,c="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null==(i=null==n?void 0:n.details)?void 0:i.includes("0 rows"))&&(n=null,l=200,c="OK"),n&&this.shouldThrowOnError)throw new s.default(n)}return{error:n,data:a,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(r=null==e?void 0:e.stack)?r:""}`,hint:"",code:`${null!=(i=null==e?void 0:e.code)?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},504:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>s,fetch:()=>n});var i=function(){if("undefined"!=typeof self)return self;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let n=i.fetch,s=i.fetch.bind(i),a=i.Headers,o=i.Request,l=i.Response},521:e=>{"use strict";e.exports=require("node:async_hooks")},528:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},556:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(950));class s{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",s),r&&(this.headers.Prefer=`count=${r}`),new n.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:s=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),s||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new n.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new n.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=s},590:(e,t,r)=>{"use strict";e.exports=r(112)},611:e=>{"use strict";function t(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function r(e){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?t(Object(i),!0).forEach(function(t){var r,n,s;r=e,n=t,s=i[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in r?Object.defineProperty(r,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):r[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):t(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}var i=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o={};function l(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function c(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function u(e){if(!e)return;let[[t,i],...n]=c(e),{domain:s,expires:a,httponly:o,maxage:l,path:u,samesite:p,secure:f,partitioned:g,priority:m}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var b,v,y=r(r(r(r(r(r(r({name:t,value:decodeURIComponent(i),domain:s},a&&{expires:new Date(a)}),o&&{httpOnly:!0}),"string"==typeof l&&{maxAge:Number(l)}),{},{path:u},p&&{sameSite:h.includes(b=(b=p).toLowerCase())?b:void 0}),f&&{secure:!0}),m&&{priority:d.includes(v=(v=m).toLowerCase())?v:void 0}),g&&{partitioned:!0});let e={};for(let t in y)y[t]&&(e[t]=y[t]);return e}}((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(o,{RequestCookies:()=>p,ResponseCookies:()=>f,parseCookie:()=>c,parseSetCookie:()=>u,stringifyCookie:()=>l}),e.exports=((e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of s(t))a.call(e,l)||l===r||i(e,l,{get:()=>t[l],enumerable:!(o=n(t,l))||o.enumerable});return e})(i({},"__esModule",{value:!0}),o);var h=["strict","lax","none"],d=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of c(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>l(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>l(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let n=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,i,n,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=n,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(n)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,i,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}(r({name:t,value:i},n))),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=l(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set(r(r({},i),{},{name:t,value:"",expires:new Date(0)}))}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(l).join("; ")}}},640:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let i=new(r(521)).AsyncLocalStorage;function n(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let i=t.url(e);return{url:i,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=n(e,t);return s?i.run(s,r):r()}function a(e,t){let r=i.getStore();return r||(e&&t?n(e,t):void 0)}},653:(e,t,r)=>{"use strict";var i=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let n=r(640),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:n,headers:s,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:n,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:p}}}async function o(e,t){let r=(0,n.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let h=await u.json(),{api:d}=h;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:g}=h.response;return new Response(g?i.from(g,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var i;return(null==r||null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},747:(e,t)=>{"use strict";t.qg=function(e,t){let r=new o,i=e.length;if(i<2)return r;let n=t?.decode||u,s=0;do{let t=e.indexOf("=",s);if(-1===t)break;let a=e.indexOf(";",s),o=-1===a?i:a;if(t>o){s=e.lastIndexOf(";",t-1)+1;continue}let u=l(e,s,t),h=c(e,t,u),d=e.slice(u,h);if(void 0===r[d]){let i=l(e,t+1,o),s=c(e,o,i),a=n(e.slice(i,s));r[d]=a}s=o+1}while(s<i);return r},t.lK=function(e,t,o){let l=o?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let c=l(t);if(!i.test(c))throw TypeError(`argument val is invalid: ${t}`);let u=e+"="+c;if(!o)return u;if(void 0!==o.maxAge){if(!Number.isInteger(o.maxAge))throw TypeError(`option maxAge is invalid: ${o.maxAge}`);u+="; Max-Age="+o.maxAge}if(o.domain){if(!n.test(o.domain))throw TypeError(`option domain is invalid: ${o.domain}`);u+="; Domain="+o.domain}if(o.path){if(!s.test(o.path))throw TypeError(`option path is invalid: ${o.path}`);u+="; Path="+o.path}if(o.expires){var h;if(h=o.expires,"[object Date]"!==a.call(h)||!Number.isFinite(o.expires.valueOf()))throw TypeError(`option expires is invalid: ${o.expires}`);u+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(u+="; HttpOnly"),o.secure&&(u+="; Secure"),o.partitioned&&(u+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${o.priority}`)}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${o.sameSite}`)}return u};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},768:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let i=r(640),n=r(653);function s(){return(0,n.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,i.withRequest)(t,n.reader,()=>e(t,r))}},852:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(488));class s extends n.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:n=i}={}){let s=n?`${n}.order`:"order",a=this.url.searchParams.get(s);return this.url.searchParams.set(s,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let n=void 0===i?"offset":`${i}.offset`,s=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(s,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:n=!1,format:s="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,n?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${s}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=s},881:(e,t,r)=>{var i;(()=>{var n={226:function(n,s){!function(a,o){"use strict";var l="function",c="undefined",u="object",h="string",d="major",p="model",f="name",g="type",m="vendor",b="version",v="architecture",y="console",w="mobile",_="tablet",O="smarttv",S="wearable",E="embedded",k="Amazon",P="Apple",T="ASUS",x="BlackBerry",R="Browser",C="Chrome",j="Firefox",A="Google",I="Huawei",N="Microsoft",D="Motorola",L="Opera",$="Samsung",M="Sharp",U="Sony",q="Xiaomi",B="Zebra",z="Facebook",V="Chromium OS",F="Mac OS",H=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},G=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},W=function(e,t){return typeof e===h&&-1!==K(t).indexOf(K(e))},K=function(e){return e.toLowerCase()},J=function(e,t){if(typeof e===h)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,i,n,s,a,c,h=0;h<t.length&&!a;){var d=t[h],p=t[h+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(n=0;n<p.length;n++)c=a[++i],typeof(s=p[n])===u&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):void 0:this[s[0]]=c?s[1].call(this,c,s[2]):void 0:4===s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):o):this[s]=c||o;h+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(W(t[r][i],e))return"?"===r?o:r}else if(W(t[r],e))return"?"===r?o:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,b],[/opios[\/ ]+([\w\.]+)/i],[b,[f,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[b,[f,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,b],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[f,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[b,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[b,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+R],b],[/\bfocus\/([\w\.]+)/i],[b,[f,j+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[f,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[f,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[f,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[b,[f,j]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+R],b],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,b],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,z],b],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[f,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,C+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[f,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[b,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[f,j+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,b],[/(cobalt)\/([\w\.]+)/i],[f,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,K]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",K]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,K]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,$],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,$],[g,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,P],[g,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,P],[g,_]],[/(macintosh);/i],[p,[m,P]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,M],[g,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,I],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,I],[g,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,q],[g,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,q],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,D],[g,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,D],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,w]],[/(pixel c)\b/i],[p,[m,A],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,A],[g,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,U],[g,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,U],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,k],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,k],[g,w]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,x],[g,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,T],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,T],[g,w]],[/(nexus 9)/i],[p,[m,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,_]],[/(surface duo)/i],[p,[m,N],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,w]],[/(u304aa)/i],[p,[m,"AT&T"],[g,w]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,w]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,_]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,w]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,w]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,_]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,w]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[m,p,[g,w]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,N],[g,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,B],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,B],[g,w]],[/smart-tv.+(samsung)/i],[m,[g,O]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,$],[g,O]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,O]],[/(apple) ?tv/i],[m,[p,P+" TV"],[g,O]],[/crkey/i],[[p,C+"cast"],[m,A],[g,O]],[/droid.+aft(\w)( bui|\))/i],[p,[m,k],[g,O]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,M],[g,O]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,U],[g,O]],[/(mitv-\w{5}) bui/i],[p,[m,q],[g,O]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,O]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,J],[p,J],[g,O]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,O]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,y]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,y]],[/(playstation [345portablevi]+)/i],[p,[m,U],[g,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,N],[g,y]],[/((pebble))app/i],[m,p,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,P],[g,S]],[/droid.+; (glass) \d/i],[p,[m,A],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,B],[g,S]],[/(quest( 2| pro)?)/i],[p,[m,z],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,E]],[/(aeobc)\b/i],[p,[m,k],[g,E]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,w]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,b],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[b,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[b,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,F],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,b],[/\(bb(10);/i],[b,[f,x]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[f,j+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[f,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,V],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,b],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,b]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),n=r&&r.userAgentData?r.userAgentData:o,s=t?H(Z,t):Z,y=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[f]=o,t[b]=o,X.call(t,i,s.browser),t[d]=typeof(e=t[b])===h?e.replace(/[^\d\.]/g,"").split(".")[0]:o,y&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[v]=o,X.call(e,i,s.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,X.call(e,i,s.device),y&&!e[g]&&n&&n.mobile&&(e[g]=w),y&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=o,e[b]=o,X.call(e,i,s.engine),e},this.getOS=function(){var e={};return e[f]=o,e[b]=o,X.call(e,i,s.os),y&&!e[f]&&n&&"Unknown"!=n.platform&&(e[f]=n.platform.replace(/chrome os/i,V).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===h&&e.length>350?J(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=G([f,b,d]),ee.CPU=G([v]),ee.DEVICE=G([p,m,g,y,w,O,_,S,E]),ee.ENGINE=ee.OS=G([f,b]),typeof s!==c?(n.exports&&(s=n.exports=ee),s.UAParser=ee):r.amdO?void 0===(i=(function(){return ee}).call(t,r,t,e))||(e.exports=i):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}(this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},i=!0;try{n[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete s[e]}return r.exports}a.ab="//",e.exports=a(226)})()},925:(e,t,r)=>{"use strict";let i;r.r(t),r.d(t,{default:()=>na});var n,s,a,o,l,c,u,h,d,p,f,g={};async function m(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(g),r.d(g,{config:()=>nr,middleware:()=>i4});let b=null;async function v(){if("phase-production-build"===process.env.NEXT_PHASE)return;b||(b=m());let e=await b;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function y(...e){let t=await m();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let w=null;function _(){return w||(w=v()),w}function O(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(O(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(O(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,i,n){if("function"==typeof n[0])return n[0](t);throw Object.defineProperty(Error(O(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),_();class S extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class E extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class k extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}function P(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?P(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let x="_N_T_",R={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function C(e){var t,r,i,n,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=n,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function j(e){let t={},r=[];if(e)for(let[i,n]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...C(n)),t[i]=1===r.length?r[0]:r):t[i]=n;return t}function A(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}T(T({},R),{},{GROUP:{builtinReact:[R.reactServerComponents,R.actionBrowser],serverOnly:[R.reactServerComponents,R.actionBrowser,R.instrument,R.middleware],neutralTarget:[R.apiNode,R.apiEdge],clientOnly:[R.serverSideRendering,R.appPagesBrowser],bundled:[R.reactServerComponents,R.actionBrowser,R.serverSideRendering,R.appPagesBrowser,R.shared,R.instrument,R.middleware],appPages:[R.reactServerComponents,R.serverSideRendering,R.appPagesBrowser,R.actionBrowser]}});let I=Symbol("response"),N=Symbol("passThrough"),D=Symbol("waitUntil");class L{constructor(e,t){this[N]=!1,this[D]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[I]||(this[I]=Promise.resolve(e))}passThroughOnException(){this[N]=!0}waitUntil(e){if("external"===this[D].kind)return(0,this[D].function)(e);this[D].promises.push(e)}}class $ extends L{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function M(e){return e.replace(/\/$/,"")||"/"}function U(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function q(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:n}=U(e);return""+t+r+i+n}function B(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:n}=U(e);return""+r+t+i+n}function z(e,t){if("string"!=typeof e)return!1;let{pathname:r}=U(e);return r===t||r.startsWith(t+"/")}let V=new WeakMap;function F(e,t){let r;if(!t)return{pathname:e};let i=V.get(t);i||(i=t.map(e=>e.toLowerCase()),V.set(t,i));let n=e.split("/",2);if(!n[1])return{pathname:e};let s=n[1].toLowerCase(),a=i.indexOf(s);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let H=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function G(e,t){return new URL(String(e).replace(H,"localhost"),t&&String(t).replace(H,"localhost"))}let W=Symbol("NextURLInternal");class K{constructor(e,t,r){let i,n;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,n=r||{}):n=r||t||{},this[W]={url:G(e,i??n.base),options:n,basePath:""},this.analyze()}analyze(){var e,t,r,i,n;let s=function(e,t){var r,i;let{basePath:n,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};n&&z(o.pathname,n)&&(o.pathname=function(e,t){if(!z(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,n),o.basePath=n);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):F(o.pathname,s.locales);o.locale=e.detectedLocale,o.pathname=null!=(i=e.pathname)?i:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):F(l,s.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[W].url.pathname,{nextConfig:this[W].options.nextConfig,parseData:!0,i18nProvider:this[W].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[W].url,this[W].options.headers);this[W].domainLocale=this[W].options.i18nProvider?this[W].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var i,n;if(t===(null==(i=s.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(n=s.locales)?void 0:n.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[W].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[W].domainLocale)?void 0:r.defaultLocale)||(null==(n=this[W].options.nextConfig)||null==(i=n.i18n)?void 0:i.defaultLocale);this[W].url.pathname=s.pathname,this[W].defaultLocale=o,this[W].basePath=s.basePath??"",this[W].buildId=s.buildId,this[W].locale=s.locale??o,this[W].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let n=e.toLowerCase();return!i&&(z(n,"/api")||z(n,"/"+t.toLowerCase()))?e:q(e,"/"+t)}((e={basePath:this[W].basePath,buildId:this[W].buildId,defaultLocale:this[W].options.forceLocale?void 0:this[W].defaultLocale,locale:this[W].locale,pathname:this[W].url.pathname,trailingSlash:this[W].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=M(t)),e.buildId&&(t=B(q(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=q(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:B(t,"/"):M(t)}formatSearch(){return this[W].url.search}get buildId(){return this[W].buildId}set buildId(e){this[W].buildId=e}get locale(){return this[W].locale??""}set locale(e){var t,r;if(!this[W].locale||!(null==(r=this[W].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[W].locale=e}get defaultLocale(){return this[W].defaultLocale}get domainLocale(){return this[W].domainLocale}get searchParams(){return this[W].url.searchParams}get host(){return this[W].url.host}set host(e){this[W].url.host=e}get hostname(){return this[W].url.hostname}set hostname(e){this[W].url.hostname=e}get port(){return this[W].url.port}set port(e){this[W].url.port=e}get protocol(){return this[W].url.protocol}set protocol(e){this[W].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[W].url=G(e),this.analyze()}get origin(){return this[W].url.origin}get pathname(){return this[W].url.pathname}set pathname(e){this[W].url.pathname=e}get hash(){return this[W].url.hash}set hash(e){this[W].url.hash=e}get search(){return this[W].url.search}set search(e){this[W].url.search=e}get password(){return this[W].url.password}set password(e){this[W].url.password=e}get username(){return this[W].url.username}set username(e){this[W].url.username=e}get basePath(){return this[W].basePath}set basePath(e){this[W].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new K(String(this),this[W].options)}}var J=r(611);let X=Symbol("internal request");class Y extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);A(r),e instanceof Request?super(e,t):super(r,t);let i=new K(r,{headers:j(this.headers),nextConfig:t.nextConfig});this[X]={cookies:new J.RequestCookies(this.headers),nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[X].cookies}get nextUrl(){return this[X].nextUrl}get page(){throw new E}get ua(){throw new k}get url(){return this[X].url}}class Q{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}function Z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function ee(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let et=Symbol("internal response"),er=new Set([301,302,303,307,308]);function ei(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[i,n]of e.request.headers)t.set("x-middleware-request-"+i,n),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class en extends Response{constructor(e,t={}){super(e,t);let r=this.headers,i=new Proxy(new J.ResponseCookies(r),{get(e,i,n){switch(i){case"delete":case"set":return(...n)=>{let s=Reflect.apply(e[i],e,n),a=new Headers(r);return s instanceof J.ResponseCookies&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,J.stringifyCookie)(e)).join(",")),ei(t,a),s};default:return Q.get(e,i,n)}}});this[et]={cookies:i,url:t.url?new K(t.url,{headers:j(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[et].cookies}static json(e,t){let r=Response.json(e,t);return new en(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!er.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let i="object"==typeof t?t:{},n=new Headers(null==i?void 0:i.headers);return n.set("Location",A(e)),new en(null,ee(ee({},i),{},{headers:n,status:r}))}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",A(e)),ei(t,r),new en(null,ee(ee({},t),{},{headers:r}))}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),ei(e,t),new en(null,ee(ee({},e),{},{headers:t}))}}function es(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),n=i.origin===r.origin;return{url:n?i.toString().slice(r.origin.length):i.toString(),isRelative:n}}let ea="Next-Router-Prefetch",eo=["RSC","Next-Router-State-Tree",ea,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],el="_rsc";class ec extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ec}}class eu extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return Q.get(t,r,i);let n=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==s)return Q.get(t,s,i)},set(t,r,i,n){if("symbol"==typeof r)return Q.set(t,r,i,n);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return Q.set(t,a??r,i,n)},has(t,r){if("symbol"==typeof r)return Q.has(t,r);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==n&&Q.has(t,n)},deleteProperty(t,r){if("symbol"==typeof r)return Q.deleteProperty(t,r);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===n||Q.deleteProperty(t,n)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ec.callable;default:return Q.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new eu(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let eh=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class ed{disable(){throw eh}getStore(){}run(){throw eh}exit(){throw eh}enterWith(){throw eh}static bind(e){return e}}let ep="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function ef(){return ep?new ep:new ed}let eg=ef(),em=ef();class eb extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new eb}}class ev{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return eb.callable;default:return Q.get(e,t,r)}}})}}let ey=Symbol.for("next.mutated.cookies");class ew{static wrap(e,t){let r=new J.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],n=new Set,s=()=>{let e=eg.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>n.has(e.name)),t){let e=[];for(let t of i){let r=new J.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},a=new Proxy(r,{get(e,t,r){switch(t){case ey:return i;case"delete":return function(...t){n.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),a}finally{s()}};case"set":return function(...t){n.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),a}finally{s()}};default:return Q.get(e,t,r)}}});return a}}function e_(e){if("action"!==function(e){let t=em.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(e).phase)throw new eb}var eO=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(eO||{}),eS=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(eS||{}),eE=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(eE||{}),ek=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ek||{}),eP=function(e){return e.startServer="startServer.startServer",e}(eP||{}),eT=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(eT||{}),ex=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(ex||{}),eR=function(e){return e.executeRoute="Router.executeRoute",e}(eR||{}),eC=function(e){return e.runHandler="Node.runHandler",e}(eC||{}),ej=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(ej||{}),eA=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(eA||{}),eI=function(e){return e.execute="Middleware.execute",e}(eI||{});let eN=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eD=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eL(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}function e$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function eM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e$(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let{context:eU,propagation:eq,trace:eB,SpanStatusCode:ez,SpanKind:eV,ROOT_CONTEXT:eF}=i=r(465);class eH extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eG=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eH})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:ez.ERROR,message:null==t?void 0:t.message})),e.end()},eW=new Map,eK=i.createContextKey("next.rootSpanId"),eJ=0,eX=()=>eJ++,eY={set(e,t,r){e.push({key:t,value:r})}};class eQ{getTracerInstance(){return eB.getTracer("next.js","0.0.1")}getContext(){return eU}getTracePropagationData(){let e=eU.active(),t=[];return eq.inject(e,t,eY),t}getActiveScopeSpan(){return eB.getSpan(null==eU?void 0:eU.active())}withPropagatedContext(e,t,r){let i=eU.active();if(eB.getSpanContext(i))return t();let n=eq.extract(i,e,r);return eU.with(n,t)}trace(...e){var t;let[r,i,n]=e,{fn:s,options:a}="function"==typeof i?{fn:i,options:{}}:{fn:n,options:eM({},i)},o=a.spanName??r;if(!eN.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return s();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=eB.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==eU?void 0:eU.active())??eF,c=!0);let u=eX();return a.attributes=eM({"next.span_name":o,"next.span_type":r},a.attributes),eU.with(l.setValue(eK,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,i=()=>{eW.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eD.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&eW.set(u,new Map(Object.entries(a.attributes??{})));try{if(s.length>1)return s(e,t=>eG(e,t));let t=s(e);if(eL(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eG(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw eG(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,n]=3===e.length?e:[e[0],{},e[1]];return eN.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof n&&(e=e.apply(this,arguments));let s=arguments.length-1,a=arguments[s];if("function"!=typeof a)return t.trace(r,e,()=>n.apply(this,arguments));{let i=t.getContext().bind(eU.active(),a);return t.trace(r,e,(e,t)=>(arguments[s]=function(e){return null==t||t(e),i.apply(this,arguments)},n.apply(this,arguments)))}}:n}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?eB.setSpan(eU.active(),e):void 0}getRootSpanAttributes(){let e=eU.active().getValue(eK);return eW.get(e)}setRootSpanAttribute(e,t){let r=eU.active().getValue(eK),i=eW.get(r);i&&i.set(e,t)}}let eZ=(()=>{let e=new eQ;return()=>e})();function e0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}let e1="__prerender_bypass";Symbol("__next_preview_data"),Symbol(e1);class e2{constructor(e,t,r,i){var n;let s=e&&function(e,t){let r=eu.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(n=r.get(e1))?void 0:n.value;this._isEnabled=!!(!s&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:e1,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:e1,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function e3(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of C(r))i.append("set-cookie",e);for(let e of new J.ResponseCookies(i).getAll())t.set(e)}}var e4=r(93),e6=r.n(e4);class e5 extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class e9{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new e9(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let e8=Symbol.for("@next/cache-handlers-map"),e7=Symbol.for("@next/cache-handlers-set"),te=globalThis;function tt(){if(te[e8])return te[e8].entries()}function tr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}async function ti(e,t){if(!e)return t();let r=tn(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),i=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!i.has(e))}}(r,tn(e));await ta(e,t)}}function tn(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tr(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e.pendingRevalidates),pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function ts(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let i=function(){if(te[e7])return te[e7].values()}();if(i)for(let t of i)r.push(t.expireTags(...e));await Promise.all(r)}async function ta(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],i=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},n=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([ts(r,e.incrementalCache),...Object.values(i),...n])}let to=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class tl{disable(){throw to}getStore(){}run(){throw to}exit(){throw to}enterWith(){throw to}static bind(e){return e}}let tc="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,tu=tc?new tc:new tl;class th{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(e6()),this.callbackQueue.pause()}after(e){if(eL(e))this.waitUntil||td(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||td();let r=em.getStore();r&&this.workUnitStores.add(r);let i=tu.getStore(),n=i?i.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let s=(t=async()=>{try{await tu.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}},tc?tc.bind(t):tl.bind(t));this.callbackQueue.add(s)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=eg.getStore();if(!e)throw Object.defineProperty(new e5("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return ti(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new e5("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function td(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function tp(e){let t,r={then:(i,n)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(i,n))};return r}class tf{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function tg(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let tm=Symbol.for("@next/request-context"),tb=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let i=r.slice(0,e).join("/");i&&(i.endsWith("/page")||i.endsWith("/route")||(i=`${i}${!i.endsWith("/")?"/":""}layout`),t.push(i))}}return t};async function tv(e,t,r){let i=[],n=r&&r.size>0;for(let t of tb(e))t=`${x}${t}`,i.push(t);if(t.pathname&&!n){let e=`${x}${t.pathname}`;i.push(e)}return{tags:i,expirationsByCacheKind:function(e){let t=new Map,r=tt();if(r)for(let[i,n]of r)"getExpiration"in n&&t.set(i,tp(async()=>n.getExpiration(...e)));return t}(i)}}class ty extends Y{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let tw={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},t_=(e,t)=>eZ().withPropagatedContext(e.headers,t,tw),tO=!1;async function tS(e){var t;let i,n;if(!tO&&(tO=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(768);e(),t_=t(t_)}await _();let s=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new K(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),u="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let h=new Map;if(!s)for(let e of eo){let t=e.toLowerCase(),r=l.get(t);null!==r&&(h.set(t,r),l.delete(t))}let d=new ty({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(el),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(d,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:tg()})}));let p=e.request.waitUntil??(null==(t=function(){let e=globalThis[tm];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new $({request:d,page:e.page,context:p?{waitUntil:p}:void 0});if((i=await t_(d,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),r=new tf;return eZ().trace(eI.execute,{spanName:`middleware ${d.method} ${d.nextUrl.pathname}`,attributes:{"http.target":d.nextUrl.pathname,"http.method":d.method}},async()=>{try{var i,s,a,l,c,u;let h=tg(),p=await tv("/",d.nextUrl,null),g=(c=d.nextUrl,u=e=>{n=e},function(e,t,r,i,n,s,a,o,l,c,u){function h(e){r&&r.setHeader("Set-Cookie",e)}let d={};return{type:"request",phase:e,implicitTags:s,url:{pathname:i.pathname,search:i.search??""},rootParams:n,get headers(){return d.headers||(d.headers=function(e){let t=eu.from(e);for(let e of eo)t.delete(e.toLowerCase());return eu.seal(t)}(t.headers)),d.headers},get cookies(){if(!d.cookies){let e=new J.RequestCookies(eu.from(t.headers));e3(t,e),d.cookies=ev.seal(e)}return d.cookies},set cookies(value){d.cookies=value},get mutableCookies(){if(!d.mutableCookies){let e=function(e,t){let r=new J.RequestCookies(eu.from(e));return ew.wrap(r,t)}(t.headers,a||(r?h:void 0));e3(t,e),d.mutableCookies=e}return d.mutableCookies},get userspaceMutableCookies(){return d.userspaceMutableCookies||(d.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,i){switch(r){case"delete":return function(...r){return e_("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return e_("cookies().set"),e.set(...r),t};default:return Q.get(e,r,i)}}});return t}(this.mutableCookies)),d.userspaceMutableCookies},get draftMode(){return d.draftMode||(d.draftMode=new e2(l,t,this.cookies,this.mutableCookies)),d.draftMode},renderResumeDataCache:o??null,isHmrRefresh:c,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",d,void 0,c,{},p,u,void 0,h,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:i,isPrefetchRequest:n,buildId:s,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,i)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===i.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:i,isPrefetchRequest:n,buildId:s,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:i}=e;return new th({waitUntil:t,onClose:r,onTaskError:i})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=tt();if(t)for(let[r,i]of t)"refreshTags"in i&&e.set(r,tp(async()=>i.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(s=e.request.nextConfig)||null==(i=s.experimental)?void 0:i.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:d.headers.has(ea),buildId:o??"",previouslyRevalidatedTags:[]});return await eg.run(m,()=>em.run(g,e.handler,d,f))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(d,f)}))&&!(i instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});i&&n&&i.headers.set("set-cookie",n);let g=null==i?void 0:i.headers.get("x-middleware-rewrite");if(i&&g&&(u||!s)){let t=new K(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});s||t.host!==d.nextUrl.host||(t.buildId=o||t.buildId,i.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:n}=es(t.toString(),a.toString());!s&&c&&i.headers.set("x-nextjs-rewrite",r),u&&n&&(a.pathname!==t.pathname&&i.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&i.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let m=null==i?void 0:i.headers.get("Location");if(i&&m&&!s){let t=new K(m,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});i=new Response(i.body,i),t.host===a.host&&(t.buildId=o||t.buildId,i.headers.set("Location",t.toString())),c&&(i.headers.delete("Location"),i.headers.set("x-nextjs-redirect",es(t.toString(),a.toString()).url))}let b=i||en.next(),v=b.headers.get("x-middleware-override-headers"),y=[];if(v){for(let[e,t]of h)b.headers.set(`x-middleware-request-${e}`,t),y.push(e);y.length>0&&b.headers.set("x-middleware-override-headers",v+","+y.join(","))}return{response:b,waitUntil:("internal"===f[D].kind?Promise.all(f[D].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:d.fetchMetrics}}var tE=r(747);let tk={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},tP=/^(.*)[.](0|[1-9][0-9]*)$/;function tT(e,t){if(e===t)return!0;let r=e.match(tP);return!!r&&r[1]===t}function tx(e,t,r){let i=r??3180,n=encodeURIComponent(t);if(n.length<=i)return[{name:e,value:t}];let s=[];for(;n.length>0;){let e=n.slice(0,i),t=e.lastIndexOf("%");t>i-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}s.push(r),n=n.slice(e.length)}return s.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function tR(e,t){let r=await t(e);if(r)return r;let i=[];for(let r=0;;r++){let n=`${e}.${r}`,s=await t(n);if(!s)break;i.push(s)}return i.length>0?i.join(""):null}let tC="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),tj=" 	\n\r=".split(""),tA=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<tj.length;t+=1)e[tj[t].charCodeAt(0)]=-2;for(let t=0;t<tC.length;t+=1)e[tC[t].charCodeAt(0)]=t;return e})();function tI(e){let t=[],r=0,i=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>{for(r=r<<8|e,i+=8;i>=6;){let e=r>>i-6&63;t.push(tC[e]),i-=6}}),i>0)for(r<<=6-i,i=6;i>=6;){let e=r>>i-6&63;t.push(tC[e]),i-=6}return t.join("")}function tN(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},n=0,s=0;for(let t=0;t<e.length;t+=1){let a=tA[e.charCodeAt(t)];if(a>-1)for(n=n<<6|a,s+=6;s>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(n>>s-8&255,i,r),s-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}function tD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function tL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tD(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let t$="base64-";async function tM({getAll:e,setAll:t,setItems:r,removedItems:i},n){let s=n.cookieEncoding,a=n.cookieOptions??null,o=await e([...r?Object.keys(r):[],...i?Object.keys(i):[]]),l=o?.map(({name:e})=>e)||[],c=Object.keys(i).flatMap(e=>l.filter(t=>tT(t,e))),u=Object.keys(r).flatMap(e=>{let t=new Set(l.filter(t=>tT(t,e))),i=r[e];"base64url"===s&&(i=t$+tI(i));let n=tx(e,i);return n.forEach(e=>{t.delete(e.name)}),c.push(...t),n}),h=tL(tL(tL({},tk),a),{},{maxAge:0}),d=tL(tL(tL({},tk),a),{},{maxAge:tk.maxAge});delete h.name,delete d.name,await t([...c.map(e=>({name:e,value:"",options:h})),...u.map(({name:e,value:t})=>({name:e,value:t,options:d}))])}function tU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}let tq=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,504)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class tB extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class tz extends tB{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class tV extends tB{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class tF extends tB{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(n||(n={}));class tH{constructor(e,{headers:t={},customFetch:r,region:i=n.Any}={}){this.url=e,this.headers=t,this.region=i,this.fetch=tq(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,n,s,a;return i=this,n=void 0,s=void 0,a=function*(){try{let i,n,{headers:s,method:a,body:o}=t,l={},{region:c}=t;c||(c=this.region),c&&"any"!==c&&(l["x-region"]=c),o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",i=o):"string"==typeof o?(l["Content-Type"]="text/plain",i=o):"undefined"!=typeof FormData&&o instanceof FormData?i=o:(l["Content-Type"]="application/json",i=JSON.stringify(o)));let u=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),s),body:i}).catch(e=>{throw new tz(e)}),h=u.headers.get("x-relay-error");if(h&&"true"===h)throw new tV(u);if(!u.ok)throw new tF(u);let d=(null!=(r=u.headers.get("Content-Type"))?r:"text/plain").split(";")[0].trim();return{data:"application/json"===d?yield u.json():"application/octet-stream"===d?yield u.blob():"text/event-stream"===d?u:"multipart/form-data"===d?yield u.formData():yield u.text(),error:null}}catch(e){return{data:null,error:e}}},new(s||(s=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var i;t.done?e(t.value):((i=t.value)instanceof s?i:new s(function(e){e(i)})).then(r,o)}l((a=a.apply(i,n||[])).next())})}}let{PostgrestClient:tG,PostgrestQueryBuilder:tW,PostgrestFilterBuilder:tK,PostgrestTransformBuilder:tJ,PostgrestBuilder:tX,PostgrestError:tY}=r(86),tQ={"X-Client-Info":"realtime-js/2.11.2"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(s||(s={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(a||(a={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(o||(o={})),(l||(l={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(c||(c={}));class tZ{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),n=t.getUint8(2),s=this.HEADER_LENGTH+2,a=r.decode(e.slice(s,s+i));s+=i;let o=r.decode(e.slice(s,s+n));return s+=n,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(s,e.byteLength)))}}}class t0{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(u||(u={}));let t1=(e,t,r={})=>{var i;let n=null!=(i=r.skipTypes)?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=t2(i,e,t,n),r),{})},t2=(e,t,r,i)=>{let n=t.find(t=>t.name===e),s=null==n?void 0:n.type,a=r[e];return s&&!i.includes(s)?t3(s,a):t4(a)},t3=(e,t)=>{if("_"===e.charAt(0))return t8(t,e.slice(1,e.length));switch(e){case u.bool:return t6(t);case u.float4:case u.float8:case u.int2:case u.int4:case u.int8:case u.numeric:case u.oid:return t5(t);case u.json:case u.jsonb:return t9(t);case u.timestamp:return t7(t);case u.abstime:case u.date:case u.daterange:case u.int4range:case u.int8range:case u.money:case u.reltime:case u.text:case u.time:case u.timestamptz:case u.timetz:case u.tsrange:case u.tstzrange:default:return t4(t)}},t4=e=>e,t6=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},t5=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},t9=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},t8=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i,n=e.slice(1,r);try{i=JSON.parse("["+n+"]")}catch(e){i=n?n.split(","):[]}return i.map(e=>t3(t,e))}return e},t7=e=>"string"==typeof e?e.replace(" ","T"):e,re=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class rt{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(h||(h={}));class rr{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=rr.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=rr.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=rr.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let n=this.cloneDeep(e),s=this.transformState(t),a={},o={};return this.map(n,(e,t)=>{s[e]||(o[e]=t)}),this.map(s,(e,t)=>{let r=n[e];if(r){let i=t.map(e=>e.presence_ref),n=r.map(e=>e.presence_ref),s=t.filter(e=>0>n.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));s.length>0&&(a[e]=s),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(n,{joins:a,leaves:o},r,i)}static syncDiff(e,t,r,i){let{joins:n,leaves:s}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(n,(t,i)=>{var n;let s=null!=(n=e[t])?n:[];if(e[t]=this.cloneDeep(i),s.length>0){let r=e[t].map(e=>e.presence_ref),i=s.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,s,i)}),this.map(s,(t,r)=>{let n=e[t];if(!n)return;let s=r.map(e=>e.presence_ref);n=n.filter(e=>0>s.indexOf(e.presence_ref)),e[t]=n,i(t,n,r),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(d||(d={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(p||(p={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(f||(f={}));class ri{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=a.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new rt(this,o.join,this.params,this.timeout),this.rejoinTimer=new t0(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=a.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=a.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=a.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=a.errored,this.rejoinTimer.scheduleTimeout())}),this._on(o.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new rr(this),this.broadcastEndpointURL=re(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:n,presence:s,private:a}}=this.params;this._onError(t=>null==e?void 0:e(f.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(f.CLOSED));let o={},l={broadcast:n,presence:s,postgres_changes:null!=(i=null==(r=this.bindings.postgres_changes)?void 0:r.map(e=>e.filter))?i:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(f.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,n=null!=(r=null==i?void 0:i.length)?r:0,s=[];for(let r=0;r<n;r++){let n=i[r],{filter:{event:a,schema:o,table:l,filter:c}}=n,u=t&&t[r];if(u&&u.event===a&&u.schema===o&&u.table===l&&u.filter===c)s.push(Object.assign(Object.assign({},n),{id:u.id}));else{this.unsubscribe(),null==e||e(f.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=s,e&&e(f.SUBSCRIBED);return}}).receive("error",t=>{null==e||e(f.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(f.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,n,s;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(s=null==(n=null==(i=this.params)?void 0:i.config)?void 0:n.broadcast)?void 0:s.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:n,payload:s}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:s,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(r=t.timeout)?r:this.timeout);return await (null==(i=e.body)?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=a.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(o.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{let i=new rt(this,o.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}async _fetchWithTimeout(e,t,r){let i=new AbortController,n=setTimeout(()=>i.abort(),r),s=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(n),s}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new rt(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,n;let s=e.toLocaleLowerCase(),{close:a,error:l,leave:c,join:u}=o;if(r&&[a,l,c,u].indexOf(s)>=0&&r!==this._joinRef())return;let h=this._onMessage(s,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(s)?null==(i=this.bindings.postgres_changes)||i.filter(e=>{var t,r,i;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(i=null==(r=e.filter)?void 0:r.event)?void 0:i.toLocaleLowerCase())===s}).map(e=>e.callback(h,r)):null==(n=this.bindings[s])||n.filter(e=>{var r,i,n,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(s))return e.type.toLocaleLowerCase()===s;if("id"in e){let s=e.id,a=null==(r=e.filter)?void 0:r.event;return s&&(null==(i=t.ids)?void 0:i.includes(s))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(n=t.data)?void 0:n.type.toLocaleLowerCase()))}{let r=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===r||r===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof h&&"ids"in h){let e=h.data,{schema:t,table:r,commit_timestamp:i,type:n,errors:s}=e;h=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:n,new:{},old:{},errors:s}),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===a.closed}_isJoined(){return this.state===a.joined}_isJoining(){return this.state===a.joining}_isLeaving(){return this.state===a.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),n={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(n):this.bindings[i]=[n],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null==(i=e.type)?void 0:i.toLocaleLowerCase())===r&&ri.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(o.close,{},e)}_onError(e){this._on(o.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=a.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=t1(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=t1(e.columns,e.old_record)),t}}let rn=()=>{},rs="undefined"!=typeof WebSocket,ra=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class ro{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=tQ,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=rn,this.conn=null,this.sendBuffer=[],this.serializer=new tZ,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,504)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${l.websocket}`,this.httpEndpoint=re(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let n=null==(i=null==t?void 0:t.params)?void 0:i.apikey;n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new t0(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),(null==t?void 0:t.worker)&&(this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl),this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(rs){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new rl(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),Promise.resolve().then(r.t.bind(r,528,23)).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case s.connecting:return c.Connecting;case s.open:return c.Open;case s.closing:return c.Closing;default:return c.Closed}}isConnected(){return this.connectionState()===c.Open}channel(e,t={config:{}}){let r=new ri(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){let{topic:t,event:r,payload:i,ref:n}=e,s=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${r} (${n})`,i),this.isConnected()?s():this.sendBuffer.push(s)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(e){}if(e&&e.exp&&!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`);this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(o.access_token,{access_token:t})})}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:n}=e;n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${n&&"("+n+")"||""}`,i),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,n)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(o.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([ra],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class rl{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=s.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class rc extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function ru(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class rh extends rc{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class rd extends rc{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let rp=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,504)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},rf=()=>(function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,504))).Response:Response}),rg=e=>{if(Array.isArray(e))return e.map(e=>rg(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=rg(r)}),t};var rm=function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rb=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),rv=(e,t,r)=>rm(void 0,void 0,void 0,function*(){e instanceof(yield rf())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new rh(rb(r),e.status||500))}).catch(e=>{t(new rd(rb(e),e))}):t(new rd(rb(e),e))}),ry=(e,t,r,i)=>{let n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(n.body=JSON.stringify(i)),Object.assign(Object.assign({},n),r))};function rw(e,t,r,i,n,s){return rm(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,ry(t,i,n,s)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>rv(e,o,i))})})}function r_(e,t,r,i){return rm(this,void 0,void 0,function*(){return rw(e,"GET",t,r,i)})}function rO(e,t,r,i,n){return rm(this,void 0,void 0,function*(){return rw(e,"POST",t,i,n,r)})}function rS(e,t,r,i,n){return rm(this,void 0,void 0,function*(){return rw(e,"DELETE",t,i,n,r)})}var rE=r(356).Buffer,rk=function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rP={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},rT={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class rx{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=rp(i)}uploadOrUpdate(e,t,r,i){return rk(this,void 0,void 0,function*(){try{let n,s=Object.assign(Object.assign({},rT),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(s.upsert)}),o=s.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((n=new FormData).append("cacheControl",s.cacheControl),o&&n.append("metadata",this.encodeMetadata(o)),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((n=r).append("cacheControl",s.cacheControl),o&&n.append("metadata",this.encodeMetadata(o))):(n=r,a["cache-control"]=`max-age=${s.cacheControl}`,a["content-type"]=s.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:n,headers:a},(null==s?void 0:s.duplex)?{duplex:s.duplex}:{})),h=yield u.json();if(u.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}upload(e,t,r){return rk(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return rk(this,void 0,void 0,function*(){let n=this._removeEmptyFolders(e),s=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${s}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:rT.upsert},i),s=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,s["cache-control"]=`max-age=${t.cacheControl}`,s["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:s}),l=yield o.json();if(o.ok)return{data:{path:n,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return rk(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let n=yield rO(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),s=new URL(this.url+n.url),a=s.searchParams.get("token");if(!a)throw new rc("No token returned by API");return{data:{signedUrl:s.toString(),path:e,token:a},error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}update(e,t,r){return rk(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return rk(this,void 0,void 0,function*(){try{return{data:yield rO(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}copy(e,t,r){return rk(this,void 0,void 0,function*(){try{return{data:{path:(yield rO(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return rk(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),n=yield rO(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n={signedUrl:encodeURI(`${this.url}${n.signedURL}${s}`)},error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return rk(this,void 0,void 0,function*(){try{let i=yield rO(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}download(e,t){return rk(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield r_(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}info(e){return rk(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield r_(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:rg(e),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}exists(e){return rk(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return rm(this,void 0,void 0,function*(){return rw(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(ru(e)&&e instanceof rd){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&i.push(n);let s=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${s?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return rk(this,void 0,void 0,function*(){try{return{data:yield rS(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}list(e,t,r){return rk(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},rP),t),{prefix:e||""});return{data:yield rO(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==rE?rE.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let rR={"X-Client-Info":"storage-js/2.7.1"};var rC=function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class rj{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},rR),t),this.fetch=rp(r)}listBuckets(){return rC(this,void 0,void 0,function*(){try{return{data:yield r_(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}getBucket(e){return rC(this,void 0,void 0,function*(){try{return{data:yield r_(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return rC(this,void 0,void 0,function*(){try{return{data:yield rO(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return rC(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,n){return rm(this,void 0,void 0,function*(){return rw(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}emptyBucket(e){return rC(this,void 0,void 0,function*(){try{return{data:yield rO(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}deleteBucket(e){return rC(this,void 0,void 0,function*(){try{return{data:yield rS(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(ru(e))return{data:null,error:e};throw e}})}}class rA extends rj{constructor(e,t={},r){super(e,t,r)}from(e){return new rx(this.url,this.headers,e,this.fetch)}}let rI="";rI="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let rN={headers:{"X-Client-Info":`supabase-js-${rI}/2.49.4`}},rD={schema:"public"},rL={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},r$={};var rM=r(504);let rU=e=>{let t;return t=e||("undefined"==typeof fetch?rM.default:fetch),(...e)=>t(...e)},rq=()=>"undefined"==typeof Headers?rM.Headers:Headers,rB=(e,t,r)=>{let i=rU(r),n=rq();return(r,s)=>(function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,l=new n(null==s?void 0:s.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(r,Object.assign(Object.assign({},s),{headers:l}))})},rz="2.69.1",rV={"X-Client-Info":`gotrue-js/${rz}`},rF="X-Supabase-Api-Version",rH={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},rG=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class rW extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function rK(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class rJ extends rW{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class rX extends rW{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class rY extends rW{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class rQ extends rY{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class rZ extends rY{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class r0 extends rY{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class r1 extends rY{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class r2 extends rY{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class r3 extends rY{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function r4(e){return rK(e)&&"AuthRetryableFetchError"===e.name}class r6 extends rY{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class r5 extends rY{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let r9="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),r8=" 	\n\r=".split(""),r7=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<r8.length;t+=1)e[r8[t].charCodeAt(0)]=-2;for(let t=0;t<r9.length;t+=1)e[r9[t].charCodeAt(0)]=t;return e})();function ie(e,t,r){let i=r7[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function it(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},s=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)ie(e.charCodeAt(t),n,s);return t.join("")}let ir=()=>!1,ii={tested:!1,writable:!1},is=()=>{if(!ir())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(ii.tested)return ii.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),ii.tested=!0,ii.writable=!0}catch(e){ii.tested=!0,ii.writable=!1}return ii.writable},ia=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,504)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},io=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,il=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},ic=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},iu=async(e,t)=>{await e.removeItem(t)};class ih{constructor(){this.promise=new ih.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function id(e){let t=e.split(".");if(3!==t.length)throw new r5("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!rG.test(t[e]))throw new r5("JWT not in base64url format");return{header:JSON.parse(it(t[0])),payload:JSON.parse(it(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)ie(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function ip(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function ig(e){return("0"+e.toString(16)).substr(-2)}async function im(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function ib(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await im(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function iv(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,ig).join("")}(),n=i;r&&(n+="/PASSWORD_RECOVERY"),await il(e,`${t}-code-verifier`,n);let s=await ib(i),a=i===s?"plain":"s256";return[s,a]}ih.promiseConstructor=Promise;let iy=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var iw=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r};let i_=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),iO=[502,503,504];async function iS(e){var t;let r,i;if(!io(e))throw new r3(i_(e),0);if(iO.includes(e.status))throw new r3(i_(e),e.status);try{r=await e.json()}catch(e){throw new rX(i_(e),e)}let n=function(e){let t=e.headers.get(rF);if(!t||!t.match(iy))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(n&&n.getTime()>=rH["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new r6(i_(r),e.status,(null==(t=r.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===i)throw new rQ}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new r6(i_(r),e.status,r.weak_password.reasons);throw new rJ(i_(r),e.status||500,i)}let iE=(e,t,r,i)=>{let n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(i),Object.assign(Object.assign({},n),r))};async function ik(e,t,r,i){var n;let s=Object.assign({},null==i?void 0:i.headers);s[rF]||(s[rF]=rH["2024-01-01"].name),(null==i?void 0:i.jwt)&&(s.Authorization=`Bearer ${i.jwt}`);let a=null!=(n=null==i?void 0:i.query)?n:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await iP(e,t,r+o,{headers:s,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function iP(e,t,r,i,n,s){let a,o=iE(t,i,n,s);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new r3(i_(e),0)}if(a.ok||await iS(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await iS(e)}}function iT(e){var t,r,i;let n=null;(i=e).access_token&&i.refresh_token&&i.expires_in&&(n=Object.assign({},e),e.expires_at||(n.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:n,user:null!=(t=e.user)?t:e},error:null}}function ix(e){let t=iT(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function iR(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function iC(e){return{data:e,error:null}}function ij(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:n,verification_type:s}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:n,verification_type:s},user:Object.assign({},iw(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function iA(e){return e}var iI=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r};class iN{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=ia(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await ik(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(rK(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await ik(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:iR})}catch(e){if(rK(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=iI(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await ik(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:ij,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(rK(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await ik(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:iR})}catch(e){if(rK(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,n,s,a,o;try{let l={nextPage:null,lastPage:0,total:0},c=await ik(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(r=null==(t=null==e?void 0:e.page)?void 0:t.toString())?r:"",per_page:null!=(n=null==(i=null==e?void 0:e.perPage)?void 0:i.toString())?n:""},xform:iA});if(c.error)throw c.error;let u=await c.json(),h=null!=(s=c.headers.get("x-total-count"))?s:0,d=null!=(o=null==(a=c.headers.get("link"))?void 0:a.split(","))?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(e){if(rK(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){try{return await ik(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:iR})}catch(e){if(rK(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){try{return await ik(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:iR})}catch(e){if(rK(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){try{return await ik(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:iR})}catch(e){if(rK(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){try{let{data:t,error:r}=await ik(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(rK(e))return{data:null,error:e};throw e}}async _deleteFactor(e){try{return{data:await ik(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(rK(e))return{data:null,error:e};throw e}}}let iD={getItem:e=>is()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{is()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{is()&&globalThis.localStorage.removeItem(e)}};function iL(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let i$={debug:!!(globalThis&&is()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class iM extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class iU extends iM{}async function iq(e,t,r){i$.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),i$.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){i$.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{i$.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}if(0===t)throw i$.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new iU(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(i$.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let iB={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:rV,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function iz(e,t,r){return await r()}class iV{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=iV.nextInstanceID,iV.nextInstanceID+=1,this.instanceID>0&&ir()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},iB),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new iN({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=ia(i.fetch),this.lock=i.lock||iz,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:ir()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=iq:this.lock=iz,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:is()?this.storage=iD:(this.memoryStorage={},this.storage=iL(this.memoryStorage)):(this.memoryStorage={},this.storage=iL(this.memoryStorage)),ir()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(r=this.broadcastChannel)||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${rz}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),ir()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:n}=await this._getSessionFromURL(t,r);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),rK(n)&&"AuthImplicitGrantRedirectError"===n.name){let t=null==(e=n.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}let{session:s,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",s,"redirect type",a),await this._saveSession(s),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",s):await this._notifyAllSubscribers("SIGNED_IN",s)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(rK(e))return{error:e};return{error:new rX("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:n,error:s}=await ik(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(r=null==(t=null==e?void 0:e.options)?void 0:t.data)?r:{},gotrue_meta_security:{captcha_token:null==(i=null==e?void 0:e.options)?void 0:i.captchaToken}},xform:iT});if(s||!n)return{data:{user:null,session:null},error:s};let a=n.session,o=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let n;if("email"in e){let{email:r,password:i,options:s}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await iv(this.storage,this.storageKey)),n=await ik(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==s?void 0:s.emailRedirectTo,body:{email:r,password:i,data:null!=(t=null==s?void 0:s.data)?t:{},gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:a,code_challenge_method:o},xform:iT})}else if("phone"in e){let{phone:t,password:s,options:a}=e;n=await ik(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:s,data:null!=(r=null==a?void 0:a.data)?r:{},channel:null!=(i=null==a?void 0:a.channel)?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:iT})}else throw new r0("You must provide either an email or phone number and a password");let{data:s,error:a}=n;if(a||!s)return{data:{user:null,session:null},error:a};let o=s.session,l=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:n}=e;t=await ik(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:ix})}else if("phone"in e){let{phone:r,password:i,options:n}=e;t=await ik(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:ix})}else throw new r0("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new rZ};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:null==(n=e.options)?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async _exchangeCodeForSession(e){let t=await ic(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:n}=await ik(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:iT});if(await iu(this.storage,`${this.storageKey}-code-verifier`),n)throw n;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new rZ};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:n}}catch(e){if(rK(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:n,nonce:s}=e,{data:a,error:o}=await ik(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:n,nonce:s,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:iT});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new rZ};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,n,s;try{if("email"in e){let{email:i,options:n}=e,s=null,a=null;"pkce"===this.flowType&&([s,a]=await iv(this.storage,this.storageKey));let{error:o}=await ik(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!=(t=null==n?void 0:n.data)?t:{},create_user:null==(r=null==n?void 0:n.shouldCreateUser)||r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:s,code_challenge_method:a},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await ik(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(i=null==r?void 0:r.data)?i:{},create_user:null==(n=null==r?void 0:r.shouldCreateUser)||n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!=(s=null==r?void 0:r.channel)?s:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new r0("You must provide either an email or phone number.")}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,n;"options"in e&&(i=null==(t=e.options)?void 0:t.redirectTo,n=null==(r=e.options)?void 0:r.captchaToken);let{data:s,error:a}=await ik(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:i,xform:iT});if(a)throw a;if(!s)throw Error("An error occurred on token verification.");let o=s.session,l=s.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let n=null,s=null;return"pkce"===this.flowType&&([n,s]=await iv(this.storage,this.storageKey)),await ik(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(r=null==(t=e.options)?void 0:t.redirectTo)?r:void 0}),(null==(i=null==e?void 0:e.options)?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:s}),headers:this.headers,xform:iC})}catch(e){if(rK(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new rQ;let{error:i}=await ik(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:n}=e,{error:s}=await ik(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){let{phone:r,type:i,options:n}=e,{data:s,error:a}=await ik(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==s?void 0:s.message_id},error:a}}throw new r0("You must provide either an email or phone number and a type")}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await ic(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{session:null},error:n};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await ik(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:iR});return await this._useSession(async e=>{var t,r,i;let{data:n,error:s}=e;if(s)throw s;return(null==(t=n.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await ik(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(i=null==(r=n.session)?void 0:r.access_token)?i:void 0,xform:iR}):{data:{user:null},error:new rQ}})}catch(e){if(rK(e))return rK(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await iu(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:n}=r;if(n)throw n;if(!i.session)throw new rQ;let s=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await iv(this.storage,this.storageKey));let{data:l,error:c}=await ik(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:s.access_token,xform:iR});if(c)throw c;return s.user=l.user,await this._saveSession(s),await this._notifyAllSubscribers("USER_UPDATED",s),{data:{user:s.user},error:null}})}catch(e){if(rK(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new rQ;let t=Date.now()/1e3,r=t,i=!0,n=null,{payload:s}=id(e.access_token);if(s.exp&&(i=(r=s.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};n=t}else{let{data:i,error:s}=await this._getUser(e.access_token);if(s)throw s;n={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(e){if(rK(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:n}=t;if(n)throw n;e=null!=(r=i.session)?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new rQ;let{session:i,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(rK(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!ir())throw new r1("No browser detected.");if(e.error||e.error_description||e.error_code)throw new r1(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new r2("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new r1("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new r2("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:n,refresh_token:s,expires_in:a,expires_at:o,token_type:l}=e;if(!n||!a||!s||!l)throw new r1("No session defined in URL");let c=Math.round(Date.now()/1e3),u=parseInt(a),h=c+u;o&&(h=parseInt(o));let d=h-c;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);let p=h-u;c-p>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",p,h,c):c-p<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",p,h,c);let{data:f,error:g}=await this._getUser(n);if(g)throw g;let m={provider_token:r,provider_refresh_token:i,access_token:n,expires_in:u,expires_at:h,refresh_token:s,token_type:l,user:f.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(e){if(rK(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await ic(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:n}=t;if(n)return{error:n};let s=null==(r=i.session)?void 0:r.access_token;if(s){let{error:t}=await this.admin.signOut(s,e);if(t&&!(rK(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await iu(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:n}=t;if(n)throw n;await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null==(i=this.stateChangeEmitters.get(e))?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await iv(this.storage,this.storageKey,!0));try{return await ik(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(rK(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(rK(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,n,s,a;let{data:o,error:l}=t;if(l)throw l;let c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(r=e.options)?void 0:r.redirectTo,scopes:null==(i=e.options)?void 0:i.scopes,queryParams:null==(n=e.options)?void 0:n.queryParams,skipBrowserRedirect:!0});return await ik(this.fetch,"GET",c,{headers:this.headers,jwt:null!=(a=null==(s=o.session)?void 0:s.access_token)?a:void 0})});if(i)throw i;return!ir()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(rK(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:n,error:s}=t;if(s)throw s;return await ik(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(i=null==(r=n.session)?void 0:r.access_token)?i:void 0})})}catch(e){if(rK(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let n=Date.now();return await (r=async r=>(r>0&&await ip(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await ik(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:iT})),i=(e,t)=>{let r=200*Math.pow(2,e);return t&&r4(t)&&Date.now()+r-n<3e4},new Promise((e,t)=>{(async()=>{for(let n=0;n<1/0;n++)try{let t=await r(n);if(!i(n,null,t))return void e(t)}catch(e){if(!i(n,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),rK(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),ir()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await ic(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!=(e=r.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),r4(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new rQ;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new ih;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new rQ;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),rK(e)){let r={session:null,error:e};return r4(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(r),r}throw null==(r=this.refreshingDeferred)||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],n=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(n),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await il(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await iu(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&ir()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof iM)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!ir()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await iv(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:n}=t;return n?{data:null,error:n}:await ik(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token})})}catch(e){if(rK(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:n,error:s}=t;if(s)return{data:null,error:s};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await ik(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(i=null==o?void 0:o.totp)?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(rK(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:n}=t;if(n)return{data:null,error:n};let{data:s,error:a}=await ik(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+s.expires_in},s)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",s),{data:s,error:a})})}catch(e){if(rK(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:n}=t;return n?{data:null,error:n}:await ik(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token})})}catch(e){if(rK(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:n}=e;if(n)return{data:null,error:n};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:s}=id(i.access_token),a=null;s.aal&&(a=s.aal);let o=a;return(null!=(r=null==(t=i.user.factors)?void 0:t.filter(e=>"verified"===e.status))?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:s.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:n}=await ik(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!i.keys||0===i.keys.length)throw new r5("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new r5("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let i=e;if(!i){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};i=e.session.access_token}let{header:n,payload:s,signature:a,raw:{header:o,payload:l}}=id(i);var r=s.exp;if(!r)throw Error("Missing exp claim");if(r<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!n.kid||"HS256"===n.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(i);if(e)throw e;return{data:{claims:s,header:n,signature:a},error:null}}let c=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(n.alg),u=await this.fetchJwk(n.kid,t),h=await crypto.subtle.importKey("jwk",u,c,!0,["verify"]);if(!await crypto.subtle.verify(c,h,a,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${l}`)))throw new r5("Invalid JWT signature");return{data:{claims:s,header:n,signature:a},error:null}}catch(e){if(rK(e))return{data:null,error:e};throw e}}}iV.nextInstanceID=0;let iF=iV;class iH extends iF{constructor(e){super(e)}}class iG{constructor(e,t,r){var i,n,s;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=e.replace(/\/$/,"");this.realtimeUrl=`${a}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${a}/auth/v1`,this.storageUrl=`${a}/storage/v1`,this.functionsUrl=`${a}/functions/v1`;let o=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,l=function(e,t){let{db:r,auth:i,realtime:n,global:s}=e,{db:a,auth:o,realtime:l,global:c}=t,u={db:Object.assign(Object.assign({},a),r),auth:Object.assign(Object.assign({},o),i),realtime:Object.assign(Object.assign({},l),n),global:Object.assign(Object.assign({},c),s),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?u.accessToken=e.accessToken:delete u.accessToken,u}(null!=r?r:{},{db:rD,realtime:r$,auth:Object.assign(Object.assign({},rL),{storageKey:o}),global:rN});this.storageKey=null!=(i=l.auth.storageKey)?i:"",this.headers=null!=(n=l.global.headers)?n:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(s=l.auth)?s:{},this.headers,l.global.fetch),this.fetch=rB(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new tG(`${a}/rest/v1`,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new tH(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new rA(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,n,s;return r=this,i=void 0,n=void 0,s=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!=(t=null==(e=r.session)?void 0:e.access_token)?t:null},new(n||(n=Promise))(function(e,t){function a(e){try{l(s.next(e))}catch(e){t(e)}}function o(e){try{l(s.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(a,o)}l((s=s.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:n,flowType:s,lock:a,debug:o},l,c){let u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new iH({url:this.authUrl,headers:Object.assign(Object.assign({},u),l),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:s,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new ro(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let iW=(e,t,r)=>new iG(e,t,r);function iK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function iJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iK(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function iX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}r(881),"undefined"==typeof URLPattern||URLPattern;var iY=r(590);new WeakMap;let iQ="function"==typeof iY.unstable_postpone;function iZ(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(iZ("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`);function i0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function i1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i0(Object(r),!0).forEach(function(t){var i,n,s;i=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(n))in i?Object.defineProperty(i,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}new WeakMap;let i2={public:["/","/login","/payment","/thank-you","/contact","/privacy","/terms","/auth/callback","/auth/confirmed","/auth/unauthorized","/auth/reset-password","/auth/confirm-reset","/api/auth/register-free","/api/auth/pre-register-paid","/api/stripe/webhook","/api/stripe/create-checkout-session","/api/stripe/create-token-checkout","/api/notify-signup","/api/user/status","/api/health","/api/auth/initiate-password-setup"],authenticated:["/app","/dashboard","/profile","/welcome","/upgrade-plan"],planRestricted:{"/plan-estudios":["pro"],"/app/ai-tutor":["usuario","pro"],"/app/summaries":["pro"],"/app/advanced-features":["pro"]}},i3={enableStrictValidation:"true"===process.env.STRICT_PLAN_VALIDATION,requirePaymentVerification:"true"===process.env.REQUIRE_PAYMENT_VERIFICATION,enableAccessLogging:"true"===process.env.ENABLE_ACCESS_LOGGING,sessionTimeout:3e5};async function i4(e){let{pathname:t}=e.nextUrl;try{let o=en.next({request:e}),l=function(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:i,getAll:n,setAll:s,setItems:a,removedItems:o}=function(e,t){let r,i,n=e.cookies??null,s=e.cookieEncoding,a={},o={};if(n)if("get"in n){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let i=await n.get(t[e]);(i||"string"==typeof i)&&r.push({name:t[e],value:i})}return r};if(r=async t=>await e(t),"set"in n&&"remove"in n)i=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:i,options:s}=e[t];i?await n.set(r,i,s):await n.remove(r,s)}};else if(t)i=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in n)if(r=async()=>await n.getAll(),"setAll"in n)i=n.setAll;else if(t)i=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).`);else if(t||1)if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],i=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};else{let e=()=>{let e=(0,tE.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),i=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,tE.lK)(e,t,r)})}}return t?{getAll:r,setAll:i,setItems:a,removedItems:o,storage:{isServer:!0,getItem:async e=>{if("string"==typeof a[e])return a[e];if(o[e])return null;let t=await r([e]),i=await tR(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!i)return null;let n=i;return"string"==typeof i&&i.startsWith(t$)&&(n=tN(i.substring(t$.length))),n},setItem:async(t,n)=>{t.endsWith("-code-verifier")&&await tM({getAll:r,setAll:i,setItems:{[t]:n},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:s}),a[t]=n,delete o[t]},removeItem:async e=>{delete a[e],o[e]=!0}}}:{getAll:r,setAll:i,setItems:a,removedItems:o,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),i=await tR(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!i)return null;let n=i;return i.startsWith(t$)&&(n=tN(i.substring(t$.length))),n},setItem:async(t,n)=>{let a=await r([t]),o=new Set((a?.map(({name:e})=>e)||[]).filter(e=>tT(e,t))),l=n;"base64url"===s&&(l=t$+tI(n));let c=tx(t,l);c.forEach(({name:e})=>{o.delete(e)});let u=tL(tL(tL({},tk),e?.cookieOptions),{},{maxAge:0}),h=tL(tL(tL({},tk),e?.cookieOptions),{},{maxAge:tk.maxAge});delete u.name,delete h.name;let d=[...[...o].map(e=>({name:e,value:"",options:u})),...c.map(({name:e,value:t})=>({name:e,value:t,options:h}))];d.length>0&&await i(d)},removeItem:async t=>{let n=await r([t]),s=(n?.map(({name:e})=>e)||[]).filter(e=>tT(e,t)),a=tL(tL(tL({},tk),e?.cookieOptions),{},{maxAge:0});delete a.name,s.length>0&&await i(s.map(e=>({name:e,value:"",options:a})))}}}}(iJ(iJ({},r),{},{cookieEncoding:r?.cookieEncoding??"base64url"}),!0),l=iW(e,t,iJ(iJ({},r),{},{global:iJ(iJ({},r?.global),{},{headers:iJ(iJ({},r?.global?.headers),{},{"X-Client-Info":"supabase-ssr/0.6.1 createServerClient"})}),auth:iJ(iJ(iJ({},r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null),r?.auth),{},{flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:i})}));return l.auth.onAuthStateChange(async e=>{(Object.keys(a).length>0||Object.keys(o).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await tM({getAll:n,setAll:s,setItems:a,removedItems:o},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),l}("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!0},cookies:{getAll:()=>e.cookies.getAll(),setAll(t){let r=t.filter(e=>!e.name.includes("auth-token")&&!e.name.includes("refresh-token"));r.forEach(({name:t,value:r,options:i})=>e.cookies.set(t,r)),o=en.next({request:e}),r.forEach(({name:e,value:t,options:r})=>o.cookies.set(e,t,i1(i1({},r),{},{maxAge:void 0,expires:void 0})))}}}),{data:{user:c},error:u}=await l.auth.getUser();if(r=t,i2.public.some(e=>r===e||r.startsWith(e+"/"))){if(c&&("/"===t||"/login"===t))return console.log(`[MW_ALLOW] Usuario autenticado en ${t}. Redirigiendo a /app.`),en.redirect(new URL("/app",e.url));return console.log(`[MW_ALLOW] Ruta p\xfablica ${t}. Permitiendo acceso.`),nt(o)}if("/auth/reset-password"===t)return console.log(`[MW_BYPASS] Permitiendo paso a ${t} para manejo de autenticaci\xf3n en el cliente.`),nt(o);if(c&&["/auth/callback","/welcome","/upgrade-plan"].some(e=>t.startsWith(e))){if((await i6(c,l)).isLegitimate)return nt(o);return ne(e,"Invalid account - please complete registration")}if(!c)return i7(e);if(i=t,i2.authenticated.some(e=>i.startsWith(e))){let t=await i9(c.id,l);if(!t.valid)if("Profile not found"!==t.reason)return ne(e,t.reason);else{let t=await i6(c,l);if(!t.isLegitimate)return ne(e,"Invalid account - please complete registration");try{let e="unknown"===t.accountType?"free":t.accountType;await i5(c.id,e,l)}catch(t){return ne(e,"Profile recovery failed")}}}if(n=t,Object.keys(i2.planRestricted).some(e=>n.startsWith(e))){let o=await i8(c.id,t,l);if(!o.valid){var r,i,n,s=e,a=o;let t=s.nextUrl.clone();return t.pathname="/auth/unauthorized",t.searchParams.set("reason",a.reason||"Access denied"),t.searchParams.set("feature",s.nextUrl.pathname),a.requiredPlans&&t.searchParams.set("required_plan",a.requiredPlans.join(",")),en.redirect(t)}}return nt(o)}catch(t){return console.error("❌ [MIDDLEWARE] Critical error:",t),i7(e)}}async function i6(e,t){try{let r=e.user_metadata||{},i=e.app_metadata||{};if(console.log(`🔍 [MIDDLEWARE] Checking user legitimacy for ${e.id}:`,{userMetadata:Object.keys(r),appMetadata:Object.keys(i),created_via:r.created_via,free_account:r.free_account,plan:r.plan}),"free_registration"===r.created_via||r.free_account)return console.log(`✅ [MIDDLEWARE] User ${e.id} is legitimate: Free account registration`),{isLegitimate:!0,accountType:"free",reason:"Free account registration"};if(r.plan&&("free"===r.plan||"usuario"===r.plan||"pro"===r.plan)){let t="free"===r.plan?"free":"paid";return console.log(`✅ [MIDDLEWARE] User ${e.id} is legitimate: User in setup process (${r.plan})`),{isLegitimate:!0,accountType:t,reason:"User in setup process"}}if(r.stripe_session_id||r.stripe_customer_id)return console.log(`✅ [MIDDLEWARE] User ${e.id} is legitimate: Has Stripe metadata`),{isLegitimate:!0,accountType:"paid",reason:"Has Stripe metadata"};let{data:n,error:s}=await t.from("stripe_transactions").select("id, status, plan_id").eq("user_id",e.id).limit(1);if(!s&&n&&n.length>0)return console.log(`✅ [MIDDLEWARE] User ${e.id} is legitimate: Has payment transactions`),{isLegitimate:!0,accountType:"paid",reason:"Has payment transactions"};if(i.created_by_admin||r.invited_by_admin)return console.log(`✅ [MIDDLEWARE] User ${e.id} is legitimate: Admin created account`),{isLegitimate:!0,accountType:"free",reason:"Admin created account"};return console.log(`🚨 [MIDDLEWARE] User ${e.id} is NOT legitimate: No legitimate registration method found`),{isLegitimate:!1,accountType:"unknown",reason:"No legitimate registration method found"}}catch(e){return console.error("❌ [MIDDLEWARE] Error checking user legitimacy:",e),{isLegitimate:!1,accountType:"unknown",reason:"Verification error"}}}async function i5(e,t,r){try{let i,n=new Date().toISOString().slice(0,7)+"-01";if("free"===t){let t=new Date;t.setDate(t.getDate()+5),i={user_id:e,subscription_plan:"free",monthly_token_limit:5e4,current_month_tokens:0,current_month:n,payment_verified:!0,plan_expires_at:t.toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString(),security_flags:{recovered_profile:!0,recovery_date:new Date().toISOString(),original_account_type:"free"}}}else i={user_id:e,subscription_plan:"free",monthly_token_limit:5e4,current_month_tokens:0,current_month:n,payment_verified:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),security_flags:{recovered_profile:!0,recovery_date:new Date().toISOString(),original_account_type:"paid",requires_manual_verification:!0}};let{error:s}=await r.from("user_profiles").insert([i]);if(s)throw console.error("Error creating recovery profile:",s),s}catch(e){throw console.error("Failed to create recovery profile:",e),e}}async function i9(e,t){try{let{data:r,error:i}=await t.from("user_profiles").select("subscription_plan, payment_verified, plan_expires_at, auto_renew, security_flags").eq("user_id",e).single();if(i||!r)return{valid:!1,reason:"Profile not found"};if(r.plan_expires_at){let e=new Date,t=new Date(r.plan_expires_at);if(e>t){let e="Account expired";return e="free"===r.subscription_plan?"Free account expired":"Subscription grace period expired",{valid:!1,reason:e}}}if(i3.requirePaymentVerification&&"free"!==r.subscription_plan&&!r.payment_verified)return{valid:!1,reason:"Payment not verified"};return{valid:!0}}catch(e){return console.error("Error validating user profile:",e),{valid:!1,reason:"Validation error"}}}async function i8(e,t,r){try{let i=Object.entries(i2.planRestricted).find(([e])=>t.startsWith(e))?.[1];if(!i)return{valid:!0};let{data:n,error:s}=await r.from("user_profiles").select("subscription_plan, payment_verified").eq("user_id",e).single();if(s||!n)return{valid:!1,reason:"Profile not found",requiredPlans:i};if(!i.includes(n.subscription_plan))return{valid:!1,reason:`Plan ${n.subscription_plan} not sufficient`,requiredPlans:i};return{valid:!0}}catch(e){return console.error("Error validating plan access:",e),{valid:!1,reason:"Validation error",requiredPlans:[]}}}function i7(e){let t=e.nextUrl.clone();return t.pathname="/login",t.searchParams.set("redirect",e.nextUrl.pathname),en.redirect(t)}function ne(e,t){let r=e.nextUrl.clone();return r.pathname="/payment",t&&r.searchParams.set("reason",t),en.redirect(r)}function nt(e){return e.headers.set("X-Frame-Options","DENY"),e.headers.set("X-Content-Type-Options","nosniff"),e.headers.set("Referrer-Policy","strict-origin-when-cross-origin"),e.headers.set("X-XSS-Protection","1; mode=block"),e.headers.set("Content-Security-Policy","default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://d3js.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.stripe.com;"),e}let nr={matcher:["/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"]},ni=(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),{...g}),nn=ni.middleware||ni.default,ns="/src/middleware";if("function"!=typeof nn)throw Object.defineProperty(Error(`The Middleware "${ns}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function na(e){return tS({...e,page:ns,handler:async(...e)=>{try{return await nn(...e)}catch(n){let t=e[0],r=new URL(t.url),i=r.pathname+r.search;throw await y(n,{path:i,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),n}}})}},950:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(852));class s extends n.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let n="";"plain"===i?n="pl":"phrase"===i?n="ph":"websearch"===i&&(n="w");let s=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${s}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=s}},e=>{var t=e(e.s=925);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2024],{72024:(e,r,t)=>{t.r(r),t.d(r,{SupabaseAdminService:()=>m,supabaseAdmin:()=>f});var n=t(37711),a=t(33311),o=t(11157),s=t(59539),i=t(28295),u=t.n(i),c=t(45408),l=t(38310);function p(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function d(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?p(Object(t),!0).forEach(function(r){(0,n.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):p(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var f=(0,c.UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",l.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),m=function(){var e,r,t,n,i,c,l,p,m,h,g,v,b,E,w;function x(){(0,o.A)(this,x)}return(0,s.A)(x,null,[{key:"createStripeTransaction",value:(e=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("stripe_transactions").insert([r]).select().single();case 2:if(n=(t=e.sent).data,!(a=t.error)){e.next=8;break}throw console.error("Error creating stripe transaction:",a),Error("Failed to create transaction: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(r){return e.apply(this,arguments)})},{key:"getTransactionBySessionId",value:(r=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("stripe_transactions").select("*").eq("stripe_session_id",r).single();case 2:if(n=(t=e.sent).data,!((a=t.error)&&"PGRST116"!==a.code)){e.next=8;break}throw console.error("Error fetching transaction:",a),Error("Failed to fetch transaction: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)})},{key:"createUserWithInvitation",value:(t=(0,a.A)(u().mark(function e(r,t){var n,a,o,s,i,c,l,p,d,m;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:r,userData:t,redirectTo:"".concat("http://localhost:3000","/auth/callback"),timestamp:new Date().toISOString()}),e.next=3,f.auth.admin.inviteUserByEmail(r,{data:t,redirectTo:"".concat("http://localhost:3000","/auth/callback")});case 3:if(d=(p=e.sent).data,m=p.error,console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!d,hasUser:!!(null!=d&&d.user),userId:null==d||null==(n=d.user)?void 0:n.id,userEmail:null==d||null==(a=d.user)?void 0:a.email,userAud:null==d||null==(o=d.user)?void 0:o.aud,userRole:null==d||null==(s=d.user)?void 0:s.role,emailConfirmed:null==d||null==(i=d.user)?void 0:i.email_confirmed_at,userMetadata:null==d||null==(c=d.user)?void 0:c.user_metadata,appMetadata:null==d||null==(l=d.user)?void 0:l.app_metadata,error:null==m?void 0:m.message,errorCode:null==m?void 0:m.status,fullError:m}),!m){e.next=10;break}throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:m.message,status:m.status,details:m}),Error("Failed to create user invitation: ".concat(m.message));case 10:return e.abrupt("return",d);case 11:case"end":return e.stop()}},e)})),function(e,r){return t.apply(this,arguments)})},{key:"createUserWithPassword",value:(n=(0,a.A)(u().mark(function e(r,t,n){var a,o,s,i,c,l,p,d,m,h=arguments;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:r,userData:n,sendConfirmationEmail:c=!(h.length>3)||void 0===h[3]||h[3],timestamp:new Date().toISOString()}),e.next=4,f.auth.admin.createUser({email:r,password:t,user_metadata:n,email_confirm:!1});case 4:if(p=(l=e.sent).data,d=l.error,console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!p,hasUser:!!(null!=p&&p.user),userId:null==p||null==(a=p.user)?void 0:a.id,userEmail:null==p||null==(o=p.user)?void 0:o.email,emailConfirmed:null==p||null==(s=p.user)?void 0:s.email_confirmed_at,userMetadata:null==p||null==(i=p.user)?void 0:i.user_metadata,error:null==d?void 0:d.message,errorCode:null==d?void 0:d.status}),!d){e.next=11;break}return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:d.message,status:d.status,details:d}),e.abrupt("return",{data:null,error:d});case 11:if(!(null!=p&&p.user&&c)){e.next=20;break}return console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n..."),e.next=15,f.auth.admin.generateLink({type:"signup",email:r,password:t,options:{redirectTo:"".concat("http://localhost:3000","/auth/confirmed")}});case 15:(m=e.sent.error)?console.error("⚠️ Error enviando email de confirmaci\xf3n:",m):console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),e.next=21;break;case 20:null!=p&&p.user&&!c&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");case 21:return e.abrupt("return",{data:p,error:null});case 22:case"end":return e.stop()}},e)})),function(e,r,t){return n.apply(this,arguments)})},{key:"sendConfirmationEmailForUser",value:(i=(0,a.A)(u().mark(function e(r){var t,n,a,o,s;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",r),e.prev=1,e.next=4,f.auth.admin.getUserById(r);case 4:if(n=(t=e.sent).data,!((a=t.error)||!(null!=n&&n.user))){e.next=10;break}return console.error("Error obteniendo datos del usuario:",a),e.abrupt("return",{success:!1,error:"Usuario no encontrado"});case 10:return o=n.user,e.next=13,f.auth.admin.updateUserById(o.id,{email_confirm:!0,user_metadata:d(d({},o.user_metadata),{},{payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()})});case 13:if(!(s=e.sent.error)){e.next=18;break}return console.error("⚠️ Error confirmando email del usuario:",s),e.abrupt("return",{success:!1,error:s.message});case 18:return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),e.abrupt("return",{success:!0});case 22:return e.prev=22,e.t0=e.catch(1),console.error("Error en sendConfirmationEmailForUser:",e.t0),e.abrupt("return",{success:!1,error:e.t0 instanceof Error?e.t0.message:"Error desconocido"});case 26:case"end":return e.stop()}},e,null,[[1,22]])})),function(e){return i.apply(this,arguments)})},{key:"sendConfirmationEmail",value:(c=(0,a.A)(u().mark(function e(r,t){var n;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",r),e.next=3,f.auth.admin.generateLink({type:"signup",email:r,password:t,options:{redirectTo:"".concat("http://localhost:3000","/auth/confirmed")}});case 3:if(!(n=e.sent.error)){e.next=10;break}return console.error("⚠️ Error enviando email de confirmaci\xf3n:",n),e.abrupt("return",{success:!1,error:n.message});case 10:return console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),e.abrupt("return",{success:!0});case 12:case"end":return e.stop()}},e)})),function(e,r){return c.apply(this,arguments)})},{key:"createUserProfile",value:(l=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("user_profiles").insert([r]).select().single();case 2:if(n=(t=e.sent).data,!(a=t.error)){e.next=8;break}throw console.error("Error creating user profile:",a),Error("Failed to create user profile: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(e){return l.apply(this,arguments)})},{key:"upsertUserProfile",value:(p=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("user_profiles").upsert([r],{onConflict:"user_id"}).select().single();case 2:if(n=(t=e.sent).data,!(a=t.error)){e.next=8;break}throw console.error("Error upserting user profile:",a),Error("Failed to upsert user profile: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(e){return p.apply(this,arguments)})},{key:"logPlanChange",value:(m=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("user_plan_history").insert([r]).select().single();case 2:if(n=(t=e.sent).data,!(a=t.error)){e.next=8;break}throw console.error("Error logging plan change:",a),Error("Failed to log plan change: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(e){return m.apply(this,arguments)})},{key:"logFeatureAccess",value:(h=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("feature_access_log").insert([r]).select().single();case 2:if(n=(t=e.sent).data,!(a=t.error)){e.next=8;break}throw console.error("Error logging feature access:",a),Error("Failed to log feature access: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(e){return h.apply(this,arguments)})},{key:"getUserProfile",value:(g=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("user_profiles").select("*").eq("user_id",r).single();case 2:if(n=(t=e.sent).data,!((a=t.error)&&"PGRST116"!==a.code)){e.next=8;break}throw console.error("Error fetching user profile:",a),Error("Failed to fetch user profile: ".concat(a.message));case 8:return e.abrupt("return",n);case 9:case"end":return e.stop()}},e)})),function(e){return g.apply(this,arguments)})},{key:"updateTransactionWithUser",value:(v=(0,a.A)(u().mark(function e(r,t){var n;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("stripe_transactions").update({user_id:t,updated_at:new Date().toISOString()}).eq("id",r);case 2:if(!(n=e.sent.error)){e.next=7;break}throw console.error("Error updating transaction with user_id:",n),Error("Failed to update transaction: ".concat(n.message));case 7:case"end":return e.stop()}},e)})),function(e,r){return v.apply(this,arguments)})},{key:"activateTransaction",value:(b=(0,a.A)(u().mark(function e(r){var t;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",r);case 2:if(!(t=e.sent.error)){e.next=7;break}throw console.error("Error activating transaction:",t),Error("Failed to activate transaction: ".concat(t.message));case 7:case"end":return e.stop()}},e)})),function(e){return b.apply(this,arguments)})},{key:"getDocumentsCount",value:(E=(0,a.A)(u().mark(function e(r){var t,n,a;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",r);case 2:if(n=(t=e.sent).count,!(a=t.error)){e.next=8;break}return console.error("Error getting documents count:",a),e.abrupt("return",0);case 8:return e.abrupt("return",n||0);case 9:case"end":return e.stop()}},e)})),function(e){return E.apply(this,arguments)})},{key:"getUserByEmail",value:(w=(0,a.A)(u().mark(function e(r){var t,n,a,o;return u().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.auth.admin.listUsers();case 3:if(n=(t=e.sent).data.users,!(a=t.error)){e.next=9;break}throw console.error("Error getting user by email:",a),Error("Failed to get user by email: ".concat(a.message));case 9:if(!(!n||0===n.length)){e.next=11;break}return e.abrupt("return",null);case 11:if(o=n.find(function(e){return e.email===r})){e.next=14;break}return e.abrupt("return",null);case 14:return e.abrupt("return",{id:o.id,email:o.email,email_confirmed_at:o.email_confirmed_at});case 17:throw e.prev=17,e.t0=e.catch(0),console.error("Error in getUserByEmail:",e.t0),e.t0;case 21:case"end":return e.stop()}},e,null,[[0,17]])})),function(e){return w.apply(this,arguments)})}]),x}()}}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2390],{3243:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(69940);function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],u=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}(e,t)||(0,n.A)(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},6066:(e,t,r)=>{"use strict";var n=r(98790);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},6639:(e,t,r)=>{"use strict";function n(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{A:()=>n})},7057:(e,t,r)=>{"use strict";r.d(t,{Bd:()=>p,Cq:()=>x,Gb:()=>w,K8:()=>I,LJ:()=>v,Oi:()=>b,PB:()=>j,Rn:()=>B,U9:()=>k,WJ:()=>f,XR:()=>y,c2:()=>U,cY:()=>A,dS:()=>S,lA:()=>g,pF:()=>m,uR:()=>h,yy:()=>E});var n=r(59539),i=r(11157),a=r(33311),o=r(28295),s=r.n(o),u=r(44893),c=r(57483),l=r(73476);function f(e){return Math.round(Date.now()/1e3)+e}function h(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}var p=function(){return"undefined"!=typeof document},d={tested:!1,writable:!1},v=function(){if(!p())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(d.tested)return d.writable;var e="lswt-".concat(Math.random()).concat(Math.random());try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),d.tested=!0,d.writable=!0}catch(e){d.tested=!0,d.writable=!1}return d.writable};function y(e){var t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach(function(e,r){t[r]=e})}catch(e){}return r.searchParams.forEach(function(e,r){t[r]=e}),t}var g=function(e){var t;return t=e||("undefined"==typeof fetch?function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Promise.resolve().then(r.bind(r,49539)).then(function(e){return e.default.apply(void 0,t)})}:fetch),function(){return t.apply(void 0,arguments)}},m=function(e){return"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json},b=function(){var e=(0,a.A)(s().mark(function e(t,r,n){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.setItem(r,JSON.stringify(n));case 2:case"end":return e.stop()}},e)}));return function(t,r,n){return e.apply(this,arguments)}}(),k=function(){var e=(0,a.A)(s().mark(function e(t,r){var n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getItem(r);case 2:if(n=e.sent){e.next=5;break}return e.abrupt("return",null);case 5:return e.prev=5,e.abrupt("return",JSON.parse(n));case 9:return e.prev=9,e.t0=e.catch(5),e.abrupt("return",n);case 12:case"end":return e.stop()}},e,null,[[5,9]])}));return function(t,r){return e.apply(this,arguments)}}(),w=function(){var e=(0,a.A)(s().mark(function e(t,r){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.removeItem(r);case 2:case"end":return e.stop()}},e)}));return function(t,r){return e.apply(this,arguments)}}(),A=(0,n.A)(function e(){var t=this;(0,i.A)(this,e),this.promise=new e.promiseConstructor(function(e,r){t.resolve=e,t.reject=r})});function x(e){var t=e.split(".");if(3!==t.length)throw new c.xL("Invalid JWT structure");for(var r=0;r<t.length;r++)if(!u.Jj.test(t[r]))throw new c.xL("JWT not in base64url format");return{header:JSON.parse((0,l.uE)(t[0])),payload:JSON.parse((0,l.uE)(t[1])),signature:(0,l.xf)(t[2]),raw:{header:t[0],payload:t[1]}}}function E(e){return O.apply(this,arguments)}function O(){return(O=(0,a.A)(s().mark(function e(t){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,new Promise(function(e){setTimeout(function(){return e(null)},t)});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function j(e,t){return new Promise(function(r,n){(0,a.A)(s().mark(function i(){var a,o;return s().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:a=0;case 1:if(!(a<1/0)){i.next=19;break}return i.prev=2,i.next=5,e(a);case 5:if(o=i.sent,t(a,null,o)){i.next=9;break}return r(o),i.abrupt("return");case 9:i.next=16;break;case 11:if(i.prev=11,i.t0=i.catch(2),t(a,i.t0)){i.next=16;break}return n(i.t0),i.abrupt("return");case 16:a++,i.next=1;break;case 19:case"end":return i.stop()}},i,null,[[2,11]])}))()})}function _(e){return("0"+e.toString(16)).substr(-2)}function T(){return(T=(0,a.A)(s().mark(function e(t){var r,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=new TextEncoder().encode(t),e.next=4,crypto.subtle.digest("SHA-256",r);case 4:return n=new Uint8Array(e.sent),e.abrupt("return",Array.from(n).map(function(e){return String.fromCharCode(e)}).join(""));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function P(){return(P=(0,a.A)(s().mark(function e(t){var r;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder){e.next=4;break}return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e.abrupt("return",t);case 4:return e.next=6,function(e){return T.apply(this,arguments)}(t);case 6:return r=e.sent,e.abrupt("return",btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""));case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function S(e,t){return R.apply(this,arguments)}function R(){return(R=(0,a.A)(s().mark(function e(t,r){var n,i,a,o,u,c=arguments;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=c.length>2&&void 0!==c[2]&&c[2],a=i=function(){var e=new Uint32Array(56);if("undefined"==typeof crypto){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=t.length,n="",i=0;i<56;i++)n+=t.charAt(Math.floor(Math.random()*r));return n}return crypto.getRandomValues(e),Array.from(e,_).join("")}(),n&&(a+="/PASSWORD_RECOVERY"),e.next=6,b(t,"".concat(r,"-code-verifier"),a);case 6:return e.next=8,function(e){return P.apply(this,arguments)}(i);case 8:return o=e.sent,u=i===o?"plain":"s256",e.abrupt("return",[o,u]);case 11:case"end":return e.stop()}},e)}))).apply(this,arguments)}A.promiseConstructor=Promise;var C=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function B(e){var t=e.headers.get(u.sk);if(!t||!t.match(C))return null;try{return new Date("".concat(t,"T00:00:00.0Z"))}catch(e){return null}}function U(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}function I(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}},8644:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(44686),i=r(63730);function a(e,t,r){return(a=!function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}()?function(e,t,r){var n=[null];n.push.apply(n,t);var a=new(Function.bind.apply(e,n));return r&&(0,i.A)(a,r.prototype),a}:Reflect.construct.bind()).apply(null,arguments)}function o(e){var t="function"==typeof Map?new Map:void 0;return(o=function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return a(e,arguments,(0,n.A)(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),(0,i.A)(r,e)})(e)}},9751:function(e,t,r){"use strict";var n=r(43458),i=r(80851),a=r(98557),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=o(r(88293));t.default=function(){function e(t,r){var n=r.headers,a=r.schema,o=r.fetch;i(this,e),this.url=t,this.headers=void 0===n?{}:n,this.schema=a,this.fetch=o}return a(e,[{key:"select",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.head,n=t.count,i=!1,a=(null!=e?e:"*").split("").map(function(e){return/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)}).join("");return this.url.searchParams.set("select",a),n&&(this.headers.Prefer="count=".concat(n)),new s.default({method:void 0!==r&&r?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}},{key:"insert",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.count,i=t.defaultToNull,a=[];if(this.headers.Prefer&&a.push(this.headers.Prefer),r&&a.push("count=".concat(r)),void 0===i||i||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){var o=e.reduce(function(e,t){return e.concat(Object.keys(t))},[]);if(o.length>0){var u=n(new Set(o)).map(function(e){return'"'.concat(e,'"')});this.url.searchParams.set("columns",u.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}},{key:"upsert",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.onConflict,i=t.ignoreDuplicates,a=t.count,o=t.defaultToNull,u=["resolution=".concat(void 0!==i&&i?"ignore":"merge","-duplicates")];if(void 0!==r&&this.url.searchParams.set("on_conflict",r),this.headers.Prefer&&u.push(this.headers.Prefer),a&&u.push("count=".concat(a)),void 0===o||o||u.push("missing=default"),this.headers.Prefer=u.join(","),Array.isArray(e)){var c=e.reduce(function(e,t){return e.concat(Object.keys(t))},[]);if(c.length>0){var l=n(new Set(c)).map(function(e){return'"'.concat(e,'"')});this.url.searchParams.set("columns",l.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}},{key:"update",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.count,n=[];return this.headers.Prefer&&n.push(this.headers.Prefer),r&&n.push("count=".concat(r)),this.headers.Prefer=n.join(","),new s.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}},{key:"delete",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.count,r=[];return t&&r.push("count=".concat(t)),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new s.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}]),e}()},10631:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(78321),i=r(69940);function a(e){return function(e){if(Array.isArray(e))return(0,n.A)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,i.A)(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},11157:(e,t,r)=>{"use strict";function n(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{A:()=>n})},16864:(e,t,r)=>{"use strict";function n(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}r.d(t,{V:()=>n})},25538:(e,t)=>{"use strict";t.qg=function(e,t){var r=new s,n=e.length;if(n<2)return r;var i=(null==t?void 0:t.decode)||l,a=0;do{var o=e.indexOf("=",a);if(-1===o)break;var f=e.indexOf(";",a),h=-1===f?n:f;if(o>h){a=e.lastIndexOf(";",o-1)+1;continue}var p=u(e,a,o),d=c(e,o,p),v=e.slice(p,d);if(void 0===r[v]){var y=u(e,o+1,h),g=c(e,h,y),m=i(e.slice(y,g));r[v]=m}a=h+1}while(a<n);return r},t.lK=function(e,t,s){var u,c=(null==s?void 0:s.encode)||encodeURIComponent;if(!r.test(e))throw TypeError("argument name is invalid: ".concat(e));var l=c(t);if(!n.test(l))throw TypeError("argument val is invalid: ".concat(t));var f=e+"="+l;if(!s)return f;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw TypeError("option maxAge is invalid: ".concat(s.maxAge));f+="; Max-Age="+s.maxAge}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid: ".concat(s.domain));f+="; Domain="+s.domain}if(s.path){if(!a.test(s.path))throw TypeError("option path is invalid: ".concat(s.path));f+="; Path="+s.path}if(s.expires){if(u=s.expires,"[object Date]"!==o.call(u)||!Number.isFinite(s.expires.valueOf()))throw TypeError("option expires is invalid: ".concat(s.expires));f+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(f+="; HttpOnly"),s.secure&&(f+="; Secure"),s.partitioned&&(f+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":f+="; Priority=Low";break;case"medium":f+="; Priority=Medium";break;case"high":f+="; Priority=High";break;default:throw TypeError("option priority is invalid: ".concat(s.priority))}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"none":f+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid: ".concat(s.sameSite))}return f};var r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,n=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,s=function(){var e=function(){};return e.prototype=Object.create(null),e}();function u(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function l(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},26493:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},29889:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(94348);function i(e){var t=function(e,t){if("object"!==(0,n.A)(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!==(0,n.A)(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===(0,n.A)(t)?t:String(t)}},33014:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,a=u(e),o=a[0],s=a[1],c=new i((o+s)*3/4-s),l=0,f=s>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[l++]=t>>16&255,c[l++]=t>>8&255,c[l++]=255&t;return 2===s&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[l++]=255&t),1===s&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[l++]=t>>8&255,c[l++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,a=[],o=0,s=n-i;o<s;o+=16383)a.push(function(e,t,n){for(var i,a=[],o=t;o<n;o+=3)i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),a.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}(e,o,o+16383>s?s:o+16383));return 1===i?a.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&a.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,s=a.length;o<s;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),a="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,s.prototype),t}function s(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return l(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!s.isEncoding(i))throw TypeError("Unknown encoding: "+i);var a=0|p(n,i),u=o(a),c=u.write(n,i);return c!==a&&(u=u.slice(0,c)),u}if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(P(e,ArrayBuffer)||e&&P(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(P(e,SharedArrayBuffer)||e&&P(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),s.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var l=e.valueOf&&e.valueOf();if(null!=l&&l!==e)return s.from(l,t,r);var d=function(e){if(s.isBuffer(e)){var t=0|h(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?o(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(d)return d;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function l(e){return c(e),o(e<0?0:0|h(e))}function f(e){for(var t=e.length<0?0:0|h(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=s,t.SlowBuffer=function(e){return+e!=e&&(e=0),s.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(e,t,r){return(c(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},s.allocUnsafe=function(e){return l(e)},s.allocUnsafeSlow=function(e){return l(e)};function h(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||P(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return _(e).length;default:if(i)return n?-1:O(e).length;t=(""+t).toLowerCase(),i=!0}}function d(e,t,r){var i,a,o,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=S[e[a]];return i}(this,t,r);case"utf8":case"utf-8":return m(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,a=t,o=r,0===a&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(a,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function v(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){var a;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(a=r*=1)!=a&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return g(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var a,o=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,s/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var l=-1;for(a=r;a<s;a++)if(c(e,a)===c(t,-1===l?0:a-l)){if(-1===l&&(l=a),a-l+1===u)return l*o}else -1!==l&&(a-=a-l),l=-1}else for(r+u>s&&(r=s-u),a=r;a>=0;a--){for(var f=!0,h=0;h<u;h++)if(c(e,a+h)!==c(t,h)){f=!1;break}if(f)return a}return -1}s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(P(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),P(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=s.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(P(a,Uint8Array)&&(a=s.from(a)),!s.isBuffer(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)v(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)v(this,t,t+3),v(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)v(this,t,t+7),v(this,t+1,t+6),v(this,t+2,t+5),v(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?m(this,0,e):d.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},a&&(s.prototype[a]=s.prototype.inspect),s.prototype.compare=function(e,t,r,n,i){if(P(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,o=r-t,u=Math.min(a,o),c=this.slice(n,i),l=e.slice(t,r),f=0;f<u;++f)if(c[f]!==l[f]){a=c[f],o=l[f];break}return a<o?-1:+(o<a)},s.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)};function m(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,s,u,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:(192&(a=e[i+1]))==128&&(u=(31&c)<<6|63&a)>127&&(l=u);break;case 3:a=e[i+1],o=e[i+2],(192&a)==128&&(192&o)==128&&(u=(15&c)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:a=e[i+1],o=e[i+2],s=e[i+3],(192&a)==128&&(192&o)==128&&(192&s)==128&&(u=(15&c)<<18|(63&a)<<12|(63&o)<<6|63&s)>65535&&u<1114112&&(l=u)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}var h=n,p=h.length;if(p<=4096)return String.fromCharCode.apply(String,h);for(var d="",v=0;v<p;)d+=String.fromCharCode.apply(String,h.slice(v,v+=4096));return d}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function k(e,t,r,n,i,a){if(!s.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function w(e,t,r,n,i,a){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function A(e,t,r,n,a){return t*=1,r>>>=0,a||w(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function x(e,t,r,n,a){return t*=1,r>>>=0,a||w(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,o,s,u,c,l,f,h=this.length-t;if((void 0===r||r>h)&&(r=h),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;n>a/2&&(n=a/2);for(var o=0;o<n;++o){var s,u=parseInt(t.substr(2*o,2),16);if((s=u)!=s)break;e[r+o]=u}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,a=r,T(O(e,this.length-i),this,i,a);case"ascii":return o=t,s=r,T(j(e),this,o,s);case"latin1":case"binary":return function(e,t,r,n){return T(j(t),e,r,n)}(this,e,t,r);case"base64":return u=t,c=r,T(_(e),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=t,f=r,T(function(e,t){for(var r,n,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(e,this.length-l),this,l,f);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},s.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},s.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},s.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;k(this,e,t,r,i,0)}var a=1,o=0;for(this[t]=255&e;++o<r&&(a*=256);)this[t+o]=e/a&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;k(this,e,t,r,i,0)}var a=r-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);k(this,e,t,r,i-1,-i)}var a=0,o=1,s=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/o|0)-s&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);k(this,e,t,r,i-1,-i)}var a=r-1,o=1,s=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/o|0)-s&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||k(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,r){return A(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return A(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return x(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return x(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(!s.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var a=i-1;a>=0;--a)e[a+t]=this[a+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,a=e.charCodeAt(0);("utf8"===n&&a<128||"latin1"===n)&&(e=a)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=s.isBuffer(e)?e:s.from(e,n),u=o.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%u]}return this};var E=/[^+/0-9A-Za-z-_]/g;function O(e,t){t=t||1/0;for(var r,n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function j(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function _(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(E,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function T(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function P(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var S=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var a,o,s=8*i-n-1,u=(1<<s)-1,c=u>>1,l=-7,f=r?i-1:0,h=r?-1:1,p=e[t+f];for(f+=h,a=p&(1<<-l)-1,p>>=-l,l+=s;l>0;a=256*a+e[t+f],f+=h,l-=8);for(o=a&(1<<-l)-1,a>>=-l,l+=n;l>0;o=256*o+e[t+f],f+=h,l-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),a-=c}return(p?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,s,u,c=8*a-i-1,l=(1<<c)-1,f=l>>1,h=5960464477539062e-23*(23===i),p=n?0:a-1,d=n?1:-1,v=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(s=+!!isNaN(t),o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=h/u:t+=h*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=l?(s=0,o=l):o+f>=1?(s=(t*u-1)*Math.pow(2,i),o+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+p]=255&s,p+=d,s/=256,i-=8);for(o=o<<i|s,c+=i;c>0;e[r+p]=255&o,p+=d,o/=256,c-=8);e[r+p-d]|=128*v}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//",e.exports=n(72)}()},33311:(e,t,r)=>{"use strict";function n(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){r(e);return}s.done?t(u):Promise.resolve(u).then(n,i)}function i(e){return function(){var t=this,r=arguments;return new Promise(function(i,a){var o=e.apply(t,r);function s(e){n(o,i,a,s,u,"next",e)}function u(e){n(o,i,a,s,u,"throw",e)}s(void 0)})}}r.d(t,{A:()=>i})},34601:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;var i=n(r(73991));t.PostgrestClient=i.default;var a=n(r(9751));t.PostgrestQueryBuilder=a.default;var o=n(r(88293));t.PostgrestFilterBuilder=o.default;var s=n(r(58299));t.PostgrestTransformBuilder=s.default;var u=n(r(97107));t.PostgrestBuilder=u.default;var c=n(r(67086));t.PostgrestError=c.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:a.default,PostgrestFilterBuilder:o.default,PostgrestTransformBuilder:s.default,PostgrestBuilder:u.default,PostgrestError:c.default}},37711:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(29889);function i(e,t,r){return(t=(0,n.A)(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},41779:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(33311),i=r(11157),a=r(59539),o=r(28295),s=r.n(o),u=r(86628),c=r(7057),l=r(57483),f=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r},h=function(){var e,t,r,o,h,p,d,v,y,g;function m(e){var t=e.url,r=e.headers,n=e.fetch;(0,i.A)(this,m),this.url=void 0===t?"":t,this.headers=void 0===r?{}:r,this.fetch=(0,c.lA)(n),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}return(0,a.A)(m,[{key:"signOut",value:(e=(0,n.A)(s().mark(function e(t){var r,n=arguments;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>1&&void 0!==n[1]?n[1]:"global",e.prev=1,e.next=4,(0,u.vE)(this.fetch,"POST","".concat(this.url,"/logout?scope=").concat(r),{headers:this.headers,jwt:t,noResolveJson:!0});case 4:return e.abrupt("return",{data:null,error:null});case 7:if(e.prev=7,e.t0=e.catch(1),!(0,l.HY)(e.t0)){e.next=11;break}return e.abrupt("return",{data:null,error:e.t0});case 11:throw e.t0;case 12:case"end":return e.stop()}},e,this,[[1,7]])})),function(t){return e.apply(this,arguments)})},{key:"inviteUserByEmail",value:(t=(0,n.A)(s().mark(function e(t){var r,n=arguments;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>1&&void 0!==n[1]?n[1]:{},e.prev=1,e.next=4,(0,u.vE)(this.fetch,"POST","".concat(this.url,"/invite"),{body:{email:t,data:r.data},headers:this.headers,redirectTo:r.redirectTo,xform:u.Cl});case 4:return e.abrupt("return",e.sent);case 7:if(e.prev=7,e.t0=e.catch(1),!(0,l.HY)(e.t0)){e.next=11;break}return e.abrupt("return",{data:{user:null},error:e.t0});case 11:throw e.t0;case 12:case"end":return e.stop()}},e,this,[[1,7]])})),function(e){return t.apply(this,arguments)})},{key:"generateLink",value:(r=(0,n.A)(s().mark(function e(t){var r,n,i;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,r=t.options,i=Object.assign(Object.assign({},n=f(t,["options"])),r),"newEmail"in n&&(i.new_email=null==n?void 0:n.newEmail,delete i.newEmail),e.next=6,(0,u.vE)(this.fetch,"POST","".concat(this.url,"/admin/generate_link"),{body:i,headers:this.headers,xform:u.EY,redirectTo:null==r?void 0:r.redirectTo});case 6:return e.abrupt("return",e.sent);case 9:if(e.prev=9,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=13;break}return e.abrupt("return",{data:{properties:null,user:null},error:e.t0});case 13:throw e.t0;case 14:case"end":return e.stop()}},e,this,[[0,9]])})),function(e){return r.apply(this,arguments)})},{key:"createUser",value:(o=(0,n.A)(s().mark(function e(t){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.vE)(this.fetch,"POST","".concat(this.url,"/admin/users"),{body:t,headers:this.headers,xform:u.Cl});case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:{user:null},error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e){return o.apply(this,arguments)})},{key:"listUsers",value:(h=(0,n.A)(s().mark(function e(t){var r,n,i,a,o,c,f,h,p,d,v,y;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,h={nextPage:null,lastPage:0,total:0},e.next=4,(0,u.vE)(this.fetch,"GET","".concat(this.url,"/admin/users"),{headers:this.headers,noResolveJson:!0,query:{page:null!=(n=null==(r=null==t?void 0:t.page)?void 0:r.toString())?n:"",per_page:null!=(a=null==(i=null==t?void 0:t.perPage)?void 0:i.toString())?a:""},xform:u.w4});case 4:if(!(p=e.sent).error){e.next=7;break}throw p.error;case 7:return e.next=9,p.json();case 9:return d=e.sent,v=null!=(o=p.headers.get("x-total-count"))?o:0,(y=null!=(f=null==(c=p.headers.get("link"))?void 0:c.split(","))?f:[]).length>0&&(y.forEach(function(e){var t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);h["".concat(r,"Page")]=t}),h.total=parseInt(v)),e.abrupt("return",{data:Object.assign(Object.assign({},d),h),error:null});case 16:if(e.prev=16,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=20;break}return e.abrupt("return",{data:{users:[]},error:e.t0});case 20:throw e.t0;case 21:case"end":return e.stop()}},e,this,[[0,16]])})),function(e){return h.apply(this,arguments)})},{key:"getUserById",value:(p=(0,n.A)(s().mark(function e(t){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.vE)(this.fetch,"GET","".concat(this.url,"/admin/users/").concat(t),{headers:this.headers,xform:u.Cl});case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:{user:null},error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e){return p.apply(this,arguments)})},{key:"updateUserById",value:(d=(0,n.A)(s().mark(function e(t,r){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.vE)(this.fetch,"PUT","".concat(this.url,"/admin/users/").concat(t),{body:r,headers:this.headers,xform:u.Cl});case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:{user:null},error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e,t){return d.apply(this,arguments)})},{key:"deleteUser",value:(v=(0,n.A)(s().mark(function e(t){var r,n=arguments;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>1&&void 0!==n[1]&&n[1],e.prev=1,e.next=4,(0,u.vE)(this.fetch,"DELETE","".concat(this.url,"/admin/users/").concat(t),{headers:this.headers,body:{should_soft_delete:r},xform:u.Cl});case 4:return e.abrupt("return",e.sent);case 7:if(e.prev=7,e.t0=e.catch(1),!(0,l.HY)(e.t0)){e.next=11;break}return e.abrupt("return",{data:{user:null},error:e.t0});case 11:throw e.t0;case 12:case"end":return e.stop()}},e,this,[[1,7]])})),function(e){return v.apply(this,arguments)})},{key:"_listFactors",value:(y=(0,n.A)(s().mark(function e(t){var r,n,i;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.vE)(this.fetch,"GET","".concat(this.url,"/admin/users/").concat(t.userId,"/factors"),{headers:this.headers,xform:function(e){return{data:{factors:e},error:null}}});case 3:return n=(r=e.sent).data,i=r.error,e.abrupt("return",{data:n,error:i});case 9:if(e.prev=9,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=13;break}return e.abrupt("return",{data:null,error:e.t0});case 13:throw e.t0;case 14:case"end":return e.stop()}},e,this,[[0,9]])})),function(e){return y.apply(this,arguments)})},{key:"_deleteFactor",value:(g=(0,n.A)(s().mark(function e(t){var r;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.vE)(this.fetch,"DELETE","".concat(this.url,"/admin/users/").concat(t.userId,"/factors/").concat(t.id),{headers:this.headers});case 3:return r=e.sent,e.abrupt("return",{data:r,error:null});case 7:if(e.prev=7,e.t0=e.catch(0),!(0,l.HY)(e.t0)){e.next=11;break}return e.abrupt("return",{data:null,error:e.t0});case 11:throw e.t0;case 12:case"end":return e.stop()}},e,this,[[0,7]])})),function(e){return g.apply(this,arguments)})}]),m}()},44686:(e,t,r)=>{"use strict";function n(e){return(n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}r.d(t,{A:()=>n})},44893:(e,t,r)=>{"use strict";r.d(t,{$W:()=>p,AQ:()=>u,Bo:()=>f,CT:()=>o,Jj:()=>h,Rn:()=>c,YI:()=>a,a4:()=>i,nf:()=>s,sk:()=>l});var n=r(98132),i=3e4,a=3,o=9e4,s="http://localhost:9999",u="supabase.auth.token",c={"X-Client-Info":"gotrue-js/".concat(n.r)},l="X-Supabase-Api-Version",f={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},h=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,p=6e5},45408:(e,t,r)=>{"use strict";r.d(t,{UU:()=>eI});var n,i,a,o,s,u,c,l,f,h,p,d=r(11157),v=r(59539),y=r(28295),g=r.n(y),m=function(e){var t;return t=e||("undefined"==typeof fetch?function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Promise.resolve().then(r.bind(r,49539)).then(function(e){return e.default.apply(void 0,t)})}:fetch),function(){return t.apply(void 0,arguments)}},b=r(49077),k=r(90064),w=r(44686),A=r(8644);function x(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=(0,w.A)(e);return r=t?Reflect.construct(n,arguments,(0,w.A)(this).constructor):n.apply(this,arguments),(0,k.A)(this,r)}}var E=function(e){(0,b.A)(r,e);var t=x(r);function r(e){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"FunctionsError",a=arguments.length>2?arguments[2]:void 0;return(0,d.A)(this,r),(n=t.call(this,e)).name=i,n.context=a,n}return(0,v.A)(r)}((0,A.A)(Error)),O=function(e){(0,b.A)(r,e);var t=x(r);function r(e){return(0,d.A)(this,r),t.call(this,"Failed to send a request to the Edge Function","FunctionsFetchError",e)}return(0,v.A)(r)}(E),j=function(e){(0,b.A)(r,e);var t=x(r);function r(e){return(0,d.A)(this,r),t.call(this,"Relay Error invoking the Edge Function","FunctionsRelayError",e)}return(0,v.A)(r)}(E),_=function(e){(0,b.A)(r,e);var t=x(r);function r(e){return(0,d.A)(this,r),t.call(this,"Edge Function returned a non-2xx status code","FunctionsHttpError",e)}return(0,v.A)(r)}(E);!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(n||(n={}));var T=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.headers,a=r.customFetch,o=r.region,s=void 0===o?n.Any:o;(0,d.A)(this,e),this.url=t,this.headers=void 0===i?{}:i,this.region=s,this.fetch=m(a)}return(0,v.A)(e,[{key:"setAuth",value:function(e){this.headers.Authorization="Bearer ".concat(e)}},{key:"invoke",value:function(e){var t,r,n,i,a,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=this,r=void 0,n=void 0,i=g().mark(function t(){var r,n,i,s,u,c,l,f,h,p;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,r=o.headers,n=o.method,i=o.body,s={},(u=o.region)||(u=this.region),u&&"any"!==u&&(s["x-region"]=u),i&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(s["Content-Type"]="application/octet-stream",c=i):"string"==typeof i?(s["Content-Type"]="text/plain",c=i):"undefined"!=typeof FormData&&i instanceof FormData?c=i:(s["Content-Type"]="application/json",c=JSON.stringify(i))),t.next=9,this.fetch("".concat(this.url,"/").concat(e),{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},s),this.headers),r),body:c}).catch(function(e){throw new O(e)});case 9:if(!((f=(l=t.sent).headers.get("x-relay-error"))&&"true"===f)){t.next=13;break}throw new j(l);case 13:if(l.ok){t.next=15;break}throw new _(l);case 15:if("application/json"!==(h=(null!=(a=l.headers.get("Content-Type"))?a:"text/plain").split(";")[0].trim())){t.next=22;break}return t.next=19,l.json();case 19:case 25:case 35:p=t.sent,t.next=41;break;case 22:if("application/octet-stream"!==h){t.next=28;break}return t.next=25,l.blob();case 28:if("text/event-stream"!==h){t.next=32;break}p=l,t.next=41;break;case 32:if("multipart/form-data"!==h){t.next=38;break}return t.next=35,l.formData();case 38:return t.next=40,l.text();case 40:p=t.sent;case 41:return t.abrupt("return",{data:p,error:null});case 44:return t.prev=44,t.t0=t.catch(0),t.abrupt("return",{data:null,error:t.t0});case 47:case"end":return t.stop()}},t,this,[[0,44]])}),new(n||(n=Promise))(function(e,a){function o(e){try{u(i.next(e))}catch(e){a(e)}}function s(e){try{u(i.throw(e))}catch(e){a(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(o,s)}u((i=i.apply(t,r||[])).next())})}}]),e}(),P=r(34601),S=P.PostgrestClient;P.PostgrestQueryBuilder,P.PostgrestFilterBuilder,P.PostgrestTransformBuilder,P.PostgrestBuilder,P.PostgrestError;var R=r(33311),C={"X-Client-Info":"realtime-js/".concat("2.11.2")};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(i||(i={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(a||(a={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(o||(o={})),(s||(s={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(u||(u={}));var B=function(){function e(){(0,d.A)(this,e),this.HEADER_LENGTH=1}return(0,v.A)(e,[{key:"decode",value:function(e,t){return t(e.constructor===ArrayBuffer?this._binaryDecode(e):"string"==typeof e?JSON.parse(e):{})}},{key:"_binaryDecode",value:function(e){var t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}},{key:"_decodeBroadcast",value:function(e,t,r){var n=t.getUint8(1),i=t.getUint8(2),a=this.HEADER_LENGTH+2,o=r.decode(e.slice(a,a+n));a+=n;var s=r.decode(e.slice(a,a+i));return a+=i,{ref:null,topic:o,event:s,payload:JSON.parse(r.decode(e.slice(a,e.byteLength)))}}}]),e}(),U=function(){function e(t,r){(0,d.A)(this,e),this.callback=t,this.timerCalc=r,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=r}return(0,v.A)(e,[{key:"reset",value:function(){this.tries=0,clearTimeout(this.timer)}},{key:"scheduleTimeout",value:function(){var e=this;clearTimeout(this.timer),this.timer=setTimeout(function(){e.tries=e.tries+1,e.callback()},this.timerCalc(this.tries+1))}}]),e}();!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(c||(c={}));var I=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=null!=(r=n.skipTypes)?r:[];return Object.keys(t).reduce(function(r,n){return r[n]=L(n,e,t,i),r},{})},L=function(e,t,r,n){var i=t.find(function(t){return t.name===e}),a=null==i?void 0:i.type,o=r[e];return a&&!n.includes(a)?M(a,o):N(o)},M=function(e,t){if("_"===e.charAt(0))return F(t,e.slice(1,e.length));switch(e){case c.bool:return D(t);case c.float4:case c.float8:case c.int2:case c.int4:case c.int8:case c.numeric:case c.oid:return J(t);case c.json:case c.jsonb:return q(t);case c.timestamp:return H(t);case c.abstime:case c.date:case c.daterange:case c.int4range:case c.int8range:case c.money:case c.reltime:case c.text:case c.time:case c.timestamptz:case c.timetz:case c.tsrange:case c.tstzrange:default:return N(t)}},N=function(e){return e},D=function(e){switch(e){case"t":return!0;case"f":return!1;default:return e}},J=function(e){if("string"==typeof e){var t=parseFloat(e);if(!Number.isNaN(t))return t}return e},q=function(e){if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log("JSON parse error: ".concat(e))}return e},F=function(e,t){if("string"!=typeof e)return e;var r=e.length-1,n=e[r];if("{"===e[0]&&"}"===n){var i,a=e.slice(1,r);try{i=JSON.parse("["+a+"]")}catch(e){i=a?a.split(","):[]}return i.map(function(e){return M(t,e)})}return e},H=function(e){return"string"==typeof e?e.replace(" ","T"):e},z=function(e){var t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")},W=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e4;(0,d.A)(this,e),this.channel=t,this.event=r,this.payload=n,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}return(0,v.A)(e,[{key:"resend",value:function(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}},{key:"send",value:function(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}},{key:"updatePayload",value:function(e){this.payload=Object.assign(Object.assign({},this.payload),e)}},{key:"receive",value:function(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}},{key:"startTimeout",value:function(){var e=this;this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},function(t){e._cancelRefEvent(),e._cancelTimeout(),e.receivedResp=t,e._matchReceive(t)}),this.timeoutTimer=setTimeout(function(){e.trigger("timeout",{})},this.timeout))}},{key:"trigger",value:function(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}},{key:"destroy",value:function(){this._cancelRefEvent(),this._cancelTimeout()}},{key:"_cancelRefEvent",value:function(){this.refEvent&&this.channel._off(this.refEvent,{})}},{key:"_cancelTimeout",value:function(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}},{key:"_matchReceive",value:function(e){var t=e.status,r=e.response;this.recHooks.filter(function(e){return e.status===t}).forEach(function(e){return e.callback(r)})}},{key:"_hasReceived",value:function(e){return this.receivedResp&&this.receivedResp.status===e}}]),e}(),K=r(10631);!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(l||(l={}));var G=function(){function e(t,r){var n=this;(0,d.A)(this,e),this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:function(){},onLeave:function(){},onSync:function(){}};var i=(null==r?void 0:r.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(i.state,{},function(t){var r=n.caller,i=r.onJoin,a=r.onLeave,o=r.onSync;n.joinRef=n.channel._joinRef(),n.state=e.syncState(n.state,t,i,a),n.pendingDiffs.forEach(function(t){n.state=e.syncDiff(n.state,t,i,a)}),n.pendingDiffs=[],o()}),this.channel._on(i.diff,{},function(t){var r=n.caller,i=r.onJoin,a=r.onLeave,o=r.onSync;n.inPendingSyncState()?n.pendingDiffs.push(t):(n.state=e.syncDiff(n.state,t,i,a),o())}),this.onJoin(function(e,t,r){n.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave(function(e,t,r){n.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(function(){n.channel._trigger("presence",{event:"sync"})})}return(0,v.A)(e,[{key:"onJoin",value:function(e){this.caller.onJoin=e}},{key:"onLeave",value:function(e){this.caller.onLeave=e}},{key:"onSync",value:function(e){this.caller.onSync=e}},{key:"inPendingSyncState",value:function(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}],[{key:"syncState",value:function(e,t,r,n){var i=this.cloneDeep(e),a=this.transformState(t),o={},s={};return this.map(i,function(e,t){a[e]||(s[e]=t)}),this.map(a,function(e,t){var r=i[e];if(r){var n=t.map(function(e){return e.presence_ref}),a=r.map(function(e){return e.presence_ref}),u=t.filter(function(e){return 0>a.indexOf(e.presence_ref)}),c=r.filter(function(e){return 0>n.indexOf(e.presence_ref)});u.length>0&&(o[e]=u),c.length>0&&(s[e]=c)}else o[e]=t}),this.syncDiff(i,{joins:o,leaves:s},r,n)}},{key:"syncDiff",value:function(e,t,r,n){var i=this,a={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)},o=a.joins,s=a.leaves;return r||(r=function(){}),n||(n=function(){}),this.map(o,function(t,n){var a=null!=(o=e[t])?o:[];if(e[t]=i.cloneDeep(n),a.length>0){var o,s,u=e[t].map(function(e){return e.presence_ref}),c=a.filter(function(e){return 0>u.indexOf(e.presence_ref)});(s=e[t]).unshift.apply(s,(0,K.A)(c))}r(t,a,n)}),this.map(s,function(t,r){var i=e[t];if(i){var a=r.map(function(e){return e.presence_ref});i=i.filter(function(e){return 0>a.indexOf(e.presence_ref)}),e[t]=i,n(t,i,r),0===i.length&&delete e[t]}}),e}},{key:"map",value:function(e,t){return Object.getOwnPropertyNames(e).map(function(r){return t(r,e[r])})}},{key:"transformState",value:function(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce(function(t,r){var n=e[r];return"metas"in n?t[r]=n.metas.map(function(e){return e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e}):t[r]=n,t},{})}},{key:"cloneDeep",value:function(e){return JSON.parse(JSON.stringify(e))}}]),e}();!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(f||(f={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(h||(h={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(p||(p={}));var Y=function(){var e,t,r,n;function i(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}},n=arguments.length>2?arguments[2]:void 0;(0,d.A)(this,i),this.topic=e,this.params=r,this.socket=n,this.bindings={},this.state=a.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},r.config),this.timeout=this.socket.timeout,this.joinPush=new W(this,o.join,this.params,this.timeout),this.rejoinTimer=new U(function(){return t._rejoinUntilConnected()},this.socket.reconnectAfterMs),this.joinPush.receive("ok",function(){t.state=a.joined,t.rejoinTimer.reset(),t.pushBuffer.forEach(function(e){return e.send()}),t.pushBuffer=[]}),this._onClose(function(){t.rejoinTimer.reset(),t.socket.log("channel","close ".concat(t.topic," ").concat(t._joinRef())),t.state=a.closed,t.socket._remove(t)}),this._onError(function(e){t._isLeaving()||t._isClosed()||(t.socket.log("channel","error ".concat(t.topic),e),t.state=a.errored,t.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",function(){t._isJoining()&&(t.socket.log("channel","timeout ".concat(t.topic),t.joinPush.timeout),t.state=a.errored,t.rejoinTimer.scheduleTimeout())}),this._on(o.reply,{},function(e,r){t._trigger(t._replyEventName(r),e)}),this.presence=new G(this),this.broadcastEndpointURL=z(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}return(0,v.A)(i,[{key:"subscribe",value:function(e){var t,r,n=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.timeout;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";var a,o=this.params.config,s=o.broadcast,u=o.presence,c=o.private;this._onError(function(t){return null==e?void 0:e(p.CHANNEL_ERROR,t)}),this._onClose(function(){return null==e?void 0:e(p.CLOSED)});var l={},f={broadcast:s,presence:u,postgres_changes:null!=(r=null==(t=this.bindings.postgres_changes)?void 0:t.map(function(e){return e.filter}))?r:[],private:c};return this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:f},l)),this.joinedOnce=!0,this._rejoin(i),this.joinPush.receive("ok",(a=(0,R.A)(g().mark(function t(r){var i,a,o,s,u,c,l,f,h,d,v,y,m;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(i=r.postgres_changes,n.socket.setAuth(),void 0!==i){t.next=7;break}return null==e||e(p.SUBSCRIBED),t.abrupt("return");case 7:s=null!=(a=null==(o=n.bindings.postgres_changes)?void 0:o.length)?a:0,u=[],c=0;case 11:if(!(c<s)){t.next=25;break}if(h=(f=(l=o[c]).filter).event,d=f.schema,v=f.table,y=f.filter,!((m=i&&i[c])&&m.event===h&&m.schema===d&&m.table===v&&m.filter===y)){t.next=19;break}u.push(Object.assign(Object.assign({},l),{id:m.id})),t.next=22;break;case 19:return n.unsubscribe(),null==e||e(p.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes")),t.abrupt("return");case 22:c++,t.next=11;break;case 25:return n.bindings.postgres_changes=u,e&&e(p.SUBSCRIBED),t.abrupt("return");case 28:case"end":return t.stop()}},t)})),function(e){return a.apply(this,arguments)})).receive("error",function(t){null==e||e(p.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",function(){null==e||e(p.TIMED_OUT)}),this}},{key:"presenceState",value:function(){return this.presence.state}},{key:"track",value:(e=(0,R.A)(g().mark(function e(t){var r,n=arguments;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>1&&void 0!==n[1]?n[1]:{},e.next=3,this.send({type:"presence",event:"track",payload:t},r.timeout||this.timeout);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(t){return e.apply(this,arguments)})},{key:"untrack",value:(t=(0,R.A)(g().mark(function e(){var t,r=arguments;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.next=3,this.send({type:"presence",event:"untrack"},t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"on",value:function(e,t,r){return this._on(e,t,r)}},{key:"send",value:(r=(0,R.A)(g().mark(function e(t){var r,n,i,a,o,s,u,c=this,l=arguments;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=l.length>1&&void 0!==l[1]?l[1]:{},!(!this._canPush()&&"broadcast"===t.type)){e.next=23;break}return a=t.event,o=t.payload,s={method:"POST",headers:{Authorization:this.socket.accessTokenValue?"Bearer ".concat(this.socket.accessTokenValue):"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:a,payload:o,private:this.private}]})},e.prev=5,e.next=8,this._fetchWithTimeout(this.broadcastEndpointURL,s,null!=(n=r.timeout)?n:this.timeout);case 8:return u=e.sent,e.next=11,null==(i=u.body)?void 0:i.cancel();case 11:return e.abrupt("return",u.ok?"ok":"error");case 14:if(e.prev=14,e.t0=e.catch(5),"AbortError"!==e.t0.name){e.next=20;break}return e.abrupt("return","timed out");case 20:return e.abrupt("return","error");case 21:e.next=24;break;case 23:return e.abrupt("return",new Promise(function(e){var n,i,a,o=c._push(t.type,t,r.timeout||c.timeout);"broadcast"!==t.type||(null==(a=null==(i=null==(n=c.params)?void 0:n.config)?void 0:i.broadcast)?void 0:a.ack)||e("ok"),o.receive("ok",function(){return e("ok")}),o.receive("error",function(){return e("error")}),o.receive("timeout",function(){return e("timed out")})}));case 24:case"end":return e.stop()}},e,this,[[5,14]])})),function(e){return r.apply(this,arguments)})},{key:"updateJoinPayload",value:function(e){this.joinPush.updatePayload(e)}},{key:"unsubscribe",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this.state=a.leaving;var r=function(){e.socket.log("channel","leave ".concat(e.topic)),e._trigger(o.close,"leave",e._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(function(n){var i=new W(e,o.leave,{},t);i.receive("ok",function(){r(),n("ok")}).receive("timeout",function(){r(),n("timed out")}).receive("error",function(){n("error")}),i.send(),e._canPush()||i.trigger("ok",{})})}},{key:"_fetchWithTimeout",value:(n=(0,R.A)(g().mark(function e(t,r,n){var i,a,o;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=new AbortController,a=setTimeout(function(){return i.abort()},n),e.next=4,this.socket.fetch(t,Object.assign(Object.assign({},r),{signal:i.signal}));case 4:return o=e.sent,clearTimeout(a),e.abrupt("return",o);case 7:case"end":return e.stop()}},e,this)})),function(e,t,r){return n.apply(this,arguments)})},{key:"_push",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.timeout;if(!this.joinedOnce)throw"tried to push '".concat(e,"' to '").concat(this.topic,"' before joining. Use channel.subscribe() before pushing events");var n=new W(this,e,t,r);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}},{key:"_onMessage",value:function(e,t,r){return t}},{key:"_isMember",value:function(e){return this.topic===e}},{key:"_joinRef",value:function(){return this.joinPush.ref}},{key:"_trigger",value:function(e,t,r){var n,i,a=this,s=e.toLocaleLowerCase(),u=o.close,c=o.error,l=o.leave,f=o.join;if(!(r&&[u,c,l,f].indexOf(s)>=0)||r===this._joinRef()){var h=this._onMessage(s,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(s)?null==(n=this.bindings.postgres_changes)||n.filter(function(e){var t,r,n;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(n=null==(r=e.filter)?void 0:r.event)?void 0:n.toLocaleLowerCase())===s}).map(function(e){return e.callback(h,r)}):null==(i=this.bindings[s])||i.filter(function(e){if(!["broadcast","presence","postgres_changes"].includes(s))return e.type.toLocaleLowerCase()===s;if("id"in e){var r,n,i,a,o,u,c=e.id,l=null==(r=e.filter)?void 0:r.event;return c&&(null==(n=t.ids)?void 0:n.includes(c))&&("*"===l||(null==l?void 0:l.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}var f=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===f||f===(null==(u=null==t?void 0:t.event)?void 0:u.toLocaleLowerCase())}).map(function(e){if("object"==typeof h&&"ids"in h){var t=h.data;h=Object.assign(Object.assign({},{schema:t.schema,table:t.table,commit_timestamp:t.commit_timestamp,eventType:t.type,new:{},old:{},errors:t.errors}),a._getPayloadRecords(t))}e.callback(h,r)})}}},{key:"_isClosed",value:function(){return this.state===a.closed}},{key:"_isJoined",value:function(){return this.state===a.joined}},{key:"_isJoining",value:function(){return this.state===a.joining}},{key:"_isLeaving",value:function(){return this.state===a.leaving}},{key:"_replyEventName",value:function(e){return"chan_reply_".concat(e)}},{key:"_on",value:function(e,t,r){var n=e.toLocaleLowerCase(),i={type:n,filter:t,callback:r};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}},{key:"_off",value:function(e,t){var r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(function(e){var n;return!((null==(n=e.type)?void 0:n.toLocaleLowerCase())===r&&i.isEqual(e.filter,t))}),this}},{key:"_rejoinUntilConnected",value:function(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}},{key:"_onClose",value:function(e){this._on(o.close,{},e)}},{key:"_onError",value:function(e){this._on(o.error,{},function(t){return e(t)})}},{key:"_canPush",value:function(){return this.socket.isConnected()&&this._isJoined()}},{key:"_rejoin",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.timeout;this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=a.joining,this.joinPush.resend(e))}},{key:"_getPayloadRecords",value:function(e){var t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=I(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=I(e.columns,e.old_record)),t}}],[{key:"isEqual",value:function(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var r in e)if(e[r]!==t[r])return!1;return!0}}]),i}(),V=function(){},$="undefined"!=typeof WebSocket,X=function(){var e,t,n,a,c;function l(e,t){var n,i=this;(0,d.A)(this,l),this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=C,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=V,this.conn=null,this.sendBuffer=[],this.serializer=new B,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=function(e){var t;return t=e||("undefined"==typeof fetch?function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Promise.resolve().then(r.bind(r,49539)).then(function(e){return e.default.apply(void 0,t)})}:fetch),function(){return t.apply(void 0,arguments)}},this.endPoint="".concat(e,"/").concat(s.websocket),this.httpEndpoint=z(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);var a=null==(n=null==t?void 0:t.params)?void 0:n.apikey;if(a&&(this.accessTokenValue=a,this.apiKey=a),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:function(e){return[1e3,2e3,5e3,1e4][e-1]||1e4},this.encode=(null==t?void 0:t.encode)?t.encode:function(e,t){return t(JSON.stringify(e))},this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new U((0,R.A)(g().mark(function e(){return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:i.disconnect(),i.connect();case 2:case"end":return e.stop()}},e)})),this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if(!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}return(0,v.A)(l,[{key:"connect",value:function(){var e=this;if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if($){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new Q(this.endpointURL(),void 0,{close:function(){e.conn=null}}),r.e(2715).then(r.t.bind(r,72715,23)).then(function(t){e.conn=new t.default(e.endpointURL(),void 0,{headers:e.headers}),e.setupConnection()})}}},{key:"endpointURL",value:function(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}},{key:"disconnect",value:function(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}},{key:"getChannels",value:function(){return this.channels}},{key:"removeChannel",value:(e=(0,R.A)(g().mark(function e(t){var r;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.unsubscribe();case 2:return r=e.sent,0===this.channels.length&&this.disconnect(),e.abrupt("return",r);case 5:case"end":return e.stop()}},e,this)})),function(t){return e.apply(this,arguments)})},{key:"removeAllChannels",value:(t=(0,R.A)(g().mark(function e(){var t;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.all(this.channels.map(function(e){return e.unsubscribe()}));case 2:return t=e.sent,this.disconnect(),e.abrupt("return",t);case 5:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"log",value:function(e,t,r){this.logger(e,t,r)}},{key:"connectionState",value:function(){switch(this.conn&&this.conn.readyState){case i.connecting:return u.Connecting;case i.open:return u.Open;case i.closing:return u.Closing;default:return u.Closed}}},{key:"isConnected",value:function(){return this.connectionState()===u.Open}},{key:"channel",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}},r=new Y("realtime:".concat(e),t,this);return this.channels.push(r),r}},{key:"push",value:function(e){var t=this,r=e.topic,n=e.event,i=e.payload,a=e.ref,o=function(){t.encode(e,function(e){var r;null==(r=t.conn)||r.send(e)})};this.log("push","".concat(r," ").concat(n," (").concat(a,")"),i),this.isConnected()?o():this.sendBuffer.push(o)}},{key:"setAuth",value:(n=(0,R.A)(g().mark(function e(){var t,r,n=arguments;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=n.length>0&&void 0!==n[0]?n[0]:null,e.t1){e.next=9;break}if(e.t2=this.accessToken,!e.t2){e.next=8;break}return e.next=7,this.accessToken();case 7:e.t2=e.sent;case 8:e.t1=e.t2;case 9:if(e.t0=e.t1,e.t0){e.next=12;break}e.t0=this.accessTokenValue;case 12:if(!(t=e.t0)){e.next=24;break}r=null;try{r=JSON.parse(atob(t.split(".")[1]))}catch(e){}if(!(r&&r.exp)||Math.floor(Date.now()/1e3)-r.exp<0){e.next=22;break}return this.log("auth",'InvalidJWTToken: Invalid value for JWT claim "exp" with value '.concat(r.exp)),e.abrupt("return",Promise.reject('InvalidJWTToken: Invalid value for JWT claim "exp" with value '.concat(r.exp)));case 22:this.accessTokenValue=t,this.channels.forEach(function(e){t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(o.access_token,{access_token:t})});case 24:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"sendHeartbeat",value:(a=(0,R.A)(g().mark(function e(){var t;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isConnected()){e.next=2;break}return e.abrupt("return");case 2:if(!this.pendingHeartbeatRef){e.next=7;break}return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null==(t=this.conn)||t.close(1e3,"hearbeat timeout"),e.abrupt("return");case 7:this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth();case 10:case"end":return e.stop()}},e,this)})),function(){return a.apply(this,arguments)})},{key:"flushSendBuffer",value:function(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(function(e){return e()}),this.sendBuffer=[])}},{key:"_makeRef",value:function(){var e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}},{key:"_leaveOpenTopic",value:function(e){var t=this.channels.find(function(t){return t.topic===e&&(t._isJoined()||t._isJoining())});t&&(this.log("transport",'leaving duplicate topic "'.concat(e,'"')),t.unsubscribe())}},{key:"_remove",value:function(e){this.channels=this.channels.filter(function(t){return t._joinRef()!==e._joinRef()})}},{key:"setupConnection",value:function(){var e=this;this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=function(){return e._onConnOpen()},this.conn.onerror=function(t){return e._onConnError(t)},this.conn.onmessage=function(t){return e._onConnMessage(t)},this.conn.onclose=function(t){return e._onConnClose(t)})}},{key:"_onConnMessage",value:function(e){var t=this;this.decode(e.data,function(e){var r=e.topic,n=e.event,i=e.payload,a=e.ref;a&&a===t.pendingHeartbeatRef&&(t.pendingHeartbeatRef=null),t.log("receive","".concat(i.status||""," ").concat(r," ").concat(n," ").concat(a&&"("+a+")"||""),i),t.channels.filter(function(e){return e._isMember(r)}).forEach(function(e){return e._trigger(n,i,a)}),t.stateChangeCallbacks.message.forEach(function(t){return t(e)})})}},{key:"_onConnOpen",value:(c=(0,R.A)(g().mark(function e(){var t,r=this;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.log("transport","connected to ".concat(this.endpointURL())),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?(this.workerUrl?this.log("worker","starting worker for from ".concat(this.workerUrl)):this.log("worker","starting default worker"),t=this._workerObjectUrl(this.workerUrl),this.workerRef=new Worker(t),this.workerRef.onerror=function(e){r.log("worker","worker error",e.message),r.workerRef.terminate()},this.workerRef.onmessage=function(e){"keepAlive"===e.data.event&&r.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})):(this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(function(){return r.sendHeartbeat()},this.heartbeatIntervalMs)),this.stateChangeCallbacks.open.forEach(function(e){return e()});case 5:case"end":return e.stop()}},e,this)})),function(){return c.apply(this,arguments)})},{key:"_onConnClose",value:function(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(function(t){return t(e)})}},{key:"_onConnError",value:function(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(function(t){return t(e)})}},{key:"_triggerChanError",value:function(){this.channels.forEach(function(e){return e._trigger(o.error)})}},{key:"_appendParams",value:function(e,t){if(0===Object.keys(t).length)return e;var r=e.match(/\?/)?"&":"?",n=new URLSearchParams(t);return"".concat(e).concat(r).concat(n)}},{key:"_workerObjectUrl",value:function(e){var t;if(e)t=e;else{var r=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(r)}return t}}]),l}(),Q=(0,v.A)(function e(t,r,n){(0,d.A)(this,e),this.binaryType="arraybuffer",this.onclose=function(){},this.onerror=function(){},this.onmessage=function(){},this.onopen=function(){},this.readyState=i.connecting,this.send=function(){},this.url=null,this.url=t,this.close=n.close});function Z(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=(0,w.A)(e);return r=t?Reflect.construct(n,arguments,(0,w.A)(this).constructor):n.apply(this,arguments),(0,k.A)(this,r)}}var ee=function(e){(0,b.A)(r,e);var t=Z(r);function r(e){var n;return(0,d.A)(this,r),(n=t.call(this,e)).__isStorageError=!0,n.name="StorageError",n}return(0,v.A)(r)}((0,A.A)(Error));function et(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}var er=function(e){(0,b.A)(r,e);var t=Z(r);function r(e,n){var i;return(0,d.A)(this,r),(i=t.call(this,e)).name="StorageApiError",i.status=n,i}return(0,v.A)(r,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,status:this.status}}}]),r}(ee),en=function(e){(0,b.A)(r,e);var t=Z(r);function r(e,n){var i;return(0,d.A)(this,r),(i=t.call(this,e)).name="StorageUnknownError",i.originalError=n,i}return(0,v.A)(r)}(ee),ei=r(3243),ea=function(e){var t;return t=e||("undefined"==typeof fetch?function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Promise.resolve().then(r.bind(r,49539)).then(function(e){return e.default.apply(void 0,t)})}:fetch),function(){return t.apply(void 0,arguments)}},eo=function(){var e,t,n,i;return e=void 0,t=void 0,n=void 0,i=g().mark(function e(){return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("undefined"!=typeof Response){e.next=4;break}return e.next=3,Promise.resolve().then(r.bind(r,49539));case 3:return e.abrupt("return",e.sent.Response);case 4:return e.abrupt("return",Response);case 5:case"end":return e.stop()}},e)}),new(n||(n=Promise))(function(r,a){function o(e){try{u(i.next(e))}catch(e){a(e)}}function s(e){try{u(i.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,s)}u((i=i.apply(e,t||[])).next())})},es=function e(t){if(Array.isArray(t))return t.map(function(t){return e(t)});if("function"==typeof t||t!==Object(t))return t;var r={};return Object.entries(t).forEach(function(t){var n=(0,ei.A)(t,2),i=n[0],a=n[1];r[i.replace(/([-_][a-z])/gi,function(e){return e.toUpperCase().replace(/[-_]/g,"")})]=e(a)}),r},eu=function(e,t,r,n){return new(r||(r=Promise))(function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,s)}u((n=n.apply(e,t||[])).next())})},ec=function(e){return e.msg||e.message||e.error_description||e.error||JSON.stringify(e)},el=function(e,t,r){return eu(void 0,void 0,void 0,g().mark(function n(){return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,eo();case 2:e instanceof n.sent&&!(null==r?void 0:r.noResolveJson)?e.json().then(function(r){t(new er(ec(r),e.status||500))}).catch(function(e){t(new en(ec(e),e))}):t(new en(ec(e),e));case 4:case"end":return n.stop()}},n)}))},ef=function(e,t,r,n){var i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n&&(i.body=JSON.stringify(n)),Object.assign(Object.assign({},i),r))};function eh(e,t,r,n,i,a){return eu(this,void 0,void 0,g().mark(function o(){return g().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.abrupt("return",new Promise(function(o,s){e(r,ef(t,n,i,a)).then(function(e){if(!e.ok)throw e;return(null==n?void 0:n.noResolveJson)?e:e.json()}).then(function(e){return o(e)}).catch(function(e){return el(e,s,n)})}));case 1:case"end":return o.stop()}},o)}))}function ep(e,t,r,n){return eu(this,void 0,void 0,g().mark(function i(){return g().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",eh(e,"GET",t,r,n));case 1:case"end":return i.stop()}},i)}))}function ed(e,t,r,n,i){return eu(this,void 0,void 0,g().mark(function a(){return g().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",eh(e,"POST",t,n,i,r));case 1:case"end":return a.stop()}},a)}))}function ev(e,t,r,n,i){return eu(this,void 0,void 0,g().mark(function a(){return g().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",eh(e,"DELETE",t,n,i,r));case 1:case"end":return a.stop()}},a)}))}var ey=r(33014).Buffer,eg=function(e,t,r,n){return new(r||(r=Promise))(function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,s)}u((n=n.apply(e,t||[])).next())})},em={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},eb={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1},ek=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;(0,d.A)(this,e),this.url=t,this.headers=r,this.bucketId=n,this.fetch=ea(i)}return(0,v.A)(e,[{key:"uploadOrUpdate",value:function(e,t,r,n){return eg(this,void 0,void 0,g().mark(function i(){var a,o,s,u,c,l,f,h,p;return g().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,o=Object.assign(Object.assign({},eb),n),s=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(o.upsert)}),u=o.metadata,"undefined"!=typeof Blob&&r instanceof Blob?((a=new FormData).append("cacheControl",o.cacheControl),u&&a.append("metadata",this.encodeMetadata(u)),a.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((a=r).append("cacheControl",o.cacheControl),u&&a.append("metadata",this.encodeMetadata(u))):(a=r,s["cache-control"]="max-age=".concat(o.cacheControl),s["content-type"]=o.contentType,u&&(s["x-metadata"]=this.toBase64(this.encodeMetadata(u)))),(null==n?void 0:n.headers)&&(s=Object.assign(Object.assign({},s),n.headers)),c=this._removeEmptyFolders(t),l=this._getFinalPath(c),i.next=10,this.fetch("".concat(this.url,"/object/").concat(l),Object.assign({method:e,body:a,headers:s},(null==o?void 0:o.duplex)?{duplex:o.duplex}:{}));case 10:return f=i.sent,i.next=13,f.json();case 13:if(h=i.sent,!f.ok){i.next=18;break}return i.abrupt("return",{data:{path:c,id:h.Id,fullPath:h.Key},error:null});case 18:return p=h,i.abrupt("return",{data:null,error:p});case 20:i.next=27;break;case 22:if(i.prev=22,i.t0=i.catch(0),!et(i.t0)){i.next=26;break}return i.abrupt("return",{data:null,error:i.t0});case 26:throw i.t0;case 27:case"end":return i.stop()}},i,this,[[0,22]])}))}},{key:"upload",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.uploadOrUpdate("POST",e,t,r));case 1:case"end":return n.stop()}},n,this)}))}},{key:"uploadToSignedUrl",value:function(e,t,r,n){return eg(this,void 0,void 0,g().mark(function i(){var a,o,s,u,c,l,f,h,p;return g().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return a=this._removeEmptyFolders(e),o=this._getFinalPath(a),(s=new URL(this.url+"/object/upload/sign/".concat(o))).searchParams.set("token",t),i.prev=4,c=Object.assign({upsert:eb.upsert},n),l=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)}),"undefined"!=typeof Blob&&r instanceof Blob?((u=new FormData).append("cacheControl",c.cacheControl),u.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(u=r).append("cacheControl",c.cacheControl):(u=r,l["cache-control"]="max-age=".concat(c.cacheControl),l["content-type"]=c.contentType),i.next=10,this.fetch(s.toString(),{method:"PUT",body:u,headers:l});case 10:return f=i.sent,i.next=13,f.json();case 13:if(h=i.sent,!f.ok){i.next=18;break}return i.abrupt("return",{data:{path:a,fullPath:h.Key},error:null});case 18:return p=h,i.abrupt("return",{data:null,error:p});case 20:i.next=27;break;case 22:if(i.prev=22,i.t0=i.catch(4),!et(i.t0)){i.next=26;break}return i.abrupt("return",{data:null,error:i.t0});case 26:throw i.t0;case 27:case"end":return i.stop()}},i,this,[[4,22]])}))}},{key:"createSignedUploadUrl",value:function(e,t){return eg(this,void 0,void 0,g().mark(function r(){var n,i,a,o,s;return g().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,n=this._getFinalPath(e),i=Object.assign({},this.headers),(null==t?void 0:t.upsert)&&(i["x-upsert"]="true"),r.next=6,ed(this.fetch,"".concat(this.url,"/object/upload/sign/").concat(n),{},{headers:i});case 6:if(a=r.sent,s=(o=new URL(this.url+a.url)).searchParams.get("token")){r.next=11;break}throw new ee("No token returned by API");case 11:return r.abrupt("return",{data:{signedUrl:o.toString(),path:e,token:s},error:null});case 14:if(r.prev=14,r.t0=r.catch(0),!et(r.t0)){r.next=18;break}return r.abrupt("return",{data:null,error:r.t0});case 18:throw r.t0;case 19:case"end":return r.stop()}},r,this,[[0,14]])}))}},{key:"update",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.uploadOrUpdate("PUT",e,t,r));case 1:case"end":return n.stop()}},n,this)}))}},{key:"move",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){var i;return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,ed(this.fetch,"".concat(this.url,"/object/move"),{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers});case 3:return i=n.sent,n.abrupt("return",{data:i,error:null});case 7:if(n.prev=7,n.t0=n.catch(0),!et(n.t0)){n.next=11;break}return n.abrupt("return",{data:null,error:n.t0});case 11:throw n.t0;case 12:case"end":return n.stop()}},n,this,[[0,7]])}))}},{key:"copy",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){var i;return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,ed(this.fetch,"".concat(this.url,"/object/copy"),{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers});case 3:return i=n.sent,n.abrupt("return",{data:{path:i.Key},error:null});case 7:if(n.prev=7,n.t0=n.catch(0),!et(n.t0)){n.next=11;break}return n.abrupt("return",{data:null,error:n.t0});case 11:throw n.t0;case 12:case"end":return n.stop()}},n,this,[[0,7]])}))}},{key:"createSignedUrl",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){var i,a,o;return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,i=this._getFinalPath(e),n.next=4,ed(this.fetch,"".concat(this.url,"/object/sign/").concat(i),Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});case 4:return a=n.sent,o=(null==r?void 0:r.download)?"&download=".concat(!0===r.download?"":r.download):"",a={signedUrl:encodeURI("".concat(this.url).concat(a.signedURL).concat(o))},n.abrupt("return",{data:a,error:null});case 11:if(n.prev=11,n.t0=n.catch(0),!et(n.t0)){n.next=15;break}return n.abrupt("return",{data:null,error:n.t0});case 15:throw n.t0;case 16:case"end":return n.stop()}},n,this,[[0,11]])}))}},{key:"createSignedUrls",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){var i,a,o=this;return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,ed(this.fetch,"".concat(this.url,"/object/sign/").concat(this.bucketId),{expiresIn:t,paths:e},{headers:this.headers});case 3:return i=n.sent,a=(null==r?void 0:r.download)?"&download=".concat(!0===r.download?"":r.download):"",n.abrupt("return",{data:i.map(function(e){return Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI("".concat(o.url).concat(e.signedURL).concat(a)):null})}),error:null});case 8:if(n.prev=8,n.t0=n.catch(0),!et(n.t0)){n.next=12;break}return n.abrupt("return",{data:null,error:n.t0});case 12:throw n.t0;case 13:case"end":return n.stop()}},n,this,[[0,8]])}))}},{key:"download",value:function(e,t){return eg(this,void 0,void 0,g().mark(function r(){var n,i,a,o,s,u;return g().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return n=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",a=(i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}))?"?".concat(i):"",r.prev=4,o=this._getFinalPath(e),r.next=8,ep(this.fetch,"".concat(this.url,"/").concat(n,"/").concat(o).concat(a),{headers:this.headers,noResolveJson:!0});case 8:return s=r.sent,r.next=11,s.blob();case 11:return u=r.sent,r.abrupt("return",{data:u,error:null});case 15:if(r.prev=15,r.t0=r.catch(4),!et(r.t0)){r.next=19;break}return r.abrupt("return",{data:null,error:r.t0});case 19:throw r.t0;case 20:case"end":return r.stop()}},r,this,[[4,15]])}))}},{key:"info",value:function(e){return eg(this,void 0,void 0,g().mark(function t(){var r,n;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=this._getFinalPath(e),t.prev=1,t.next=4,ep(this.fetch,"".concat(this.url,"/object/info/").concat(r),{headers:this.headers});case 4:return n=t.sent,t.abrupt("return",{data:es(n),error:null});case 8:if(t.prev=8,t.t0=t.catch(1),!et(t.t0)){t.next=12;break}return t.abrupt("return",{data:null,error:t.t0});case 12:throw t.t0;case 13:case"end":return t.stop()}},t,this,[[1,8]])}))}},{key:"exists",value:function(e){return eg(this,void 0,void 0,g().mark(function t(){var r,n;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=this._getFinalPath(e),t.prev=1,t.next=4,function(e,t,r,n){return eu(this,void 0,void 0,g().mark(function n(){return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",eh(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0));case 1:case"end":return n.stop()}},n)}))}(this.fetch,"".concat(this.url,"/object/").concat(r),{headers:this.headers});case 4:return t.abrupt("return",{data:!0,error:null});case 7:if(t.prev=7,t.t0=t.catch(1),!(et(t.t0)&&t.t0 instanceof en)||![400,404].includes(null==(n=t.t0.originalError)?void 0:n.status)){t.next=13;break}return t.abrupt("return",{data:!1,error:t.t0});case 13:throw t.t0;case 14:case"end":return t.stop()}},t,this,[[1,7]])}))}},{key:"getPublicUrl",value:function(e,t){var r=this._getFinalPath(e),n=[],i=(null==t?void 0:t.download)?"download=".concat(!0===t.download?"":t.download):"";""!==i&&n.push(i);var a=void 0!==(null==t?void 0:t.transform),o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&n.push(o);var s=n.join("&");return""!==s&&(s="?".concat(s)),{data:{publicUrl:encodeURI("".concat(this.url,"/").concat(a?"render/image":"object","/public/").concat(r).concat(s))}}}},{key:"remove",value:function(e){return eg(this,void 0,void 0,g().mark(function t(){var r;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ev(this.fetch,"".concat(this.url,"/object/").concat(this.bucketId),{prefixes:e},{headers:this.headers});case 3:return r=t.sent,t.abrupt("return",{data:r,error:null});case 7:if(t.prev=7,t.t0=t.catch(0),!et(t.t0)){t.next=11;break}return t.abrupt("return",{data:null,error:t.t0});case 11:throw t.t0;case 12:case"end":return t.stop()}},t,this,[[0,7]])}))}},{key:"list",value:function(e,t,r){return eg(this,void 0,void 0,g().mark(function n(){var i,a;return g().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,i=Object.assign(Object.assign(Object.assign({},em),t),{prefix:e||""}),n.next=4,ed(this.fetch,"".concat(this.url,"/object/list/").concat(this.bucketId),i,{headers:this.headers},r);case 4:return a=n.sent,n.abrupt("return",{data:a,error:null});case 8:if(n.prev=8,n.t0=n.catch(0),!et(n.t0)){n.next=12;break}return n.abrupt("return",{data:null,error:n.t0});case 12:throw n.t0;case 13:case"end":return n.stop()}},n,this,[[0,8]])}))}},{key:"encodeMetadata",value:function(e){return JSON.stringify(e)}},{key:"toBase64",value:function(e){return void 0!==ey?ey.from(e).toString("base64"):btoa(e)}},{key:"_getFinalPath",value:function(e){return"".concat(this.bucketId,"/").concat(e)}},{key:"_removeEmptyFolders",value:function(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}},{key:"transformOptsToQueryString",value:function(e){var t=[];return e.width&&t.push("width=".concat(e.width)),e.height&&t.push("height=".concat(e.height)),e.resize&&t.push("resize=".concat(e.resize)),e.format&&t.push("format=".concat(e.format)),e.quality&&t.push("quality=".concat(e.quality)),t.join("&")}}]),e}(),ew={"X-Client-Info":"storage-js/".concat("2.7.1")},eA=function(e,t,r,n){return new(r||(r=Promise))(function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,s)}u((n=n.apply(e,t||[])).next())})},ex=function(e){(0,b.A)(n,e);var t,r=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=(0,w.A)(n);return e=t?Reflect.construct(r,arguments,(0,w.A)(this).constructor):r.apply(this,arguments),(0,k.A)(this,e)});function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;return(0,d.A)(this,n),r.call(this,e,t,i)}return(0,v.A)(n,[{key:"from",value:function(e){return new ek(this.url,this.headers,e,this.fetch)}}]),n}(function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;(0,d.A)(this,e),this.url=t,this.headers=Object.assign(Object.assign({},ew),r),this.fetch=ea(n)}return(0,v.A)(e,[{key:"listBuckets",value:function(){return eA(this,void 0,void 0,g().mark(function e(){var t;return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,ep(this.fetch,"".concat(this.url,"/bucket"),{headers:this.headers});case 3:return t=e.sent,e.abrupt("return",{data:t,error:null});case 7:if(e.prev=7,e.t0=e.catch(0),!et(e.t0)){e.next=11;break}return e.abrupt("return",{data:null,error:e.t0});case 11:throw e.t0;case 12:case"end":return e.stop()}},e,this,[[0,7]])}))}},{key:"getBucket",value:function(e){return eA(this,void 0,void 0,g().mark(function t(){var r;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ep(this.fetch,"".concat(this.url,"/bucket/").concat(e),{headers:this.headers});case 3:return r=t.sent,t.abrupt("return",{data:r,error:null});case 7:if(t.prev=7,t.t0=t.catch(0),!et(t.t0)){t.next=11;break}return t.abrupt("return",{data:null,error:t.t0});case 11:throw t.t0;case 12:case"end":return t.stop()}},t,this,[[0,7]])}))}},{key:"createBucket",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{public:!1};return eA(this,void 0,void 0,g().mark(function r(){var n;return g().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,ed(this.fetch,"".concat(this.url,"/bucket"),{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});case 3:return n=r.sent,r.abrupt("return",{data:n,error:null});case 7:if(r.prev=7,r.t0=r.catch(0),!et(r.t0)){r.next=11;break}return r.abrupt("return",{data:null,error:r.t0});case 11:throw r.t0;case 12:case"end":return r.stop()}},r,this,[[0,7]])}))}},{key:"updateBucket",value:function(e,t){return eA(this,void 0,void 0,g().mark(function r(){var n;return g().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,function(e,t,r,n,i){return eu(this,void 0,void 0,g().mark(function i(){return g().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",eh(e,"PUT",t,n,void 0,r));case 1:case"end":return i.stop()}},i)}))}(this.fetch,"".concat(this.url,"/bucket/").concat(e),{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});case 3:return n=r.sent,r.abrupt("return",{data:n,error:null});case 7:if(r.prev=7,r.t0=r.catch(0),!et(r.t0)){r.next=11;break}return r.abrupt("return",{data:null,error:r.t0});case 11:throw r.t0;case 12:case"end":return r.stop()}},r,this,[[0,7]])}))}},{key:"emptyBucket",value:function(e){return eA(this,void 0,void 0,g().mark(function t(){var r;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ed(this.fetch,"".concat(this.url,"/bucket/").concat(e,"/empty"),{},{headers:this.headers});case 3:return r=t.sent,t.abrupt("return",{data:r,error:null});case 7:if(t.prev=7,t.t0=t.catch(0),!et(t.t0)){t.next=11;break}return t.abrupt("return",{data:null,error:t.t0});case 11:throw t.t0;case 12:case"end":return t.stop()}},t,this,[[0,7]])}))}},{key:"deleteBucket",value:function(e){return eA(this,void 0,void 0,g().mark(function t(){var r;return g().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,ev(this.fetch,"".concat(this.url,"/bucket/").concat(e),{},{headers:this.headers});case 3:return r=t.sent,t.abrupt("return",{data:r,error:null});case 7:if(t.prev=7,t.t0=t.catch(0),!et(t.t0)){t.next=11;break}return t.abrupt("return",{data:null,error:t.t0});case 11:throw t.t0;case 12:case"end":return t.stop()}},t,this,[[0,7]])}))}}]),e}()),eE="";eE="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";var eO={headers:{"X-Client-Info":"supabase-js-".concat(eE,"/").concat("2.49.4")}},ej={schema:"public"},e_={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eT={},eP=r(49539),eS=function(e){var t;return t=e||("undefined"==typeof fetch?eP.default:fetch),function(){return t.apply(void 0,arguments)}},eR=function(e,t,r){var n=eS(r),i="undefined"==typeof Headers?eP.Headers:Headers;return function(r,a){var o,s,u,c;return o=void 0,s=void 0,u=void 0,c=g().mark(function o(){var s,u,c;return g().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,t();case 2:if(o.t1=s=o.sent,o.t0=null!==o.t1,!o.t0){o.next=6;break}o.t0=void 0!==s;case 6:if(!o.t0){o.next=10;break}o.t2=s,o.next=11;break;case 10:o.t2=e;case 11:return u=o.t2,(c=new i(null==a?void 0:a.headers)).has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization","Bearer ".concat(u)),o.abrupt("return",n(r,Object.assign(Object.assign({},a),{headers:c})));case 16:case"end":return o.stop()}},o)}),new(u||(u=Promise))(function(e,t){function r(e){try{i(c.next(e))}catch(e){t(e)}}function n(e){try{i(c.throw(e))}catch(e){t(e)}}function i(t){var i;t.done?e(t.value):((i=t.value)instanceof u?i:new u(function(e){e(i)})).then(r,n)}i((c=c.apply(o,s||[])).next())})}};r(41779);var eC=r(1337).A;r(53048);var eB=function(e){(0,b.A)(n,e);var t,r=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=(0,w.A)(n);return e=t?Reflect.construct(r,arguments,(0,w.A)(this).constructor):r.apply(this,arguments),(0,k.A)(this,e)});function n(e){return(0,d.A)(this,n),r.call(this,e)}return(0,v.A)(n)}(eC),eU=function(){function e(t,r,n){if((0,d.A)(this,e),this.supabaseUrl=t,this.supabaseKey=r,!t)throw Error("supabaseUrl is required.");if(!r)throw Error("supabaseKey is required.");var i,a,o,s=t.replace(/\/$/,"");this.realtimeUrl="".concat(s,"/realtime/v1").replace(/^http/i,"ws"),this.authUrl="".concat(s,"/auth/v1"),this.storageUrl="".concat(s,"/storage/v1"),this.functionsUrl="".concat(s,"/functions/v1");var u="sb-".concat(new URL(this.authUrl).hostname.split(".")[0],"-auth-token"),c=function(e,t){var r=this,n=e.db,i=e.auth,a=e.realtime,o=e.global,s=t.db,u=t.auth,c=t.realtime,l=t.global,f={db:Object.assign(Object.assign({},s),n),auth:Object.assign(Object.assign({},u),i),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign({},l),o),accessToken:function(){var e,t,n;return e=void 0,t=void 0,n=g().mark(function e(){return g().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return","");case 1:case"end":return e.stop()}},e)}),new(t||(t=Promise))(function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var r;e.done?i(e.value):((r=e.value)instanceof t?r:new t(function(e){e(r)})).then(o,s)}u((n=n.apply(r,e||[])).next())})}};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}(null!=n?n:{},{db:ej,realtime:eT,auth:Object.assign(Object.assign({},e_),{storageKey:u}),global:eO});this.storageKey=null!=(i=c.auth.storageKey)?i:"",this.headers=null!=(a=c.global.headers)?a:{},c.accessToken?(this.accessToken=c.accessToken,this.auth=new Proxy({},{get:function(e,t){throw Error("@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.".concat(String(t)," is not possible"))}})):this.auth=this._initSupabaseAuthClient(null!=(o=c.auth)?o:{},this.headers,c.global.fetch),this.fetch=eR(r,this._getAccessToken.bind(this),c.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},c.realtime)),this.rest=new S("".concat(s,"/rest/v1"),{headers:this.headers,schema:c.db.schema,fetch:this.fetch}),c.accessToken||this._listenForAuthEvents()}return(0,v.A)(e,[{key:"functions",get:function(){return new T(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}},{key:"storage",get:function(){return new ex(this.storageUrl,this.headers,this.fetch)}},{key:"from",value:function(e){return this.rest.from(e)}},{key:"schema",value:function(e){return this.rest.schema(e)}},{key:"rpc",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.rest.rpc(e,t,r)}},{key:"channel",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};return this.realtime.channel(e,t)}},{key:"getChannels",value:function(){return this.realtime.getChannels()}},{key:"removeChannel",value:function(e){return this.realtime.removeChannel(e)}},{key:"removeAllChannels",value:function(){return this.realtime.removeAllChannels()}},{key:"_getAccessToken",value:function(){var e,t,r,n,i,a;return r=this,n=void 0,i=void 0,a=g().mark(function r(){var n;return g().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(!this.accessToken){r.next=4;break}return r.next=3,this.accessToken();case 3:return r.abrupt("return",r.sent);case 4:return r.next=6,this.auth.getSession();case 6:return n=r.sent.data,r.abrupt("return",null!=(t=null==(e=n.session)?void 0:e.access_token)?t:null);case 9:case"end":return r.stop()}},r,this)}),new(i||(i=Promise))(function(e,t){function o(e){try{u(a.next(e))}catch(e){t(e)}}function s(e){try{u(a.throw(e))}catch(e){t(e)}}function u(t){var r;t.done?e(t.value):((r=t.value)instanceof i?r:new i(function(e){e(r)})).then(o,s)}u((a=a.apply(r,n||[])).next())})}},{key:"_initSupabaseAuthClient",value:function(e,t,r){var n=e.autoRefreshToken,i=e.persistSession,a=e.detectSessionInUrl,o=e.storage,s=e.storageKey,u=e.flowType,c=e.lock,l=e.debug,f={Authorization:"Bearer ".concat(this.supabaseKey),apikey:"".concat(this.supabaseKey)};return new eB({url:this.authUrl,headers:Object.assign(Object.assign({},f),t),storageKey:s,autoRefreshToken:n,persistSession:i,detectSessionInUrl:a,storage:o,flowType:u,lock:c,debug:l,fetch:r,hasCustomAuthorizationHeader:"Authorization"in this.headers})}},{key:"_initRealtimeClient",value:function(e){return new X(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}},{key:"_listenForAuthEvents",value:function(){var e=this;return this.auth.onAuthStateChange(function(t,r){e._handleTokenChanged(t,"CLIENT",null==r?void 0:r.access_token)})}},{key:"_handleTokenChanged",value:function(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}]),e}(),eI=function(e,t,r){return new eU(e,t,r)}},49077:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(63730);function i(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,n.A)(e,t)}},49539:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>o,Request:()=>s,Response:()=>u,default:()=>a,fetch:()=>i});var n="undefined"!=typeof self?self:window,i=n.fetch;let a=n.fetch.bind(n);var o=n.Headers,s=n.Request,u=n.Response},53048:(e,t,r)=>{"use strict";r.d(t,{JS:()=>g,nb:()=>v});var n=r(33311),i=r(59539),a=r(11157),o=r(49077),s=r(90064),u=r(44686),c=r(8644),l=r(28295),f=r.n(l),h=r(7057);function p(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=(0,u.A)(e);return r=t?Reflect.construct(n,arguments,(0,u.A)(this).constructor):n.apply(this,arguments),(0,s.A)(this,r)}}var d={debug:!!(globalThis&&(0,h.LJ)()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))},v=function(e){(0,o.A)(r,e);var t=p(r);function r(e){var n;return(0,a.A)(this,r),(n=t.call(this,e)).isAcquireTimeout=!0,n}return(0,i.A)(r)}((0,c.A)(Error)),y=function(e){(0,o.A)(r,e);var t=p(r);function r(){return(0,a.A)(this,r),t.apply(this,arguments)}return(0,i.A)(r)}(v);function g(e,t,r){return m.apply(this,arguments)}function m(){return(m=(0,n.A)(f().mark(function e(t,r,i){var a;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return d.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",t,r),a=new globalThis.AbortController,r>0&&setTimeout(function(){a.abort(),d.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",t)},r),e.next=5,Promise.resolve().then(function(){return globalThis.navigator.locks.request(t,0===r?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:a.signal},function(){var e=(0,n.A)(f().mark(function e(n){return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!n){e.next=11;break}return d.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",t,n.name),e.prev=2,e.next=5,i();case 5:return e.abrupt("return",e.sent);case 6:return e.prev=6,d.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",t,n.name),e.finish(6);case 9:e.next=31;break;case 11:if(0!==r){e.next=16;break}throw d.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",t),new y('Acquiring an exclusive Navigator LockManager lock "'.concat(t,'" immediately failed'));case 16:if(!d.debug){e.next=27;break}return e.prev=17,e.next=20,globalThis.navigator.locks.query();case 20:console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e.sent,null,"  ")),e.next=27;break;case 24:e.prev=24,e.t0=e.catch(17),console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e.t0);case 27:return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),e.next=30,i();case 30:return e.abrupt("return",e.sent);case 31:case"end":return e.stop()}},e,null,[[2,,6,9],[17,24]])}));return function(t){return e.apply(this,arguments)}}())});case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e)}))).apply(this,arguments)}var b={}},57483:(e,t,r)=>{"use strict";r.d(t,{Fw:()=>x,HU:()=>p,HY:()=>l,MJ:()=>k,MR:()=>b,NA:()=>h,OI:()=>E,Ox:()=>g,RC:()=>f,U_:()=>m,Uw:()=>w,jG:()=>v,xL:()=>O,zh:()=>A,zq:()=>y});var n=r(59539),i=r(11157),a=r(49077),o=r(90064),s=r(44686);function u(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=(0,s.A)(e);return r=t?Reflect.construct(n,arguments,(0,s.A)(this).constructor):n.apply(this,arguments),(0,o.A)(this,r)}}var c=function(e){(0,a.A)(r,e);var t=u(r);function r(e,n,a){var o;return(0,i.A)(this,r),(o=t.call(this,e)).__isAuthError=!0,o.name="AuthError",o.status=n,o.code=a,o}return(0,n.A)(r)}((0,r(8644).A)(Error));function l(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}var f=function(e){(0,a.A)(r,e);var t=u(r);function r(e,n,a){var o;return(0,i.A)(this,r),(o=t.call(this,e,n,a)).name="AuthApiError",o.status=n,o.code=a,o}return(0,n.A)(r)}(c);function h(e){return l(e)&&"AuthApiError"===e.name}var p=function(e){(0,a.A)(r,e);var t=u(r);function r(e,n){var a;return(0,i.A)(this,r),(a=t.call(this,e)).name="AuthUnknownError",a.originalError=n,a}return(0,n.A)(r)}(c),d=function(e){(0,a.A)(r,e);var t=u(r);function r(e,n,a,o){var s;return(0,i.A)(this,r),(s=t.call(this,e,a,o)).name=n,s.status=a,s}return(0,n.A)(r)}(c),v=function(e){(0,a.A)(r,e);var t=u(r);function r(){return(0,i.A)(this,r),t.call(this,"Auth session missing!","AuthSessionMissingError",400,void 0)}return(0,n.A)(r)}(d);function y(e){return l(e)&&"AuthSessionMissingError"===e.name}var g=function(e){(0,a.A)(r,e);var t=u(r);function r(){return(0,i.A)(this,r),t.call(this,"Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}return(0,n.A)(r)}(d),m=function(e){(0,a.A)(r,e);var t=u(r);function r(e){return(0,i.A)(this,r),t.call(this,e,"AuthInvalidCredentialsError",400,void 0)}return(0,n.A)(r)}(d),b=function(e){(0,a.A)(r,e);var t=u(r);function r(e){var n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return(0,i.A)(this,r),(n=t.call(this,e,"AuthImplicitGrantRedirectError",500,void 0)).details=null,n.details=a,n}return(0,n.A)(r,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}]),r}(d);function k(e){return l(e)&&"AuthImplicitGrantRedirectError"===e.name}var w=function(e){(0,a.A)(r,e);var t=u(r);function r(e){var n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return(0,i.A)(this,r),(n=t.call(this,e,"AuthPKCEGrantCodeExchangeError",500,void 0)).details=null,n.details=a,n}return(0,n.A)(r,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}]),r}(d),A=function(e){(0,a.A)(r,e);var t=u(r);function r(e,n){return(0,i.A)(this,r),t.call(this,e,"AuthRetryableFetchError",n,void 0)}return(0,n.A)(r)}(d);function x(e){return l(e)&&"AuthRetryableFetchError"===e.name}var E=function(e){(0,a.A)(r,e);var t=u(r);function r(e,n,a){var o;return(0,i.A)(this,r),(o=t.call(this,e,"AuthWeakPasswordError",n,"weak_password")).reasons=a,o}return(0,n.A)(r)}(d),O=function(e){(0,a.A)(r,e);var t=u(r);function r(e){return(0,i.A)(this,r),t.call(this,e,"AuthInvalidJwtError",400,"invalid_jwt")}return(0,n.A)(r)}(d)},58299:function(e,t,r){"use strict";var n=r(80851),i=r(98557),a=r(63819),o=r(61626),s=r(69456),u=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){a(u,e);var t,r=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=s(u);return e=t?Reflect.construct(r,arguments,s(this).constructor):r.apply(this,arguments),o(this,e)});function u(){return n(this,u),r.apply(this,arguments)}return i(u,[{key:"select",value:function(e){var t=!1,r=(null!=e?e:"*").split("").map(function(e){return/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)}).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}},{key:"order",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.ascending,n=t.nullsFirst,i=t.foreignTable,a=t.referencedTable,o=void 0===a?i:a,s=o?"".concat(o,".order"):"order",u=this.url.searchParams.get(s);return this.url.searchParams.set(s,"".concat(u?"".concat(u,","):"").concat(e,".").concat(void 0===r||r?"asc":"desc").concat(void 0===n?"":n?".nullsfirst":".nullslast")),this}},{key:"limit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.foreignTable,n=t.referencedTable,i=void 0===n?r:n;return this.url.searchParams.set(void 0===i?"limit":"".concat(i,".limit"),"".concat(e)),this}},{key:"range",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.foreignTable,i=r.referencedTable,a=void 0===i?n:i,o=void 0===a?"offset":"".concat(a,".offset"),s=void 0===a?"limit":"".concat(a,".limit");return this.url.searchParams.set(o,"".concat(e)),this.url.searchParams.set(s,"".concat(t-e+1)),this}},{key:"abortSignal",value:function(e){return this.signal=e,this}},{key:"single",value:function(){return this.headers.Accept="application/vnd.pgrst.object+json",this}},{key:"maybeSingle",value:function(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}},{key:"csv",value:function(){return this.headers.Accept="text/csv",this}},{key:"geojson",value:function(){return this.headers.Accept="application/geo+json",this}},{key:"explain",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.analyze,n=t.verbose,i=t.settings,a=t.buffers,o=t.wal,s=t.format,u=void 0===s?"text":s,c=[void 0!==r&&r?"analyze":null,void 0!==n&&n?"verbose":null,void 0!==i&&i?"settings":null,void 0!==a&&a?"buffers":null,void 0!==o&&o?"wal":null].filter(Boolean).join("|"),l=null!=(e=this.headers.Accept)?e:"application/json";return this.headers.Accept="application/vnd.pgrst.plan+".concat(u,'; for="').concat(l,'"; options=').concat(c,";"),this}},{key:"rollback",value:function(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}},{key:"returns",value:function(){return this}}]),u}(u(r(97107)).default)},59539:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(29889);function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(0,n.A)(i.key),i)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},63730:(e,t,r)=>{"use strict";function n(e,t){return(n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}r.d(t,{A:()=>n})},67086:(e,t,r)=>{"use strict";var n=r(98557),i=r(80851),a=r(63819),o=r(61626),s=r(69456),u=r(30795);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){a(u,e);var t,r=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=s(u);return e=t?Reflect.construct(r,arguments,s(this).constructor):r.apply(this,arguments),o(this,e)});function u(e){var t;return i(this,u),(t=r.call(this,e.message)).name="PostgrestError",t.details=e.details,t.hint=e.hint,t.code=e.code,t}return n(u)}(u(Error))},67939:(e,t,r)=>{"use strict";r.d(t,{r:()=>a,y:()=>i});var n=r(7057),i={getItem:function(e){return(0,n.LJ)()?globalThis.localStorage.getItem(e):null},setItem:function(e,t){(0,n.LJ)()&&globalThis.localStorage.setItem(e,t)},removeItem:function(e){(0,n.LJ)()&&globalThis.localStorage.removeItem(e)}};function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{getItem:function(t){return e[t]||null},setItem:function(t,r){e[t]=r},removeItem:function(t){delete e[t]}}}},69940:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(78321);function i(e,t){if(e){if("string"==typeof e)return(0,n.A)(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return(0,n.A)(e,t)}}},73476:(e,t,r)=>{"use strict";r.d(t,{Al:()=>c,uE:()=>s,xf:()=>u});var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),i=" 	\n\r=".split(""),a=function(){for(var e=Array(128),t=0;t<e.length;t+=1)e[t]=-1;for(var r=0;r<i.length;r+=1)e[i[r].charCodeAt(0)]=-2;for(var a=0;a<n.length;a+=1)e[n[a].charCodeAt(0)]=a;return e}();function o(e,t,r){var n=a[e];if(n>-1)for(t.queue=t.queue<<6|n,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===n)return;else throw Error('Invalid Base64-URL character "'.concat(String.fromCharCode(e),'"'))}function s(e){for(var t=[],r=function(e){t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},a=function(e){!function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(var n=1;n<6;n+=1)if((e>>7-n&1)==0){t.utf8seq=n;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,n,r)},s=0;s<e.length;s+=1)o(e.charCodeAt(s),i,a);return t.join("")}function u(e){for(var t=[],r={queue:0,queuedBits:0},n=function(e){t.push(e)},i=0;i<e.length;i+=1)o(e.charCodeAt(i),r,n);return new Uint8Array(t)}function c(e){var t=[];return function(e,t){for(var r=0;r<e.length;r+=1){var n=e.charCodeAt(r);if(n>55295&&n<=56319){var i=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|i)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error("Unrecognized Unicode codepoint: ".concat(e.toString(16)))}(n,t)}}(e,function(e){return t.push(e)}),new Uint8Array(t)}},73728:(e,t,r)=>{"use strict";r.d(t,{createBrowserClient:()=>P});var n,i=r(37711),a=r(45408),o=r(25538);function s(){return void 0!==window.document}var u={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},c=r(33311),l=r(28295),f=r.n(l),h=/^(.*)[.](0|[1-9][0-9]*)$/;function p(e,t){if(e===t)return!0;var r=e.match(h);return!!r&&r[1]===t}function d(e,t,r){var n=null!=r?r:3180,i=encodeURIComponent(t);if(i.length<=n)return[{name:e,value:t}];for(var a=[];i.length>0;){var o=i.slice(0,n),s=o.lastIndexOf("%");s>n-3&&(o=o.slice(0,s));for(var u="";o.length>0;)try{u=decodeURIComponent(o);break}catch(e){if(e instanceof URIError&&"%"===o.at(-3)&&o.length>3)o=o.slice(0,o.length-3);else throw e}a.push(u),i=i.slice(o.length)}return a.map(function(t,r){return{name:"".concat(e,".").concat(r),value:t}})}function v(e,t){return y.apply(this,arguments)}function y(){return(y=(0,c.A)(f().mark(function e(t,r){var n,i,a,o,s;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r(t);case 2:if(!(n=e.sent)){e.next=5;break}return e.abrupt("return",n);case 5:i=[],a=0;case 7:return o="".concat(t,".").concat(a),e.next=10,r(o);case 10:if(s=e.sent){e.next=13;break}return e.abrupt("break",17);case 13:i.push(s);case 14:a++,e.next=7;break;case 17:if(!(i.length>0)){e.next=19;break}return e.abrupt("return",i.join(""));case 19:return e.abrupt("return",null);case 20:case"end":return e.stop()}},e)}))).apply(this,arguments)}var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),m=" 	\n\r=".split(""),b=function(){for(var e=Array(128),t=0;t<e.length;t+=1)e[t]=-1;for(var r=0;r<m.length;r+=1)e[m[r].charCodeAt(0)]=-2;for(var n=0;n<g.length;n+=1)e[g[n].charCodeAt(0)]=n;return e}();function k(e){var t=[],r=0,n=0;if(function(e,t){for(var r=0;r<e.length;r+=1){var n=e.charCodeAt(r);if(n>55295&&n<=56319){var i=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|i)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error("Unrecognized Unicode codepoint: ".concat(e.toString(16)))}(n,t)}}(e,function(e){for(r=r<<8|e,n+=8;n>=6;){var i=r>>n-6&63;t.push(g[i]),n-=6}}),n>0)for(r<<=6-n,n=6;n>=6;){var i=r>>n-6&63;t.push(g[i]),n-=6}return t.join("")}function w(e){for(var t=[],r=function(e){t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},i=0,a=0,o=0;o<e.length;o+=1){var s=b[e.charCodeAt(o)];if(s>-1)for(i=i<<6|s,a+=6;a>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(var n=1;n<6;n+=1)if((e>>7-n&1)==0){t.utf8seq=n;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(i>>a-8&255,n,r),a-=8;else if(-2===s)continue;else throw Error('Invalid Base64-URL character "'.concat(e.at(o),'" at position ').concat(o))}return t.join("")}var A=r(10631);function x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?x(Object(r),!0).forEach(function(t){(0,i.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var O="base64-";function j(){return(j=(0,c.A)(f().mark(function e(t,r){var n,i,a,o,s,c,l,h,v,y,g,m,b;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.getAll,a=t.setAll,o=t.setItems,s=t.removedItems,c=r.cookieEncoding,l=null!=(n=r.cookieOptions)?n:null,e.next=5,i([].concat((0,A.A)(o?Object.keys(o):[]),(0,A.A)(s?Object.keys(s):[])));case 5:return v=(null==(h=e.sent)?void 0:h.map(function(e){return e.name}))||[],y=Object.keys(s).flatMap(function(e){return v.filter(function(t){return p(t,e)})}),g=Object.keys(o).flatMap(function(e){var t=new Set(v.filter(function(t){return p(t,e)})),r=o[e];"base64url"===c&&(r=O+k(r));var n=d(e,r);return n.forEach(function(e){t.delete(e.name)}),y.push.apply(y,(0,A.A)(t)),n}),m=E(E(E({},u),l),{},{maxAge:0}),b=E(E(E({},u),l),{},{maxAge:u.maxAge}),delete m.name,delete b.name,e.next=15,a([].concat((0,A.A)(y.map(function(e){return{name:e,value:"",options:m}})),(0,A.A)(g.map(function(e){return{name:e.name,value:e.value,options:b}}))));case 15:case"end":return e.stop()}},e)}))).apply(this,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){(0,i.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function P(e,t,r){var l,h,y,g=(null==r?void 0:r.isSingleton)===!0||(!r||!("isSingleton"in r))&&s();if(g&&n)return n;if(!e||!t)throw Error("@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api");var m=function(e,t){var r,n,a,l,h,y,g,m,b,x,_,T=null!=(b=e.cookies)?b:null,P=e.cookieEncoding,S={},R={};if(T)if("get"in T){var C,B,U,I,L=(C=(0,c.A)(f().mark(function e(t){var r,n,i,a;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=t.flatMap(function(e){return[e].concat((0,A.A)(Array.from({length:5}).map(function(t,r){return"".concat(e,".").concat(r)})))}),n=[],i=0;case 3:if(!(i<r.length)){e.next=13;break}return e.next=6,T.get(r[i]);case 6:if(!(!(a=e.sent)&&"string"!=typeof a)){e.next=9;break}return e.abrupt("continue",10);case 9:n.push({name:r[i],value:a});case 10:i+=1,e.next=3;break;case 13:return e.abrupt("return",n);case 14:case"end":return e.stop()}},e)})),function(e){return C.apply(this,arguments)});if(B=(0,c.A)(f().mark(function e(t){return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,L(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),x=function(e){return B.apply(this,arguments)},"set"in T&&"remove"in T)U=(0,c.A)(f().mark(function e(t){var r,n,i,a,o;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=0;case 1:if(!(r<t.length)){e.next=13;break}if(i=(n=t[r]).name,a=n.value,o=n.options,!a){e.next=8;break}return e.next=6,T.set(i,a,o);case 6:e.next=10;break;case 8:return e.next=10,T.remove(i,o);case 10:r+=1,e.next=1;break;case 13:case"end":return e.stop()}},e)})),_=function(e){return U.apply(this,arguments)};else if(t)I=(0,c.A)(f().mark(function e(){return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.");case 1:case"end":return e.stop()}},e)})),_=function(){return I.apply(this,arguments)};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in T)if(r=(0,c.A)(f().mark(function e(){return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,T.getAll();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),x=function(){return r.apply(this,arguments)},"setAll"in T)_=T.setAll;else if(t)n=(0,c.A)(f().mark(function e(){return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.");case 1:case"end":return e.stop()}},e)})),_=function(){return n.apply(this,arguments)};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error("@supabase/ssr: ".concat(t?"createServerClient":"createBrowserClient"," requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).").concat(s()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""));else if(!t&&s()){var M=function(){var e=(0,o.qg)(document.cookie);return Object.keys(e).map(function(t){var r;return{name:t,value:null!=(r=e[t])?r:""}})};x=function(){return M()},_=function(e){e.forEach(function(e){var t=e.name,r=e.value,n=e.options;document.cookie=(0,o.lK)(t,r,n)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else x=function(){return[]},_=function(){throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:x,setAll:_,setItems:S,removedItems:R,storage:{isServer:!0,getItem:(y=(0,c.A)(f().mark(function e(t){var r,n,i;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("string"!=typeof S[t]){e.next=2;break}return e.abrupt("return",S[t]);case 2:if(!R[t]){e.next=4;break}return e.abrupt("return",null);case 4:return e.next=6,x([t]);case 6:return r=e.sent,e.next=9,v(t,function(){var e=(0,c.A)(f().mark(function e(t){var n;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=(null==r?void 0:r.find(function(e){return e.name===t}))||null){e.next=3;break}return e.abrupt("return",null);case 3:return e.abrupt("return",n.value);case 4:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}());case 9:if(n=e.sent){e.next=12;break}return e.abrupt("return",null);case 12:return i=n,"string"==typeof n&&n.startsWith(O)&&(i=w(n.substring(O.length))),e.abrupt("return",i);case 15:case"end":return e.stop()}},e)})),function(e){return y.apply(this,arguments)}),setItem:(g=(0,c.A)(f().mark(function t(r,n){var a;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!r.endsWith("-code-verifier")){t.next=3;break}return t.next=3,function(e,t){return j.apply(this,arguments)}({getAll:x,setAll:_,setItems:(0,i.A)({},r,n),removedItems:{}},{cookieOptions:null!=(a=null==e?void 0:e.cookieOptions)?a:null,cookieEncoding:P});case 3:S[r]=n,delete R[r];case 5:case"end":return t.stop()}},t)})),function(e,t){return g.apply(this,arguments)}),removeItem:(m=(0,c.A)(f().mark(function e(t){return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:delete S[t],R[t]=!0;case 2:case"end":return e.stop()}},e)})),function(e){return m.apply(this,arguments)})}}:{getAll:x,setAll:_,setItems:S,removedItems:R,storage:{isServer:!1,getItem:(a=(0,c.A)(f().mark(function e(t){var r,n,i;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,x([t]);case 2:return r=e.sent,e.next=5,v(t,function(){var e=(0,c.A)(f().mark(function e(t){var n;return f().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=(null==r?void 0:r.find(function(e){return e.name===t}))||null){e.next=3;break}return e.abrupt("return",null);case 3:return e.abrupt("return",n.value);case 4:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}());case 5:if(n=e.sent){e.next=8;break}return e.abrupt("return",null);case 8:return i=n,n.startsWith(O)&&(i=w(n.substring(O.length))),e.abrupt("return",i);case 11:case"end":return e.stop()}},e)})),function(e){return a.apply(this,arguments)}),setItem:(l=(0,c.A)(f().mark(function t(r,n){var i,a,o,s,c,l,h;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,x([r]);case 2:if(a=new Set(((null==(i=t.sent)?void 0:i.map(function(e){return e.name}))||[]).filter(function(e){return p(e,r)})),o=n,"base64url"===P&&(o=O+k(n)),(s=d(r,o)).forEach(function(e){var t=e.name;a.delete(t)}),c=E(E(E({},u),null==e?void 0:e.cookieOptions),{},{maxAge:0}),l=E(E(E({},u),null==e?void 0:e.cookieOptions),{},{maxAge:u.maxAge}),delete c.name,delete l.name,!((h=[].concat((0,A.A)((0,A.A)(a).map(function(e){return{name:e,value:"",options:c}})),(0,A.A)(s.map(function(e){return{name:e.name,value:e.value,options:l}})))).length>0)){t.next=17;break}return t.next=17,_(h);case 17:case"end":return t.stop()}},t)})),function(e,t){return l.apply(this,arguments)}),removeItem:(h=(0,c.A)(f().mark(function t(r){var n,i,a;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,x([r]);case 2:if(i=((null==(n=t.sent)?void 0:n.map(function(e){return e.name}))||[]).filter(function(e){return p(e,r)}),a=E(E(E({},u),null==e?void 0:e.cookieOptions),{},{maxAge:0}),delete a.name,!(i.length>0)){t.next=10;break}return t.next=10,_(i.map(function(e){return{name:e,value:"",options:a}}));case 10:case"end":return t.stop()}},t)})),function(e){return h.apply(this,arguments)})}}}(T(T({},r),{},{cookieEncoding:null!=(l=null==r?void 0:r.cookieEncoding)?l:"base64url"}),!1).storage,b=(0,a.UU)(e,t,T(T({},r),{},{global:T(T({},null==r?void 0:r.global),{},{headers:T(T({},null==r||null==(h=r.global)?void 0:h.headers),{},{"X-Client-Info":"supabase-ssr/".concat("0.6.1"," createBrowserClient")})}),auth:T(T(T({},null==r?void 0:r.auth),null!=r&&null!=(y=r.cookieOptions)&&y.name?{storageKey:r.cookieOptions.name}:null),{},{flowType:"pkce",autoRefreshToken:s(),detectSessionInUrl:s(),persistSession:!0,storage:m})}));return g&&(n=b),b}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}},73991:function(e,t,r){"use strict";var n=r(95289),i=r(80851),a=r(98557),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=o(r(9751)),u=o(r(88293)),c=r(94508);t.default=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.headers,a=r.schema,o=r.fetch;i(this,e),this.url=t,this.headers=Object.assign(Object.assign({},c.DEFAULT_HEADERS),void 0===n?{}:n),this.schemaName=a,this.fetch=o}return a(e,[{key:"from",value:function(e){var t=new URL("".concat(this.url,"/").concat(e));return new s.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}},{key:"schema",value:function(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}},{key:"rpc",value:function(e){var t,r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=a.head,s=void 0!==o&&o,c=a.get,l=a.count,f=new URL("".concat(this.url,"/rpc/").concat(e));s||void 0!==c&&c?(t=s?"HEAD":"GET",Object.entries(i).filter(function(e){var t=n(e,2);return void 0!==(t[0],t[1])}).map(function(e){var t=n(e,2),r=t[0],i=t[1];return[r,Array.isArray(i)?"{".concat(i.join(","),"}"):"".concat(i)]}).forEach(function(e){var t=n(e,2),r=t[0],i=t[1];f.searchParams.append(r,i)})):(t="POST",r=i);var h=Object.assign({},this.headers);return l&&(h.Prefer="count=".concat(l)),new u.default({method:t,url:f,headers:h,schema:this.schemaName,body:r,fetch:this.fetch,allowEmpty:!1})}}]),e}()},78321:(e,t,r)=>{"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}r.d(t,{A:()=>n})},86628:(e,t,r)=>{"use strict";r.d(t,{Cl:()=>k,EY:()=>A,GC:()=>b,R_:()=>m,c2:()=>w,vE:()=>v,w4:()=>x});var n=r(33311),i=r(28295),a=r.n(i),o=r(44893),s=r(7057),u=r(57483),c=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r},l=function(e){return e.msg||e.message||e.error_description||e.error||JSON.stringify(e)},f=[502,503,504];function h(e){return p.apply(this,arguments)}function p(){return(p=(0,n.A)(a().mark(function e(t){var r,n,i,c;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((0,s.pF)(t)){e.next=2;break}throw new u.zh(l(t),0);case 2:if(!f.includes(t.status)){e.next=4;break}throw new u.zh(l(t),t.status);case 4:return e.prev=4,e.next=7,t.json();case 7:n=e.sent,e.next=13;break;case 10:throw e.prev=10,e.t0=e.catch(4),new u.HU(l(e.t0),e.t0);case 13:if(i=void 0,(c=(0,s.Rn)(t))&&c.getTime()>=o.Bo["2024-01-01"].timestamp&&"object"==typeof n&&n&&"string"==typeof n.code?i=n.code:"object"==typeof n&&n&&"string"==typeof n.error_code&&(i=n.error_code),i){e.next=21;break}if(!("object"==typeof n&&n&&"object"==typeof n.weak_password&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce(function(e,t){return e&&"string"==typeof t},!0))){e.next=19;break}throw new u.OI(l(n),t.status,n.weak_password.reasons);case 19:e.next=27;break;case 21:if("weak_password"!==i){e.next=25;break}throw new u.OI(l(n),t.status,(null==(r=n.weak_password)?void 0:r.reasons)||[]);case 25:if("session_not_found"!==i){e.next=27;break}throw new u.jG;case 27:throw new u.RC(l(n),t.status||500,i);case 28:case"end":return e.stop()}},e,null,[[4,10]])}))).apply(this,arguments)}var d=function(e,t,r,n){var i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(n),Object.assign(Object.assign({},i),r))};function v(e,t,r,n){return y.apply(this,arguments)}function y(){return(y=(0,n.A)(a().mark(function e(t,r,n,i){var s,u,c,l,f;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(u=Object.assign({},null==i?void 0:i.headers))[o.sk]||(u[o.sk]=o.Bo["2024-01-01"].name),(null==i?void 0:i.jwt)&&(u.Authorization="Bearer ".concat(i.jwt)),c=null!=(s=null==i?void 0:i.query)?s:{},(null==i?void 0:i.redirectTo)&&(c.redirect_to=i.redirectTo),l=Object.keys(c).length?"?"+new URLSearchParams(c).toString():"",e.next=8,function(e,t,r,n,i,a){return g.apply(this,arguments)}(t,r,n+l,{headers:u,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);case 8:return f=e.sent,e.abrupt("return",(null==i?void 0:i.xform)?null==i?void 0:i.xform(f):{data:Object.assign({},f),error:null});case 10:case"end":return e.stop()}},e)}))).apply(this,arguments)}function g(){return(g=(0,n.A)(a().mark(function e(t,r,n,i,o,s){var c,f;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return c=d(r,i,o,s),e.prev=1,e.next=4,t(n,Object.assign({},c));case 4:f=e.sent,e.next=11;break;case 7:throw e.prev=7,e.t0=e.catch(1),console.error(e.t0),new u.zh(l(e.t0),0);case 11:if(f.ok){e.next=14;break}return e.next=14,h(f);case 14:if(!(null==i?void 0:i.noResolveJson)){e.next=16;break}return e.abrupt("return",f);case 16:return e.prev=16,e.next=19,f.json();case 19:return e.abrupt("return",e.sent);case 22:return e.prev=22,e.t1=e.catch(16),e.next=26,h(e.t1);case 26:case"end":return e.stop()}},e,null,[[1,7],[16,22]])}))).apply(this,arguments)}function m(e){var t,r,n=null;return(t=e).access_token&&t.refresh_token&&t.expires_in&&(n=Object.assign({},e),e.expires_at||(n.expires_at=(0,s.WJ)(e.expires_in))),{data:{session:n,user:null!=(r=e.user)?r:e},error:null}}function b(e){var t=m(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce(function(e,t){return e&&"string"==typeof t},!0)&&(t.data.weak_password=e.weak_password),t}function k(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function w(e){return{data:e,error:null}}function A(e){return{data:{properties:{action_link:e.action_link,email_otp:e.email_otp,hashed_token:e.hashed_token,redirect_to:e.redirect_to,verification_type:e.verification_type},user:Object.assign({},c(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function x(e){return e}},88293:function(e,t,r){"use strict";var n=r(95289),i=r(80851),a=r(98557),o=r(63819),s=r(61626),u=r(69456),c=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){o(c,e);var t,r=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=u(c);return e=t?Reflect.construct(r,arguments,u(this).constructor):r.apply(this,arguments),s(this,e)});function c(){return i(this,c),r.apply(this,arguments)}return a(c,[{key:"eq",value:function(e,t){return this.url.searchParams.append(e,"eq.".concat(t)),this}},{key:"neq",value:function(e,t){return this.url.searchParams.append(e,"neq.".concat(t)),this}},{key:"gt",value:function(e,t){return this.url.searchParams.append(e,"gt.".concat(t)),this}},{key:"gte",value:function(e,t){return this.url.searchParams.append(e,"gte.".concat(t)),this}},{key:"lt",value:function(e,t){return this.url.searchParams.append(e,"lt.".concat(t)),this}},{key:"lte",value:function(e,t){return this.url.searchParams.append(e,"lte.".concat(t)),this}},{key:"like",value:function(e,t){return this.url.searchParams.append(e,"like.".concat(t)),this}},{key:"likeAllOf",value:function(e,t){return this.url.searchParams.append(e,"like(all).{".concat(t.join(","),"}")),this}},{key:"likeAnyOf",value:function(e,t){return this.url.searchParams.append(e,"like(any).{".concat(t.join(","),"}")),this}},{key:"ilike",value:function(e,t){return this.url.searchParams.append(e,"ilike.".concat(t)),this}},{key:"ilikeAllOf",value:function(e,t){return this.url.searchParams.append(e,"ilike(all).{".concat(t.join(","),"}")),this}},{key:"ilikeAnyOf",value:function(e,t){return this.url.searchParams.append(e,"ilike(any).{".concat(t.join(","),"}")),this}},{key:"is",value:function(e,t){return this.url.searchParams.append(e,"is.".concat(t)),this}},{key:"in",value:function(e,t){var r=Array.from(new Set(t)).map(function(e){return"string"==typeof e&&RegExp("[,()]").test(e)?'"'.concat(e,'"'):"".concat(e)}).join(",");return this.url.searchParams.append(e,"in.(".concat(r,")")),this}},{key:"contains",value:function(e,t){return"string"==typeof t?this.url.searchParams.append(e,"cs.".concat(t)):Array.isArray(t)?this.url.searchParams.append(e,"cs.{".concat(t.join(","),"}")):this.url.searchParams.append(e,"cs.".concat(JSON.stringify(t))),this}},{key:"containedBy",value:function(e,t){return"string"==typeof t?this.url.searchParams.append(e,"cd.".concat(t)):Array.isArray(t)?this.url.searchParams.append(e,"cd.{".concat(t.join(","),"}")):this.url.searchParams.append(e,"cd.".concat(JSON.stringify(t))),this}},{key:"rangeGt",value:function(e,t){return this.url.searchParams.append(e,"sr.".concat(t)),this}},{key:"rangeGte",value:function(e,t){return this.url.searchParams.append(e,"nxl.".concat(t)),this}},{key:"rangeLt",value:function(e,t){return this.url.searchParams.append(e,"sl.".concat(t)),this}},{key:"rangeLte",value:function(e,t){return this.url.searchParams.append(e,"nxr.".concat(t)),this}},{key:"rangeAdjacent",value:function(e,t){return this.url.searchParams.append(e,"adj.".concat(t)),this}},{key:"overlaps",value:function(e,t){return"string"==typeof t?this.url.searchParams.append(e,"ov.".concat(t)):this.url.searchParams.append(e,"ov.{".concat(t.join(","),"}")),this}},{key:"textSearch",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.config,i=r.type,a="";return"plain"===i?a="pl":"phrase"===i?a="ph":"websearch"===i&&(a="w"),this.url.searchParams.append(e,"".concat(a,"fts").concat(void 0===n?"":"(".concat(n,")"),".").concat(t)),this}},{key:"match",value:function(e){var t=this;return Object.entries(e).forEach(function(e){var r=n(e,2),i=r[0],a=r[1];t.url.searchParams.append(i,"eq.".concat(a))}),this}},{key:"not",value:function(e,t,r){return this.url.searchParams.append(e,"not.".concat(t,".").concat(r)),this}},{key:"or",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.foreignTable,n=t.referencedTable,i=void 0===n?r:n;return this.url.searchParams.append(i?"".concat(i,".or"):"or","(".concat(e,")")),this}},{key:"filter",value:function(e,t,r){return this.url.searchParams.append(e,"".concat(t,".").concat(r)),this}}]),c}(c(r(58299)).default)},90064:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(94348),i=r(6639);function a(e,t){if(t&&("object"===(0,n.A)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,i.A)(e)}},94348:(e,t,r)=>{"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.d(t,{A:()=>n})},94508:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;var n=r(26493);t.DEFAULT_HEADERS={"X-Client-Info":"postgrest-js/".concat(n.version)}},97107:function(e,t,r){"use strict";var n=r(28295),i=r(32525),a=r(80851),o=r(98557),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=s(r(49539)),c=s(r(67086));t.default=function(){function e(t){a(this,e),this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:"undefined"==typeof fetch?this.fetch=u.default:this.fetch=fetch}return o(e,[{key:"throwOnError",value:function(){return this.shouldThrowOnError=!0,this}},{key:"setHeader",value:function(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}},{key:"then",value:function(e,t){var r,a=this;void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");var o=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((r=i(n.mark(function e(t){var r,i,o,s,u,l,f,h,p,d,v,y,g;return n.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(s=null,u=null,l=null,f=t.status,h=t.statusText,!t.ok){e.next=17;break}if("HEAD"===a.method){e.next=11;break}return e.next=9,t.text();case 9:""===(p=e.sent)||(u="text/csv"===a.headers.Accept||a.headers.Accept&&a.headers.Accept.includes("application/vnd.pgrst.plan+text")?p:JSON.parse(p));case 11:d=null==(r=a.headers.Prefer)?void 0:r.match(/count=(exact|planned|estimated)/),v=null==(i=t.headers.get("content-range"))?void 0:i.split("/"),d&&v&&v.length>1&&(l=parseInt(v[1])),a.isMaybeSingle&&"GET"===a.method&&Array.isArray(u)&&(u.length>1?(s={code:"PGRST116",details:"Results contain ".concat(u.length," rows, application/vnd.pgrst.object+json requires 1 row"),hint:null,message:"JSON object requested, multiple (or no) rows returned"},u=null,l=null,f=406,h="Not Acceptable"):u=1===u.length?u[0]:null),e.next=24;break;case 17:return e.next=19,t.text();case 19:y=e.sent;try{s=JSON.parse(y),Array.isArray(s)&&404===t.status&&(u=[],s=null,f=200,h="OK")}catch(e){404===t.status&&""===y?(f=204,h="No Content"):s={message:y}}if(s&&a.isMaybeSingle&&(null==(o=null==s?void 0:s.details)?void 0:o.includes("0 rows"))&&(s=null,f=200,h="OK"),!(s&&a.shouldThrowOnError)){e.next=24;break}throw new c.default(s);case 24:return g={error:s,data:u,count:l,status:f,statusText:h},e.abrupt("return",g);case 26:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)}));return this.shouldThrowOnError||(o=o.catch(function(e){var t,r,n;return{error:{message:"".concat(null!=(t=null==e?void 0:e.name)?t:"FetchError",": ").concat(null==e?void 0:e.message),details:"".concat(null!=(r=null==e?void 0:e.stack)?r:""),hint:"",code:"".concat(null!=(n=null==e?void 0:e.code)?n:"")},data:null,count:null,status:0,statusText:""}})),o.then(e,t)}},{key:"returns",value:function(){return this}},{key:"overrideTypes",value:function(){return this}}]),e}()},98132:(e,t,r)=>{"use strict";r.d(t,{r:()=>n});var n="2.69.1"}}]);
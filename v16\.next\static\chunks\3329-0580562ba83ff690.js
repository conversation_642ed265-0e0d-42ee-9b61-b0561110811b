"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3329],{2216:(e,r,t)=>{var n=t(95289);function o(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return u(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return u(e,r)}}(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){c=!0,a=e},f:function(){try{i||null==t.return||t.return()}finally{if(c)throw a}}}}function u(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function a(e){var r,t={},u=o(e.entries());try{for(u.s();!(r=u.n()).done;){var a=n(r.value,2),i=a[0],c=a[1],f=t[i];void 0===f?t[i]=c:Array.isArray(f)?f.push(c):t[i]=[f,c]}}catch(e){u.e(e)}finally{u.f()}return t}function i(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function c(e){for(var r=new URLSearchParams,t=0,u=Object.entries(e);t<u.length;t++){var a=n(u[t],2),c=a[0],f=a[1];if(Array.isArray(f)){var l,s=o(f);try{for(s.s();!(l=s.n()).done;){var p=l.value;r.append(c,i(p))}}catch(e){s.e(e)}finally{s.f()}}else r.set(c,i(f))}return r}function f(e){for(var r=arguments.length,t=Array(r>1?r-1:0),u=1;u<r;u++)t[u-1]=arguments[u];for(var a=0;a<t.length;a++){var i,c=t[a],f=o(c.keys());try{for(f.s();!(i=f.n()).done;){var l=i.value;e.delete(l)}}catch(e){f.e(e)}finally{f.f()}var s,p=o(c.entries());try{for(p.s();!(s=p.n()).done;){var d=n(s.value,2),h=d[0],v=d[1];e.append(h,v)}}catch(e){p.e(e)}finally{p.f()}}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{assign:function(){return f},searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return c}})},3464:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{formatUrl:function(){return u},formatWithValidation:function(){return i},urlObjectKeys:function(){return a}});var n=t(3759)._(t(2216)),o=/https?|ftp|gopher|file/;function u(e){var r=e.auth,t=e.hostname,u=e.protocol||"",a=e.pathname||"",i=e.hash||"",c=e.query||"",f=!1;r=r?encodeURIComponent(r).replace(/%3A/i,":")+"@":"",e.host?f=r+e.host:t&&(f=r+(~t.indexOf(":")?"["+t+"]":t),e.port&&(f+=":"+e.port)),c&&"object"==typeof c&&(c=String(n.urlQueryToSearchParams(c)));var l=e.search||c&&"?"+c||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==f?(f="//"+(f||""),a&&"/"!==a[0]&&(a="/"+a)):f||(f=""),i&&"#"!==i[0]&&(i="#"+i),l&&"?"!==l[0]&&(l="?"+l),""+u+f+(a=a.replace(/[?#]/g,encodeURIComponent))+(l=l.replace("#","%23"))+i}var a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return u(e)}},19005:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});var n=t(12115);function o(e,r){var t=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(function(n){if(null===n){var a=t.current;a&&(t.current=null,a());var i=o.current;i&&(o.current=null,i())}else e&&(t.current=u(e,n)),r&&(o.current=u(r,n))},[e,r])}function u(e,r){if("function"!=typeof e)return e.current=r,function(){e.current=null};var t=e(r);return"function"==typeof t?t:function(){return e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},30385:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return u}});var n=t(67910),o=t(72663);function u(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{var r=(0,n.getLocationOrigin)(),t=new URL(e,r);return t.origin===r&&(0,o.hasBasePath)(t.pathname)}catch(e){return!1}}},67910:(e,r,t)=>{var n=t(28295),o=t(98557),u=t(80851),a=t(63819),i=t(61626),c=t(69456),f=t(30795),l=t(32525);function s(e){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var t,n=c(e);return t=r?Reflect.construct(n,arguments,c(this).constructor):n.apply(this,arguments),i(this,t)}}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DecodeError:function(){return S},MiddlewareNotFoundError:function(){return N},MissingStaticPage:function(){return C},NormalizeError:function(){return w},PageNotFoundError:function(){return A},SP:function(){return _},ST:function(){return E},WEB_VITALS:function(){return p},execOnce:function(){return d},getDisplayName:function(){return g},getLocationOrigin:function(){return y},getURL:function(){return b},isAbsoluteUrl:function(){return v},isResSent:function(){return m},loadGetInitialProps:function(){return P},normalizeRepeatedSlashes:function(){return O},stringifyError:function(){return T}});var p=["CLS","FCP","FID","INP","LCP","TTFB"];function d(e){var r,t=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return t||(t=!0,r=e.apply(void 0,o)),r}}var h=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,v=function(e){return h.test(e)};function y(){var e=window.location,r=e.protocol,t=e.hostname,n=e.port;return r+"//"+t+(n?":"+n:"")}function b(){var e=window.location.href,r=y();return e.substring(r.length)}function g(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function m(e){return e.finished||e.headersSent}function O(e){var r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}function P(e,r){return j.apply(this,arguments)}function j(){return(j=l(n.mark(function e(r,t){var o,u;return n.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.next=4;break;case 4:if(o=t.res||t.ctx&&t.ctx.res,r.getInitialProps){e.next=12;break}if(!(t.ctx&&t.Component)){e.next=11;break}return e.next=9,P(t.Component,t.ctx);case 9:return e.t0=e.sent,e.abrupt("return",{pageProps:e.t0});case 11:return e.abrupt("return",{});case 12:return e.next=14,r.getInitialProps(t);case 14:if(u=e.sent,!(o&&m(o))){e.next=17;break}return e.abrupt("return",u);case 17:if(u){e.next=20;break}throw Object.defineProperty(Error('"'+g(r)+'.getInitialProps()" should resolve to an object. But found "'+u+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});case 20:return e.abrupt("return",u);case 22:case"end":return e.stop()}},e)}))).apply(this,arguments)}var _="undefined"!=typeof performance,E=_&&["mark","measure","getEntriesByName"].every(function(e){return"function"==typeof performance[e]}),S=function(e){a(t,e);var r=s(t);function t(){return u(this,t),r.apply(this,arguments)}return o(t)}(f(Error)),w=function(e){a(t,e);var r=s(t);function t(){return u(this,t),r.apply(this,arguments)}return o(t)}(f(Error)),A=function(e){a(t,e);var r=s(t);function t(e){var n;return u(this,t),(n=r.call(this)).code="ENOENT",n.name="PageNotFoundError",n.message="Cannot find module for page: "+e,n}return o(t)}(f(Error)),C=function(e){a(t,e);var r=s(t);function t(e,n){var o;return u(this,t),(o=r.call(this)).message="Failed to load static file for page: "+e+" "+n,o}return o(t)}(f(Error)),N=function(e){a(t,e);var r=s(t);function t(){var e;return u(this,t),(e=r.call(this)).code="ENOENT",e.message="Cannot find the middleware module",e}return o(t)}(f(Error));function T(e){return JSON.stringify({message:e.message,stack:e.stack})}},72895:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return t}});var t=function(e){}},73329:(e,r,t)=>{var n=t(43277),o=t(2333),u=t(95289),a=["href","as","children","prefetch","passHref","replace","shallow","scroll","onClick","onMouseEnter","onTouchStart","legacyBehavior","onNavigate","ref","unstable_dynamicOnHover"];function i(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?i(Object(t),!0).forEach(function(r){n(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return j},useLinkStatus:function(){return E}});var f=t(3759),l=t(95155),s=f._(t(12115)),p=t(3464),d=t(79926),h=t(93961),v=t(19005),y=t(67910),b=t(27532);t(46175);var g=t(37841),m=t(30385),O=t(30605);function P(e){return"string"==typeof e?e:(0,p.formatUrl)(e)}function j(e){var r,t,n,i=u((0,s.useOptimistic)(g.IDLE_LINK_STATUS),2),f=i[0],p=i[1],j=(0,s.useRef)(null),E=e.href,S=e.as,w=e.children,A=e.prefetch,C=void 0===A?null:A,N=e.passHref,T=e.replace,k=(e.shallow,e.scroll),x=e.onClick,R=e.onMouseEnter,M=e.onTouchStart,L=e.legacyBehavior,I=void 0!==L&&L,U=e.onNavigate,D=e.ref,B=e.unstable_dynamicOnHover,F=o(e,a);r=w,I&&("string"==typeof r||"number"==typeof r)&&(r=(0,l.jsx)("a",{children:r}));var K=s.default.useContext(d.AppRouterContext),z=!1!==C,H=null===C?h.PrefetchKind.AUTO:h.PrefetchKind.FULL,Q=s.default.useMemo(function(){var e=P(E);return{href:e,as:S?P(S):e}},[E,S]),W=Q.href,q=Q.as;I&&(t=s.default.Children.only(r));var V=I?t&&"object"==typeof t&&t.ref:D,Z=s.default.useCallback(function(e){return null!==K&&(j.current=(0,g.mountLinkInstance)(e,W,K,H,z,p)),function(){j.current&&((0,g.unmountLinkForCurrentNavigation)(j.current),j.current=null),(0,g.unmountPrefetchableInstance)(e)}},[z,W,K,H,p]),G={ref:(0,v.useMergedRef)(Z,V),onClick:function(e){I||"function"!=typeof x||x(e),I&&t.props&&"function"==typeof t.props.onClick&&t.props.onClick(e),K&&(e.defaultPrevented||function(e,r,t,n,o,u,a){var i;if(!("A"===e.currentTarget.nodeName.toUpperCase()&&((i=e.currentTarget.getAttribute("target"))&&"_self"!==i||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which)||e.currentTarget.hasAttribute("download"))){if(!(0,m.isLocalURL)(r)){o&&(e.preventDefault(),location.replace(r));return}e.preventDefault(),s.default.startTransition(function(){if(a){var e=!1;if(a({preventDefault:function(){e=!0}}),e)return}(0,O.dispatchNavigateAction)(t||r,o?"replace":"push",null==u||u,n.current)})}}(e,W,q,j,T,k,U))},onMouseEnter:function(e){I||"function"!=typeof R||R(e),I&&t.props&&"function"==typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),K&&z&&(0,g.onNavigationIntent)(e.currentTarget,!0===B)},onTouchStart:function(e){I||"function"!=typeof M||M(e),I&&t.props&&"function"==typeof t.props.onTouchStart&&t.props.onTouchStart(e),K&&z&&(0,g.onNavigationIntent)(e.currentTarget,!0===B)}};return(0,y.isAbsoluteUrl)(q)?G.href=q:I&&!N&&("a"!==t.type||"href"in t.props)||(G.href=(0,b.addBasePath)(q)),n=I?s.default.cloneElement(t,G):(0,l.jsx)("a",c(c(c({},F),G),{},{children:r})),(0,l.jsx)(_.Provider,{value:f,children:n})}t(72895);var _=(0,s.createContext)(g.IDLE_LINK_STATUS),E=function(){return(0,s.useContext)(_)};("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)}}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5730],{1337:(e,r,t)=>{t.d(r,{A:()=>m});var n=t(10631),s=t(3243),a=t(11157),i=t(59539),u=t(33311),o=t(28295),c=t.n(o),l=t(41779),h=t(44893),p=t(57483),d=t(86628),f=t(7057),v=t(67939),k=t(16864),b=t(98132),x=t(53048),_=t(73476);(0,k.V)();var w={url:h.nf,storageKey:h.AQ,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:h.Rn,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function y(e,r,t){return g.apply(this,arguments)}function g(){return(g=(0,u.A)(c().mark(function e(r,t,n){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}var m=function(){var e,r,t,o,k,g,m,A,S,T,I,R,E,C,L,P,U,O,j,D,q,N,G,H,Y,z,K,F,V,B,M,W,J,Q,X,$,Z,ee,er,et,en,es,ea,ei,eu,eo,ec,el,eh,ep,ed,ef,ev,ek,eb,ex,e_,ew,ey,eg;function em(e){var r,t,n,s=this;(0,a.A)(this,em),this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=em.nextInstanceID,em.nextInstanceID+=1,this.instanceID>0&&(0,f.Bd)()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");var i=Object.assign(Object.assign({},w),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new l.A({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=(0,f.lA)(i.fetch),this.lock=i.lock||y,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:(0,f.Bd)()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=x.JS:this.lock=y,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:(0,f.LJ)()?this.storage=v.y:(this.memoryStorage={},this.storage=(0,v.r)(this.memoryStorage)):(this.memoryStorage={},this.storage=(0,v.r)(this.memoryStorage)),(0,f.Bd)()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(n=this.broadcastChannel)||n.addEventListener("message",(r=(0,u.A)(c().mark(function e(r){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s._debug("received broadcast notification from other tab or client",r),e.next=3,s._notifyAllSubscribers(r.data.event,r.data.session,!1);case 3:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)}))}this.initialize()}return(0,i.A)(em,[{key:"_debug",value:function(){if(this.logDebugMessages){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];this.logger.apply(this,["GoTrueClient@".concat(this.instanceID," (").concat(b.r,") ").concat(new Date().toISOString())].concat(r))}return this}},{key:"initialize",value:(e=(0,u.A)(c().mark(function e(){var r=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.initializePromise){e.next=4;break}return e.next=3,this.initializePromise;case 3:case 7:return e.abrupt("return",e.sent);case 4:return this.initializePromise=(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r._initialize();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))(),e.next=7,this.initializePromise;case 8:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"_initialize",value:(r=(0,u.A)(c().mark(function e(){var r,t,n,s,a,i,o,l,h,d=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t=(0,f.XR)(window.location.href),n="none",!this._isImplicitGrantCallback(t)){e.next=7;break}n="implicit",e.next=11;break;case 7:return e.next=9,this._isPKCECallback(t);case 9:if(!e.sent){e.next=11;break}n="pkce";case 11:if(!((0,f.Bd)()&&this.detectSessionInUrl&&"none"!==n)){e.next=32;break}return e.next=14,this._getSessionFromURL(t,n);case 14:if(a=(s=e.sent).data,!(i=s.error)){e.next=26;break}if(this._debug("#_initialize()","error detecting session from URL",i),!(0,p.MJ)(i)||"identity_already_exists"!==(o=null==(r=i.details)?void 0:r.code)&&"identity_not_found"!==o&&"single_identity_not_deletable"!==o){e.next=23;break}return e.abrupt("return",{error:i});case 23:return e.next=25,this._removeSession();case 25:return e.abrupt("return",{error:i});case 26:return l=a.session,h=a.redirectType,this._debug("#_initialize()","detected session in URL",l,"redirect type",h),e.next=30,this._saveSession(l);case 30:return setTimeout((0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("recovery"!==h){e.next=5;break}return e.next=3,d._notifyAllSubscribers("PASSWORD_RECOVERY",l);case 3:e.next=7;break;case 5:return e.next=7,d._notifyAllSubscribers("SIGNED_IN",l);case 7:case"end":return e.stop()}},e)})),0),e.abrupt("return",{error:null});case 32:return e.next=34,this._recoverAndRefresh();case 34:return e.abrupt("return",{error:null});case 37:if(e.prev=37,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=41;break}return e.abrupt("return",{error:e.t0});case 41:return e.abrupt("return",{error:new p.HU("Unexpected error during initialization",e.t0)});case 42:return e.prev=42,e.next=45,this._handleVisibilityChange();case 45:return this._debug("#_initialize()","end"),e.finish(42);case 47:case"end":return e.stop()}},e,this,[[0,37,42,47]])})),function(){return r.apply(this,arguments)})},{key:"signInAnonymously",value:(t=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,body:{data:null!=(n=null==(t=null==r?void 0:r.options)?void 0:t.data)?n:{},gotrue_meta_security:{captcha_token:null==(s=null==r?void 0:r.options)?void 0:s.captchaToken}},xform:d.R_});case 3:if(i=(a=e.sent).data,!((u=a.error)||!i)){e.next=7;break}return e.abrupt("return",{data:{user:null,session:null},error:u});case 7:if(o=i.session,l=i.user,!i.session){e.next=14;break}return e.next=12,this._saveSession(i.session);case 12:return e.next=14,this._notifyAllSubscribers("SIGNED_IN",o);case 14:return e.abrupt("return",{data:{user:l,session:o},error:null});case 17:if(e.prev=17,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=21;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 21:throw e.t0;case 22:case"end":return e.stop()}},e,this,[[0,17]])})),function(e){return t.apply(this,arguments)})},{key:"signUp",value:(o=(0,u.A)(c().mark(function e(r){var t,n,a,i,u,o,l,h,v,k,b,x,_,w,y,g,m,A,S;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!("email"in r)){e.next=18;break}if(u=r.email,o=r.password,l=r.options,h=null,v=null,"pkce"!==this.flowType){e.next=13;break}return e.next=9,(0,f.dS)(this.storage,this.storageKey);case 9:k=e.sent,h=(b=(0,s.A)(k,2))[0],v=b[1];case 13:return e.next=15,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,redirectTo:null==l?void 0:l.emailRedirectTo,body:{email:u,password:o,data:null!=(t=null==l?void 0:l.data)?t:{},gotrue_meta_security:{captcha_token:null==l?void 0:l.captchaToken},code_challenge:h,code_challenge_method:v},xform:d.R_});case 15:case 22:i=e.sent,e.next=26;break;case 18:if(!("phone"in r)){e.next=25;break}return x=r.phone,_=r.password,w=r.options,e.next=22,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/signup"),{headers:this.headers,body:{phone:x,password:_,data:null!=(n=null==w?void 0:w.data)?n:{},channel:null!=(a=null==w?void 0:w.channel)?a:"sms",gotrue_meta_security:{captcha_token:null==w?void 0:w.captchaToken}},xform:d.R_});case 25:throw new p.U_("You must provide either an email or phone number and a password");case 26:if(g=(y=i).data,!((m=y.error)||!g)){e.next=29;break}return e.abrupt("return",{data:{user:null,session:null},error:m});case 29:if(A=g.session,S=g.user,!g.session){e.next=36;break}return e.next=34,this._saveSession(g.session);case 34:return e.next=36,this._notifyAllSubscribers("SIGNED_IN",A);case 36:return e.abrupt("return",{data:{user:S,session:A},error:null});case 39:if(e.prev=39,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=43;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 43:throw e.t0;case 44:case"end":return e.stop()}},e,this,[[0,39]])})),function(e){return o.apply(this,arguments)})},{key:"signInWithPassword",value:(k=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l,h,f;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!("email"in r)){e.next=8;break}return n=r.email,s=r.password,a=r.options,e.next=5,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/token?grant_type=password"),{headers:this.headers,body:{email:n,password:s,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:d.GC});case 5:case 12:t=e.sent,e.next=16;break;case 8:if(!("phone"in r)){e.next=15;break}return i=r.phone,u=r.password,o=r.options,e.next=12,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/token?grant_type=password"),{headers:this.headers,body:{phone:i,password:u,gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:d.GC});case 15:throw new p.U_("You must provide either an email or phone number and a password");case 16:if(h=(l=t).data,!(f=l.error)){e.next=21;break}return e.abrupt("return",{data:{user:null,session:null},error:f});case 21:if(!(!h||!h.session||!h.user)){e.next=23;break}return e.abrupt("return",{data:{user:null,session:null},error:new p.Ox});case 23:if(!h.session){e.next=28;break}return e.next=26,this._saveSession(h.session);case 26:return e.next=28,this._notifyAllSubscribers("SIGNED_IN",h.session);case 28:return e.abrupt("return",{data:Object.assign({user:h.user,session:h.session},h.weak_password?{weakPassword:h.weak_password}:null),error:f});case 31:if(e.prev=31,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=35;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 35:throw e.t0;case 36:case"end":return e.stop()}},e,this,[[0,31]])})),function(e){return k.apply(this,arguments)})},{key:"signInWithOAuth",value:(g=(0,u.A)(c().mark(function e(r){var t,n,s,a;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._handleProviderSignIn(r.provider,{redirectTo:null==(t=r.options)?void 0:t.redirectTo,scopes:null==(n=r.options)?void 0:n.scopes,queryParams:null==(s=r.options)?void 0:s.queryParams,skipBrowserRedirect:null==(a=r.options)?void 0:a.skipBrowserRedirect});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e){return g.apply(this,arguments)})},{key:"exchangeCodeForSession",value:(m=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.initializePromise;case 2:return e.abrupt("return",this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",t._exchangeCodeForSession(r));case 1:case"end":return e.stop()}},e)}))));case 3:case"end":return e.stop()}},e,this)})),function(e){return m.apply(this,arguments)})},{key:"_exchangeCodeForSession",value:(A=(0,u.A)(c().mark(function e(r){var t,n,a,i,u,o,l,h;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,f.U9)(this.storage,"".concat(this.storageKey,"-code-verifier"));case 2:return n=(null!=(t=e.sent)?t:"").split("/"),i=(a=(0,s.A)(n,2))[0],u=a[1],e.prev=4,e.next=7,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/token?grant_type=pkce"),{headers:this.headers,body:{auth_code:r,code_verifier:i},xform:d.R_});case 7:return l=(o=e.sent).data,h=o.error,e.next=12,(0,f.Gb)(this.storage,"".concat(this.storageKey,"-code-verifier"));case 12:if(!h){e.next=14;break}throw h;case 14:if(!(!l||!l.session||!l.user)){e.next=16;break}return e.abrupt("return",{data:{user:null,session:null,redirectType:null},error:new p.Ox});case 16:if(!l.session){e.next=21;break}return e.next=19,this._saveSession(l.session);case 19:return e.next=21,this._notifyAllSubscribers("SIGNED_IN",l.session);case 21:return e.abrupt("return",{data:Object.assign(Object.assign({},l),{redirectType:null!=u?u:null}),error:h});case 24:if(e.prev=24,e.t0=e.catch(4),!(0,p.HY)(e.t0)){e.next=28;break}return e.abrupt("return",{data:{user:null,session:null,redirectType:null},error:e.t0});case 28:throw e.t0;case 29:case"end":return e.stop()}},e,this,[[4,24]])})),function(e){return A.apply(this,arguments)})},{key:"signInWithIdToken",value:(S=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=r.options,n=r.provider,s=r.token,a=r.access_token,i=r.nonce,e.next=4,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/token?grant_type=id_token"),{headers:this.headers,body:{provider:n,id_token:s,access_token:a,nonce:i,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:d.R_});case 4:if(o=(u=e.sent).data,!(l=u.error)){e.next=10;break}return e.abrupt("return",{data:{user:null,session:null},error:l});case 10:if(!(!o||!o.session||!o.user)){e.next=12;break}return e.abrupt("return",{data:{user:null,session:null},error:new p.Ox});case 12:if(!o.session){e.next=17;break}return e.next=15,this._saveSession(o.session);case 15:return e.next=17,this._notifyAllSubscribers("SIGNED_IN",o.session);case 17:return e.abrupt("return",{data:o,error:l});case 20:if(e.prev=20,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=24;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 24:throw e.t0;case 25:case"end":return e.stop()}},e,this,[[0,20]])})),function(e){return S.apply(this,arguments)})},{key:"signInWithOtp",value:(T=(0,u.A)(c().mark(function e(r){var t,n,a,i,u,o,l,h,v,k,b,x,_,w,y,g,m;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!("email"in r)){e.next=18;break}if(o=r.email,l=r.options,h=null,v=null,"pkce"!==this.flowType){e.next=13;break}return e.next=9,(0,f.dS)(this.storage,this.storageKey);case 9:k=e.sent,h=(b=(0,s.A)(k,2))[0],v=b[1];case 13:return e.next=15,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/otp"),{headers:this.headers,body:{email:o,data:null!=(t=null==l?void 0:l.data)?t:{},create_user:null==(n=null==l?void 0:l.shouldCreateUser)||n,gotrue_meta_security:{captcha_token:null==l?void 0:l.captchaToken},code_challenge:h,code_challenge_method:v},redirectTo:null==l?void 0:l.emailRedirectTo});case 15:return x=e.sent.error,e.abrupt("return",{data:{user:null,session:null},error:x});case 18:if(!("phone"in r)){e.next=26;break}return _=r.phone,w=r.options,e.next=22,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/otp"),{headers:this.headers,body:{phone:_,data:null!=(a=null==w?void 0:w.data)?a:{},create_user:null==(i=null==w?void 0:w.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==w?void 0:w.captchaToken},channel:null!=(u=null==w?void 0:w.channel)?u:"sms"}});case 22:return g=(y=e.sent).data,m=y.error,e.abrupt("return",{data:{user:null,session:null,messageId:null==g?void 0:g.message_id},error:m});case 26:throw new p.U_("You must provide either an email or phone number.");case 29:if(e.prev=29,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=33;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 33:throw e.t0;case 34:case"end":return e.stop()}},e,this,[[0,29]])})),function(e){return T.apply(this,arguments)})},{key:"verifyOtp",value:(I=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l,h;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,s=void 0,a=void 0,"options"in r&&(s=null==(t=r.options)?void 0:t.redirectTo,a=null==(n=r.options)?void 0:n.captchaToken),e.next=6,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/verify"),{headers:this.headers,body:Object.assign(Object.assign({},r),{gotrue_meta_security:{captcha_token:a}}),redirectTo:s,xform:d.R_});case 6:if(u=(i=e.sent).data,!(o=i.error)){e.next=11;break}throw o;case 11:if(u){e.next=13;break}throw Error("An error occurred on token verification.");case 13:if(l=u.session,h=u.user,!(null==l?void 0:l.access_token)){e.next=20;break}return e.next=18,this._saveSession(l);case 18:return e.next=20,this._notifyAllSubscribers("recovery"==r.type?"PASSWORD_RECOVERY":"SIGNED_IN",l);case 20:return e.abrupt("return",{data:{user:h,session:l},error:null});case 23:if(e.prev=23,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=27;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 27:throw e.t0;case 28:case"end":return e.stop()}},e,this,[[0,23]])})),function(e){return I.apply(this,arguments)})},{key:"signInWithSSO",value:(R=(0,u.A)(c().mark(function e(r){var t,n,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,i=null,u=null,"pkce"!==this.flowType){e.next=11;break}return e.next=7,(0,f.dS)(this.storage,this.storageKey);case 7:o=e.sent,i=(l=(0,s.A)(o,2))[0],u=l[1];case 11:return e.next=13,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/sso"),{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in r?{provider_id:r.providerId}:null),"domain"in r?{domain:r.domain}:null),{redirect_to:null!=(n=null==(t=r.options)?void 0:t.redirectTo)?n:void 0}),(null==(a=null==r?void 0:r.options)?void 0:a.captchaToken)?{gotrue_meta_security:{captcha_token:r.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:u}),headers:this.headers,xform:d.c2});case 13:return e.abrupt("return",e.sent);case 16:if(e.prev=16,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=20;break}return e.abrupt("return",{data:null,error:e.t0});case 20:throw e.t0;case 21:case"end":return e.stop()}},e,this,[[0,16]])})),function(e){return R.apply(this,arguments)})},{key:"reauthenticate",value:(E=(0,u.A)(c().mark(function e(){var r=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.initializePromise;case 2:return e.next=4,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r._reauthenticate();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e,this)})),function(){return E.apply(this,arguments)})},{key:"_reauthenticate",value:(C=(0,u.A)(c().mark(function e(){var r=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(t){var n,s,a;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.data.session,!(s=t.error)){e.next=3;break}throw s;case 3:if(n){e.next=5;break}throw new p.jG;case 5:return e.next=7,(0,d.vE)(r.fetch,"GET","".concat(r.url,"/reauthenticate"),{headers:r.headers,jwt:n.access_token});case 7:return a=e.sent.error,e.abrupt("return",{data:{user:null,session:null},error:a});case 10:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(){return C.apply(this,arguments)})},{key:"resend",value:(L=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l,h,f,v;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t="".concat(this.url,"/resend"),!("email"in r)){e.next=11;break}return n=r.email,s=r.type,a=r.options,e.next=6,(0,d.vE)(this.fetch,"POST",t,{headers:this.headers,body:{email:n,type:s,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},redirectTo:null==a?void 0:a.emailRedirectTo});case 6:return i=e.sent.error,e.abrupt("return",{data:{user:null,session:null},error:i});case 11:if(!("phone"in r)){e.next=19;break}return u=r.phone,o=r.type,l=r.options,e.next=15,(0,d.vE)(this.fetch,"POST",t,{headers:this.headers,body:{phone:u,type:o,gotrue_meta_security:{captcha_token:null==l?void 0:l.captchaToken}}});case 15:return f=(h=e.sent).data,v=h.error,e.abrupt("return",{data:{user:null,session:null,messageId:null==f?void 0:f.message_id},error:v});case 19:throw new p.U_("You must provide either an email or phone number and a type");case 22:if(e.prev=22,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=26;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 26:throw e.t0;case 27:case"end":return e.stop()}},e,this,[[0,22]])})),function(e){return L.apply(this,arguments)})},{key:"getSession",value:(P=(0,u.A)(c().mark(function e(){var r,t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.initializePromise;case 2:return e.next=4,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",t._useSession(function(){var e=(0,u.A)(c().mark(function e(r){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",r);case 1:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}()));case 1:case"end":return e.stop()}},e)})));case 4:return r=e.sent,e.abrupt("return",r);case 6:case"end":return e.stop()}},e,this)})),function(){return P.apply(this,arguments)})},{key:"_acquireLock",value:(U=(0,u.A)(c().mark(function e(r,t){var s,a,i=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("#_acquireLock","begin",r),e.prev=1,!this.lockAcquired){e.next=7;break}return s=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),a=(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s;case 2:return e.next=4,t();case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e)}))(),this.pendingInLock.push((0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a;case 3:e.next=7;break;case 5:e.prev=5,e.t0=e.catch(0);case 7:case"end":return e.stop()}},e,null,[[0,5]])}))()),e.abrupt("return",a);case 7:return e.next=9,this.lock("lock:".concat(this.storageKey),r,(0,u.A)(c().mark(function e(){var r,s;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i._debug("#_acquireLock","lock acquired for storage key",i.storageKey),e.prev=1,i.lockAcquired=!0,r=t(),i.pendingInLock.push((0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,r;case 3:e.next=7;break;case 5:e.prev=5,e.t0=e.catch(0);case 7:case"end":return e.stop()}},e,null,[[0,5]])}))()),e.next=7,r;case 7:if(!i.pendingInLock.length){e.next=14;break}return s=(0,n.A)(i.pendingInLock),e.next=11,Promise.all(s);case 11:i.pendingInLock.splice(0,s.length),e.next=7;break;case 14:return e.next=16,r;case 16:return e.abrupt("return",e.sent);case 17:return e.prev=17,i._debug("#_acquireLock","lock released for storage key",i.storageKey),i.lockAcquired=!1,e.finish(17);case 21:case"end":return e.stop()}},e,null,[[1,,17,21]])})));case 9:return e.abrupt("return",e.sent);case 10:return e.prev=10,this._debug("#_acquireLock","end"),e.finish(10);case 13:case"end":return e.stop()}},e,this,[[1,,10,13]])})),function(e,r){return U.apply(this,arguments)})},{key:"_useSession",value:(O=(0,u.A)(c().mark(function e(r){var t;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("#_useSession","begin"),e.prev=1,e.next=4,this.__loadSession();case 4:return t=e.sent,e.next=7,r(t);case 7:return e.abrupt("return",e.sent);case 8:return e.prev=8,this._debug("#_useSession","end"),e.finish(8);case 11:case"end":return e.stop()}},e,this,[[1,,8,11]])})),function(e){return O.apply(this,arguments)})},{key:"__loadSession",value:(j=(0,u.A)(c().mark(function e(){var r,t,n,s,a,i,u,o=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack),e.prev=2,r=null,e.next=6,(0,f.U9)(this.storage,this.storageKey);case 6:if(t=e.sent,this._debug("#getSession()","session from storage",t),null===t){e.next=16;break}if(!this._isValidSession(t)){e.next=13;break}r=t,e.next=16;break;case 13:return this._debug("#getSession()","session from storage is not valid"),e.next=16,this._removeSession();case 16:if(r){e.next=18;break}return e.abrupt("return",{data:{session:null},error:null});case 18:if(n=!!r.expires_at&&1e3*r.expires_at-Date.now()<h.CT,this._debug("#__loadSession()","session has".concat(n?"":" not"," expired"),"expires_at",r.expires_at),n){e.next=23;break}return this.storage.isServer&&(s=this.suppressGetSessionWarning,r=new Proxy(r,{get:function(e,r,t){return s||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),s=!0,o.suppressGetSessionWarning=!0),Reflect.get(e,r,t)}})),e.abrupt("return",{data:{session:r},error:null});case 23:return e.next=25,this._callRefreshToken(r.refresh_token);case 25:if(i=(a=e.sent).session,!(u=a.error)){e.next=30;break}return e.abrupt("return",{data:{session:null},error:u});case 30:return e.abrupt("return",{data:{session:i},error:null});case 31:return e.prev=31,this._debug("#__loadSession()","end"),e.finish(31);case 34:case"end":return e.stop()}},e,this,[[2,,31,34]])})),function(){return j.apply(this,arguments)})},{key:"getUser",value:(D=(0,u.A)(c().mark(function e(r){var t,n=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!r){e.next=4;break}return e.next=3,this._getUser(r);case 3:return e.abrupt("return",e.sent);case 4:return e.next=6,this.initializePromise;case 6:return e.next=8,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n._getUser();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 8:return t=e.sent,e.abrupt("return",t);case 10:case"end":return e.stop()}},e,this)})),function(e){return D.apply(this,arguments)})},{key:"_getUser",value:(q=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!r){e.next=5;break}return e.next=4,(0,d.vE)(this.fetch,"GET","".concat(this.url,"/user"),{headers:this.headers,jwt:r,xform:d.Cl});case 4:case 7:return e.abrupt("return",e.sent);case 5:return e.next=7,this._useSession(function(){var e=(0,u.A)(c().mark(function e(r){var n,s,a,i,u;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=r.data,!(u=r.error)){e.next=3;break}throw u;case 3:if(!(!(null==(n=i.session)?void 0:n.access_token)&&!t.hasCustomAuthorizationHeader)){e.next=5;break}return e.abrupt("return",{data:{user:null},error:new p.jG});case 5:return e.next=7,(0,d.vE)(t.fetch,"GET","".concat(t.url,"/user"),{headers:t.headers,jwt:null!=(a=null==(s=i.session)?void 0:s.access_token)?a:void 0,xform:d.Cl});case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 10:if(e.prev=10,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=19;break}if(!(0,p.zq)(e.t0)){e.next=18;break}return e.next=16,this._removeSession();case 16:return e.next=18,(0,f.Gb)(this.storage,"".concat(this.storageKey,"-code-verifier"));case 18:return e.abrupt("return",{data:{user:null},error:e.t0});case 19:throw e.t0;case 20:case"end":return e.stop()}},e,this,[[0,10]])})),function(e){return q.apply(this,arguments)})},{key:"updateUser",value:(N=(0,u.A)(c().mark(function e(r){var t,n=this,s=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=s.length>1&&void 0!==s[1]?s[1]:{},e.next=3,this.initializePromise;case 3:return e.next=5,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n._updateUser(r,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e,this)})),function(e){return N.apply(this,arguments)})},{key:"_updateUser",value:(G=(0,u.A)(c().mark(function e(r){var t,n=this,a=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.length>1&&void 0!==a[1]?a[1]:{},e.prev=1,e.next=4,this._useSession(function(){var e=(0,u.A)(c().mark(function e(a){var i,u,o,l,h,v,k,b,x,_;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=a.data,!(u=a.error)){e.next=3;break}throw u;case 3:if(i.session){e.next=5;break}throw new p.jG;case 5:if(o=i.session,l=null,h=null,"pkce"!==n.flowType||null==r.email){e.next=16;break}return e.next=12,(0,f.dS)(n.storage,n.storageKey);case 12:v=e.sent,l=(k=(0,s.A)(v,2))[0],h=k[1];case 16:return e.next=18,(0,d.vE)(n.fetch,"PUT","".concat(n.url,"/user"),{headers:n.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},r),{code_challenge:l,code_challenge_method:h}),jwt:o.access_token,xform:d.Cl});case 18:if(x=(b=e.sent).data,!(_=b.error)){e.next=23;break}throw _;case 23:return o.user=x.user,e.next=26,n._saveSession(o);case 26:return e.next=28,n._notifyAllSubscribers("USER_UPDATED",o);case 28:return e.abrupt("return",{data:{user:o.user},error:null});case 29:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 4:return e.abrupt("return",e.sent);case 7:if(e.prev=7,e.t0=e.catch(1),!(0,p.HY)(e.t0)){e.next=11;break}return e.abrupt("return",{data:{user:null},error:e.t0});case 11:throw e.t0;case 12:case"end":return e.stop()}},e,this,[[1,7]])})),function(e){return G.apply(this,arguments)})},{key:"setSession",value:(H=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.initializePromise;case 2:return e.next=4,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t._setSession(r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e,this)})),function(e){return H.apply(this,arguments)})},{key:"_setSession",value:(Y=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l,h,d,v;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!(!r.access_token||!r.refresh_token)){e.next=3;break}throw new p.jG;case 3:if(n=t=Date.now()/1e3,s=!0,a=null,(i=(0,f.Cq)(r.access_token).payload).exp&&(s=(n=i.exp)<=t),!s){e.next=22;break}return e.next=12,this._callRefreshToken(r.refresh_token);case 12:if(o=(u=e.sent).session,!(l=u.error)){e.next=17;break}return e.abrupt("return",{data:{user:null,session:null},error:l});case 17:if(o){e.next=19;break}return e.abrupt("return",{data:{user:null,session:null},error:null});case 19:a=o,e.next=34;break;case 22:return e.next=24,this._getUser(r.access_token);case 24:if(d=(h=e.sent).data,!(v=h.error)){e.next=29;break}throw v;case 29:return a={access_token:r.access_token,refresh_token:r.refresh_token,user:d.user,token_type:"bearer",expires_in:n-t,expires_at:n},e.next=32,this._saveSession(a);case 32:return e.next=34,this._notifyAllSubscribers("SIGNED_IN",a);case 34:return e.abrupt("return",{data:{user:a.user,session:a},error:null});case 37:if(e.prev=37,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=41;break}return e.abrupt("return",{data:{session:null,user:null},error:e.t0});case 41:throw e.t0;case 42:case"end":return e.stop()}},e,this,[[0,37]])})),function(e){return Y.apply(this,arguments)})},{key:"refreshSession",value:(z=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.initializePromise;case 2:return e.next=4,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t._refreshSession(r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e,this)})),function(e){return z.apply(this,arguments)})},{key:"_refreshSession",value:(K=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r){e.next=5;break}if(a=n.data,!(i=n.error)){e.next=4;break}throw i;case 4:r=null!=(s=a.session)?s:void 0;case 5:if(null==r?void 0:r.refresh_token){e.next=7;break}throw new p.jG;case 7:return e.next=9,t._callRefreshToken(r.refresh_token);case 9:if(o=(u=e.sent).session,!(l=u.error)){e.next=14;break}return e.abrupt("return",{data:{user:null,session:null},error:l});case 14:if(o){e.next=16;break}return e.abrupt("return",{data:{user:null,session:null},error:null});case 16:return e.abrupt("return",{data:{user:o.user,session:o},error:null});case 17:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:{user:null,session:null},error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e){return K.apply(this,arguments)})},{key:"_getSessionFromURL",value:(F=(0,u.A)(c().mark(function e(r,t){var n,s,a,i,u,o,l,d,v,k,b,x,_,w,y,g,m,A,S,T;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,(0,f.Bd)()){e.next=3;break}throw new p.MR("No browser detected.");case 3:if(!(r.error||r.error_description||r.error_code)){e.next=5;break}throw new p.MR(r.error_description||"Error in URL with unspecified error_description",{error:r.error||"unspecified_error",code:r.error_code||"unspecified_code"});case 5:e.t0=t,e.next="implicit"===e.t0?8:"pkce"===e.t0?11:14;break;case 8:if("pkce"!==this.flowType){e.next=10;break}throw new p.Uw("Not a valid PKCE flow url.");case 10:case 13:return e.abrupt("break",14);case 11:if("implicit"!==this.flowType){e.next=13;break}throw new p.MR("Not a valid implicit grant flow url.");case 14:if("pkce"!==t){e.next=29;break}if(this._debug("#_initialize()","begin","is PKCE flow",!0),r.code){e.next=18;break}throw new p.Uw("No code detected.");case 18:return e.next=20,this._exchangeCodeForSession(r.code);case 20:if(s=(n=e.sent).data,!(a=n.error)){e.next=25;break}throw a;case 25:return(i=new URL(window.location.href)).searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),e.abrupt("return",{data:{session:s.session,redirectType:null},error:null});case 29:if(u=r.provider_token,o=r.provider_refresh_token,l=r.access_token,d=r.refresh_token,v=r.expires_in,k=r.expires_at,b=r.token_type,!(!l||!v||!d||!b)){e.next=32;break}throw new p.MR("No session defined in URL");case 32:return w=(x=Math.round(Date.now()/1e3))+(_=parseInt(v)),k&&(w=parseInt(k)),1e3*(y=w-x)<=h.a4&&console.warn("@supabase/gotrue-js: Session as retrieved from URL expires in ".concat(y,"s, should have been closer to ").concat(_,"s")),x-(g=w-_)>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",g,w,x):x-g<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",g,w,x),e.next=42,this._getUser(l);case 42:if(A=(m=e.sent).data,!(S=m.error)){e.next=47;break}throw S;case 47:return T={provider_token:u,provider_refresh_token:o,access_token:l,expires_in:_,expires_at:w,refresh_token:d,token_type:b,user:A.user},window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),e.abrupt("return",{data:{session:T,redirectType:r.type},error:null});case 53:if(e.prev=53,e.t1=e.catch(0),!(0,p.HY)(e.t1)){e.next=57;break}return e.abrupt("return",{data:{session:null,redirectType:null},error:e.t1});case 57:throw e.t1;case 58:case"end":return e.stop()}},e,this,[[0,53]])})),function(e,r){return F.apply(this,arguments)})},{key:"_isImplicitGrantCallback",value:function(e){return!!(e.access_token||e.error_description)}},{key:"_isPKCECallback",value:(V=(0,u.A)(c().mark(function e(r){var t;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,f.U9)(this.storage,"".concat(this.storageKey,"-code-verifier"));case 2:return t=e.sent,e.abrupt("return",!!(r.code&&t));case 4:case"end":return e.stop()}},e,this)})),function(e){return V.apply(this,arguments)})},{key:"signOut",value:(B=(0,u.A)(c().mark(function e(){var r,t=this,n=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>0&&void 0!==n[0]?n[0]:{scope:"global"},e.next=3,this.initializePromise;case 3:return e.next=5,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t._signOut(r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})));case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e,this)})),function(){return B.apply(this,arguments)})},{key:"_signOut",value:(M=(0,u.A)(c().mark(function e(){var r,t=this,n=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=(n.length>0&&void 0!==n[0]?n[0]:{scope:"global"}).scope,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i,u,o;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n.data,!(i=n.error)){e.next=3;break}return e.abrupt("return",{error:i});case 3:if(!(u=null==(s=a.session)?void 0:s.access_token)){e.next=12;break}return e.next=7,t.admin.signOut(u,r);case 7:if(!(o=e.sent.error)||(0,p.NA)(o)&&(404===o.status||401===o.status||403===o.status)){e.next=12;break}return e.abrupt("return",{error:o});case 12:if("others"===r){e.next=17;break}return e.next=15,t._removeSession();case 15:return e.next=17,(0,f.Gb)(t.storage,"".concat(t.storageKey,"-code-verifier"));case 17:return e.abrupt("return",{error:null});case 18:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(){return M.apply(this,arguments)})},{key:"onAuthStateChange",value:function(e){var r=this,t=(0,f.uR)(),n={id:t,callback:e,unsubscribe:function(){r._debug("#unsubscribe()","state change callback with id removed",t),r.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,n),(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.initializePromise;case 2:return e.next=4,r._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r._emitInitialSession(t);case 1:case"end":return e.stop()}},e)})));case 4:case"end":return e.stop()}},e)}))(),{data:{subscription:n}}}},{key:"_emitInitialSession",value:(W=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i,u;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,i=n.data.session,!(u=n.error)){e.next=4;break}throw u;case 4:return e.next=6,null==(s=t.stateChangeEmitters.get(r))?void 0:s.callback("INITIAL_SESSION",i);case 6:t._debug("INITIAL_SESSION","callback id",r,"session",i),e.next=15;break;case 9:return e.prev=9,e.t0=e.catch(0),e.next=13,null==(a=t.stateChangeEmitters.get(r))?void 0:a.callback("INITIAL_SESSION",null);case 13:t._debug("INITIAL_SESSION","callback id",r,"error",e.t0),console.error(e.t0);case 15:case"end":return e.stop()}},e,null,[[0,9]])}));return function(r){return e.apply(this,arguments)}}());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(e){return W.apply(this,arguments)})},{key:"resetPasswordForEmail",value:(J=(0,u.A)(c().mark(function e(r){var t,n,a,i,u,o=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=o.length>1&&void 0!==o[1]?o[1]:{},n=null,a=null,"pkce"!==this.flowType){e.next=11;break}return e.next=7,(0,f.dS)(this.storage,this.storageKey,!0);case 7:i=e.sent,n=(u=(0,s.A)(i,2))[0],a=u[1];case 11:return e.prev=11,e.next=14,(0,d.vE)(this.fetch,"POST","".concat(this.url,"/recover"),{body:{email:r,code_challenge:n,code_challenge_method:a,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo});case 14:return e.abrupt("return",e.sent);case 17:if(e.prev=17,e.t0=e.catch(11),!(0,p.HY)(e.t0)){e.next=21;break}return e.abrupt("return",{data:null,error:e.t0});case 21:throw e.t0;case 22:case"end":return e.stop()}},e,this,[[11,17]])})),function(e){return J.apply(this,arguments)})},{key:"getUserIdentities",value:(Q=(0,u.A)(c().mark(function e(){var r,t,n,s;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getUser();case 3:if(n=(t=e.sent).data,!(s=t.error)){e.next=8;break}throw s;case 8:return e.abrupt("return",{data:{identities:null!=(r=n.user.identities)?r:[]},error:null});case 11:if(e.prev=11,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=15;break}return e.abrupt("return",{data:null,error:e.t0});case 15:throw e.t0;case 16:case"end":return e.stop()}},e,this,[[0,11]])})),function(){return Q.apply(this,arguments)})},{key:"linkIdentity",value:(X=(0,u.A)(c().mark(function e(r){var t,n,s,a,i=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(t){var n,s,a,u,o,l,h,p;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(l=t.data,!(h=t.error)){e.next=3;break}throw h;case 3:return e.next=5,i._getUrlForProvider("".concat(i.url,"/user/identities/authorize"),r.provider,{redirectTo:null==(n=r.options)?void 0:n.redirectTo,scopes:null==(s=r.options)?void 0:s.scopes,queryParams:null==(a=r.options)?void 0:a.queryParams,skipBrowserRedirect:!0});case 5:return p=e.sent,e.next=8,(0,d.vE)(i.fetch,"GET",p,{headers:i.headers,jwt:null!=(o=null==(u=l.session)?void 0:u.access_token)?o:void 0});case 8:return e.abrupt("return",e.sent);case 9:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:if(s=(n=e.sent).data,!(a=n.error)){e.next=8;break}throw a;case 8:return!(0,f.Bd)()||(null==(t=r.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==s?void 0:s.url),e.abrupt("return",{data:{provider:r.provider,url:null==s?void 0:s.url},error:null});case 12:if(e.prev=12,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=16;break}return e.abrupt("return",{data:{provider:r.provider,url:null},error:e.t0});case 16:throw e.t0;case 17:case"end":return e.stop()}},e,this,[[0,12]])})),function(e){return X.apply(this,arguments)})},{key:"unlinkIdentity",value:($=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i,u;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=n.data,!(u=n.error)){e.next=3;break}throw u;case 3:return e.next=5,(0,d.vE)(t.fetch,"DELETE","".concat(t.url,"/user/identities/").concat(r.identity_id),{headers:t.headers,jwt:null!=(a=null==(s=i.session)?void 0:s.access_token)?a:void 0});case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:null,error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e){return $.apply(this,arguments)})},{key:"_refreshAccessToken",value:(Z=(0,u.A)(c().mark(function e(r){var t,n,s=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="#_refreshAccessToken(".concat(r.substring(0,5),"...)"),this._debug(t,"begin"),e.prev=2,n=Date.now(),e.next=6,(0,f.PB)(function(){var e=(0,u.A)(c().mark(function e(n){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n>0)){e.next=3;break}return e.next=3,(0,f.yy)(200*Math.pow(2,n-1));case 3:return s._debug(t,"refreshing attempt",n),e.next=6,(0,d.vE)(s.fetch,"POST","".concat(s.url,"/token?grant_type=refresh_token"),{body:{refresh_token:r},headers:s.headers,xform:d.R_});case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}(),function(e,r){var t=200*Math.pow(2,e);return r&&(0,p.Fw)(r)&&Date.now()+t-n<h.a4});case 6:return e.abrupt("return",e.sent);case 9:if(e.prev=9,e.t0=e.catch(2),this._debug(t,"error",e.t0),!(0,p.HY)(e.t0)){e.next=14;break}return e.abrupt("return",{data:{session:null,user:null},error:e.t0});case 14:throw e.t0;case 15:return e.prev=15,this._debug(t,"end"),e.finish(15);case 18:case"end":return e.stop()}},e,this,[[2,9,15,18]])})),function(e){return Z.apply(this,arguments)})},{key:"_isValidSession",value:function(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}},{key:"_handleProviderSignIn",value:(ee=(0,u.A)(c().mark(function e(r,t){var n;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._getUrlForProvider("".concat(this.url,"/authorize"),r,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});case 2:return n=e.sent,this._debug("#_handleProviderSignIn()","provider",r,"options",t,"url",n),(0,f.Bd)()&&!t.skipBrowserRedirect&&window.location.assign(n),e.abrupt("return",{data:{provider:r,url:n},error:null});case 6:case"end":return e.stop()}},e,this)})),function(e,r){return ee.apply(this,arguments)})},{key:"_recoverAndRefresh",value:(er=(0,u.A)(c().mark(function e(){var r,t,n,s,a;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="#_recoverAndRefresh()",this._debug(t,"begin"),e.prev=2,e.next=5,(0,f.U9)(this.storage,this.storageKey);case 5:if(n=e.sent,this._debug(t,"session from storage",n),this._isValidSession(n)){e.next=13;break}if(this._debug(t,"session is not valid"),null===n){e.next=12;break}return e.next=12,this._removeSession();case 12:return e.abrupt("return");case 13:if(s=(null!=(r=n.expires_at)?r:1/0)*1e3-Date.now()<h.CT,this._debug(t,"session has".concat(s?"":" not"," expired with margin of ").concat(h.CT,"s")),!s){e.next=29;break}if(!(this.autoRefreshToken&&n.refresh_token)){e.next=27;break}return e.next=19,this._callRefreshToken(n.refresh_token);case 19:if(!(a=e.sent.error)||(console.error(a),(0,p.Fw)(a))){e.next=27;break}return this._debug(t,"refresh failed with a non-retryable error, removing the session",a),e.next=27,this._removeSession();case 27:e.next=31;break;case 29:return e.next=31,this._notifyAllSubscribers("SIGNED_IN",n);case 31:e.next=38;break;case 33:return e.prev=33,e.t0=e.catch(2),this._debug(t,"error",e.t0),console.error(e.t0),e.abrupt("return");case 38:return e.prev=38,this._debug(t,"end"),e.finish(38);case 41:case"end":return e.stop()}},e,this,[[2,33,38,41]])})),function(){return er.apply(this,arguments)})},{key:"_callRefreshToken",value:(et=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r){e.next=2;break}throw new p.jG;case 2:if(!this.refreshingDeferred){e.next=4;break}return e.abrupt("return",this.refreshingDeferred.promise);case 4:return s="#_callRefreshToken(".concat(r.substring(0,5),"...)"),this._debug(s,"begin"),e.prev=6,this.refreshingDeferred=new f.cY,e.next=10,this._refreshAccessToken(r);case 10:if(i=(a=e.sent).data,!(u=a.error)){e.next=15;break}throw u;case 15:if(i.session){e.next=17;break}throw new p.jG;case 17:return e.next=19,this._saveSession(i.session);case 19:return e.next=21,this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);case 21:return o={session:i.session,error:null},this.refreshingDeferred.resolve(o),e.abrupt("return",o);case 26:if(e.prev=26,e.t0=e.catch(6),this._debug(s,"error",e.t0),!(0,p.HY)(e.t0)){e.next=36;break}if(l={session:null,error:e.t0},(0,p.Fw)(e.t0)){e.next=34;break}return e.next=34,this._removeSession();case 34:return null==(t=this.refreshingDeferred)||t.resolve(l),e.abrupt("return",l);case 36:throw null==(n=this.refreshingDeferred)||n.reject(e.t0),e.t0;case 38:return e.prev=38,this.refreshingDeferred=null,this._debug(s,"end"),e.finish(38);case 42:case"end":return e.stop()}},e,this,[[6,26,38,42]])})),function(e){return et.apply(this,arguments)})},{key:"_notifyAllSubscribers",value:(en=(0,u.A)(c().mark(function e(r,t){var n,s,a,i,o,l=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=!(l.length>2)||void 0===l[2]||l[2],s="#_notifyAllSubscribers(".concat(r,")"),this._debug(s,"begin",t,"broadcast = ".concat(n)),e.prev=3,this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:r,session:t}),a=[],i=Array.from(this.stateChangeEmitters.values()).map(function(){var e=(0,u.A)(c().mark(function e(n){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.callback(r,t);case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),a.push(e.t0);case 8:case"end":return e.stop()}},e,null,[[0,5]])}));return function(r){return e.apply(this,arguments)}}()),e.next=9,Promise.all(i);case 9:if(!(a.length>0)){e.next=12;break}for(o=0;o<a.length;o+=1)console.error(a[o]);throw a[0];case 12:return e.prev=12,this._debug(s,"end"),e.finish(12);case 15:case"end":return e.stop()}},e,this,[[3,,12,15]])})),function(e,r){return en.apply(this,arguments)})},{key:"_saveSession",value:(es=(0,u.A)(c().mark(function e(r){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("#_saveSession()",r),this.suppressGetSessionWarning=!0,e.next=4,(0,f.Oi)(this.storage,this.storageKey,r);case 4:case"end":return e.stop()}},e,this)})),function(e){return es.apply(this,arguments)})},{key:"_removeSession",value:(ea=(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("#_removeSession()"),e.next=3,(0,f.Gb)(this.storage,this.storageKey);case 3:return e.next=5,this._notifyAllSubscribers("SIGNED_OUT",null);case 5:case"end":return e.stop()}},e,this)})),function(){return ea.apply(this,arguments)})},{key:"_removeVisibilityChangedCallback",value:function(){this._debug("#_removeVisibilityChangedCallback()");var e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&(0,f.Bd)()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}},{key:"_startAutoRefresh",value:(ei=(0,u.A)(c().mark(function e(){var r,t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._stopAutoRefresh();case 2:this._debug("#_startAutoRefresh()"),r=setInterval(function(){return t._autoRefreshTokenTick()},h.a4),this.autoRefreshTicker=r,r&&"object"==typeof r&&"function"==typeof r.unref?r.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(r),setTimeout((0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.initializePromise;case 2:return e.next=4,t._autoRefreshTokenTick();case 4:case"end":return e.stop()}},e)})),0);case 7:case"end":return e.stop()}},e,this)})),function(){return ei.apply(this,arguments)})},{key:"_stopAutoRefresh",value:(eu=(0,u.A)(c().mark(function e(){var r;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this._debug("#_stopAutoRefresh()"),r=this.autoRefreshTicker,this.autoRefreshTicker=null,r&&clearInterval(r);case 4:case"end":return e.stop()}},e,this)})),function(){return eu.apply(this,arguments)})},{key:"startAutoRefresh",value:(eo=(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._removeVisibilityChangedCallback(),e.next=3,this._startAutoRefresh();case 3:case"end":return e.stop()}},e,this)})),function(){return eo.apply(this,arguments)})},{key:"stopAutoRefresh",value:(ec=(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._removeVisibilityChangedCallback(),e.next=3,this._stopAutoRefresh();case 3:case"end":return e.stop()}},e,this)})),function(){return ec.apply(this,arguments)})},{key:"_autoRefreshTokenTick",value:(el=(0,u.A)(c().mark(function e(){var r=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("#_autoRefreshTokenTick()","begin"),e.prev=1,e.next=4,this._acquireLock(0,(0,u.A)(c().mark(function e(){var t;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=Date.now(),e.prev=2,e.next=5,r._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(!(s=n.data.session)||!s.refresh_token||!s.expires_at)){e.next=4;break}return r._debug("#_autoRefreshTokenTick()","no session"),e.abrupt("return");case 4:if(a=Math.floor((1e3*s.expires_at-t)/h.a4),r._debug("#_autoRefreshTokenTick()","access token expires in ".concat(a," ticks, a tick lasts ").concat(h.a4,"ms, refresh threshold is ").concat(h.YI," ticks")),!(a<=h.YI)){e.next=9;break}return e.next=9,r._callRefreshToken(s.refresh_token);case 9:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 5:return e.abrupt("return",e.sent);case 8:e.prev=8,e.t0=e.catch(2),console.error("Auto refresh tick failed with error. This is likely a transient error.",e.t0);case 11:return e.prev=11,r._debug("#_autoRefreshTokenTick()","end"),e.finish(11);case 14:case"end":return e.stop()}},e,null,[[0,,11,14],[2,8]])})));case 4:e.next=13;break;case 6:if(e.prev=6,e.t0=e.catch(1),!(e.t0.isAcquireTimeout||e.t0 instanceof x.nb)){e.next=12;break}this._debug("auto refresh token tick lock not available"),e.next=13;break;case 12:throw e.t0;case 13:case"end":return e.stop()}},e,this,[[1,6]])})),function(){return el.apply(this,arguments)})},{key:"_handleVisibilityChange",value:(eh=(0,u.A)(c().mark(function e(){var r=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("#_handleVisibilityChange()"),!(!(0,f.Bd)()||!(null==window?void 0:window.addEventListener))){e.next=4;break}return this.autoRefreshToken&&this.startAutoRefresh(),e.abrupt("return",!1);case 4:return e.prev=4,this.visibilityChangedCallback=(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r._onVisibilityChanged(!1);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),e.next=9,this._onVisibilityChanged(!0);case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),console.error("_handleVisibilityChange",e.t0);case 14:case"end":return e.stop()}},e,this,[[4,11]])})),function(){return eh.apply(this,arguments)})},{key:"_onVisibilityChanged",value:(ep=(0,u.A)(c().mark(function e(r){var t,n=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t="#_onVisibilityChanged(".concat(r,")"),this._debug(t,"visibilityState",document.visibilityState),"visible"!==document.visibilityState){e.next=11;break}if(this.autoRefreshToken&&this._startAutoRefresh(),r){e.next=9;break}return e.next=7,this.initializePromise;case 7:return e.next=9,this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("visible"===document.visibilityState){e.next=3;break}return n._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting"),e.abrupt("return");case 3:return e.next=5,n._recoverAndRefresh();case 5:case"end":return e.stop()}},e)})));case 9:e.next=12;break;case 11:"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh();case 12:case"end":return e.stop()}},e,this)})),function(e){return ep.apply(this,arguments)})},{key:"_getUrlForProvider",value:(ed=(0,u.A)(c().mark(function e(r,t,n){var a,i,u,o,l,h,p;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=["provider=".concat(encodeURIComponent(t))],(null==n?void 0:n.redirectTo)&&a.push("redirect_to=".concat(encodeURIComponent(n.redirectTo))),(null==n?void 0:n.scopes)&&a.push("scopes=".concat(encodeURIComponent(n.scopes))),"pkce"!==this.flowType){e.next=12;break}return e.next=6,(0,f.dS)(this.storage,this.storageKey);case 6:i=e.sent,o=(u=(0,s.A)(i,2))[0],l=u[1],h=new URLSearchParams({code_challenge:"".concat(encodeURIComponent(o)),code_challenge_method:"".concat(encodeURIComponent(l))}),a.push(h.toString());case 12:return(null==n?void 0:n.queryParams)&&(p=new URLSearchParams(n.queryParams),a.push(p.toString())),(null==n?void 0:n.skipBrowserRedirect)&&a.push("skip_http_redirect=".concat(n.skipBrowserRedirect)),e.abrupt("return","".concat(r,"?").concat(a.join("&")));case 15:case"end":return e.stop()}},e,this)})),function(e,r,t){return ed.apply(this,arguments)})},{key:"_unenroll",value:(ef=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n.data,!(i=n.error)){e.next=3;break}return e.abrupt("return",{data:null,error:i});case 3:return e.next=5,(0,d.vE)(t.fetch,"DELETE","".concat(t.url,"/factors/").concat(r.factorId),{headers:t.headers,jwt:null==(s=null==a?void 0:a.session)?void 0:s.access_token});case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:null,error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e){return ef.apply(this,arguments)})},{key:"_enroll",value:(ev=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i,u,o,l,h,p;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=n.data,!(u=n.error)){e.next=3;break}return e.abrupt("return",{data:null,error:u});case 3:return o=Object.assign({friendly_name:r.friendlyName,factor_type:r.factorType},"phone"===r.factorType?{phone:r.phone}:{issuer:r.issuer}),e.next=6,(0,d.vE)(t.fetch,"POST","".concat(t.url,"/factors"),{body:o,headers:t.headers,jwt:null==(s=null==i?void 0:i.session)?void 0:s.access_token});case 6:if(h=(l=e.sent).data,!(p=l.error)){e.next=11;break}return e.abrupt("return",{data:null,error:p});case 11:return"totp"===r.factorType&&(null==(a=null==h?void 0:h.totp)?void 0:a.qr_code)&&(h.totp.qr_code="data:image/svg+xml;utf-8,".concat(h.totp.qr_code)),e.abrupt("return",{data:h,error:null});case 13:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:null,error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,this,[[0,6]])})),function(e){return ev.apply(this,arguments)})},{key:"_verify",value:(ek=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n.data,!(i=n.error)){e.next=3;break}return e.abrupt("return",{data:null,error:i});case 3:return e.next=5,(0,d.vE)(t.fetch,"POST","".concat(t.url,"/factors/").concat(r.factorId,"/verify"),{body:{code:r.code,challenge_id:r.challengeId},headers:t.headers,jwt:null==(s=null==a?void 0:a.session)?void 0:s.access_token});case 5:if(o=(u=e.sent).data,!(l=u.error)){e.next=10;break}return e.abrupt("return",{data:null,error:l});case 10:return e.next=12,t._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o));case 12:return e.next=14,t._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o);case 14:return e.abrupt("return",{data:o,error:l});case 15:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:null,error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,null,[[0,6]])}))));case 1:case"end":return e.stop()}},e,this)})),function(e){return ek.apply(this,arguments)})},{key:"_challenge",value:(eb=(0,u.A)(c().mark(function e(r){var t=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t._useSession(function(){var e=(0,u.A)(c().mark(function e(n){var s,a,i;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n.data,!(i=n.error)){e.next=3;break}return e.abrupt("return",{data:null,error:i});case 3:return e.next=5,(0,d.vE)(t.fetch,"POST","".concat(t.url,"/factors/").concat(r.factorId,"/challenge"),{body:{channel:r.channel},headers:t.headers,jwt:null==(s=null==a?void 0:a.session)?void 0:s.access_token});case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 3:return e.abrupt("return",e.sent);case 6:if(e.prev=6,e.t0=e.catch(0),!(0,p.HY)(e.t0)){e.next=10;break}return e.abrupt("return",{data:null,error:e.t0});case 10:throw e.t0;case 11:case"end":return e.stop()}},e,null,[[0,6]])}))));case 1:case"end":return e.stop()}},e,this)})),function(e){return eb.apply(this,arguments)})},{key:"_challengeAndVerify",value:(ex=(0,u.A)(c().mark(function e(r){var t,n,s;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._challenge({factorId:r.factorId});case 2:if(n=(t=e.sent).data,!(s=t.error)){e.next=7;break}return e.abrupt("return",{data:null,error:s});case 7:return e.next=9,this._verify({factorId:r.factorId,challengeId:n.id,code:r.code});case 9:return e.abrupt("return",e.sent);case 10:case"end":return e.stop()}},e,this)})),function(e){return ex.apply(this,arguments)})},{key:"_listFactors",value:(e_=(0,u.A)(c().mark(function e(){var r,t,n,s,a,i;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getUser();case 2:if(t=(r=e.sent).data.user,!(n=r.error)){e.next=7;break}return e.abrupt("return",{data:null,error:n});case 7:return a=(s=(null==t?void 0:t.factors)||[]).filter(function(e){return"totp"===e.factor_type&&"verified"===e.status}),i=s.filter(function(e){return"phone"===e.factor_type&&"verified"===e.status}),e.abrupt("return",{data:{all:s,totp:a,phone:i},error:null});case 11:case"end":return e.stop()}},e,this)})),function(){return e_.apply(this,arguments)})},{key:"_getAuthenticatorAssuranceLevel",value:(ew=(0,u.A)(c().mark(function e(){var r=this;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this._acquireLock(-1,(0,u.A)(c().mark(function e(){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r._useSession(function(){var e=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(s=r.data.session,!(a=r.error)){e.next=3;break}return e.abrupt("return",{data:null,error:a});case 3:if(s){e.next=5;break}return e.abrupt("return",{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null});case 5:return i=(0,f.Cq)(s.access_token).payload,u=null,i.aal&&(u=i.aal),o=u,(null!=(n=null==(t=s.user.factors)?void 0:t.filter(function(e){return"verified"===e.status}))?n:[]).length>0&&(o="aal2"),l=i.amr||[],e.abrupt("return",{data:{currentLevel:u,nextLevel:o,currentAuthenticationMethods:l},error:null});case 13:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}());case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))));case 1:case"end":return e.stop()}},e,this)})),function(){return ew.apply(this,arguments)})},{key:"fetchJwk",value:(ey=(0,u.A)(c().mark(function e(r){var t,n,s,a,i=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(i.length>1&&void 0!==i[1]?i[1]:{keys:[]}).keys.find(function(e){return e.kid===r}))){e.next=4;break}return e.abrupt("return",t);case 4:if(!((t=this.jwks.keys.find(function(e){return e.kid===r}))&&this.jwks_cached_at+h.$W>Date.now())){e.next=7;break}return e.abrupt("return",t);case 7:return e.next=9,(0,d.vE)(this.fetch,"GET","".concat(this.url,"/.well-known/jwks.json"),{headers:this.headers});case 9:if(s=(n=e.sent).data,!(a=n.error)){e.next=14;break}throw a;case 14:if(!(!s.keys||0===s.keys.length)){e.next=16;break}throw new p.xL("JWKS is empty");case 16:if(this.jwks=s,this.jwks_cached_at=Date.now(),t=s.keys.find(function(e){return e.kid===r})){e.next=21;break}throw new p.xL("No matching signing key found in JWKS");case 21:return e.abrupt("return",t);case 22:case"end":return e.stop()}},e,this)})),function(e){return ey.apply(this,arguments)})},{key:"getClaims",value:(eg=(0,u.A)(c().mark(function e(r){var t,n,s,a,i,u,o,l,h,d,v,k,b,x,w,y,g=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=g.length>1&&void 0!==g[1]?g[1]:{keys:[]},e.prev=1,n=r){e.next=12;break}return e.next=6,this.getSession();case 6:if(a=(s=e.sent).data,!((i=s.error)||!a.session)){e.next=11;break}return e.abrupt("return",{data:null,error:i});case 11:n=a.session.access_token;case 12:if(o=(u=(0,f.Cq)(n)).header,l=u.payload,h=u.signature,v=(d=u.raw).header,k=d.payload,(0,f.c2)(l.exp),!(!o.kid||"HS256"===o.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto))){e.next=22;break}return e.next=17,this.getUser(n);case 17:if(!(b=e.sent.error)){e.next=21;break}throw b;case 21:case 34:return e.abrupt("return",{data:{claims:l,header:o,signature:h},error:null});case 22:return x=(0,f.K8)(o.alg),e.next=25,this.fetchJwk(o.kid,t);case 25:return w=e.sent,e.next=28,crypto.subtle.importKey("jwk",w,x,!0,["verify"]);case 28:return y=e.sent,e.next=31,crypto.subtle.verify(x,y,h,(0,_.Al)("".concat(v,".").concat(k)));case 31:if(e.sent){e.next=34;break}throw new p.xL("Invalid JWT signature");case 37:if(e.prev=37,e.t0=e.catch(1),!(0,p.HY)(e.t0)){e.next=41;break}return e.abrupt("return",{data:null,error:e.t0});case 41:throw e.t0;case 42:case"end":return e.stop()}},e,this,[[1,37]])})),function(e){return eg.apply(this,arguments)})}]),em}();m.nextInstanceID=0}}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4659],{20144:(t,e,n)=>{n.d(e,{G:()=>y,WI:()=>v,eq:()=>p});var r=n(10631),i=n(11157),o=n(59539),a=n(6639),s=n(49077),u=n(90064),c=n(44686),l=n(8644),f=n(77167);function d(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return h(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(t,e)}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var p=f.ZS.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),v=function(t){return JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:")},y=function(t){(0,s.A)(l,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,c.A)(l);return t=e?Reflect.construct(n,arguments,(0,c.A)(this).constructor):n.apply(this,arguments),(0,u.A)(this,t)});function l(t){(0,i.A)(this,l),(e=n.call(this)).issues=[],e.addIssue=function(t){e.issues=[].concat((0,r.A)(e.issues),[t])},e.addIssues=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];e.issues=[].concat((0,r.A)(e.issues),(0,r.A)(t))};var e,o=(this instanceof l?this.constructor:void 0).prototype;return Object.setPrototypeOf?Object.setPrototypeOf((0,a.A)(e),o):e.__proto__=o,e.name="ZodError",e.issues=t,e}return(0,o.A)(l,[{key:"errors",get:function(){return this.issues}},{key:"format",value:function(t){var e=t||function(t){return t.message},n={_errors:[]};return!function t(r){var i,o=d(r.issues);try{for(o.s();!(i=o.n()).done;){var a=i.value;if("invalid_union"===a.code)a.unionErrors.map(t);else if("invalid_return_type"===a.code)t(a.returnTypeError);else if("invalid_arguments"===a.code)t(a.argumentsError);else if(0===a.path.length)n._errors.push(e(a));else for(var s=n,u=0;u<a.path.length;){var c=a.path[u];u===a.path.length-1?(s[c]=s[c]||{_errors:[]},s[c]._errors.push(e(a))):s[c]=s[c]||{_errors:[]},s=s[c],u++}}}catch(t){o.e(t)}finally{o.f()}}(this),n}},{key:"toString",value:function(){return this.message}},{key:"message",get:function(){return JSON.stringify(this.issues,f.ZS.jsonStringifyReplacer,2)}},{key:"isEmpty",get:function(){return 0===this.issues.length}},{key:"flatten",value:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(t){return t.message},n={},r=[],i=d(this.issues);try{for(i.s();!(t=i.n()).done;){var o=t.value;o.path.length>0?(n[o.path[0]]=n[o.path[0]]||[],n[o.path[0]].push(e(o))):r.push(e(o))}}catch(t){i.e(t)}finally{i.f()}return{formErrors:r,fieldErrors:n}}},{key:"formErrors",get:function(){return this.flatten()}}],[{key:"assert",value:function(t){if(!(t instanceof l))throw Error("Not a ZodError: ".concat(t))}}]),l}((0,l.A)(Error));y.create=function(t){return new y(t)}},21410:(t,e,n)=>{n.d(e,{Gb:()=>U,Jt:()=>F,hZ:()=>R,mN:()=>tw});var r=n(10631),i=n(33311),o=n(37711),a=n(3243),s=n(61810),u=n(28295),c=n(12115),l=["_f"],f=["name"],d=["_f"],h=["ref","message","type"];function p(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return v(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(t,e)}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach(function(e){(0,o.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var g=function(t){return"checkbox"===t.type},b=function(t){return t instanceof Date},O=function(t){return null==t},A=function(t){return"object"==typeof t},x=function(t){return!O(t)&&!Array.isArray(t)&&A(t)&&!b(t)},P=function(t,e){return t.has(e.substring(0,e.search(/\.\d+(\.|$)/))||e)},w=function(t){var e=t.constructor&&t.constructor.prototype;return x(e)&&e.hasOwnProperty("isPrototypeOf")},k=void 0!==window.HTMLElement&&"undefined"!=typeof document;function j(t){var e,n=Array.isArray(t),r="undefined"!=typeof FileList&&t instanceof FileList;if(t instanceof Date)e=new Date(t);else if(t instanceof Set)e=new Set(t);else if(!(!(k&&(t instanceof Blob||r))&&(n||x(t))))return t;else if(e=n?[]:{},n||w(t))for(var i in t)t.hasOwnProperty(i)&&(e[i]=j(t[i]));else e=t;return e}var S=function(t){return Array.isArray(t)?t.filter(Boolean):[]},D=function(t){return void 0===t},F=function(t,e,n){if(!e||!x(t))return n;var r=S(e.split(/[,[\].]+?/)).reduce(function(t,e){return O(t)?t:t[e]},t);return D(r)||r===t?D(t[e])?n:t[e]:r},E=function(t){return"boolean"==typeof t},T=function(t){return/^\w*$/.test(t)},V=function(t){return S(t.replace(/["|']|\]/g,"").split(/\.|\[/))},R=function(t,e,n){for(var r=-1,i=T(e)?[e]:V(e),o=i.length,a=o-1;++r<o;){var s=i[r],u=n;if(r!==a){var c=t[s];u=x(c)||Array.isArray(c)?c:isNaN(+i[r+1])?{}:[]}if("__proto__"===s||"constructor"===s||"prototype"===s)return;t[s]=u,t=t[s]}},C={BLUR:"blur",FOCUS_OUT:"focusout"},M={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},B={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},_=function(t,e,n){var r=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i={defaultValues:e._defaultValues},o=function(o){Object.defineProperty(i,o,{get:function(){return e._proxyFormState[o]!==M.all&&(e._proxyFormState[o]=!r||M.all),n&&(n[o]=!0),t[o]}})};for(var a in t)o(a);return i},L=c.useLayoutEffect,I=function(t){return"string"==typeof t},U=function(t,e,n,r,i){return e?m(m({},n[t]),{},{types:m(m({},n[t]&&n[t].types?n[t].types:{}),{},(0,o.A)({},r,i||!0))}):{}},N=function(t){return Array.isArray(t)?t:[t]},Z=function(){var t=[];return{get observers(){return t},next:function(e){var n,r=p(t);try{for(r.s();!(n=r.n()).done;){var i=n.value;i.next&&i.next(e)}}catch(t){r.e(t)}finally{r.f()}},subscribe:function(e){return t.push(e),{unsubscribe:function(){t=t.filter(function(t){return t!==e})}}},unsubscribe:function(){t=[]}}},W=function(t){return O(t)||!A(t)};function q(t,e){if(W(t)||W(e))return t===e;if(b(t)&&b(e))return t.getTime()===e.getTime();var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(var i=0;i<n.length;i++){var o=n[i],a=t[o];if(!r.includes(o))return!1;if("ref"!==o){var s=e[o];if(b(a)&&b(s)||x(a)&&x(s)||Array.isArray(a)&&Array.isArray(s)?!q(a,s):a!==s)return!1}}return!0}var z=function(t){return x(t)&&!Object.keys(t).length},Y=function(t){return"file"===t.type},K=function(t){return"function"==typeof t},X=function(t){if(!k)return!1;var e=t?t.ownerDocument:0;return t instanceof(e&&e.defaultView?e.defaultView.HTMLElement:HTMLElement)},H=function(t){return"select-multiple"===t.type},G=function(t){return"radio"===t.type},$=function(t){return X(t)&&t.isConnected};function J(t,e){var n=Array.isArray(e)?e:T(e)?[e]:V(e),r=1===n.length?t:function(t,e){for(var n=e.slice(0,-1).length,r=0;r<n;)t=D(t)?r++:t[e[r++]];return t}(t,n),i=n.length-1,o=n[i];return r&&delete r[o],0!==i&&(x(r)&&z(r)||Array.isArray(r)&&function(t){for(var e in t)if(t.hasOwnProperty(e)&&!D(t[e]))return!1;return!0}(r))&&J(t,n.slice(0,-1)),t}var Q=function(t){for(var e in t)if(K(t[e]))return!0;return!1};function tt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Array.isArray(t);if(x(t)||n)for(var r in t)Array.isArray(t[r])||x(t[r])&&!Q(t[r])?(e[r]=Array.isArray(t[r])?[]:{},tt(t[r],e[r])):O(t[r])||(e[r]=!0);return e}var te=function(t,e){return function t(e,n,r){var i=Array.isArray(e);if(x(e)||i)for(var o in e)Array.isArray(e[o])||x(e[o])&&!Q(e[o])?D(n)||W(r[o])?r[o]=Array.isArray(e[o])?tt(e[o],[]):m({},tt(e[o])):t(e[o],O(n)?{}:n[o],r[o]):r[o]=!q(e[o],n[o]);return r}(t,e,tt(e))},tn={value:!1,isValid:!1},tr={value:!0,isValid:!0},ti=function(t){if(Array.isArray(t)){if(t.length>1){var e=t.filter(function(t){return t&&t.checked&&!t.disabled}).map(function(t){return t.value});return{value:e,isValid:!!e.length}}return t[0].checked&&!t[0].disabled?t[0].attributes&&!D(t[0].attributes.value)?D(t[0].value)||""===t[0].value?tr:{value:t[0].value,isValid:!0}:tr:tn}return tn},to=function(t,e){var n=e.valueAsNumber,r=e.valueAsDate,i=e.setValueAs;return D(t)?t:n?""===t?NaN:t?+t:t:r&&I(t)?new Date(t):i?i(t):t},ta={isValid:!1,value:null},ts=function(t){return Array.isArray(t)?t.reduce(function(t,e){return e&&e.checked&&!e.disabled?{isValid:!0,value:e.value}:t},ta):ta};function tu(t){var e=t.ref;return Y(e)?e.files:G(e)?ts(t.refs).value:H(e)?(0,r.A)(e.selectedOptions).map(function(t){return t.value}):g(e)?ti(t.refs).value:to(D(e.value)?t.ref.value:e.value,t)}var tc=function(t,e,n,i){var o,a={},s=p(t);try{for(s.s();!(o=s.n()).done;){var u=o.value,c=F(e,u);c&&R(a,u,c._f)}}catch(t){s.e(t)}finally{s.f()}return{criteriaMode:n,names:(0,r.A)(t),fields:a,shouldUseNativeValidation:i}},tl=function(t){return t instanceof RegExp},tf=function(t){return D(t)?t:tl(t)?t.source:x(t)?tl(t.value)?t.value.source:t.value:t},td=function(t){return{isOnSubmit:!t||t===M.onSubmit,isOnBlur:t===M.onBlur,isOnChange:t===M.onChange,isOnAll:t===M.all,isOnTouch:t===M.onTouched}},th="AsyncFunction",tp=function(t,e,n){return!n&&(e.watchAll||e.watch.has(t)||(0,r.A)(e.watch).some(function(e){return t.startsWith(e)&&/^\.\w+/.test(t.slice(e.length))}))},tv=function t(e,n,r,i){var o,a=p(r||Object.keys(e));try{for(a.s();!(o=a.n()).done;){var u=o.value,c=F(e,u);if(c){var f=c._f,d=(0,s.A)(c,l);if(f){if(f.refs&&f.refs[0]&&n(f.refs[0],u)&&!i)return!0;else if(f.ref&&n(f.ref,f.name)&&!i)return!0;else if(t(d,n))break}else if(x(d)&&t(d,n))break}}}catch(t){a.e(t)}finally{a.f()}};function ty(t,e,n){var r=F(t,n);if(r||T(n))return{error:r,name:n};for(var i=n.split(".");i.length;){var o=i.join("."),a=F(e,o),s=F(t,o);if(a&&!Array.isArray(a)&&n!==o)break;if(s&&s.type)return{name:o,error:s};i.pop()}return{name:n}}var tm=function(t,e,n,r){n(t),t.name;var i=(0,s.A)(t,f);return z(i)||Object.keys(i).length>=Object.keys(e).length||Object.keys(i).find(function(t){return e[t]===(!r||M.all)})},tg=function(t,e,n){var r=N(F(t,n));return R(r,"root",e[n]),R(t,n,r),t},tb=function(t){return I(t)};function tO(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"validate";if(tb(t)||Array.isArray(t)&&t.every(tb)||E(t)&&!t)return{type:n,message:tb(t)?t:"",ref:e}}var tA=function(t){return x(t)&&!tl(t)?t:{value:t,message:""}},tx=function(){var t=(0,i.A)(u.mark(function t(e,n,r,i,o,a){var s,c,l,f,d,h,p,v,y,b,A,P,w,k,j,S,T,V,R,C,M,_,L,N,Z,W,q,H,$,J,Q,tt,te,tn,tr,to,ta,tu,tc,tf,td,th,tp,tv,ty,tm;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(c=(s=e._f).ref,l=s.refs,f=s.required,d=s.maxLength,h=s.minLength,p=s.min,v=s.max,y=s.pattern,b=s.validate,A=s.name,P=s.valueAsNumber,w=s.mount,k=F(r,A),!(!w||n.has(A))){t.next=4;break}return t.abrupt("return",{});case 4:if(j=l?l[0]:c,S=function(t){o&&j.reportValidity&&(j.setCustomValidity(E(t)?"":t||""),j.reportValidity())},T={},V=G(c),R=g(c),C=V||R,M=(P||Y(c))&&D(c.value)&&D(k)||X(c)&&""===c.value||""===k||Array.isArray(k)&&!k.length,_=U.bind(null,A,i,T),L=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:B.maxLength,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:B.minLength,o=t?e:n;T[A]=m({type:t?r:i,message:o,ref:c},_(t?r:i,o))},!(a?!Array.isArray(k)||!k.length:f&&(!C&&(M||O(k))||E(k)&&!k||R&&!ti(l).isValid||V&&!ts(l).isValid))||(Z=(N=tb(f)?{value:!!f,message:f}:tA(f)).value,W=N.message,!Z)||(T[A]=m({type:B.required,message:W,ref:j},_(B.required,W)),i)){t.next=20;break}return S(W),t.abrupt("return",T);case 20:if(!(!M&&(!O(p)||!O(v)))||($=tA(v),J=tA(p),O(k)||isNaN(k)?(tt=c.valueAsDate||new Date(k),te=function(t){return new Date(new Date().toDateString()+" "+t)},tn="time"==c.type,tr="week"==c.type,I($.value)&&k&&(q=tn?te(k)>te($.value):tr?k>$.value:tt>new Date($.value)),I(J.value)&&k&&(H=tn?te(k)<te(J.value):tr?k<J.value:tt<new Date(J.value))):(Q=c.valueAsNumber||(k?+k:k),O($.value)||(q=Q>$.value),O(J.value)||(H=Q<J.value)),!(q||H))||(L(!!q,$.message,J.message,B.max,B.min),i)){t.next=29;break}return S(T[A].message),t.abrupt("return",T);case 29:if(!((d||h)&&!M&&(I(k)||a&&Array.isArray(k)))||(to=tA(d),ta=tA(h),tu=!O(to.value)&&k.length>+to.value,tc=!O(ta.value)&&k.length<+ta.value,!(tu||tc))||(L(tu,to.message,ta.message),i)){t.next=39;break}return S(T[A].message),t.abrupt("return",T);case 39:if(!(y&&!M&&I(k))||(td=(tf=tA(y)).value,th=tf.message,!(tl(td)&&!k.match(td)))||(T[A]=m({type:B.pattern,message:th,ref:c},_(B.pattern,th)),i)){t.next=46;break}return S(th),t.abrupt("return",T);case 46:if(!b){t.next=80;break}if(!K(b)){t.next=59;break}return t.next=50,b(k,r);case 50:if(!(tp=tO(t.sent,j))||(T[A]=m(m({},tp),_(B.validate,tp.message)),i)){t.next=57;break}return S(tp.message),t.abrupt("return",T);case 57:t.next=80;break;case 59:if(!x(b)){t.next=80;break}tv={},t.t0=u.keys(b);case 62:if((t.t1=t.t0()).done){t.next=76;break}if(ty=t.t1.value,!(!z(tv)&&!i)){t.next=66;break}return t.abrupt("break",76);case 66:return t.t2=tO,t.next=69,b[ty](k,r);case 69:t.t3=t.sent,t.t4=j,t.t5=ty,(tm=(0,t.t2)(t.t3,t.t4,t.t5))&&(tv=m(m({},tm),_(ty,tm.message)),S(tm.message),i&&(T[A]=tv)),t.next=62;break;case 76:if(z(tv)||(T[A]=m({ref:j},tv),i)){t.next=80;break}return t.abrupt("return",T);case 80:return S(!0),t.abrupt("return",T);case 82:case"end":return t.stop()}},t)}));return function(e,n,r,i,o,a){return t.apply(this,arguments)}}(),tP={mode:M.onSubmit,reValidateMode:M.onChange,shouldFocusError:!0};function tw(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=c.useRef(void 0),n=c.useRef(void 0),l=c.useState({isDirty:!1,isValidating:!1,isLoading:K(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,isReady:!1,defaultValues:K(t.defaultValues)?void 0:t.defaultValues}),f=(0,a.A)(l,2),v=f[0],y=f[1];!e.current&&(e.current=m(m({},t.formControl?t.formControl:function(){var t,e,n,a,c,l,f,v=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},y=m(m({},tP),v),A={submitCount:0,isDirty:!1,isReady:!1,isLoading:K(y.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:y.errors||{},disabled:y.disabled||!1},w={},T=(x(y.defaultValues)||x(y.values))&&j(y.defaultValues||y.values)||{},V=y.shouldUnregister?{}:j(T),B={action:!1,mount:!1,watch:!1},_={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},L=0,U={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},W=m({},U),Q={array:Z(),state:Z()},tt=y.criteriaMode===M.all,tn=(t=(0,i.A)(u.mark(function t(e){var n;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(!y.disabled&&(U.isValid||W.isValid||e))){t.next=14;break}if(!y.resolver){t.next=9;break}return t.t1=z,t.next=5,tb();case 5:t.t2=t.sent.errors,t.t0=(0,t.t1)(t.t2),t.next=12;break;case 9:return t.next=11,tA(w,!0);case 11:t.t0=t.sent;case 12:(n=t.t0)!==A.isValid&&Q.state.next({isValid:n});case 14:case"end":return t.stop()}},t)})),function(e){return t.apply(this,arguments)}),tr=function(t,e){!y.disabled&&(U.isValidating||U.validatingFields||W.isValidating||W.validatingFields)&&((t||Array.from(_.mount)).forEach(function(t){t&&(e?R(A.validatingFields,t,e):J(A.validatingFields,t))}),Q.state.next({validatingFields:A.validatingFields,isValidating:!z(A.validatingFields)}))},ti=function(t,e){R(A.errors,t,e),Q.state.next({errors:A.errors})},ta=function(t,e,n,r){var i=F(w,t);if(i){var o=F(V,t,D(n)?F(T,t):n);D(o)||r&&r.defaultChecked||e?R(V,t,e?o:tu(i._f)):tj(t,o),B.mount&&tn()}},ts=function(t,e,n,r,i){var o=!1,a=!1,s={name:t};if(!y.disabled){if(!n||r){(U.isDirty||W.isDirty)&&(a=A.isDirty,A.isDirty=s.isDirty=tw(),o=a!==s.isDirty);var u=q(F(T,t),e);a=!!F(A.dirtyFields,t),u?J(A.dirtyFields,t):R(A.dirtyFields,t,!0),s.dirtyFields=A.dirtyFields,o=o||(U.dirtyFields||W.dirtyFields)&&!u!==a}if(n){var c=F(A.touchedFields,t);c||(R(A.touchedFields,t,n),s.touchedFields=A.touchedFields,o=o||(U.touchedFields||W.touchedFields)&&c!==n)}o&&i&&Q.state.next(s)}return o?s:{}},tl=function(t,e,n,r){var i,o=F(A.errors,t),a=(U.isValid||W.isValid)&&E(e)&&A.isValid!==e;if(y.delayError&&n?(i=function(){return ti(t,n)},(f=function(t){clearTimeout(L),L=setTimeout(i,t)})(y.delayError)):(clearTimeout(L),f=null,n?R(A.errors,t,n):J(A.errors,t)),(n?!q(o,n):o)||!z(r)||a){var s=m(m(m({},r),a&&E(e)?{isValid:e}:{}),{},{errors:A.errors,name:t});A=m(m({},A),s),Q.state.next(s)}},tb=(e=(0,i.A)(u.mark(function t(e){var n;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return tr(e,!0),t.next=3,y.resolver(V,y.context,tc(e||_.mount,w,y.criteriaMode,y.shouldUseNativeValidation));case 3:return n=t.sent,tr(e),t.abrupt("return",n);case 6:case"end":return t.stop()}},t)})),function(t){return e.apply(this,arguments)}),tO=(n=(0,i.A)(u.mark(function t(e){var n,r,i,o,a;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,tb(e);case 2:if(n=t.sent.errors,e){r=p(e);try{for(r.s();!(i=r.n()).done;)o=i.value,(a=F(n,o))?R(A.errors,o,a):J(A.errors,o)}catch(t){r.e(t)}finally{r.f()}}else A.errors=n;return t.abrupt("return",n);case 6:case"end":return t.stop()}},t)})),function(t){return n.apply(this,arguments)}),tA=(a=(0,i.A)(u.mark(function t(e,n){var r,i,o,a,c,l,f,h,p=arguments;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=p.length>2&&void 0!==p[2]?p[2]:{valid:!0},t.t0=u.keys(e);case 2:var v;if((t.t1=t.t0()).done){t.next=26;break}if(!(o=e[i=t.t1.value])){t.next=24;break}if(a=o._f,c=(0,s.A)(o,d),!a){t.next=20;break}return l=_.array.has(a.name),(f=o._f&&!!(v=o._f)&&!!v.validate&&!!(K(v.validate)&&v.validate.constructor.name===th||x(v.validate)&&Object.values(v.validate).find(function(t){return t.constructor.name===th})))&&U.validatingFields&&tr([i],!0),t.next=13,tx(o,_.disabled,V,tt,y.shouldUseNativeValidation&&!n,l);case 13:if(h=t.sent,f&&U.validatingFields&&tr([i]),!h[a.name]||(r.valid=!1,!n)){t.next=19;break}return t.abrupt("break",26);case 19:n||(F(h,a.name)?l?tg(A.errors,h,a.name):R(A.errors,a.name,h[a.name]):J(A.errors,a.name));case 20:if(t.t2=!z(c),!t.t2){t.next=24;break}return t.next=24,tA(c,n,r);case 24:t.next=2;break;case 26:return t.abrupt("return",r.valid);case 27:case"end":return t.stop()}},t)})),function(t,e){return a.apply(this,arguments)}),tw=function(t,e){return!y.disabled&&(t&&e&&R(V,t,e),!q(tV(),T))},tk=function(t,e,n){var r,i,a,s,u;return r=t,i=_,a=m({},B.mount?V:D(e)?T:I(t)?(0,o.A)({},t,e):e),s=n,u=e,I(r)?(s&&i.watch.add(r),F(a,r,u)):Array.isArray(r)?r.map(function(t){return s&&i.watch.add(t),F(a,t)}):(s&&(i.watchAll=!0),a)},tj=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=F(w,t),o=e;if(i){var a=i._f;a&&(a.disabled||R(V,t,to(e,a)),o=X(a.ref)&&O(e)?"":e,H(a.ref)?(0,r.A)(a.ref.options).forEach(function(t){return t.selected=o.includes(t.value)}):a.refs?g(a.ref)?a.refs.forEach(function(t){t.defaultChecked&&t.disabled||(Array.isArray(o)?t.checked=!!o.find(function(e){return e===t.value}):t.checked=o===t.value||!!o)}):a.refs.forEach(function(t){return t.checked=t.value===o}):Y(a.ref)?a.ref.value="":(a.ref.value=o,a.ref.type||Q.state.next({name:t,values:j(V)})))}(n.shouldDirty||n.shouldTouch)&&ts(t,o,n.shouldTouch,n.shouldDirty,!0),n.shouldValidate&&tT(t)},tS=function t(e,n,r){for(var i in n){if(!n.hasOwnProperty(i))return;var o=n[i],a="".concat(e,".").concat(i),s=F(w,a);(_.array.has(e)||x(o)||s&&!s._f)&&!b(o)?t(a,o,r):tj(a,o,r)}},tD=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=F(w,t),i=_.array.has(t),o=j(e);R(V,t,o),i?(Q.array.next({name:t,values:j(V)}),(U.isDirty||U.dirtyFields||W.isDirty||W.dirtyFields)&&n.shouldDirty&&Q.state.next({name:t,dirtyFields:te(T,V),isDirty:tw(t,o)})):!r||r._f||O(o)?tj(t,o,n):tS(t,o,n),tp(t,_)&&Q.state.next(m({},A)),Q.state.next({name:B.mount?t:void 0,values:j(V)})},tF=(c=(0,i.A)(u.mark(function t(e){var n,r,i,o,a,s,c,l,d,h,p,v,O,P,k,S,D,E;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:var u,T,M,L,I,N,Z;if(B.mount=!0,r=(n=e.target).name,i=!0,o=F(w,r),a=function(t){i=Number.isNaN(t)||b(t)&&isNaN(t.getTime())||q(t,F(V,r,t))},s=td(y.mode),c=td(y.reValidateMode),!o){t.next=48;break}if(h=n.type?tu(o._f):x(T=e)&&T.target?g(T.target)?T.target.checked:T.target.value:T,p=e.type===C.BLUR||e.type===C.FOCUS_OUT,v=!((u=o._f).mount&&(u.required||u.min||u.max||u.maxLength||u.minLength||u.pattern||u.validate))&&!y.resolver&&!F(A.errors,r)&&!o._f.deps||(M=p,L=F(A.touchedFields,r),I=A.isSubmitted,N=c,!(Z=s).isOnAll&&(!I&&Z.isOnTouch?!(L||M):(I?N.isOnBlur:Z.isOnBlur)?!M:(I?!N.isOnChange:!Z.isOnChange)||M)),O=tp(r,_,p),R(V,r,h),p?(o._f.onBlur&&o._f.onBlur(e),f&&f(0)):o._f.onChange&&o._f.onChange(e),k=!z(P=ts(r,h,p))||O,p||Q.state.next({name:r,type:e.type,values:j(V)}),!v){t.next=21;break}return(U.isValid||W.isValid)&&("onBlur"===y.mode?p&&tn():p||tn()),t.abrupt("return",k&&Q.state.next(m({name:r},O?{}:P)));case 21:if(!p&&O&&Q.state.next(m({},A)),!y.resolver){t.next=31;break}return t.next=25,tb([r]);case 25:S=t.sent.errors,a(h),i&&(D=ty(A.errors,w,r),l=(E=ty(S,w,D.name||r)).error,r=E.name,d=z(S)),t.next=47;break;case 31:return tr([r],!0),t.next=34,tx(o,_.disabled,V,tt,y.shouldUseNativeValidation);case 34:if(t.t0=r,l=t.sent[t.t0],tr([r]),a(h),!i){t.next=47;break}if(!l){t.next=43;break}d=!1,t.next=47;break;case 43:if(!(U.isValid||W.isValid)){t.next=47;break}return t.next=46,tA(w,!0);case 46:d=t.sent;case 47:i&&(o._f.deps&&tT(o._f.deps),tl(r,d,l,P));case 48:case"end":return t.stop()}},t)})),function(t){return c.apply(this,arguments)}),tE=function(t,e){if(F(A.errors,e)&&t.focus)return t.focus(),1},tT=(l=(0,i.A)(u.mark(function t(e){var n,r,a,s,c,l=arguments;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(n=l.length>1&&void 0!==l[1]?l[1]:{},s=N(e),!y.resolver){t.next=10;break}return t.next=5,tO(D(e)?e:s);case 5:r=z(c=t.sent),a=e?!s.some(function(t){return F(c,t)}):r,t.next=20;break;case 10:if(!e){t.next=17;break}return t.next=13,Promise.all(s.map(function(){var t=(0,i.A)(u.mark(function t(e){var n;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n=F(w,e),t.next=3,tA(n&&n._f?(0,o.A)({},e,n):n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()));case 13:((a=t.sent.every(Boolean))||A.isValid)&&tn(),t.next=20;break;case 17:return t.next=19,tA(w);case 19:a=r=t.sent;case 20:return Q.state.next(m(m(m({},!I(e)||(U.isValid||W.isValid)&&r!==A.isValid?{}:{name:e}),y.resolver||!e?{isValid:r}:{}),{},{errors:A.errors})),n.shouldFocus&&!a&&tv(w,tE,e?s:_.mount),t.abrupt("return",a);case 23:case"end":return t.stop()}},t)})),function(t){return l.apply(this,arguments)}),tV=function(t){var e=m({},B.mount?V:T);return D(t)?e:I(t)?F(e,t):t.map(function(t){return F(e,t)})},tR=function(t,e){return{invalid:!!F((e||A).errors,t),isDirty:!!F((e||A).dirtyFields,t),error:F((e||A).errors,t),isValidating:!!F(A.validatingFields,t),isTouched:!!F((e||A).touchedFields,t)}},tC=function(t,e,n){var r=(F(w,t,{_f:{}})._f||{}).ref,i=F(A.errors,t)||{};i.ref,i.message,i.type;var o=(0,s.A)(i,h);R(A.errors,t,m(m(m({},o),e),{},{ref:r})),Q.state.next({name:t,errors:A.errors,isValid:!1}),n&&n.shouldFocus&&r&&r.focus&&r.focus()},tM=function(t){return Q.state.subscribe({next:function(e){var n,r,i;n=t.name,r=e.name,i=t.exact,(!n||!r||n===r||N(n).some(function(t){return t&&(i?t===r:t.startsWith(r)||r.startsWith(t))}))&&tm(e,t.formState||U,tW,t.reRenderRoot)&&t.callback(m(m({values:m({},V)},A),e))}}).unsubscribe},tB=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=p(t?N(t):_.mount);try{for(r.s();!(e=r.n()).done;){var i=e.value;_.mount.delete(i),_.array.delete(i),n.keepValue||(J(w,i),J(V,i)),n.keepError||J(A.errors,i),n.keepDirty||J(A.dirtyFields,i),n.keepTouched||J(A.touchedFields,i),n.keepIsValidating||J(A.validatingFields,i),y.shouldUnregister||n.keepDefaultValue||J(T,i)}}catch(t){r.e(t)}finally{r.f()}Q.state.next({values:j(V)}),Q.state.next(m(m({},A),n.keepDirty?{isDirty:tw()}:{})),n.keepIsValid||tn()},t_=function(t){var e=t.disabled,n=t.name;(E(e)&&B.mount||e||_.disabled.has(n))&&(e?_.disabled.add(n):_.disabled.delete(n))},tL=function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=F(w,e),o=E(n.disabled)||E(y.disabled);return R(w,e,m(m({},i||{}),{},{_f:m(m({},i&&i._f?i._f:{ref:{name:e}}),{},{name:e,mount:!0},n)})),_.mount.add(e),i?t_({disabled:E(n.disabled)?n.disabled:y.disabled,name:e}):ta(e,!0,n.value),m(m(m({},o?{disabled:n.disabled||y.disabled}:{}),y.progressive?{required:!!n.required,min:tf(n.min),max:tf(n.max),minLength:tf(n.minLength),maxLength:tf(n.maxLength),pattern:tf(n.pattern)}:{}),{},{name:e,onChange:tF,onBlur:tF,ref:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(o){if(o){t(e,n),i=F(w,e);var a=D(o.value)&&o.querySelectorAll&&o.querySelectorAll("input,select,textarea")[0]||o,s=G(a)||g(a),u=i._f.refs||[];(s?u.find(function(t){return t===a}):a===i._f.ref)||(R(w,e,{_f:m(m({},i._f),s?{refs:[].concat((0,r.A)(u.filter($)),[a],(0,r.A)(Array.isArray(F(T,e))?[{}]:[])),ref:{type:a.type,name:e}}:{ref:a})}),ta(e,!1,void 0,a))}else(i=F(w,e,{}))._f&&(i._f.mount=!1),(y.shouldUnregister||n.shouldUnregister)&&!(P(_.array,e)&&B.action)&&_.unMount.add(e)})})},tI=function(){return y.shouldFocusError&&tv(w,tE,_.mount)},tU=function(t,e){var n;return n=(0,i.A)(u.mark(function n(r){var i,o,a,s,c,l,f,d;return u.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(i=void 0,r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist()),o=j(V),Q.state.next({isSubmitting:!0}),!y.resolver){n.next=14;break}return n.next=7,tb();case 7:s=(a=n.sent).errors,c=a.values,A.errors=s,o=c,n.next=16;break;case 14:return n.next=16,tA(w);case 16:if(_.disabled.size){l=p(_.disabled);try{for(l.s();!(f=l.n()).done;)d=f.value,R(o,d,void 0)}catch(t){l.e(t)}finally{l.f()}}if(J(A.errors,"root"),!z(A.errors)){n.next=30;break}return Q.state.next({errors:{}}),n.prev=20,n.next=23,t(o,r);case 23:n.next=28;break;case 25:n.prev=25,n.t0=n.catch(20),i=n.t0;case 28:n.next=35;break;case 30:if(!e){n.next=33;break}return n.next=33,e(m({},A.errors),r);case 33:tI(),setTimeout(tI);case 35:if(Q.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:z(A.errors)&&!i,submitCount:A.submitCount+1,errors:A.errors}),!i){n.next=38;break}throw i;case 38:case"end":return n.stop()}},n,null,[[20,25]])})),function(t){return n.apply(this,arguments)}},tN=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t?j(t):T,i=j(n),o=z(t),a=o?T:i;if(e.keepDefaultValues||(T=n),!e.keepValues){if(e.keepDirtyValues)for(var s=new Set([].concat((0,r.A)(_.mount),(0,r.A)(Object.keys(te(T,V))))),u=0,c=Array.from(s);u<c.length;u++){var l=c[u];F(A.dirtyFields,l)?R(a,l,F(V,l)):tD(l,F(a,l))}else{if(k&&D(t)){var f,d=p(_.mount);try{for(d.s();!(f=d.n()).done;){var h=f.value,v=F(w,h);if(v&&v._f){var g=Array.isArray(v._f.refs)?v._f.refs[0]:v._f.ref;if(X(g)){var b=g.closest("form");if(b){b.reset();break}}}}}catch(t){d.e(t)}finally{d.f()}}var O,x=p(_.mount);try{for(x.s();!(O=x.n()).done;){var P=O.value;tD(P,F(a,P))}}catch(t){x.e(t)}finally{x.f()}}V=j(a),Q.array.next({values:m({},a)}),Q.state.next({values:m({},a)})}_={mount:e.keepDirtyValues?_.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},B.mount=!U.isValid||!!e.keepIsValid||!!e.keepDirtyValues,B.watch=!!y.shouldUnregister,Q.state.next({submitCount:e.keepSubmitCount?A.submitCount:0,isDirty:!o&&(e.keepDirty?A.isDirty:!!(e.keepDefaultValues&&!q(t,T))),isSubmitted:!!e.keepIsSubmitted&&A.isSubmitted,dirtyFields:o?{}:e.keepDirtyValues?e.keepDefaultValues&&V?te(T,V):A.dirtyFields:e.keepDefaultValues&&t?te(T,t):e.keepDirty?A.dirtyFields:{},touchedFields:e.keepTouched?A.touchedFields:{},errors:e.keepErrors?A.errors:{},isSubmitSuccessful:!!e.keepIsSubmitSuccessful&&A.isSubmitSuccessful,isSubmitting:!1})},tZ=function(t,e){return tN(K(t)?t(V):t,e)},tW=function(t){A=m(m({},A),t)},tq={control:{register:tL,unregister:tB,getFieldState:tR,handleSubmit:tU,setError:tC,_subscribe:tM,_runSchema:tb,_getWatch:tk,_getDirty:tw,_setValid:tn,_setFieldArray:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=!(arguments.length>5)||void 0===arguments[5]||arguments[5];if(r&&n&&!y.disabled){if(B.action=!0,o&&Array.isArray(F(w,t))){var a=n(F(w,t),r.argA,r.argB);i&&R(w,t,a)}if(o&&Array.isArray(F(A.errors,t))){var s,u=n(F(A.errors,t),r.argA,r.argB);i&&R(A.errors,t,u),S(F(s=A.errors,t)).length||J(s,t)}if((U.touchedFields||W.touchedFields)&&o&&Array.isArray(F(A.touchedFields,t))){var c=n(F(A.touchedFields,t),r.argA,r.argB);i&&R(A.touchedFields,t,c)}(U.dirtyFields||W.dirtyFields)&&(A.dirtyFields=te(T,V)),Q.state.next({name:t,isDirty:tw(t,e),dirtyFields:A.dirtyFields,errors:A.errors,isValid:A.isValid})}else R(V,t,e)},_setDisabledField:t_,_setErrors:function(t){A.errors=t,Q.state.next({errors:A.errors,isValid:!1})},_getFieldArray:function(t){return S(F(B.mount?V:T,t,y.shouldUnregister?F(T,t,[]):[]))},_reset:tN,_resetDefaultValues:function(){return K(y.defaultValues)&&y.defaultValues().then(function(t){tZ(t,y.resetOptions),Q.state.next({isLoading:!1})})},_removeUnmounted:function(){var t,e=p(_.unMount);try{for(e.s();!(t=e.n()).done;){var n=t.value,r=F(w,n);r&&(r._f.refs?r._f.refs.every(function(t){return!$(t)}):!$(r._f.ref))&&tB(n)}}catch(t){e.e(t)}finally{e.f()}_.unMount=new Set},_disableForm:function(t){E(t)&&(Q.state.next({disabled:t}),tv(w,function(e,n){var r=F(w,n);r&&(e.disabled=r._f.disabled||t,Array.isArray(r._f.refs)&&r._f.refs.forEach(function(e){e.disabled=r._f.disabled||t}))},0,!1))},_subjects:Q,_proxyFormState:U,get _fields(){return w},get _formValues(){return V},get _state(){return B},set _state(value){B=value},get _defaultValues(){return T},get _names(){return _},set _names(value){_=value},get _formState(){return A},get _options(){return y},set _options(value){y=m(m({},y),value)}},subscribe:function(t){return B.mount=!0,W=m(m({},W),t.formState),tM(m(m({},t),{},{formState:W}))},trigger:tT,register:tL,handleSubmit:tU,watch:function(t,e){return K(t)?Q.state.subscribe({next:function(n){return t(tk(void 0,e),n)}}):tk(t,e,!0)},setValue:tD,getValues:tV,reset:tZ,resetField:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};F(w,t)&&(D(e.defaultValue)?tD(t,j(F(T,t))):(tD(t,e.defaultValue),R(T,t,j(e.defaultValue))),e.keepTouched||J(A.touchedFields,t),e.keepDirty||(J(A.dirtyFields,t),A.isDirty=e.defaultValue?tw(t,j(F(T,t))):tw()),!e.keepError&&(J(A.errors,t),U.isValid&&tn()),Q.state.next(m({},A)))},clearErrors:function(t){t&&N(t).forEach(function(t){return J(A.errors,t)}),Q.state.next({errors:t?A.errors:{}})},unregister:tB,setError:tC,setFocus:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=F(w,t),r=n&&n._f;if(r){var i=r.refs?r.refs[0]:r.ref;i.focus&&(i.focus(),e.shouldSelect&&K(i.select)&&i.select())}},getFieldState:tR};return m(m({},tq),{},{formControl:tq})}(t)),{},{formState:v}),t.formControl&&t.defaultValues&&!K(t.defaultValues)&&t.formControl.reset(t.defaultValues,t.resetOptions));var A=e.current.control;return A._options=t,L(function(){var t=A._subscribe({formState:A._proxyFormState,callback:function(){return y(m({},A._formState))},reRenderRoot:!0});return y(function(t){return m(m({},t),{},{isReady:!0})}),A._formState.isReady=!0,t},[A]),c.useEffect(function(){return A._disableForm(t.disabled)},[A,t.disabled]),c.useEffect(function(){t.mode&&(A._options.mode=t.mode),t.reValidateMode&&(A._options.reValidateMode=t.reValidateMode),t.errors&&!z(t.errors)&&A._setErrors(t.errors)},[A,t.errors,t.mode,t.reValidateMode]),c.useEffect(function(){t.shouldUnregister&&A._subjects.state.next({values:A._getWatch()})},[A,t.shouldUnregister]),c.useEffect(function(){if(A._proxyFormState.isDirty){var t=A._getDirty();t!==v.isDirty&&A._subjects.state.next({isDirty:t})}},[A,v.isDirty]),c.useEffect(function(){t.values&&!q(t.values,n.current)?(A._reset(t.values,A._options.resetOptions),n.current=t.values,y(function(t){return m({},t)})):A._resetDefaultValues()},[A,t.values]),c.useEffect(function(){A._state.mount||(A._setValid(),A._state.mount=!0),A._state.watch&&(A._state.watch=!1,A._subjects.state.next(m({},A._formState))),A._removeUnmounted()}),e.current.formState=_(v,A),e.current}},30014:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(20144),i=n(77167);let o=function(t,e){var n;switch(t.code){case r.eq.invalid_type:n=t.received===i.Zp.undefined?"Required":"Expected ".concat(t.expected,", received ").concat(t.received);break;case r.eq.invalid_literal:n="Invalid literal value, expected ".concat(JSON.stringify(t.expected,i.ZS.jsonStringifyReplacer));break;case r.eq.unrecognized_keys:n="Unrecognized key(s) in object: ".concat(i.ZS.joinValues(t.keys,", "));break;case r.eq.invalid_union:n="Invalid input";break;case r.eq.invalid_union_discriminator:n="Invalid discriminator value. Expected ".concat(i.ZS.joinValues(t.options));break;case r.eq.invalid_enum_value:n="Invalid enum value. Expected ".concat(i.ZS.joinValues(t.options),", received '").concat(t.received,"'");break;case r.eq.invalid_arguments:n="Invalid function arguments";break;case r.eq.invalid_return_type:n="Invalid function return type";break;case r.eq.invalid_date:n="Invalid date";break;case r.eq.invalid_string:"object"==typeof t.validation?"includes"in t.validation?(n='Invalid input: must include "'.concat(t.validation.includes,'"'),"number"==typeof t.validation.position&&(n="".concat(n," at one or more positions greater than or equal to ").concat(t.validation.position))):"startsWith"in t.validation?n='Invalid input: must start with "'.concat(t.validation.startsWith,'"'):"endsWith"in t.validation?n='Invalid input: must end with "'.concat(t.validation.endsWith,'"'):i.ZS.assertNever(t.validation):n="regex"!==t.validation?"Invalid ".concat(t.validation):"Invalid";break;case r.eq.too_small:n="array"===t.type?"Array must contain ".concat(t.exact?"exactly":t.inclusive?"at least":"more than"," ").concat(t.minimum," element(s)"):"string"===t.type?"String must contain ".concat(t.exact?"exactly":t.inclusive?"at least":"over"," ").concat(t.minimum," character(s)"):"number"===t.type?"Number must be ".concat(t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than ").concat(t.minimum):"date"===t.type?"Date must be ".concat(t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than ").concat(new Date(Number(t.minimum))):"Invalid input";break;case r.eq.too_big:n="array"===t.type?"Array must contain ".concat(t.exact?"exactly":t.inclusive?"at most":"less than"," ").concat(t.maximum," element(s)"):"string"===t.type?"String must contain ".concat(t.exact?"exactly":t.inclusive?"at most":"under"," ").concat(t.maximum," character(s)"):"number"===t.type?"Number must be ".concat(t.exact?"exactly":t.inclusive?"less than or equal to":"less than"," ").concat(t.maximum):"bigint"===t.type?"BigInt must be ".concat(t.exact?"exactly":t.inclusive?"less than or equal to":"less than"," ").concat(t.maximum):"date"===t.type?"Date must be ".concat(t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"," ").concat(new Date(Number(t.maximum))):"Invalid input";break;case r.eq.custom:n="Invalid input";break;case r.eq.invalid_intersection_types:n="Intersection results could not be merged";break;case r.eq.not_multiple_of:n="Number must be a multiple of ".concat(t.multipleOf);break;case r.eq.not_finite:n="Number must be finite";break;default:n=e.defaultError,i.ZS.assertNever(t)}return{message:n}}},35431:(t,e,n)=>{n.d(e,{z:()=>r});var r={};n.r(r),n.d(r,{BRAND:()=>s.qt,DIRTY:()=>o.jm,EMPTY_PATH:()=>o.I3,INVALID:()=>o.uY,NEVER:()=>s.tm,OK:()=>o.OK,ParseStatus:()=>o.MY,Schema:()=>s.Sj,ZodAny:()=>s.Ml,ZodArray:()=>s.n,ZodBigInt:()=>s.Lr,ZodBoolean:()=>s.WF,ZodBranded:()=>s.eN,ZodCatch:()=>s.hw,ZodDate:()=>s.aP,ZodDefault:()=>s.Xi,ZodDiscriminatedUnion:()=>s.jv,ZodEffects:()=>s.k1,ZodEnum:()=>s.Vb,ZodError:()=>u.G,ZodFirstPartyTypeKind:()=>s.kY,ZodFunction:()=>s.CZ,ZodIntersection:()=>s.Jv,ZodIssueCode:()=>u.eq,ZodLazy:()=>s.Ih,ZodLiteral:()=>s.DN,ZodMap:()=>s.Ut,ZodNaN:()=>s.Tq,ZodNativeEnum:()=>s.WM,ZodNever:()=>s.iS,ZodNull:()=>s.PQ,ZodNullable:()=>s.l1,ZodNumber:()=>s.rS,ZodObject:()=>s.bv,ZodOptional:()=>s.Ii,ZodParsedType:()=>a.Zp,ZodPipeline:()=>s._c,ZodPromise:()=>s.$i,ZodReadonly:()=>s.EV,ZodRecord:()=>s.b8,ZodSchema:()=>s.lK,ZodSet:()=>s.Kz,ZodString:()=>s.ND,ZodSymbol:()=>s.K5,ZodTransformer:()=>s.BG,ZodTuple:()=>s.y0,ZodType:()=>s.aR,ZodUndefined:()=>s._Z,ZodUnion:()=>s.fZ,ZodUnknown:()=>s._,ZodVoid:()=>s.a0,addIssueToContext:()=>o.zn,any:()=>s.bz,array:()=>s.YO,bigint:()=>s.o,boolean:()=>s.zM,coerce:()=>s.au,custom:()=>s.Ie,date:()=>s.p6,datetimeRegex:()=>s.fm,defaultErrorMap:()=>i.su,discriminatedUnion:()=>s.gM,effect:()=>s.QZ,enum:()=>s.k5,function:()=>s.fH,getErrorMap:()=>i.$W,getParsedType:()=>a.CR,instanceof:()=>s.Nl,intersection:()=>s.E$,isAborted:()=>o.G4,isAsync:()=>o.xP,isDirty:()=>o.DM,isValid:()=>o.fn,late:()=>s.fn,lazy:()=>s.RZ,literal:()=>s.eu,makeIssue:()=>o.y7,map:()=>s.Tj,nan:()=>s.oi,nativeEnum:()=>s.fc,never:()=>s.Zm,null:()=>s.ch,nullable:()=>s.me,number:()=>s.ai,object:()=>s.Ik,objectUtil:()=>a.o6,oboolean:()=>s.yN,onumber:()=>s.p7,optional:()=>s.lq,ostring:()=>s.Di,pipeline:()=>s.Tk,preprocess:()=>s.vk,promise:()=>s.iv,quotelessJson:()=>u.WI,record:()=>s.g1,set:()=>s.hZ,setErrorMap:()=>i.pJ,strictObject:()=>s.re,string:()=>s.Yj,symbol:()=>s.HR,transformer:()=>s.Gu,tuple:()=>s.PV,undefined:()=>s.Vx,union:()=>s.KC,unknown:()=>s.L5,util:()=>a.ZS,void:()=>s.rI});var i=n(50926),o=n(92106),a=n(77167),s=n(9006),u=n(20144)},50926:(t,e,n)=>{n.d(e,{$W:()=>a,pJ:()=>o,su:()=>r.A});var r=n(30014),i=r.A;function o(t){i=t}function a(){return i}},53552:(t,e,n)=>{n.d(e,{u:()=>c});var r=n(21410),i=function(t,e,n){if(t&&"reportValidity"in t){var i=(0,r.Jt)(n,e);t.setCustomValidity(i&&i.message||""),t.reportValidity()}},o=function(t,e){var n=function(n){var r=e.fields[n];r&&r.ref&&"reportValidity"in r.ref?i(r.ref,n,t):r&&r.refs&&r.refs.forEach(function(e){return i(e,n,t)})};for(var r in e.fields)n(r)},a=function(t,e){e.shouldUseNativeValidation&&o(t,e);var n={};for(var i in t){var a=(0,r.Jt)(e.fields,i),u=Object.assign(t[i]||{},{ref:a&&a.ref});if(s(e.names||Object.keys(t),i)){var c=Object.assign({},(0,r.Jt)(n,i));(0,r.hZ)(c,"root",u),(0,r.hZ)(n,i,c)}else(0,r.hZ)(n,i,u)}return n},s=function(t,e){var n=u(e);return t.some(function(t){return u(t).match("^".concat(n,"\\.\\d+"))})};function u(t){return t.replace(/\]|\[/g,"")}function c(t,e,n){return void 0===n&&(n={}),function(i,s,u){try{return Promise.resolve(function(r,a){try{var s=Promise.resolve(t["sync"===n.mode?"parse":"parseAsync"](i,e)).then(function(t){return u.shouldUseNativeValidation&&o({},u),{errors:{},values:n.raw?Object.assign({},i):t}})}catch(t){return a(t)}return s&&s.then?s.then(void 0,a):s}(0,function(t){if(Array.isArray(null==t?void 0:t.errors))return{values:{},errors:a(function(t,e){for(var n={};t.length;){var i=t[0],o=i.code,a=i.message,s=i.path.join(".");if(!n[s])if("unionErrors"in i){var u=i.unionErrors[0].errors[0];n[s]={message:u.message,type:u.code}}else n[s]={message:a,type:o};if("unionErrors"in i&&i.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return t.push(e)})}),e){var c=n[s].types,l=c&&c[i.code];n[s]=(0,r.Gb)(s,e,n,o,l?[].concat(l,i.message):i.message)}t.shift()}return n}(t.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw t}))}catch(t){return Promise.reject(t)}}}},59667:(t,e,n)=>{var r;n.d(e,{r:()=>r}),function(t){t.errToObj=function(t){return"string"==typeof t?{message:t}:t||{}},t.toString=function(t){return"string"==typeof t?t:null==t?void 0:t.message}}(r||(r={}))},61810:(t,e,n)=>{n.d(e,{A:()=>r});function r(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}},77167:(t,e,n)=>{n.d(e,{CR:()=>f,ZS:()=>r,Zp:()=>l,o6:()=>i});var r,i,o=n(37711);function a(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?a(Object(n),!0).forEach(function(e){(0,o.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function u(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return c(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(t,e)}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}!function(t){t.assertEqual=function(t){},t.assertIs=function(t){},t.assertNever=function(t){throw Error()},t.arrayToEnum=function(t){var e,n={},r=u(t);try{for(r.s();!(e=r.n()).done;){var i=e.value;n[i]=i}}catch(t){r.e(t)}finally{r.f()}return n},t.getValidEnumValues=function(e){var n,r=t.objectKeys(e).filter(function(t){return"number"!=typeof e[e[t]]}),i={},o=u(r);try{for(o.s();!(n=o.n()).done;){var a=n.value;i[a]=e[a]}}catch(t){o.e(t)}finally{o.f()}return t.objectValues(i)},t.objectValues=function(e){return t.objectKeys(e).map(function(t){return e[t]})},t.objectKeys="function"==typeof Object.keys?function(t){return Object.keys(t)}:function(t){var e=[];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.push(n);return e},t.find=function(t,e){var n,r=u(t);try{for(r.s();!(n=r.n()).done;){var i=n.value;if(e(i))return i}}catch(t){r.e(t)}finally{r.f()}},t.isInteger="function"==typeof Number.isInteger?function(t){return Number.isInteger(t)}:function(t){return"number"==typeof t&&Number.isFinite(t)&&Math.floor(t)===t},t.joinValues=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" | ";return t.map(function(t){return"string"==typeof t?"'".concat(t,"'"):t}).join(e)},t.jsonStringifyReplacer=function(t,e){return"bigint"==typeof e?e.toString():e}}(r||(r={})),(i||(i={})).mergeShapes=function(t,e){return s(s({},t),e)};var l=r.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),f=function(t){switch(typeof t){case"undefined":return l.undefined;case"string":return l.string;case"number":return Number.isNaN(t)?l.nan:l.number;case"boolean":return l.boolean;case"function":return l.function;case"bigint":return l.bigint;case"symbol":return l.symbol;case"object":if(Array.isArray(t))return l.array;if(null===t)return l.null;if(t.then&&"function"==typeof t.then&&t.catch&&"function"==typeof t.catch)return l.promise;if("undefined"!=typeof Map&&t instanceof Map)return l.map;if("undefined"!=typeof Set&&t instanceof Set)return l.set;if("undefined"!=typeof Date&&t instanceof Date)return l.date;return l.object;default:return l.unknown}}},92106:(t,e,n)=>{n.d(e,{DM:()=>P,G4:()=>x,I3:()=>y,MY:()=>g,OK:()=>A,fn:()=>w,jm:()=>O,uY:()=>b,xP:()=>k,y7:()=>v,zn:()=>m});var r=n(33311),i=n(11157),o=n(59539),a=n(37711),s=n(10631),u=n(28295),c=n(50926),l=n(30014);function f(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return d(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,e)}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var v=function(t){var e=t.data,n=t.path,r=t.errorMaps,i=t.issueData,o=[].concat((0,s.A)(n),(0,s.A)(i.path||[])),a=p(p({},i),{},{path:o});if(void 0!==i.message)return p(p({},i),{},{path:o,message:i.message});var u,c="",l=f(r.filter(function(t){return!!t}).slice().reverse());try{for(l.s();!(u=l.n()).done;)c=(0,u.value)(a,{data:e,defaultError:c}).message}catch(t){l.e(t)}finally{l.f()}return p(p({},i),{},{path:o,message:c})},y=[];function m(t,e){var n=(0,c.$W)(),r=v({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,n,n===l.A?void 0:l.A].filter(function(t){return!!t})});t.common.issues.push(r)}var g=function(){var t;function e(){(0,i.A)(this,e),this.value="valid"}return(0,o.A)(e,[{key:"dirty",value:function(){"valid"===this.value&&(this.value="dirty")}},{key:"abort",value:function(){"aborted"!==this.value&&(this.value="aborted")}}],[{key:"mergeArray",value:function(t,e){var n,r=[],i=f(e);try{for(i.s();!(n=i.n()).done;){var o=n.value;if("aborted"===o.status)return b;"dirty"===o.status&&t.dirty(),r.push(o.value)}}catch(t){i.e(t)}finally{i.f()}return{status:t.value,value:r}}},{key:"mergeObjectAsync",value:(t=(0,r.A)(u.mark(function t(n,r){var i,o,a,s,c,l;return u.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:i=[],o=f(r),t.prev=2,o.s();case 4:if((a=o.n()).done){t.next=15;break}return s=a.value,t.next=8,s.key;case 8:return c=t.sent,t.next=11,s.value;case 11:l=t.sent,i.push({key:c,value:l});case 13:t.next=4;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(2),o.e(t.t0);case 20:return t.prev=20,o.f(),t.finish(20);case 23:return t.abrupt("return",e.mergeObjectSync(n,i));case 24:case"end":return t.stop()}},t,null,[[2,17,20,23]])})),function(e,n){return t.apply(this,arguments)})},{key:"mergeObjectSync",value:function(t,e){var n,r={},i=f(e);try{for(i.s();!(n=i.n()).done;){var o=n.value,a=o.key,s=o.value;if("aborted"===a.status||"aborted"===s.status)return b;"dirty"===a.status&&t.dirty(),"dirty"===s.status&&t.dirty(),"__proto__"!==a.value&&(void 0!==s.value||o.alwaysSet)&&(r[a.value]=s.value)}}catch(t){i.e(t)}finally{i.f()}return{status:t.value,value:r}}}]),e}(),b=Object.freeze({status:"aborted"}),O=function(t){return{status:"dirty",value:t}},A=function(t){return{status:"valid",value:t}},x=function(t){return"aborted"===t.status},P=function(t){return"dirty"===t.status},w=function(t){return"valid"===t.status},k=function(t){return"undefined"!=typeof Promise&&t instanceof Promise}},94042:(t,e,n)=>{n.d(e,{P:()=>aI});var r,i,o,a=n(37711),s=n(11157),u=n(59539),c=n(49077),l=n(90064),f=n(44686);function d(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}var h=n(61810),p=n(10631),v=n(3243);function y(t){var e=[{},{}];return null==t||t.values.forEach(function(t,n){e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function m(t,e,n,r){if("function"==typeof e){var i=y(r),o=(0,v.A)(i,2),a=o[0],s=o[1];e=e(void 0!==n?n:t.custom,a,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){var u=y(r),c=(0,v.A)(u,2),l=c[0],f=c[1];e=e(void 0!==n?n:t.custom,l,f)}return e}function g(t,e,n){var r=t.getProps();return m(r,e,void 0!==n?n:r.custom,t)}function b(t,e){var n,r;return null!=(n=null!=(r=null==t?void 0:t[e])?r:null==t?void 0:t.default)?n:t}var O=function(t){return t},A={},x=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],P={value:null,addProjectionMetrics:null};function w(t,e){var n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=function(){return n=!0},a=x.reduce(function(t,n){return t[n]=function(t,e){var n=new Set,r=new Set,i=!1,o=!1,a=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},u=0;function c(e){a.has(e)&&(l.schedule(e),t()),u++,e(s)}var l={schedule:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=o&&i?n:r;return e&&a.add(t),s.has(t)||s.add(t),t},cancel:function(t){r.delete(t),a.delete(t)},process:function(t){if(s=t,i){o=!0;return}i=!0;var a=[r,n];n=a[0],r=a[1],n.forEach(c),e&&P.value&&P.value.frameloop[e].push(u),u=0,n.clear(),i=!1,o&&(o=!1,l.process(t))}};return l}(o,e?n:void 0),t},{}),s=a.setup,u=a.read,c=a.resolveKeyframes,l=a.preUpdate,f=a.update,d=a.preRender,h=a.render,p=a.postRender,v=function o(){var a=A.useManualTiming?i.timestamp:performance.now();n=!1,A.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,s.process(i),u.process(i),c.process(i),l.process(i),f.process(i),d.process(i),h.process(i),p.process(i),i.isProcessing=!1,n&&e&&(r=!1,t(o))},y=function(){n=!0,r=!0,i.isProcessing||t(v)};return{schedule:x.reduce(function(t,e){var r=a[e];return t[e]=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||y(),r.schedule(t,e,i)},t},{}),cancel:function(t){for(var e=0;e<x.length;e++)a[x[e]].cancel(t)},state:i,steps:a}}var k=w("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:O,!0),j=k.schedule,S=k.cancel,D=k.state,F=k.steps,E=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],T=new Set(E),V=new Set(["width","height","top","left","right","bottom"].concat((0,p.A)(E)));function R(t,e){-1===t.indexOf(e)&&t.push(e)}function C(t,e){var n=t.indexOf(e);n>-1&&t.splice(n,1)}var M=function(){function t(){(0,s.A)(this,t),this.subscriptions=[]}return(0,u.A)(t,[{key:"add",value:function(t){var e=this;return R(this.subscriptions,t),function(){return C(e.subscriptions,t)}}},{key:"notify",value:function(t,e,n){var r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,n);else for(var i=0;i<r;i++){var o=this.subscriptions[i];o&&o(t,e,n)}}},{key:"getSize",value:function(){return this.subscriptions.length}},{key:"clear",value:function(){this.subscriptions.length=0}}]),t}();function B(){o=void 0}var _={now:function(){return void 0===o&&_.set(D.isProcessing||A.useManualTiming?D.timestamp:performance.now()),o},set:function(t){o=t,queueMicrotask(B)}};function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var I={current:void 0},U=function(){function t(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,s.A)(this,t),this.canTrackVelocity=null,this.events={},this.updateAndNotify=function(t){var e,r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=_.now();if(n.updatedAt!==i&&n.setPrevFrameValue(),n.prev=n.current,n.setCurrent(t),n.current!==n.prev&&(null==(o=n.events.change)||o.notify(n.current),n.dependents)){var o,a,s=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return L(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(t,e)}}(t))){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}(n.dependents);try{for(s.s();!(a=s.n()).done;)a.value.dirty()}catch(t){s.e(t)}finally{s.f()}}r&&(null==(e=n.events.renderRequest)||e.notify(n.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=r.owner}return(0,u.A)(t,[{key:"setCurrent",value:function(t){this.current=t,this.updatedAt=_.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}},{key:"setPrevFrameValue",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}},{key:"onChange",value:function(t){return this.on("change",t)}},{key:"on",value:function(t,e){var n=this;this.events[t]||(this.events[t]=new M);var r=this.events[t].add(e);return"change"===t?function(){r(),j.read(function(){n.events.change.getSize()||n.stop()})}:r}},{key:"clearListeners",value:function(){for(var t in this.events)this.events[t].clear()}},{key:"attach",value:function(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}},{key:"set",value:function(t){var e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}},{key:"setWithVelocity",value:function(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}},{key:"jump",value:function(t){var e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}},{key:"dirty",value:function(){var t;null==(t=this.events.change)||t.notify(this.current)}},{key:"addDependent",value:function(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}},{key:"removeDependent",value:function(t){this.dependents&&this.dependents.delete(t)}},{key:"get",value:function(){return I.current&&I.current.push(this),this.current}},{key:"getPrevious",value:function(){return this.prev}},{key:"getVelocity",value:function(){var t,e=_.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;var n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*t:0}},{key:"start",value:function(t){var e=this;return this.stop(),new Promise(function(n){e.hasAnimated=!0,e.animation=t(n),e.events.animationStart&&e.events.animationStart.notify()}).then(function(){e.events.animationComplete&&e.events.animationComplete.notify(),e.clearAnimation()})}},{key:"stop",value:function(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}},{key:"isAnimating",value:function(){return!!this.animation}},{key:"clearAnimation",value:function(){delete this.animation}},{key:"destroy",value:function(){var t,e;null==(t=this.dependents)||t.clear(),null==(e=this.events.destroy)||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}]),t}();function N(t,e){return new U(t,e)}var Z=function(t){return Array.isArray(t)},W=["transitionEnd","transition"];function q(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function z(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?q(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var Y=function(t){return!!(t&&t.getVelocity)};function K(t,e){var n=t.getValue("willChange");if(Y(n)&&n.add)return n.add(e);if(!n&&A.WillChange){var r=new A.WillChange("auto");t.addValue("willChange",r),r.add(e)}}var X=function(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()},H="data-"+X("framerAppearId"),G=function(t,e){return function(n){return e(t(n))}},$=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.reduce(G)},J=function(t,e,n){return n>e?e:n<t?t:n},Q=function(t){return 1e3*t},tt=function(t){return t/1e3},te={layout:0,mainThread:0,waapi:0},tn=function(){},tr=function(){},ti=function(t){return function(e){return"string"==typeof e&&e.startsWith(t)}},to=ti("--"),ta=ti("var(--"),ts=function(t){return!!ta(t)&&tu.test(t.split("/*")[0].trim())},tu=/var\(\x2D\x2D(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[\s\S]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[\s\S]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[\s\S]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)$/i;function tc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tc(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tc(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var tf={test:function(t){return"number"==typeof t},parse:parseFloat,transform:function(t){return t}},td=tl(tl({},tf),{},{transform:function(t){return J(0,1,t)}}),th=tl(tl({},tf),{},{default:1}),tp=function(t){return Math.round(1e5*t)/1e5},tv=/\x2D?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/g,ty=/^(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:\x2D?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}\x2D?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))$/i,tm=function(t,e){return function(n){return!!("string"==typeof n&&ty.test(n)&&n.startsWith(t)||e&&null!=n&&Object.prototype.hasOwnProperty.call(n,e))}},tg=function(t,e,n){return function(r){if("string"!=typeof r)return r;var i,o=r.match(tv),s=(0,v.A)(o,4),u=s[0],c=s[1],l=s[2],f=s[3];return i={},(0,a.A)(i,t,parseFloat(u)),(0,a.A)(i,e,parseFloat(c)),(0,a.A)(i,n,parseFloat(l)),(0,a.A)(i,"alpha",void 0!==f?parseFloat(f):1),i}};function tb(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tO(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tb(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tb(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var tA=tO(tO({},tf),{},{transform:function(t){return Math.round(J(0,255,t))}}),tx={test:tm("rgb","red"),parse:tg("red","green","blue"),transform:function(t){var e=t.red,n=t.green,r=t.blue,i=t.alpha;return"rgba("+tA.transform(e)+", "+tA.transform(n)+", "+tA.transform(r)+", "+tp(td.transform(void 0===i?1:i))+")"}},tP={test:tm("#"),parse:function(t){var e="",n="",r="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),r=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,r+=r,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:tx.transform};function tw(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tk(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tw(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tw(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var tj=function(t){return{test:function(e){return"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length},parse:parseFloat,transform:function(e){return"".concat(e).concat(t)}}},tS=tj("deg"),tD=tj("%"),tF=tj("px"),tE=tj("vh"),tT=tj("vw"),tV=tk(tk({},tD),{},{parse:function(t){return tD.parse(t)/100},transform:function(t){return tD.transform(100*t)}}),tR={test:tm("hsl","hue"),parse:tg("hue","saturation","lightness"),transform:function(t){var e=t.hue,n=t.saturation,r=t.lightness,i=t.alpha;return"hsla("+Math.round(e)+", "+tD.transform(tp(n))+", "+tD.transform(tp(r))+", "+tp(td.transform(void 0===i?1:i))+")"}},tC={test:function(t){return tx.test(t)||tP.test(t)||tR.test(t)},parse:function(t){return tx.test(t)?tx.parse(t):tR.test(t)?tR.parse(t):tP.parse(t)},transform:function(t){return"string"==typeof t?t:t.hasOwnProperty("red")?tx.transform(t):tR.transform(t)}},tM=/(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:\x2D?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}\x2D?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))/gi,tB="number",t_="color",tL=/var[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\x2D\x2D(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[\s\S]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[\s\S]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[\s\S]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)|#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:\x2D?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}\x2D?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\)|\x2D?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/gi;function tI(t){var e=t.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,a=e.replace(tL,function(t){return tC.test(t)?(r.color.push(o),i.push(t_),n.push(tC.parse(t))):t.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(t)):(r.number.push(o),i.push(tB),n.push(parseFloat(t))),++o,"${}"}).split("${}");return{values:n,split:a,indexes:r,types:i}}function tU(t){return tI(t).values}function tN(t){var e=tI(t),n=e.split,r=e.types,i=n.length;return function(t){for(var e="",o=0;o<i;o++)if(e+=n[o],void 0!==t[o]){var a=r[o];a===tB?e+=tp(t[o]):a===t_?e+=tC.transform(t[o]):e+=t[o]}return e}}var tZ=function(t){return"number"==typeof t?0:t},tW={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(tv))?void 0:e.length)||0)+((null==(n=t.match(tM))?void 0:n.length)||0)>0},parse:tU,createTransformer:tN,getAnimatableNone:function(t){var e=tU(t);return tN(t)(e.map(tZ))}};function tq(t,e,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?t+(e-t)*6*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function tz(t,e){return function(n){return n>0?e:t}}var tY=function(t,e,n){return t+(e-t)*n};function tK(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}var tX=function(t,e,n){var r=t*t,i=n*(e*e-r)+r;return i<0?0:Math.sqrt(i)},tH=[tP,tx,tR];function tG(t){var e=tH.find(function(e){return e.test(t)});if(tn(!!e,"'".concat(t,"' is not an animatable color. Use the equivalent color code instead.")),!e)return!1;var n=e.parse(t);return e===tR&&(n=function(t){var e=t.hue,n=t.saturation,r=t.lightness,i=t.alpha;e/=360,r/=100;var o=0,a=0,s=0;if(n/=100){var u=r<.5?r*(1+n):r+n-r*n,c=2*r-u;o=tq(c,u,e+1/3),a=tq(c,u,e),s=tq(c,u,e-1/3)}else o=a=s=r;return{red:Math.round(255*o),green:Math.round(255*a),blue:Math.round(255*s),alpha:i}}(n)),n}var t$=function(t,e){var n=tG(t),r=tG(e);if(!n||!r)return tz(t,e);var i=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tK(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tK(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},n);return function(t){return i.red=tX(n.red,r.red,t),i.green=tX(n.green,r.green,t),i.blue=tX(n.blue,r.blue,t),i.alpha=tY(n.alpha,r.alpha,t),tx.transform(i)}},tJ=new Set(["none","hidden"]);function tQ(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function t0(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tQ(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tQ(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function t1(t,e){return function(n){return tY(t,e,n)}}function t2(t){return"number"==typeof t?t1:"string"==typeof t?ts(t)?tz:tC.test(t)?t$:t9:Array.isArray(t)?t3:"object"==typeof t?tC.test(t)?t$:t5:tz}function t3(t,e){var n=(0,p.A)(t),r=n.length,i=t.map(function(t,n){return t2(t)(t,e[n])});return function(t){for(var e=0;e<r;e++)n[e]=i[e](t);return n}}function t5(t,e){var n=t0(t0({},t),e),r={};for(var i in n)void 0!==t[i]&&void 0!==e[i]&&(r[i]=t2(t[i])(t[i],e[i]));return function(t){for(var e in r)n[e]=r[e](t);return n}}var t9=function(t,e){var n=tW.createTransformer(e),r=tI(t),i=tI(e);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?tJ.has(t)&&!i.values.length||tJ.has(e)&&!r.values.length?tJ.has(t)?function(n){return n<=0?t:e}:function(n){return n>=1?e:t}:$(t3(function(t,e){for(var n=[],r={color:0,var:0,number:0},i=0;i<e.values.length;i++){var o,a=e.types[i],s=t.indexes[a][r[a]],u=null!=(o=t.values[s])?o:0;n[i]=u,r[a]++}return n}(r,i),i.values),n):(tn(!0,"Complex values '".concat(t,"' and '").concat(e,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.")),tz(t,e))};function t8(t,e,n){return"number"==typeof t&&"number"==typeof e&&"number"==typeof n?tY(t,e,n):t2(t)(t,e)}var t4=function(t){var e=function(e){return t(e.timestamp)};return{start:function(){var t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return j.update(e,t)},stop:function(){return S(e)},now:function(){return D.isProcessing?D.timestamp:_.now()}}},t6=function(t,e){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="",i=Math.max(Math.round(e/n),2),o=0;o<i;o++)r+=t(o/(i-1))+", ";return"linear(".concat(r.substring(0,r.length-2),")")};function t7(t){for(var e=0,n=t.next(e);!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}function et(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ee(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?et(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):et(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function en(t,e,n){var r,i,o=Math.max(e-5,0);return r=n-t(o),(i=e-o)?1e3/i*r:0}var er={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ei(t,e){return t*Math.sqrt(1-e*e)}function eo(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ea(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?eo(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):eo(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var es=["duration","bounce"],eu=["stiffness","damping","mass"];function ec(t,e){return e.some(function(e){return void 0!==t[e]})}function el(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:er.visualDuration,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:er.bounce,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:n}:e,i=r.restSpeed,o=r.restDelta,a=r.keyframes[0],s=r.keyframes[r.keyframes.length-1],u={done:!1,value:a},c=function(t){var e=ea({velocity:er.velocity,stiffness:er.stiffness,damping:er.damping,mass:er.mass,isResolvedFromDuration:!1},t);if(!ec(t,eu)&&ec(t,es))if(t.visualDuration){var n=2*Math.PI/(1.2*t.visualDuration),r=n*n,i=2*J(.05,1,1-(t.bounce||0))*Math.sqrt(r);e=ea(ea({},e),{},{mass:er.mass,stiffness:r,damping:i})}else{var o=function(t){var e,n,r=t.duration,i=void 0===r?er.duration:r,o=t.bounce,a=void 0===o?er.bounce:o,s=t.velocity,u=void 0===s?er.velocity:s,c=t.mass,l=void 0===c?er.mass:c;tn(i<=Q(er.maxDuration),"Spring duration must be 10 seconds or less");var f=1-a;f=J(er.minDamping,er.maxDamping,f),i=J(er.minDuration,er.maxDuration,tt(i)),f<1?(e=function(t){var e=t*f,n=e*i;return .001-(e-u)/ei(t,f)*Math.exp(-n)},n=function(t){var n=t*f*i,r=Math.pow(f,2)*Math.pow(t,2)*i,o=Math.exp(-n),a=ei(Math.pow(t,2),f);return(n*u+u-r)*o*(-e(t)+.001>0?-1:1)/a}):(e=function(t){return -.001+Math.exp(-t*i)*((t-u)*i+1)},n=function(t){return i*i*(u-t)*Math.exp(-t*i)});var d=function(t,e,n){for(var r=n,i=1;i<12;i++)r-=t(r)/e(r);return r}(e,n,5/i);if(i=Q(i),isNaN(d))return{stiffness:er.stiffness,damping:er.damping,duration:i};var h=Math.pow(d,2)*l;return{stiffness:h,damping:2*f*Math.sqrt(l*h),duration:i}}(t);(e=ea(ea(ea({},e),o),{},{mass:er.mass})).isResolvedFromDuration=!0}return e}(ea(ea({},r),{},{velocity:-tt(r.velocity||0)})),l=c.stiffness,f=c.damping,d=c.mass,h=c.duration,p=c.velocity,v=c.isResolvedFromDuration,y=p||0,m=f/(2*Math.sqrt(l*d)),g=s-a,b=tt(Math.sqrt(l/d)),O=5>Math.abs(g);if(i||(i=O?er.restSpeed.granular:er.restSpeed.default),o||(o=O?er.restDelta.granular:er.restDelta.default),m<1){var A=ei(b,m);t=function(t){return s-Math.exp(-m*b*t)*((y+m*b*g)/A*Math.sin(A*t)+g*Math.cos(A*t))}}else if(1===m)t=function(t){return s-Math.exp(-b*t)*(g+(y+b*g)*t)};else{var x=b*Math.sqrt(m*m-1);t=function(t){var e=Math.exp(-m*b*t),n=Math.min(x*t,300);return s-e*((y+m*b*g)*Math.sinh(n)+x*g*Math.cosh(n))/x}}var P={calculatedDuration:v&&h||null,next:function(e){var n=t(e);if(v)u.done=e>=h;else{var r=0===e?y:0;m<1&&(r=0===e?Q(y):en(t,e,n));var a=Math.abs(s-n)<=o;u.done=Math.abs(r)<=i&&a}return u.value=u.done?s:n,u},toString:function(){var t=Math.min(t7(P),2e4),e=t6(function(e){return P.next(t*e).value},t,30);return t+"ms "+e},toTransition:function(){}};return P}function ef(t){var e,n,r=t.keyframes,i=t.velocity,o=t.power,a=t.timeConstant,s=void 0===a?325:a,u=t.bounceDamping,c=void 0===u?10:u,l=t.bounceStiffness,f=void 0===l?500:l,d=t.modifyTarget,h=t.min,p=t.max,v=t.restDelta,y=void 0===v?.5:v,m=t.restSpeed,g=r[0],b={done:!1,value:g},O=(void 0===o?.8:o)*(void 0===i?0:i),A=g+O,x=void 0===d?A:d(A);x!==A&&(O=x-g);var P=function(t){return-O*Math.exp(-t/s)},w=function(t){return x+P(t)},k=function(t){var e=P(t),n=w(t);b.done=Math.abs(e)<=y,b.value=b.done?x:n},j=function(t){var r,i;r=b.value,(void 0!==h&&r<h||void 0!==p&&r>p)&&(e=t,n=el({keyframes:[b.value,(i=b.value,void 0===h?p:void 0===p||Math.abs(h-i)<Math.abs(p-i)?h:p)],velocity:en(w,t,b.value),damping:c,stiffness:f,restDelta:y,restSpeed:m}))};return j(0),{calculatedDuration:null,next:function(t){var r=!1;return(n||void 0!==e||(r=!0,k(t),j(t)),void 0!==e&&t>=e)?n.next(t-e):(r||k(t),b)}}}el.applyToOptions=function(t){var e=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,n=arguments.length>2?arguments[2]:void 0,r=n(ee(ee({},t),{},{keyframes:[0,e]})),i=Math.min(t7(r),2e4);return{type:"keyframes",ease:function(t){return r.next(i*t).value/e},duration:tt(i)}}(t,100,el);return t.ease=e.ease,t.duration=Q(e.duration),t.type="keyframes",t};var ed=function(t,e,n){return(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t};function eh(t,e,n,r){if(t===e&&n===r)return O;var i=function(e){var r,i,o=0,a=1,s=0;do(r=ed(i=o+(a-o)/2,t,n)-e)>0?a=i:o=i;while(Math.abs(r)>1e-7&&++s<12);return i};return function(t){return 0===t||1===t?t:ed(i(t),e,r)}}var ep=eh(.42,0,1,1),ev=eh(0,0,.58,1),ey=eh(.42,0,.58,1),em=function(t){return function(e){return e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2}},eg=function(t){return function(e){return 1-t(1-e)}},eb=eh(.33,1.53,.69,.99),eO=eg(eb),eA=em(eO),ex=function(t){return(t*=2)<1?.5*eO(t):.5*(2-Math.pow(2,-10*(t-1)))},eP=function(t){return 1-Math.sin(Math.acos(t))},ew=eg(eP),ek=em(eP),ej=function(t){return Array.isArray(t)&&"number"==typeof t[0]},eS={linear:O,easeIn:ep,easeInOut:ey,easeOut:ev,circIn:eP,circInOut:ek,circOut:ew,backIn:eO,backInOut:eA,backOut:eb,anticipate:ex},eD=function(t){if(ej(t)){tr(4===t.length,"Cubic bezier arrays must contain four numerical values.");var e=(0,v.A)(t,4);return eh(e[0],e[1],e[2],e[3])}return"string"==typeof t?(tr(void 0!==eS[t],"Invalid easing type '".concat(t,"'")),eS[t]):t},eF=function(t,e,n){var r=e-t;return 0===r?1:(n-t)/r};function eE(t){var e,n,r=t.duration,i=void 0===r?300:r,o=t.keyframes,a=t.times,s=t.ease,u=void 0===s?"easeInOut":s,c=Array.isArray(u)&&"number"!=typeof u[0]?u.map(eD):eD(u),l={done:!1,value:o[0]},f=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.clamp,i=n.ease,o=n.mixer,a=t.length;if(tr(a===e.length,"Both input and output ranges must be the same length"),1===a)return function(){return e[0]};if(2===a&&e[0]===e[1])return function(){return e[1]};var s=t[0]===t[1];t[0]>t[a-1]&&(t=(0,p.A)(t).reverse(),e=(0,p.A)(e).reverse());var u=function(t,e,n){for(var r=[],i=n||A.mix||t8,o=t.length-1,a=0;a<o;a++){var s=i(t[a],t[a+1]);e&&(s=$(Array.isArray(e)?e[a]||O:e,s)),r.push(s)}return r}(e,i,o),c=u.length,l=function(n){if(s&&n<t[0])return e[0];var r=0;if(c>1)for(;r<t.length-2&&!(n<t[r+1]);r++);var i=eF(t[r],t[r+1],n);return u[r](i)};return void 0===r||r?function(e){return l(J(t[0],t[a-1],e))}:l}((n=a&&a.length===o.length?a:(!function(t,e){for(var n=t[t.length-1],r=1;r<=e;r++){var i=eF(0,e,r);t.push(tY(n,1,i))}}(e=[0],o.length-1),e),n.map(function(t){return t*i})),o,{ease:Array.isArray(c)?c:o.map(function(){return c||ey}).splice(0,o.length-1)});return{calculatedDuration:i,next:function(t){return l.value=f(t),l.done=t>=i,l}}}var eT=function(t){return null!==t};function eV(t,e,n){var r=e.repeat,i=e.repeatType,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,a=t.filter(eT),s=o<0||r&&"loop"!==(void 0===i?"loop":i)&&r%2==1?0:a.length-1;return s&&void 0!==n?n:a[s]}var eR={decay:ef,inertia:ef,tween:eE,keyframes:eE,spring:el};function eC(t){"string"==typeof t.type&&(t.type=eR[t.type])}var eM=function(){function t(){(0,s.A)(this,t),this.updateFinished()}return(0,u.A)(t,[{key:"finished",get:function(){return this._finished}},{key:"updateFinished",value:function(){var t=this;this._finished=new Promise(function(e){t.resolve=e})}},{key:"notifyFinished",value:function(){this.resolve()}},{key:"then",value:function(t,e){return this.finished.then(t,e)}}]),t}();function eB(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function e_(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?eB(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):eB(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var eL=function(t){return t/100},eI=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t){var e;return(0,s.A)(this,r),(e=n.call(this)).state="idle",e.startTime=null,e.isStopped=!1,e.currentTime=0,e.holdTime=null,e.playbackSpeed=1,e.stop=function(){var t,n,r=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(r){var i=e.options.motionValue;i&&i.updatedAt!==_.now()&&e.tick(_.now())}e.isStopped=!0,"idle"!==e.state&&(e.teardown(),null==(t=(n=e.options).onStop)||t.call(n))},te.mainThread++,e.options=t,e.initAnimation(),e.play(),!1===t.autoplay&&e.pause(),e}return(0,u.A)(r,[{key:"initAnimation",value:function(){var t=this.options;eC(t);var e=t.type,n=t.repeat,r=t.repeatDelay,i=void 0===r?0:r,o=t.repeatType,a=t.velocity,s=t.keyframes,u=(void 0===e?eE:e)||eE;u!==eE&&"number"!=typeof s[0]&&(this.mixKeyframes=$(eL,t8(s[0],s[1])),s=[0,100]);var c=u(e_(e_({},t),{},{keyframes:s}));"mirror"===o&&(this.mirroredGenerator=u(e_(e_({},t),{},{keyframes:(0,p.A)(s).reverse(),velocity:-(void 0===a?0:a)}))),null===c.calculatedDuration&&(c.calculatedDuration=t7(c));var l=c.calculatedDuration;this.calculatedDuration=l,this.resolvedDuration=l+i,this.totalDuration=this.resolvedDuration*((void 0===n?0:n)+1)-i,this.generator=c}},{key:"updateTime",value:function(t){var e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}},{key:"tick",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.generator,r=this.totalDuration,i=this.mixKeyframes,o=this.mirroredGenerator,a=this.resolvedDuration,s=this.calculatedDuration;if(null===this.startTime)return n.next(0);var u=this.options,c=u.delay,l=u.keyframes,f=u.repeat,d=u.repeatType,h=u.repeatDelay,p=u.type,v=u.onUpdate,y=u.finalKeyframe;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);var m=this.currentTime-(void 0===c?0:c)*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?m<0:m>r;this.currentTime=Math.max(m,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);var b=this.currentTime,O=n;if(f){var A=Math.min(this.currentTime,r)/a,x=Math.floor(A),P=A%1;!P&&A>=1&&(P=1),1===P&&x--,(x=Math.min(x,f+1))%2&&("reverse"===d?(P=1-P,h&&(P-=h/a)):"mirror"===d&&(O=o)),b=J(0,1,P)*a}var w=g?{done:!1,value:l[0]}:O.next(b);i&&(w.value=i(w.value));var k=w.done;g||null===s||(k=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);var j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&k);return j&&p!==ef&&(w.value=eV(l,this.options,y,this.speed)),v&&v(w.value),j&&this.finish(),w}},{key:"then",value:function(t,e){return this.finished.then(t,e)}},{key:"duration",get:function(){return tt(this.calculatedDuration)}},{key:"time",get:function(){return tt(this.currentTime)},set:function(t){var e;t=Q(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),null==(e=this.driver)||e.start(!1)}},{key:"speed",get:function(){return this.playbackSpeed},set:function(t){this.updateTime(_.now());var e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=tt(this.currentTime))}},{key:"play",value:function(){var t,e,n=this;if(!this.isStopped){var r=this.options,i=r.driver,o=r.startTime;this.driver||(this.driver=(void 0===i?t4:i)(function(t){return n.tick(t)})),null==(t=(e=this.options).onPlay)||t.call(e);var a=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=a):null!==this.holdTime?this.startTime=a-this.holdTime:this.startTime||(this.startTime=null!=o?o:a),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}}},{key:"pause",value:function(){this.state="paused",this.updateTime(_.now()),this.holdTime=this.currentTime}},{key:"complete",value:function(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}},{key:"finish",value:function(){var t,e;this.notifyFinished(),this.teardown(),this.state="finished",null==(t=(e=this.options).onComplete)||t.call(e)}},{key:"cancel",value:function(){var t,e;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(t=(e=this.options).onCancel)||t.call(e)}},{key:"teardown",value:function(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,te.mainThread--}},{key:"stopDriver",value:function(){this.driver&&(this.driver.stop(),this.driver=void 0)}},{key:"sample",value:function(t){return this.startTime=0,this.tick(t,!0)}},{key:"attachTimeline",value:function(t){var e;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(e=this.driver)||e.stop(),t.observe(this)}}]),r}(eM),eU=function(t){return 180*t/Math.PI},eN=function(t){return eW(eU(Math.atan2(t[1],t[0])))},eZ={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:function(t){return(Math.abs(t[0])+Math.abs(t[3]))/2},rotate:eN,rotateZ:eN,skewX:function(t){return eU(Math.atan(t[1]))},skewY:function(t){return eU(Math.atan(t[2]))},skew:function(t){return(Math.abs(t[1])+Math.abs(t[2]))/2}},eW=function(t){return(t%=360)<0&&(t+=360),t},eq=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},ez=function(t){return Math.sqrt(t[4]*t[4]+t[5]*t[5])},eY={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eq,scaleY:ez,scale:function(t){return(eq(t)+ez(t))/2},rotateX:function(t){return eW(eU(Math.atan2(t[6],t[5])))},rotateY:function(t){return eW(eU(Math.atan2(-t[2],t[0])))},rotateZ:eN,rotate:eN,skewX:function(t){return eU(Math.atan(t[4]))},skewY:function(t){return eU(Math.atan(t[1]))},skew:function(t){return(Math.abs(t[1])+Math.abs(t[4]))/2}};function eK(t){return+!!t.includes("scale")}function eX(t,e){if(!t||"none"===t)return eK(e);var n,r,i=t.match(/^matrix3d\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);if(i)n=eY,r=i;else{var o=t.match(/^matrix\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);n=eZ,r=o}if(!r)return eK(e);var a=n[e],s=r[1].split(",").map(eG);return"function"==typeof a?a(s):s[a]}var eH=function(t,e){var n=getComputedStyle(t).transform;return eX(void 0===n?"none":n,e)};function eG(t){return parseFloat(t.trim())}var e$=function(t){return t===tf||t===tF},eJ=new Set(["x","y","z"]),eQ=E.filter(function(t){return!eJ.has(t)}),e0={width:function(t,e){var n=t.x,r=e.paddingLeft,i=e.paddingRight;return n.max-n.min-parseFloat(void 0===r?"0":r)-parseFloat(void 0===i?"0":i)},height:function(t,e){var n=t.y,r=e.paddingTop,i=e.paddingBottom;return n.max-n.min-parseFloat(void 0===r?"0":r)-parseFloat(void 0===i?"0":i)},top:function(t,e){return parseFloat(e.top)},left:function(t,e){return parseFloat(e.left)},bottom:function(t,e){var n=t.y;return parseFloat(e.top)+(n.max-n.min)},right:function(t,e){var n=t.x;return parseFloat(e.left)+(n.max-n.min)},x:function(t,e){return eX(e.transform,"x")},y:function(t,e){return eX(e.transform,"y")}};e0.translateX=e0.x,e0.translateY=e0.y;var e1=new Set,e2=!1,e3=!1,e5=!1;function e9(){if(e3){var t=Array.from(e1).filter(function(t){return t.needsMeasurement}),e=new Set(t.map(function(t){return t.element})),n=new Map;e.forEach(function(t){var e,r=(e=[],eQ.forEach(function(n){var r=t.getValue(n);void 0!==r&&(e.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),e);r.length&&(n.set(t,r),t.render())}),t.forEach(function(t){return t.measureInitialState()}),e.forEach(function(t){t.render();var e=n.get(t);e&&e.forEach(function(e){var n,r=(0,v.A)(e,2),i=r[0],o=r[1];null==(n=t.getValue(i))||n.set(o)})}),t.forEach(function(t){return t.measureEndState()}),t.forEach(function(t){void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}e3=!1,e2=!1,e1.forEach(function(t){return t.complete(e5)}),e1.clear()}function e8(){e1.forEach(function(t){t.readKeyframes(),t.needsMeasurement&&(e3=!0)})}var e4=function(){function t(e,n,r,i,o){var a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];(0,s.A)(this,t),this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=(0,p.A)(e),this.onComplete=n,this.name=r,this.motionValue=i,this.element=o,this.isAsync=a}return(0,u.A)(t,[{key:"scheduleResolve",value:function(){this.state="scheduled",this.isAsync?(e1.add(this),e2||(e2=!0,j.read(e8),j.resolveKeyframes(e9))):(this.readKeyframes(),this.complete())}},{key:"readKeyframes",value:function(){var t=this.unresolvedKeyframes,e=this.name,n=this.element,r=this.motionValue;if(null===t[0]){var i=null==r?void 0:r.get(),o=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){var a=n.readValue(e,o);null!=a&&(t[0]=a)}void 0===t[0]&&(t[0]=o),r&&void 0===i&&r.set(t[0])}for(var s,u=1;u<t.length;u++)null!=t[u]||(t[u]=t[u-1])}},{key:"setFinalKeyframe",value:function(){}},{key:"measureInitialState",value:function(){}},{key:"renderEndStyles",value:function(){}},{key:"measureEndState",value:function(){}},{key:"complete",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),e1.delete(this)}},{key:"cancel",value:function(){"scheduled"===this.state&&(e1.delete(this),this.state="pending")}},{key:"resume",value:function(){"pending"===this.state&&this.scheduleResolve()}}]),t}();function e6(t){var e;return function(){return void 0===e&&(e=t()),e}}var e7=e6(function(){return void 0!==window.ScrollTimeline}),nt={},ne=function(t,e){var n=e6(t);return function(){var t;return null!=(t=nt[e])?t:n()}}(function(){try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),nn=function(t){var e=(0,v.A)(t,4),n=e[0],r=e[1],i=e[2],o=e[3];return"cubic-bezier(".concat(n,", ").concat(r,", ").concat(i,", ").concat(o,")")},nr={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:nn([0,.65,.55,1]),circOut:nn([.55,0,1,.45]),backIn:nn([.31,.01,.66,-.59]),backOut:nn([.33,1.53,.69,.99])};function ni(t){return"function"==typeof t&&"applyToOptions"in t}var no=["type"],na=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t){if((0,s.A)(this,r),(c=n.call(this)).finishedTime=null,c.isStopped=!1,!t)return(0,l.A)(c);var e,i,o,u,c,f=t.element,d=t.name,p=t.keyframes,v=t.pseudoElement,y=t.allowFlatten,m=t.finalKeyframe,g=t.onComplete;c.isPseudoElement=!!v,c.allowFlatten=void 0!==y&&y,c.options=t,tr("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');var b=(o=t.type,u=(0,h.A)(t,no),ni(o)&&ne()?o.applyToOptions(u):(null!=u.duration||(u.duration=300),null!=u.ease||(u.ease="easeOut"),u));return c.animation=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.delay,o=r.duration,s=void 0===o?300:o,u=r.repeat,c=r.repeatType,l=r.ease,f=r.times,d=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0,h=(0,a.A)({},e,n);f&&(h.offset=f);var p=function t(e,n){if(e)return"function"==typeof e?ne()?t6(e,n):"ease-out":ej(e)?nn(e):Array.isArray(e)?e.map(function(e){return t(e,n)||nr.easeOut}):nr[e]}(void 0===l?"easeOut":l,s);Array.isArray(p)&&(h.easing=p),P.value&&te.waapi++;var v={delay:void 0===i?0:i,duration:s,easing:Array.isArray(p)?"linear":p,fill:"both",iterations:(void 0===u?0:u)+1,direction:"reverse"===(void 0===c?"loop":c)?"alternate":"normal"};d&&(v.pseudoElement=d);var y=t.animate(h,v);return P.value&&y.finished.finally(function(){te.waapi--}),y}(f,d,p,b,v),!1===b.autoplay&&c.animation.pause(),c.animation.onfinish=function(){if(c.finishedTime=c.time,!v){var t=eV(p,c.options,m,c.speed);c.updateMotionValue?c.updateMotionValue(t):d.startsWith("--")?f.style.setProperty(d,t):f.style[d]=t,c.animation.cancel()}null==g||g(),c.notifyFinished()},c}return(0,u.A)(r,[{key:"play",value:function(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}},{key:"pause",value:function(){this.animation.pause()}},{key:"complete",value:function(){var t,e;null==(t=(e=this.animation).finish)||t.call(e)}},{key:"cancel",value:function(){try{this.animation.cancel()}catch(t){}}},{key:"stop",value:function(){if(!this.isStopped){this.isStopped=!0;var t=this.state;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}}},{key:"commitStyles",value:function(){if(!this.isPseudoElement){var t,e;null==(t=(e=this.animation).commitStyles)||t.call(e)}}},{key:"duration",get:function(){var t,e;return tt(Number((null==(t=this.animation.effect)||null==(e=t.getComputedTiming)?void 0:e.call(t).duration)||0))}},{key:"time",get:function(){return tt(Number(this.animation.currentTime)||0)},set:function(t){this.finishedTime=null,this.animation.currentTime=Q(t)}},{key:"speed",get:function(){return this.animation.playbackRate},set:function(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}},{key:"state",get:function(){return null!==this.finishedTime?"finished":this.animation.playState}},{key:"startTime",get:function(){return Number(this.animation.startTime)},set:function(t){this.animation.startTime=t}},{key:"attachTimeline",value:function(t){var e,n=t.timeline,r=t.observe;return(this.allowFlatten&&(null==(e=this.animation.effect)||e.updateTiming({easing:"linear"})),this.animation.onfinish=null,n&&e7())?(this.animation.timeline=n,O):r(this)}}]),r}(eM),ns={anticipate:ex,backInOut:eA,circInOut:ek},nu=["motionValue","onUpdate","onComplete","element"];function nc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nc(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nc(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var nf=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t){var e;return(0,s.A)(this,r),"string"==typeof t.ease&&t.ease in ns&&(t.ease=ns[t.ease]),eC(t),e=n.call(this,t),t.startTime&&(e.startTime=t.startTime),e.options=t,e}return(0,u.A)(r,[{key:"updateMotionValue",value:function(t){var e,n=this.options,r=n.motionValue,i=(n.onUpdate,n.onComplete,n.element,(0,h.A)(n,nu));if(r){if(void 0!==t)return void r.set(t);var o=new eI(nl(nl({},i),{},{autoplay:!1})),a=Q(null!=(e=this.finishedTime)?e:this.time);r.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}}]),r}(na),nd=function(t,e){return"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tW.test(t)||"0"===t)&&!t.startsWith("url("))};function nh(t){return"object"==typeof t&&null!==t}function np(t){return nh(t)&&"offsetHeight"in t}var nv=new Set(["opacity","clipPath","filter","transform"]),ny=e6(function(){return Object.hasOwnProperty.call(Element.prototype,"animate")}),nm=["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"];function ng(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nb(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ng(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ng(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var nO=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t){var e,i,o=t.autoplay,a=t.delay,u=t.type,c=t.repeat,l=t.repeatDelay,f=t.repeatType,d=t.keyframes,p=t.name,v=t.motionValue,y=t.element,m=(0,h.A)(t,nm);(0,s.A)(this,r),(i=n.call(this)).stop=function(){var t,e;i._animation&&(i._animation.stop(),null==(e=i.stopTimeline)||e.call(i)),null==(t=i.keyframeResolver)||t.cancel()},i.createdAt=_.now();var g=nb({autoplay:void 0===o||o,delay:void 0===a?0:a,type:void 0===u?"keyframes":u,repeat:void 0===c?0:c,repeatDelay:void 0===l?0:l,repeatType:void 0===f?"loop":f,name:p,motionValue:v,element:y},m);return i.keyframeResolver=new((null==y?void 0:y.KeyframeResolver)||e4)(d,function(t,e,n){return i.onKeyframesResolved(t,e,g,!n)},p,v,y),null==(e=i.keyframeResolver)||e.scheduleResolve(),i}return(0,u.A)(r,[{key:"onKeyframesResolved",value:function(t,e,n,r){var i=this;this.keyframeResolver=void 0;var o=n.name,a=n.type,s=n.velocity,u=n.delay,c=n.isHandoff,l=n.onUpdate;this.resolvedAt=_.now(),!function(t,e,n,r){var i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;var o=t[t.length-1],a=nd(i,e),s=nd(o,e);return tn(a===s,"You are trying to animate ".concat(e,' from "').concat(i,'" to "').concat(o,'". ').concat(i," is not an animatable value - to enable this animation set ").concat(i," to a value animatable to ").concat(o," via the `style` property.")),!!a&&!!s&&(function(t){var e=t[0];if(1===t.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||ni(n))&&r)}(t,o,a,s)&&((A.instantAnimations||!u)&&(null==l||l(eV(t,n,e))),t[0]=t[t.length-1],n.duration=0,n.repeat=0);var f=nb(nb({startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e},n),{},{keyframes:t}),d=!c&&function(t){var e,n=t.motionValue,r=t.name,i=t.repeatDelay,o=t.repeatType,a=t.damping,s=t.type;if(!np(null==n||null==(e=n.owner)?void 0:e.current))return!1;var u=n.owner.getProps(),c=u.onUpdate,l=u.transformTemplate;return ny()&&r&&nv.has(r)&&("transform"!==r||!l)&&!c&&!i&&"mirror"!==o&&0!==a&&"inertia"!==s}(f)?new nf(nb(nb({},f),{},{element:f.motionValue.owner.current})):new eI(f);d.finished.then(function(){return i.notifyFinished()}).catch(O),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}},{key:"finished",get:function(){return this._animation?this.animation.finished:this._finished}},{key:"then",value:function(t,e){return this.finished.finally(t).then(function(){})}},{key:"animation",get:function(){if(!this._animation){var t;null==(t=this.keyframeResolver)||t.resume(),e5=!0,e8(),e9(),e5=!1}return this._animation}},{key:"duration",get:function(){return this.animation.duration}},{key:"time",get:function(){return this.animation.time},set:function(t){this.animation.time=t}},{key:"speed",get:function(){return this.animation.speed},set:function(t){this.animation.speed=t}},{key:"state",get:function(){return this.animation.state}},{key:"startTime",get:function(){return this.animation.startTime}},{key:"attachTimeline",value:function(t){var e=this;return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,function(){return e.stop()}}},{key:"play",value:function(){this.animation.play()}},{key:"pause",value:function(){this.animation.pause()}},{key:"complete",value:function(){this.animation.complete()}},{key:"cancel",value:function(){var t;this._animation&&this.animation.cancel(),null==(t=this.keyframeResolver)||t.cancel()}}]),r}(eM),nA=function(t){return null!==t},nx={type:"spring",stiffness:500,damping:25,restSpeed:10},nP={type:"keyframes",duration:.8},nw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nk=function(t,e){var n=e.keyframes;return n.length>2?nP:T.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:nx:nw},nj=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];function nS(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nD(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nS(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nS(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var nF=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;return function(a){var s=b(r,t)||{},u=s.delay||r.delay||0,c=r.elapsed,l=void 0===c?0:c;l-=Q(u);var f=nD(nD({keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity()},s),{},{delay:-l,onUpdate:function(t){e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:function(){a(),s.onComplete&&s.onComplete()},name:t,motionValue:e,element:o?void 0:i});s.when,s.delay,s.delayChildren,s.staggerChildren,s.staggerDirection,s.repeat,s.repeatType,s.repeatDelay,s.from,s.elapsed,Object.keys((0,h.A)(s,nj)).length||Object.assign(f,nk(t,f)),f.duration&&(f.duration=Q(f.duration)),f.repeatDelay&&(f.repeatDelay=Q(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);var d=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0===f.delay&&(d=!0)),(A.instantAnimations||A.skipAnimations)&&(d=!0,f.duration=0,f.delay=0),f.allowFlatten=!s.type&&!s.ease,d&&!o&&void 0!==e.get()){var p,v,y,m,g,O=(p=f.keyframes,v=s.repeat,y=s.repeatType,m=p.filter(nA),g=v&&"loop"!==(void 0===y?"loop":y)&&v%2==1?0:m.length-1,m[g]);if(void 0!==O)return void j.update(function(){f.onUpdate(O),f.onComplete()})}return s.isSync?new eI(f):new nO(f)}},nE=["transition","transitionEnd"];function nT(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nV(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.delay,i=void 0===r?0:r,o=n.transitionOverride,s=n.type,u=e.transition,c=void 0===u?t.getDefaultTransition():u,l=e.transitionEnd,f=(0,h.A)(e,nE);o&&(c=o);var d=[],p=s&&t.animationState&&t.animationState.getState()[s];for(var v in f){var y,m=t.getValue(v,null!=(y=t.latestValues[v])?y:null),O=f[v];if(!(void 0===O||p&&function(t,e){var n=t.protectedKeys,r=t.needsAnimating,i=n.hasOwnProperty(e)&&!0!==r[e];return r[e]=!1,i}(p,v))){var A=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nT(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nT(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({delay:i},b(c||{},v)),x=m.get();if(void 0===x||m.isAnimating||Array.isArray(O)||O!==x||A.velocity){var P=!1;if(window.MotionHandoffAnimation){var w=t.props[H];if(w){var k=window.MotionHandoffAnimation(w,v,j);null!==k&&(A.startTime=k,P=!0)}}K(t,v),m.start(nF(v,m,O,t.shouldReduceMotion&&V.has(v)?{type:!1}:A,t,P));var S=m.animation;S&&d.push(S)}}}return l&&Promise.all(d).then(function(){j.update(function(){l&&function(t,e){var n=g(t,e)||{},r=n.transitionEnd,i=(n.transition,(0,h.A)(n,W));for(var o in i=z(z({},i),void 0===r?{}:r)){var a,s=Z(a=i[o])?a[a.length-1]||0:a;t.hasValue(o)?t.getValue(o).set(s):t.addValue(o,N(s))}}(t,l)})}),d}function nR(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nC(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nR(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nR(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function nM(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=g(t,e,"exit"===r.type?null==(n=t.presenceContext)?void 0:n.custom:void 0),o=(i||{}).transition,a=void 0===o?t.getDefaultTransition()||{}:o;r.transitionOverride&&(a=r.transitionOverride);var s=i?function(){return Promise.all(nV(t,i,r))}:function(){return Promise.resolve()},u=t.variantChildren&&t.variantChildren.size?function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=a,o=i.delayChildren;return function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=arguments.length>5?arguments[5]:void 0,a=[],s=(t.variantChildren.size-1)*r,u=1===i?function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t*r}:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return s-t*r};return Array.from(t.variantChildren).sort(nB).forEach(function(t,r){t.notify("AnimationStart",e),a.push(nM(t,e,nC(nC({},o),{},{delay:n+u(r)})).then(function(){return t.notify("AnimationComplete",e)}))}),Promise.all(a)}(t,e,(void 0===o?0:o)+n,i.staggerChildren,i.staggerDirection,r)}:function(){return Promise.resolve()},c=a.when;if(!c)return Promise.all([s(),u(r.delay)]);var l=(0,v.A)("beforeChildren"===c?[s,u]:[u,s],2),f=l[0],d=l[1];return f().then(function(){return d()})}function nB(t,e){return t.sortNodePosition(e)}function n_(t,e){if(!Array.isArray(e))return!1;var n=e.length;if(n!==t.length)return!1;for(var r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0}function nL(t){return"string"==typeof t||Array.isArray(t)}var nI=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nU=["initial"].concat(nI),nN=nU.length,nZ=["transition","transitionEnd"];function nW(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nq(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nW(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nW(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var nz=(0,p.A)(nI).reverse(),nY=nI.length;function nK(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nX(){return{animate:nK(!0),whileInView:nK(),whileHover:nK(),whileTap:nK(),whileDrag:nK(),whileFocus:nK(),exit:nK()}}var nH=function(){function t(e){(0,s.A)(this,t),this.isMounted=!1,this.node=e}return(0,u.A)(t,[{key:"update",value:function(){}}]),t}(),nG=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t){var e;return(0,s.A)(this,r),e=n.call(this,t),t.animationState||(t.animationState=function(t){var e=function(e){return Promise.all(e.map(function(e){return function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(function(e){return nM(t,e,r)}));else if("string"==typeof e)n=nM(t,e,r);else{var i="function"==typeof e?g(t,e,r.custom):e;n=Promise.all(nV(t,i,r))}return n.then(function(){t.notify("AnimationComplete",e)})}(t,e.animation,e.options)}))},n=nX(),r=!0;function i(i){for(var o=t.props,a=function t(e){if(e){if(!e.isControllingVariants){var n=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(n.initial=e.props.initial),n}for(var r={},i=0;i<nN;i++){var o=nU[i],a=e.props[o];(nL(a)||!1===a)&&(r[o]=a)}return r}}(t.parent)||{},s=[],u=new Set,c={},l=1/0,f=0;f<nY;f++)if(function(){var e,v,y=nz[f],m=n[y],b=void 0!==o[y]?o[y]:a[y],O=nL(b),A=y===i?m.isActive:null;!1===A&&(l=f);var x=b===a[y]&&b!==o[y]&&O;if(x&&r&&t.manuallyAnimateOnMount&&(x=!1),m.protectedKeys=nq({},c),!m.isActive&&null===A||!b&&!m.prevProp||d(b)||"boolean"==typeof b)return 1;var P=(e=m.prevProp,"string"==typeof(v=b)?v!==e:!!Array.isArray(v)&&!n_(v,e)),w=P||y===i&&m.isActive&&!x&&O||f>l&&O,k=!1,j=Array.isArray(b)?b:[b],S=j.reduce(function(e,n){var r,i=g(t,n,"exit"===y?null==(r=t.presenceContext)?void 0:r.custom:void 0);if(i){i.transition;var o=i.transitionEnd,a=(0,h.A)(i,nZ);e=nq(nq(nq({},e),a),o)}return e},{});!1===A&&(S={});var D=m.prevResolvedValues,F=void 0===D?{}:D,E=nq(nq({},F),S),T=function(e){w=!0,u.has(e)&&(k=!0,u.delete(e)),m.needsAnimating[e]=!0;var n=t.getValue(e);n&&(n.liveStyle=!1)};for(var V in E){var R=S[V],C=F[V];if(!c.hasOwnProperty(V)){var M=!1;(Z(R)&&Z(C)?n_(R,C):R===C)?void 0!==R&&u.has(V)?T(V):m.protectedKeys[V]=!0:null!=R?T(V):u.add(V)}}m.prevProp=b,m.prevResolvedValues=S,m.isActive&&(c=nq(nq({},c),S)),r&&t.blockInitialAnimation&&(w=!1);var B=!(x&&P)||k;w&&B&&s.push.apply(s,(0,p.A)(j.map(function(t){return{animation:t,options:{type:y}}})))}())continue;if(u.size){var v={};if("boolean"!=typeof o.initial){var y=g(t,Array.isArray(o.initial)?o.initial[0]:o.initial);y&&y.transition&&(v.transition=y.transition)}u.forEach(function(e){var n=t.getBaseTarget(e),r=t.getValue(e);r&&(r.liveStyle=!0),v[e]=null!=n?n:null}),s.push({animation:v})}var m=!!s.length;return r&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(m=!1),r=!1,m?e(s):Promise.resolve()}return{animateChanges:i,setActive:function(e,r){if(n[e].isActive===r)return Promise.resolve();null==(o=t.variantChildren)||o.forEach(function(t){var n;return null==(n=t.animationState)?void 0:n.setActive(e,r)}),n[e].isActive=r;var o,a=i(e);for(var s in n)n[s].protectedKeys={};return a},setAnimateFunction:function(n){e=n(t)},getState:function(){return n},reset:function(){n=nX(),r=!0}}}(t)),e}return(0,u.A)(r,[{key:"updateAnimationControlsSubscription",value:function(){var t=this.node.getProps().animate;d(t)&&(this.unmountControls=t.subscribe(this.node))}},{key:"mount",value:function(){this.updateAnimationControlsSubscription()}},{key:"update",value:function(){this.node.getProps().animate!==(this.node.prevProps||{}).animate&&this.updateAnimationControlsSubscription()}},{key:"unmount",value:function(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}]),r}(nH),n$=0,nJ=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.id=n$++,t}return(0,u.A)(r,[{key:"update",value:function(){var t=this;if(this.node.presenceContext){var e=this.node.presenceContext,n=e.isPresent,r=e.onExitComplete,i=(this.node.prevPresenceContext||{}).isPresent;if(this.node.animationState&&n!==i){var o=this.node.animationState.setActive("exit",!n);r&&!n&&o.then(function(){r(t.id)})}}}},{key:"mount",value:function(){var t=this.node.presenceContext||{},e=t.register,n=t.onExitComplete;n&&n(this.id),e&&(this.unmount=e(this.id))}},{key:"unmount",value:function(){}}]),r}(nH),nQ={x:!1,y:!1};function n0(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,n,r),function(){return t.removeEventListener(e,n)}}var n1=function(t){return"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary};function n2(t){return{point:{x:t.pageX,y:t.pageY}}}function n3(t,e,n,r){return n0(t,e,function(t){return n1(t)&&n(t,n2(t))},r)}function n5(t){var e=t.top;return{x:{min:t.left,max:t.right},y:{min:e,max:t.bottom}}}function n9(t){return t.max-t.min}function n8(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=r,t.originPoint=tY(e.min,e.max,t.origin),t.scale=n9(n)/n9(e),t.translate=tY(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function n4(t,e,n,r){n8(t.x,e.x,n.x,r?r.originX:void 0),n8(t.y,e.y,n.y,r?r.originY:void 0)}function n6(t,e,n){t.min=n.min+e.min,t.max=t.min+n9(e)}function n7(t,e,n){t.min=e.min-n.min,t.max=t.min+n9(e)}function rt(t,e,n){n7(t.x,e.x,n.x),n7(t.y,e.y,n.y)}var re=function(){return{translate:0,scale:1,origin:0,originPoint:0}},rn=function(){return{x:re(),y:re()}},rr=function(){return{min:0,max:0}},ri=function(){return{x:rr(),y:rr()}};function ro(t){return[t("x"),t("y")]}function ra(t){return void 0===t||1===t}function rs(t){var e=t.scale,n=t.scaleX,r=t.scaleY;return!ra(e)||!ra(n)||!ra(r)}function ru(t){return rs(t)||rc(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function rc(t){var e,n;return(e=t.x)&&"0%"!==e||(n=t.y)&&"0%"!==n}function rl(t,e,n,r,i){return void 0!==i&&(t=r+i*(t-r)),r+n*(t-r)+e}function rf(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;t.min=rl(t.min,e,n,r,i),t.max=rl(t.max,e,n,r,i)}function rd(t,e){var n=e.x,r=e.y;rf(t.x,n.translate,n.scale,n.originPoint),rf(t.y,r.translate,r.scale,r.originPoint)}function rh(t,e){t.min=t.min+e,t.max=t.max+e}function rp(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,o=tY(t.min,t.max,i);rf(t,e,n,o,r)}function rv(t,e){rp(t.x,e.x,e.scaleX,e.scale,e.originX),rp(t.y,e.y,e.scaleY,e.scale,e.originY)}function ry(t,e){return n5(function(t,e){if(!e)return t;var n=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}var rm=function(t){var e=t.current;return e?e.ownerDocument.defaultView:null};function rg(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var rb=function(t,e){return Math.abs(t-e)};function rO(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function rA(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?rO(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):rO(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var rx=function(){function t(e,n){var r=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=i.transformPagePoint,a=i.contextWindow,u=i.dragSnapToOrigin;if((0,s.A)(this,t),this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=function(){if(r.lastMoveEvent&&r.lastMoveEventInfo){var t,e,n=rk(r.lastMoveEventInfo,r.history),i=null!==r.startEvent,o=(t=n.offset,e={x:0,y:0},Math.sqrt(Math.pow(rb(t.x,e.x),2)+Math.pow(rb(t.y,e.y),2))>=3);if(i||o){var a=n.point,s=D.timestamp;r.history.push(rA(rA({},a),{},{timestamp:s}));var u=r.handlers,c=u.onStart,l=u.onMove;i||(c&&c(r.lastMoveEvent,n),r.startEvent=r.lastMoveEvent),l&&l(r.lastMoveEvent,n)}}},this.handlePointerMove=function(t,e){r.lastMoveEvent=t,r.lastMoveEventInfo=rP(e,r.transformPagePoint),j.update(r.updatePoint,!0)},this.handlePointerUp=function(t,e){r.end();var n=r.handlers,i=n.onEnd,o=n.onSessionEnd,a=n.resumeAnimation;if(r.dragSnapToOrigin&&a&&a(),r.lastMoveEvent&&r.lastMoveEventInfo){var s=rk("pointercancel"===t.type?r.lastMoveEventInfo:rP(e,r.transformPagePoint),r.history);r.startEvent&&i&&i(t,s),o&&o(t,s)}},n1(e)){this.dragSnapToOrigin=void 0!==u&&u,this.handlers=n,this.transformPagePoint=o,this.contextWindow=a||window;var c=rP(n2(e),this.transformPagePoint),l=c.point,f=D.timestamp;this.history=[rA(rA({},l),{},{timestamp:f})];var d=n.onSessionStart;d&&d(e,rk(c,this.history)),this.removeListeners=$(n3(this.contextWindow,"pointermove",this.handlePointerMove),n3(this.contextWindow,"pointerup",this.handlePointerUp),n3(this.contextWindow,"pointercancel",this.handlePointerUp))}}return(0,u.A)(t,[{key:"updateHandlers",value:function(t){this.handlers=t}},{key:"end",value:function(){this.removeListeners&&this.removeListeners(),S(this.updatePoint)}}]),t}();function rP(t,e){return e?{point:e(t.point)}:t}function rw(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rk(t,e){var n=t.point;return{point:n,delta:rw(n,rj(e)),offset:rw(n,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};for(var n=t.length-1,r=null,i=rj(t);n>=0&&(r=t[n],!(i.timestamp-r.timestamp>Q(.1)));)n--;if(!r)return{x:0,y:0};var o=tt(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};var a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function rj(t){return t[t.length-1]}function rS(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function rD(t,e){var n=e.min-t.min,r=e.max-t.max;if(e.max-e.min<t.max-t.min){var i=[r,n];n=i[0],r=i[1]}return{min:n,max:r}}function rF(t,e,n){return{min:rE(t,e),max:rE(t,n)}}function rE(t,e){return"number"==typeof t?t:t[e]||0}function rT(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function rV(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?rT(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):rT(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var rR=new WeakMap,rC=function(){function t(e){(0,s.A)(this,t),this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ri(),this.visualElement=e}return(0,u.A)(t,[{key:"start",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.snapToCursor,i=void 0!==r&&r,o=this.visualElement.presenceContext;if(!o||!1!==o.isPresent){var a=this.getProps().dragSnapToOrigin;this.panSession=new rx(t,{onSessionStart:function(t){e.getProps().dragSnapToOrigin?e.pauseAnimation():e.stopAnimation(),i&&e.snapToCursor(n2(t).point)},onStart:function(t,n){var r=e.getProps(),i=r.drag,o=r.dragPropagation,a=r.onDragStart;if(!i||o||(e.openDragLock&&e.openDragLock(),e.openDragLock=function(t){if("x"===t||"y"===t)if(nQ[t])return null;else return nQ[t]=!0,function(){nQ[t]=!1};return nQ.x||nQ.y?null:(nQ.x=nQ.y=!0,function(){nQ.x=nQ.y=!1})}(i),e.openDragLock)){e.isDragging=!0,e.currentDirection=null,e.resolveConstraints(),e.visualElement.projection&&(e.visualElement.projection.isAnimationBlocked=!0,e.visualElement.projection.target=void 0),ro(function(t){var n=e.getAxisMotionValue(t).get()||0;if(tD.test(n)){var r=e.visualElement.projection;if(r&&r.layout){var i=r.layout.layoutBox[t];i&&(n=n9(i)*(parseFloat(n)/100))}}e.originPoint[t]=n}),a&&j.postRender(function(){return a(t,n)}),K(e.visualElement,"transform");var s=e.visualElement.animationState;s&&s.setActive("whileDrag",!0)}},onMove:function(t,n){var r=e.getProps(),i=r.dragPropagation,o=r.dragDirectionLock,a=r.onDirectionLock,s=r.onDrag;if(i||e.openDragLock){var u=n.offset;if(o&&null===e.currentDirection){e.currentDirection=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(u),null!==e.currentDirection&&a&&a(e.currentDirection);return}e.updateAxis("x",n.point,u),e.updateAxis("y",n.point,u),e.visualElement.render(),s&&s(t,n)}},onSessionEnd:function(t,n){return e.stop(t,n)},resumeAnimation:function(){return ro(function(t){var n;return"paused"===e.getAnimationState(t)&&(null==(n=e.getAxisMotionValue(t).animation)?void 0:n.play())})}},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:a,contextWindow:rm(this.visualElement)})}}},{key:"stop",value:function(t,e){var n=this.isDragging;if(this.cancel(),n){var r=e.velocity;this.startAnimation(r);var i=this.getProps().onDragEnd;i&&j.postRender(function(){return i(t,e)})}}},{key:"cancel",value:function(){this.isDragging=!1;var t=this.visualElement,e=t.projection,n=t.animationState;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0,!this.getProps().dragPropagation&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}},{key:"updateAxis",value:function(t,e,n){var r=this.getProps().drag;if(n&&rM(t,r,this.currentDirection)){var i,o,a,s,u,c=this.getAxisMotionValue(t),l=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(i=l,o=this.constraints[t],a=this.elastic[t],s=o.min,u=o.max,void 0!==s&&i<s?i=a?tY(s,i,a.min):Math.max(i,s):void 0!==u&&i>u&&(i=a?tY(u,i,a.max):Math.min(i,u)),l=i),c.set(l)}}},{key:"resolveConstraints",value:function(){var t,e,n,r,i,o,a=this,s=this.getProps(),u=s.dragConstraints,c=s.dragElastic,l=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(o=this.visualElement.projection)?void 0:o.layout,f=this.constraints;u&&rg(u)?this.constraints||(this.constraints=this.resolveRefConstraints()):u&&l?this.constraints=(t=l.layoutBox,e=u.top,n=u.left,r=u.bottom,i=u.right,{x:rS(t.x,n,i),y:rS(t.y,e,r)}):this.constraints=!1,this.elastic=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:rF(t,"left","right"),y:rF(t,"top","bottom")}}(c),f!==this.constraints&&l&&this.constraints&&!this.hasMutatedConstraints&&ro(function(t){var e,n,r;!1!==a.constraints&&a.getAxisMotionValue(t)&&(a.constraints[t]=(e=l.layoutBox[t],n=a.constraints[t],r={},void 0!==n.min&&(r.min=n.min-e.min),void 0!==n.max&&(r.max=n.max-e.min),r))})}},{key:"resolveRefConstraints",value:function(){var t=this.getProps(),e=t.dragConstraints,n=t.onMeasureDragConstraints;if(!e||!rg(e))return!1;var r=e.current;tr(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");var i=this.visualElement.projection;if(!i||!i.layout)return!1;var o=(s=i.root,u=ry(r,this.visualElement.getTransformPagePoint()),(c=s.scroll)&&(rh(u.x,c.offset.x),rh(u.y,c.offset.y)),u),a=(l=i.layout.layoutBox,{x:rD(l.x,o.x),y:rD(l.y,o.y)});if(n){var s,u,c,l,f,d,h,p=n((d=(f=a).x,{top:(h=f.y).min,right:d.max,bottom:h.max,left:d.min}));this.hasMutatedConstraints=!!p,p&&(a=n5(p))}return a}},{key:"startAnimation",value:function(t){var e=this,n=this.getProps(),r=n.drag,i=n.dragMomentum,o=n.dragElastic,a=n.dragTransition,s=n.dragSnapToOrigin,u=n.onDragTransitionEnd,c=this.constraints||{};return Promise.all(ro(function(n){if(rM(n,r,e.currentDirection)){var u=c&&c[n]||{};s&&(u={min:0,max:0});var l=rV(rV({type:"inertia",velocity:i?t[n]:0,bounceStiffness:o?200:1e6,bounceDamping:o?40:1e7,timeConstant:750,restDelta:1,restSpeed:10},a),u);return e.startAxisValueAnimation(n,l)}})).then(u)}},{key:"startAxisValueAnimation",value:function(t,e){var n=this.getAxisMotionValue(t);return K(this.visualElement,t),n.start(nF(t,n,0,e,this.visualElement,!1))}},{key:"stopAnimation",value:function(){var t=this;ro(function(e){return t.getAxisMotionValue(e).stop()})}},{key:"pauseAnimation",value:function(){var t=this;ro(function(e){var n;return null==(n=t.getAxisMotionValue(e).animation)?void 0:n.pause()})}},{key:"getAnimationState",value:function(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}},{key:"getAxisMotionValue",value:function(t){var e="_drag".concat(t.toUpperCase()),n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}},{key:"snapToCursor",value:function(t){var e=this;ro(function(n){if(rM(n,e.getProps().drag,e.currentDirection)){var r=e.visualElement.projection,i=e.getAxisMotionValue(n);if(r&&r.layout){var o=r.layout.layoutBox[n],a=o.min,s=o.max;i.set(t[n]-tY(a,s,.5))}}})}},{key:"scalePositionWithinConstraints",value:function(){var t=this;if(this.visualElement.current){var e=this.getProps(),n=e.drag,r=e.dragConstraints,i=this.visualElement.projection;if(rg(r)&&i&&this.constraints){this.stopAnimation();var o={x:0,y:0};ro(function(e){var n=t.getAxisMotionValue(e);if(n&&!1!==t.constraints){var r,i,a,s,u,c=n.get();o[e]=(r={min:c,max:c},i=t.constraints[e],a=.5,s=n9(r),(u=n9(i))>s?a=eF(i.min,i.max-s,r.min):s>u&&(a=eF(r.min,r.max-u,i.min)),J(0,1,a))}});var a=this.visualElement.getProps().transformTemplate;this.visualElement.current.style.transform=a?a({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ro(function(e){if(rM(e,n,null)){var r=t.getAxisMotionValue(e),i=t.constraints[e],a=i.min,s=i.max;r.set(tY(a,s,o[e]))}})}}}},{key:"addListeners",value:function(){var t=this;if(this.visualElement.current){rR.set(this.visualElement,this);var e=n3(this.visualElement.current,"pointerdown",function(e){var n=t.getProps(),r=n.drag,i=n.dragListener;r&&(void 0===i||i)&&t.start(e)}),n=function(){var e=t.getProps().dragConstraints;rg(e)&&e.current&&(t.constraints=t.resolveRefConstraints())},r=this.visualElement.projection,i=r.addEventListener("measure",n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),j.read(n);var o=n0(window,"resize",function(){return t.scalePositionWithinConstraints()}),a=r.addEventListener("didUpdate",function(e){var n=e.delta,r=e.hasLayoutChanged;t.isDragging&&r&&(ro(function(e){var r=t.getAxisMotionValue(e);r&&(t.originPoint[e]+=n[e].translate,r.set(r.get()+n[e].translate))}),t.visualElement.render())});return function(){o(),e(),i(),a&&a()}}}},{key:"getProps",value:function(){var t=this.visualElement.getProps(),e=t.drag,n=t.dragDirectionLock,r=t.dragPropagation,i=t.dragConstraints,o=t.dragElastic,a=t.dragMomentum;return rV(rV({},t),{},{drag:void 0!==e&&e,dragDirectionLock:void 0!==n&&n,dragPropagation:void 0!==r&&r,dragConstraints:void 0!==i&&i,dragElastic:void 0===o?.35:o,dragMomentum:void 0===a||a})}}]),t}();function rM(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}var rB=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t){var e;return(0,s.A)(this,r),(e=n.call(this,t)).removeGroupControls=O,e.removeListeners=O,e.controls=new rC(t),e}return(0,u.A)(r,[{key:"mount",value:function(){var t=this.node.getProps().dragControls;t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||O}},{key:"unmount",value:function(){this.removeGroupControls(),this.removeListeners()}}]),r}(nH),r_=function(t){return function(e,n){t&&j.postRender(function(){return t(e,n)})}},rL=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.removePointerDownListener=O,t}return(0,u.A)(r,[{key:"onPointerDown",value:function(t){this.session=new rx(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rm(this.node)})}},{key:"createPanHandlers",value:function(){var t=this,e=this.node.getProps(),n=e.onPanSessionStart,r=e.onPanStart,i=e.onPan,o=e.onPanEnd;return{onSessionStart:r_(n),onStart:r_(r),onMove:i,onEnd:function(e,n){delete t.session,o&&j.postRender(function(){return o(e,n)})}}}},{key:"mount",value:function(){var t=this;this.removePointerDownListener=n3(this.node.current,"pointerdown",function(e){return t.onPointerDown(e)})}},{key:"update",value:function(){this.session&&this.session.updateHandlers(this.createPanHandlers())}},{key:"unmount",value:function(){this.removePointerDownListener(),this.session&&this.session.end()}}]),r}(nH),rI=n(95155),rU=w(queueMicrotask,!1),rN=rU.schedule;rU.cancel;var rZ=n(12115),rW=(0,rZ.createContext)(null),rq=(0,rZ.createContext)({}),rz=(0,rZ.createContext)({}),rY={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rK(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}var rX={correct:function(t,e){if(!e.target)return t;if("string"==typeof t)if(!tF.test(t))return t;else t=parseFloat(t);var n=rK(t,e.target.x),r=rK(t,e.target.y);return"".concat(n,"% ").concat(r,"%")}},rH={};function rG(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function r$(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?rG(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):rG(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var rJ=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){return(0,s.A)(this,r),n.apply(this,arguments)}return(0,u.A)(r,[{key:"componentDidMount",value:function(){var t=this,e=this.props,n=e.visualElement,r=e.layoutGroup,i=e.switchLayoutGroup,o=e.layoutId,a=n.projection;for(var s in r0)rH[s]=r0[s],to(s)&&(rH[s].isCSSVariable=!0);a&&(r.group&&r.group.add(a),i&&i.register&&o&&i.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",function(){t.safeToRemove()}),a.setOptions(r$(r$({},a.options),{},{onExitComplete:function(){return t.safeToRemove()}}))),rY.hasEverUpdated=!0}},{key:"getSnapshotBeforeUpdate",value:function(t){var e=this,n=this.props,r=n.layoutDependency,i=n.visualElement,o=n.drag,a=n.isPresent,s=i.projection;return s&&(s.isPresent=a,o||t.layoutDependency!==r||void 0===r||t.isPresent!==a?s.willUpdate():this.safeToRemove(),t.isPresent!==a&&(a?s.promote():s.relegate()||j.postRender(function(){var t=s.getStack();t&&t.members.length||e.safeToRemove()}))),null}},{key:"componentDidUpdate",value:function(){var t=this,e=this.props.visualElement.projection;e&&(e.root.didUpdate(),rN.postRender(function(){!e.currentAnimation&&e.isLead()&&t.safeToRemove()}))}},{key:"componentWillUnmount",value:function(){var t=this.props,e=t.visualElement,n=t.layoutGroup,r=t.switchLayoutGroup,i=e.projection;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}},{key:"safeToRemove",value:function(){var t=this.props.safeToRemove;t&&t()}},{key:"render",value:function(){return null}}]),r}(rZ.Component);function rQ(t){var e=function(){var t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],e=(0,rZ.useContext)(rW);if(null===e)return[!0,null];var n=e.isPresent,r=e.onExitComplete,i=e.register,o=(0,rZ.useId)();(0,rZ.useEffect)(function(){if(t)return i(o)},[t]);var a=(0,rZ.useCallback)(function(){return t&&r&&r(o)},[o,r,t]);return!n&&r?[!1,a]:[!0]}(),n=(0,v.A)(e,2),r=n[0],i=n[1],o=(0,rZ.useContext)(rq);return(0,rI.jsx)(rJ,r$(r$({},t),{},{layoutGroup:o,switchLayoutGroup:(0,rZ.useContext)(rz),isPresent:r,safeToRemove:i}))}var r0={borderRadius:r$(r$({},rX),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:rX,borderTopRightRadius:rX,borderBottomLeftRadius:rX,borderBottomRightRadius:rX,boxShadow:{correct:function(t,e){var n=e.treeScale,r=e.projectionDelta,i=tW.parse(t);if(i.length>5)return t;var o=tW.createTransformer(t),a=+("number"!=typeof i[0]),s=r.x.scale*n.x,u=r.y.scale*n.y;i[0+a]/=s,i[1+a]/=u;var c=tY(s,u,.5);return"number"==typeof i[2+a]&&(i[2+a]/=c),"number"==typeof i[3+a]&&(i[3+a]/=c),o(i)}}};function r1(t){return nh(t)&&"ownerSVGElement"in t}var r2=function(t,e){return t.depth-e.depth},r3=function(){function t(){(0,s.A)(this,t),this.children=[],this.isDirty=!1}return(0,u.A)(t,[{key:"add",value:function(t){R(this.children,t),this.isDirty=!0}},{key:"remove",value:function(t){C(this.children,t),this.isDirty=!0}},{key:"forEach",value:function(t){this.isDirty&&this.children.sort(r2),this.isDirty=!1,this.children.forEach(t)}}]),t}();function r5(t){return Y(t)?t.get():t}var r9=["TopLeft","TopRight","BottomLeft","BottomRight"],r8=r9.length,r4=function(t){return"string"==typeof t?parseFloat(t):t},r6=function(t){return"number"==typeof t||tF.test(t)};function r7(t,e){return void 0!==t[e]?t[e]:t.borderRadius}var it=ir(0,.5,ew),ie=ir(.5,.95,O);function ir(t,e,n){return function(r){return r<t?0:r>e?1:n(eF(t,e,r))}}function ii(t,e){t.min=e.min,t.max=e.max}function io(t,e){ii(t.x,e.x),ii(t.y,e.y)}function ia(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function is(t,e,n,r,i){return t-=e,t=r+1/n*(t-r),void 0!==i&&(t=r+1/i*(t-r)),t}function iu(t,e,n,r,i){var o=(0,v.A)(n,3),a=o[0],s=o[1],u=o[2];!function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(tD.test(e)&&(e=parseFloat(e),e=tY(a.min,a.max,e/100)-a.min),"number"==typeof e){var s=tY(o.min,o.max,r);t===o&&(s-=e),t.min=is(t.min,e,n,s,i),t.max=is(t.max,e,n,s,i)}}(t,e[a],e[s],e[u],e.scale,r,i)}var ic=["x","scaleX","originX"],il=["y","scaleY","originY"];function id(t,e,n,r){iu(t.x,e,ic,n?n.x:void 0,r?r.x:void 0),iu(t.y,e,il,n?n.y:void 0,r?r.y:void 0)}function ih(t){return 0===t.translate&&1===t.scale}function ip(t){return ih(t.x)&&ih(t.y)}function iv(t,e){return t.min===e.min&&t.max===e.max}function iy(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function im(t,e){return iy(t.x,e.x)&&iy(t.y,e.y)}function ig(t){return n9(t.x)/n9(t.y)}function ib(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}var iO=function(){function t(){(0,s.A)(this,t),this.members=[]}return(0,u.A)(t,[{key:"add",value:function(t){R(this.members,t),t.scheduleRender()}},{key:"remove",value:function(t){if(C(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){var e=this.members[this.members.length-1];e&&this.promote(e)}}},{key:"relegate",value:function(t){var e,n=this.members.findIndex(function(e){return t===e});if(0===n)return!1;for(var r=n;r>=0;r--){var i=this.members[r];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}},{key:"promote",value:function(t,e){var n=this.lead;t!==n&&(this.prevLead=n,this.lead=t,t.show(),n&&(n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0),!1===t.options.crossfade&&n.hide()))}},{key:"exitAnimationComplete",value:function(){this.members.forEach(function(t){var e=t.options,n=t.resumingFrom;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}},{key:"scheduleRender",value:function(){this.members.forEach(function(t){t.instance&&t.scheduleRender(!1)})}},{key:"removeLeadSnapshot",value:function(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}]),t}();function iA(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ix(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?iA(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):iA(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var iP={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iw=["","X","Y","Z"],ik={visibility:"hidden"},ij=0;function iS(t,e,n,r){var i=e.latestValues;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),r&&(r[t]=0))}function iD(t){var e=t.attachResizeListener,n=t.defaultParent,r=t.measureScroll,i=t.checkIsScrollRoot,o=t.resetTransform;function a(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==n?void 0:n();(0,s.A)(this,a),this.id=ij++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=function(){return t.update()},this.projectionUpdateScheduled=!1,this.checkUpdateFailed=function(){t.isUpdating&&(t.isUpdating=!1,t.clearAllSnapshots())},this.updateProjection=function(){t.projectionUpdateScheduled=!1,P.value&&(iP.nodes=iP.calculatedTargetDeltas=iP.calculatedProjections=0),t.nodes.forEach(iT),t.nodes.forEach(iL),t.nodes.forEach(iI),t.nodes.forEach(iV),P.addProjectionMetrics&&P.addProjectionMetrics(iP)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[].concat((0,p.A)(r.path),[r]):[],this.parent=r,this.depth=r?r.depth+1:0;for(var i=0;i<this.path.length;i++)this.path[i].shouldResetTransform=!0;this.root===this&&(this.nodes=new r3)}return(0,u.A)(a,[{key:"addEventListener",value:function(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new M),this.eventHandlers.get(t).add(e)}},{key:"notifyListeners",value:function(t){for(var e=this.eventHandlers.get(t),n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];e&&e.notify.apply(e,r)}},{key:"hasListeners",value:function(t){return this.eventHandlers.has(t)}},{key:"mount",value:function(t){var n=this;if(!this.instance){this.isSVG=r1(t)&&!(r1(t)&&"svg"===t.tagName),this.instance=t;var r=this.options,i=r.layoutId,o=r.layout,a=r.visualElement;if(a&&!a.current&&a.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(o||i)&&(this.isLayoutDirty=!0),e){var s,u=function(){return n.root.updateBlockedByResize=!1};e(t,function(){var t,e,r,i;n.root.updateBlockedByResize=!0,s&&s(),t=u,e=250,r=_.now(),i=function n(i){var o=i.timestamp-r;o>=250&&(S(n),t(o-e))},j.setup(i,!0),s=function(){return S(i)},rY.hasAnimatedSinceResize&&(rY.hasAnimatedSinceResize=!1,n.nodes.forEach(i_))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&a&&(i||o)&&this.addEventListener("didUpdate",function(t){var e=t.delta,r=t.hasLayoutChanged,i=t.hasRelativeLayoutChanged,o=t.layout;if(n.isTreeAnimationBlocked()){n.target=void 0,n.relativeTarget=void 0;return}var s=n.options.transition||a.getDefaultTransition()||iz,u=a.getProps(),c=u.onLayoutAnimationStart,l=u.onLayoutAnimationComplete,f=!n.targetLayout||!im(n.targetLayout,o),d=!r&&i;if(n.options.layoutRoot||n.resumeFrom||d||r&&(f||!n.currentAnimation)){n.resumeFrom&&(n.resumingFrom=n.resumeFrom,n.resumingFrom.resumingFrom=void 0),n.setAnimationOrigin(e,d);var h=ix(ix({},b(s,"layout")),{},{onPlay:c,onComplete:l});(a.shouldReduceMotion||n.options.layoutRoot)&&(h.delay=0,h.type=!1),n.startAnimation(h)}else r||i_(n),n.isLead()&&n.options.onExitComplete&&n.options.onExitComplete();n.targetLayout=o})}}},{key:"unmount",value:function(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);var t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),S(this.updateProjection)}},{key:"blockUpdate",value:function(){this.updateManuallyBlocked=!0}},{key:"unblockUpdate",value:function(){this.updateManuallyBlocked=!1}},{key:"isUpdateBlocked",value:function(){return this.updateManuallyBlocked||this.updateBlockedByResize}},{key:"isTreeAnimationBlocked",value:function(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}},{key:"startUpdate",value:function(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iU),this.animationId++)}},{key:"getTransformTemplate",value:function(){var t=this.options.visualElement;return t&&t.getProps().transformTemplate}},{key:"willUpdate",value:function(){var t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root!==e){var n=e.options.visualElement;if(n){var r=n.props[H];if(window.MotionHasOptimisedAnimation(r,"transform")){var i=e.options,o=i.layout,a=i.layoutId;window.MotionCancelOptimisedAnimation(r,"transform",j,!(o||a))}var s=e.parent;s&&!s.hasCheckedOptimisedAppear&&t(s)}}}(this),this.root.isUpdating||this.root.startUpdate(),!this.isLayoutDirty){this.isLayoutDirty=!0;for(var e=0;e<this.path.length;e++){var n=this.path[e];n.shouldResetTransform=!0,n.updateScroll("snapshot"),n.options.layoutRoot&&n.willUpdate(!1)}var r=this.options,i=r.layoutId,o=r.layout;if(void 0!==i||o){var a=this.getTransformTemplate();this.prevTransformTemplateValue=a?a(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}}}},{key:"update",value:function(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iC);return}this.isUpdating||this.nodes.forEach(iM),this.isUpdating=!1,this.nodes.forEach(iB),this.nodes.forEach(iF),this.nodes.forEach(iE),this.clearAllSnapshots();var t=_.now();D.delta=J(0,1e3/60,t-D.timestamp),D.timestamp=t,D.isProcessing=!0,F.update.process(D),F.preRender.process(D),F.render.process(D),D.isProcessing=!1}},{key:"didUpdate",value:function(){this.updateScheduled||(this.updateScheduled=!0,rN.read(this.scheduleUpdate))}},{key:"clearAllSnapshots",value:function(){this.nodes.forEach(iR),this.sharedNodes.forEach(iN)}},{key:"scheduleUpdateProjection",value:function(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,j.preRender(this.updateProjection,!1,!0))}},{key:"scheduleCheckAfterUnmount",value:function(){var t=this;j.postRender(function(){t.isLayoutDirty?t.root.didUpdate():t.root.checkUpdateFailed()})}},{key:"updateSnapshot",value:function(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||n9(this.snapshot.measuredBox.x)||n9(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}},{key:"updateLayout",value:function(){if(this.instance&&(this.updateScroll(),this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty)){if(this.resumeFrom&&!this.resumeFrom.instance)for(var t=0;t<this.path.length;t++)this.path[t].updateScroll();var e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ri(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);var n=this.options.visualElement;n&&n.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}}},{key:"updateScroll",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){var n=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:n,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:n}}}},{key:"resetTransform",value:function(){if(o){var t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ip(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,i=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||ru(this.latestValues)||i)&&(o(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}}},{key:"measure",value:function(){var t,e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),iX((t=r).x),iX(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}},{key:"measurePageBox",value:function(){var t,e=this.options.visualElement;if(!e)return ri();var n=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(iG))){var r=this.root.scroll;r&&(rh(n.x,r.offset.x),rh(n.y,r.offset.y))}return n}},{key:"removeElementScroll",value:function(t){var e,n=ri();if(io(n,t),null!=(e=this.scroll)&&e.wasRoot)return n;for(var r=0;r<this.path.length;r++){var i=this.path[r],o=i.scroll,a=i.options;i!==this.root&&o&&a.layoutScroll&&(o.wasRoot&&io(n,t),rh(n.x,o.offset.x),rh(n.y,o.offset.y))}return n}},{key:"applyTransform",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=ri();io(n,t);for(var r=0;r<this.path.length;r++){var i=this.path[r];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rv(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ru(i.latestValues)&&rv(n,i.latestValues)}return ru(this.latestValues)&&rv(n,this.latestValues),n}},{key:"removeTransform",value:function(t){var e=ri();io(e,t);for(var n=0;n<this.path.length;n++){var r=this.path[n];if(r.instance&&ru(r.latestValues)){rs(r.latestValues)&&r.updateSnapshot();var i=ri();io(i,r.measurePageBox()),id(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}}return ru(this.latestValues)&&id(e,this.latestValues),e}},{key:"setTargetDelta",value:function(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}},{key:"setOptions",value:function(t){this.options=ix(ix(ix({},this.options),t),{},{crossfade:void 0===t.crossfade||t.crossfade})}},{key:"clearMeasurements",value:function(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}},{key:"forceRelativeParentToResolveTarget",value:function(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==D.timestamp&&this.relativeParent.resolveTargetDelta(!0)}},{key:"resolveTargetDelta",value:function(){var t,e,n,r,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);var a=!!this.resumingFrom||this!==o;if(i||a&&this.isSharedProjectionDirty||this.isProjectionDirty||null!=(r=this.parent)&&r.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize){var s=this.options,u=s.layout,c=s.layoutId;if(this.layout&&(u||c)){if(this.resolvedRelativeTargetAt=D.timestamp,!this.targetDelta&&!this.relativeTarget){var l=this.getClosestProjectingParent();l&&l.layout&&1!==this.animationProgress?(this.relativeParent=l,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ri(),this.relativeTargetOrigin=ri(),rt(this.relativeTargetOrigin,this.layout.layoutBox,l.layout.layoutBox),io(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=ri(),this.targetWithTransforms=ri()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),t=this.target,e=this.relativeTarget,n=this.relativeParent.target,n6(t.x,e.x,n.x),n6(t.y,e.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):io(this.target,this.layout.layoutBox),rd(this.target,this.targetDelta)):io(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;var f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&1!==this.animationProgress?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ri(),this.relativeTargetOrigin=ri(),rt(this.relativeTargetOrigin,this.target,f.target),io(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}P.value&&iP.calculatedTargetDeltas++}}}}},{key:"getClosestProjectingParent",value:function(){if(!(!this.parent||rs(this.parent.latestValues)||rc(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}},{key:"isProjecting",value:function(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}},{key:"calcProjection",value:function(){var t,e=this.getLead(),n=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||null!=(t=this.parent)&&t.isProjectionDirty)&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===D.timestamp&&(r=!1),!r){var i=this.options,o=i.layout,a=i.layoutId;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),this.layout&&(o||a)){io(this.layoutCorrected,this.layout.layoutBox);var s=this.treeScale.x,u=this.treeScale.y;!function(t,e,n){var r,i,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=n.length;if(a){e.x=e.y=1;for(var s=0;s<a;s++){i=(r=n[s]).projectionDelta;var u=r.options.visualElement;(!u||!u.props.style||"contents"!==u.props.style.display)&&(o&&r.options.layoutScroll&&r.scroll&&r!==r.root&&rv(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),i&&(e.x*=i.x.scale,e.y*=i.y.scale,rd(t,i)),o&&ru(r.latestValues)&&rv(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,n),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=ri());var c=e.target;if(!c){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ia(this.prevProjectionDelta.x,this.projectionDelta.x),ia(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),n4(this.projectionDelta,this.layoutCorrected,c,this.latestValues),this.treeScale.x===s&&this.treeScale.y===u&&ib(this.projectionDelta.x,this.prevProjectionDelta.x)&&ib(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",c)),P.value&&iP.calculatedProjections++}}}},{key:"hide",value:function(){this.isVisible=!1}},{key:"show",value:function(){this.isVisible=!0}},{key:"scheduleRender",value:function(){var t,e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(t=this.options.visualElement)||t.scheduleRender(),e){var n=this.getStack();n&&n.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}},{key:"createProjectionDeltas",value:function(){this.prevProjectionDelta=rn(),this.projectionDelta=rn(),this.projectionDeltaWithTransform=rn()}},{key:"setAnimationOrigin",value:function(t){var e,n=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.snapshot,o=i?i.latestValues:{},a=ix({},this.latestValues),s=rn();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!r;var u=ri(),c=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),f=!l||l.members.length<=1,d=!!(c&&!f&&!0===this.options.crossfade&&!this.path.some(iq));this.animationProgress=0,this.mixTargetDelta=function(r){var i,l,h,p,v,y,m=r/1e3;if(iZ(s.x,t.x,m),iZ(s.y,t.y,m),n.setTargetDelta(s),n.relativeTarget&&n.relativeTargetOrigin&&n.layout&&n.relativeParent&&n.relativeParent.layout){rt(u,n.layout.layoutBox,n.relativeParent.layout.layoutBox),i=n.relativeTarget,l=n.relativeTargetOrigin,h=u,p=m,iW(i.x,l.x,h.x,p),iW(i.y,l.y,h.y,p),e&&(v=n.relativeTarget,y=e,iv(v.x,y.x)&&iv(v.y,y.y))&&(n.isProjectionDirty=!1),e||(e=ri()),io(e,n.relativeTarget)}c&&(n.animationValues=a,function(t,e,n,r,i,o){i?(t.opacity=tY(0,null!=(a=n.opacity)?a:1,it(r)),t.opacityExit=tY(null!=(s=e.opacity)?s:1,0,ie(r))):o&&(t.opacity=tY(null!=(u=e.opacity)?u:1,null!=(c=n.opacity)?c:1,r));for(var a,s,u,c,l=0;l<r8;l++){var f="border".concat(r9[l],"Radius"),d=r7(e,f),h=r7(n,f);(void 0!==d||void 0!==h)&&(d||(d=0),h||(h=0),0===d||0===h||r6(d)===r6(h)?(t[f]=Math.max(tY(r4(d),r4(h),r),0),(tD.test(h)||tD.test(d))&&(t[f]+="%")):t[f]=h)}(e.rotate||n.rotate)&&(t.rotate=tY(e.rotate||0,n.rotate||0,r))}(a,o,n.latestValues,m,d,f)),n.root.scheduleUpdateProjection(),n.scheduleRender(),n.animationProgress=m},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}},{key:"startAnimation",value:function(t){var e,n,r=this;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(!1),null==(n=this.resumingFrom)||null==(n=n.currentAnimation)||n.stop(!1),this.pendingAnimation&&(S(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=j.update(function(){var e,n,i;rY.hasAnimatedSinceResize=!0,te.layout++,r.motionValue||(r.motionValue=N(0)),e=r.motionValue,n=ix(ix({},t),{},{isSync:!0,onUpdate:function(e){r.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:function(){te.layout--},onComplete:function(){te.layout--,t.onComplete&&t.onComplete(),r.completeAnimation()}}),(i=Y(e)?e:N(e)).start(nF("",i,[0,1e3],n)),r.currentAnimation=i.animation,r.resumingFrom&&(r.resumingFrom.currentAnimation=r.currentAnimation),r.pendingAnimation=void 0})}},{key:"completeAnimation",value:function(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);var t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}},{key:"finishAnimation",value:function(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}},{key:"applyTransformsToTarget",value:function(){var t=this.getLead(),e=t.targetWithTransforms,n=t.target,r=t.layout,i=t.latestValues;if(e&&n&&r){if(this!==t&&this.layout&&r&&iH(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||ri();var o=n9(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+o;var a=n9(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+a}io(e,n),rv(e,i),n4(this.projectionDeltaWithTransform,this.layoutCorrected,e,i)}}},{key:"registerSharedNode",value:function(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iO),this.sharedNodes.get(t).add(e);var n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}},{key:"isLead",value:function(){var t=this.getStack();return!t||t.lead===this}},{key:"getLead",value:function(){var t;return this.options.layoutId&&(null==(t=this.getStack())?void 0:t.lead)||this}},{key:"getPrevLead",value:function(){var t;return this.options.layoutId?null==(t=this.getStack())?void 0:t.prevLead:void 0}},{key:"getStack",value:function(){var t=this.options.layoutId;if(t)return this.root.sharedNodes.get(t)}},{key:"promote",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.needsReset,n=t.transition,r=t.preserveFollowOpacity,i=this.getStack();i&&i.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),n&&this.setOptions({transition:n})}},{key:"relegate",value:function(){var t=this.getStack();return!!t&&t.relegate(this)}},{key:"resetSkewAndRotation",value:function(){var t=this.options.visualElement;if(t){var e=!1,n=t.latestValues;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),e){var r={};n.z&&iS("z",t,r,this.animationValues);for(var i=0;i<iw.length;i++)iS("rotate".concat(iw[i]),t,r,this.animationValues),iS("skew".concat(iw[i]),t,r,this.animationValues);for(var o in t.render(),r)t.setStaticValue(o,r[o]),this.animationValues&&(this.animationValues[o]=r[o]);t.scheduleRender()}}}},{key:"getProjectionStyles",value:function(t){if(this.instance&&!this.isSVG){if(!this.isVisible)return ik;var e,n,r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=r5(null==t?void 0:t.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;var o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){var a={};return this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=r5(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ru(this.latestValues)&&(a.transform=i?i({},""):"none",this.hasProjected=!1),a}var s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=function(t,e,n){var r="",i=t.x.translate/e.x,o=t.y.translate/e.y,a=(null==n?void 0:n.z)||0;if((i||o||a)&&(r="translate3d(".concat(i,"px, ").concat(o,"px, ").concat(a,"px) ")),(1!==e.x||1!==e.y)&&(r+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),n){var s=n.transformPerspective,u=n.rotate,c=n.rotateX,l=n.rotateY,f=n.skewX,d=n.skewY;s&&(r="perspective(".concat(s,"px) ").concat(r)),u&&(r+="rotate(".concat(u,"deg) ")),c&&(r+="rotateX(".concat(c,"deg) ")),l&&(r+="rotateY(".concat(l,"deg) ")),f&&(r+="skewX(".concat(f,"deg) ")),d&&(r+="skewY(".concat(d,"deg) "))}var h=t.x.scale*e.x,p=t.y.scale*e.y;return(1!==h||1!==p)&&(r+="scale(".concat(h,", ").concat(p,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(r.transform=i(s,r.transform));var u=this.projectionDelta,c=u.x,l=u.y;for(var f in r.transformOrigin="".concat(100*c.origin,"% ").concat(100*l.origin,"% 0"),o.animationValues?r.opacity=o===this?null!=(e=null!=(n=s.opacity)?n:this.latestValues.opacity)?e:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:r.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,rH)if(void 0!==s[f]){var d=rH[f],h=d.correct,p=d.applyTo,v=d.isCSSVariable,y="none"===r.transform?s[f]:h(s[f],o);if(p)for(var m=p.length,g=0;g<m;g++)r[p[g]]=y;else v?this.options.visualElement.renderState.vars[f]=y:r[f]=y}return this.options.layoutId&&(r.pointerEvents=o===this?r5(null==t?void 0:t.pointerEvents)||"":"none"),r}}},{key:"clearSnapshot",value:function(){this.resumeFrom=this.snapshot=void 0}},{key:"resetTree",value:function(){this.root.nodes.forEach(function(t){var e;return null==(e=t.currentAnimation)?void 0:e.stop(!1)}),this.root.nodes.forEach(iC),this.root.sharedNodes.clear()}}]),a}function iF(t){t.updateLayout()}function iE(t){var e,n=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){var r=t.layout,i=r.layoutBox,o=r.measuredBox,a=t.options.animationType,s=n.source!==t.layout.source;"size"===a?ro(function(t){var e=s?n.measuredBox[t]:n.layoutBox[t],r=n9(e);e.min=i[t].min,e.max=e.min+r}):iH(a,n.layoutBox,i)&&ro(function(e){var r=s?n.measuredBox[e]:n.layoutBox[e],o=n9(i[e]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[e].max=t.relativeTarget[e].min+o)});var u=rn();n4(u,i,n.layoutBox);var c=rn();s?n4(c,t.applyTransform(o,!0),n.measuredBox):n4(c,i,n.layoutBox);var l=!ip(u),f=!1;if(!t.resumeFrom){var d=t.getClosestProjectingParent();if(d&&!d.resumeFrom){var h=d.snapshot,p=d.layout;if(h&&p){var v=ri();rt(v,n.layoutBox,h.layoutBox);var y=ri();rt(y,i,p.layoutBox),im(v,y)||(f=!0),d.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=v,t.relativeParent=d)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:n,delta:c,layoutDelta:u,hasLayoutChanged:l,hasRelativeLayoutChanged:f})}else if(t.isLead()){var m=t.options.onExitComplete;m&&m()}t.options.transition=void 0}function iT(t){P.value&&iP.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iV(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iR(t){t.clearSnapshot()}function iC(t){t.clearMeasurements()}function iM(t){t.isLayoutDirty=!1}function iB(t){var e=t.options.visualElement;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i_(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function iL(t){t.resolveTargetDelta()}function iI(t){t.calcProjection()}function iU(t){t.resetSkewAndRotation()}function iN(t){t.removeLeadSnapshot()}function iZ(t,e,n){t.translate=tY(e.translate,0,n),t.scale=tY(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function iW(t,e,n,r){t.min=tY(e.min,n.min,r),t.max=tY(e.max,n.max,r)}function iq(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}var iz={duration:.45,ease:[.4,0,.1,1]},iY=function(t){return"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t)},iK=iY("applewebkit/")&&!iY("chrome/")?Math.round:O;function iX(t){t.min=iK(t.min),t.max=iK(t.max)}function iH(t,e,n){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ig(e)-ig(n)))}function iG(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}var i$=iD({attachResizeListener:function(t,e){return n0(t,"resize",e)},measureScroll:function(){return{x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}},checkIsScrollRoot:function(){return!0}}),iJ={current:void 0},iQ=iD({measureScroll:function(t){return{x:t.scrollLeft,y:t.scrollTop}},defaultParent:function(){if(!iJ.current){var t=new i$({});t.mount(window),t.setOptions({layoutScroll:!0}),iJ.current=t}return iJ.current},resetTransform:function(t,e){t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:function(t){return"fixed"===window.getComputedStyle(t).position}});function i0(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function i1(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i0(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i0(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function i2(t,e){var n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){var r=document.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}(t),r=new AbortController;return[n,i1(i1({passive:!0},e),{},{signal:r.signal}),function(){return r.abort()}]}function i3(t){return!("touch"===t.pointerType||nQ.x||nQ.y)}function i5(t,e,n){var r=t.props;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===n);var i=r["onHover"+n];i&&j.postRender(function(){return i(e,n2(e))})}var i9=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){return(0,s.A)(this,r),n.apply(this,arguments)}return(0,u.A)(r,[{key:"mount",value:function(){var t=this,e=this.node.current;e&&(this.unmount=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i2(t,n),i=(0,v.A)(r,3),o=i[0],a=i[1],s=i[2],u=function(t){if(i3(t)){var n=t.target,r=e(n,t);"function"==typeof r&&n&&n.addEventListener("pointerleave",function t(e){i3(e)&&(r(e),n.removeEventListener("pointerleave",t))},a)}};return o.forEach(function(t){t.addEventListener("pointerenter",u,a)}),s}(e,function(e,n){return i5(t.node,n,"Start"),function(e){return i5(t.node,e,"End")}}))}},{key:"unmount",value:function(){}}]),r}(nH),i8=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.isActive=!1,t}return(0,u.A)(r,[{key:"onFocus",value:function(){var t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}},{key:"onBlur",value:function(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}},{key:"mount",value:function(){var t=this;this.unmount=$(n0(this.node.current,"focus",function(){return t.onFocus()}),n0(this.node.current,"blur",function(){return t.onBlur()}))}},{key:"unmount",value:function(){}}]),r}(nH),i4=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),i6=new WeakSet;function i7(t){return function(e){"Enter"===e.key&&t(e)}}function ot(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}var oe=function(t,e){var n=t.currentTarget;if(n){var r=i7(function(){if(!i6.has(n)){ot(n,"down");var t=i7(function(){ot(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",function(){return ot(n,"cancel")},e)}});n.addEventListener("keydown",r,e),n.addEventListener("blur",function(){return n.removeEventListener("keydown",r)},e)}};function on(t){return n1(t)&&!(nQ.x||nQ.y)}function or(t,e,n){var r=t.props;if(!(t.current instanceof HTMLButtonElement)||!t.current.disabled){t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===n);var i=r["onTap"+("End"===n?"":n)];i&&j.postRender(function(){return i(e,n2(e))})}}var oi=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){return(0,s.A)(this,r),n.apply(this,arguments)}return(0,u.A)(r,[{key:"mount",value:function(){var t=this,e=this.node.current;e&&(this.unmount=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i2(t,n),i=(0,v.A)(r,3),o=i[0],a=i[1],s=i[2],u=function(t){var r=t.currentTarget;if(on(t)){i6.add(r);var i=e(r,t),o=function(t,e){window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",u),i6.has(r)&&i6.delete(r),on(t)&&"function"==typeof i&&i(t,{success:e})},s=function(t){o(t,r===window||r===document||n.useGlobalTarget||function t(e,n){return!!n&&(e===n||t(e,n.parentElement))}(r,t.target))},u=function(t){o(t,!1)};window.addEventListener("pointerup",s,a),window.addEventListener("pointercancel",u,a)}};return o.forEach(function(t){((n.useGlobalTarget?window:t).addEventListener("pointerdown",u,a),np(t))&&(t.addEventListener("focus",function(t){return oe(t,a)}),i4.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(e,function(e,n){return or(t.node,n,"Start"),function(e,n){var r=n.success;return or(t.node,e,r?"End":"Cancel")}},{useGlobalTarget:this.node.props.globalTapTarget}))}},{key:"unmount",value:function(){}}]),r}(nH),oo=["root"];function oa(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}var os=new WeakMap,ou=new WeakMap,oc=function(t){var e=os.get(t.target);e&&e(t)},ol=function(t){t.forEach(oc)},of={some:0,all:1},od=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.hasEnteredView=!1,t.isInView=!1,t}return(0,u.A)(r,[{key:"startObserver",value:function(){var t,e,n,r,i,o,s,u,c=this;this.unmount();var l=this.node.getProps().viewport,f=void 0===l?{}:l,d=f.root,p=f.margin,v=f.amount,y=void 0===v?"some":v,m=f.once,g={root:d?d.current:void 0,rootMargin:p,threshold:"number"==typeof y?y:of[y]};return t=this.node.current,e=function(t){var e=t.isIntersecting;if(c.isInView!==e){if(c.isInView=e,m&&!e&&c.hasEnteredView)return;e&&(c.hasEnteredView=!0),c.node.animationState&&c.node.animationState.setActive("whileInView",e);var n=c.node.getProps(),r=n.onViewportEnter,i=n.onViewportLeave,o=e?r:i;o&&o(t)}},n=g.root,r=(0,h.A)(g,oo),i=n||document,ou.has(i)||ou.set(i,{}),(o=ou.get(i))[s=JSON.stringify(r)]||(o[s]=new IntersectionObserver(ol,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oa(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oa(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({root:n},r))),u=o[s],os.set(t,e),u.observe(t),function(){os.delete(t),u.unobserve(t)}}},{key:"mount",value:function(){this.startObserver()}},{key:"update",value:function(){if("undefined"!=typeof IntersectionObserver){var t=this.node;["amount","margin","root"].some(function(t){var e=t.viewport,n=void 0===e?{}:e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.viewport,o=void 0===i?{}:i;return function(t){return n[t]!==o[t]}}(t.props,t.prevProps))&&this.startObserver()}}},{key:"unmount",value:function(){}}]),r}(nH),oh=(0,rZ.createContext)({strict:!1}),op=(0,rZ.createContext)({transformPagePoint:function(t){return t},isStatic:!1,reducedMotion:"never"}),ov=(0,rZ.createContext)({});function oy(t){return d(t.animate)||nU.some(function(e){return nL(t[e])})}function om(t){return!!(oy(t)||t.variants)}function og(t){return Array.isArray(t)?t.join(" "):t}var ob={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},oO={},oA=function(t){oO[t]={isEnabled:function(e){return ob[t].some(function(t){return!!e[t]})}}};for(var ox in ob)oA(ox);function oP(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ow(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oP(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oP(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var ok=Symbol.for("motionComponentSymbol"),oj=rZ.useLayoutEffect;function oS(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function oD(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oS(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oS(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function oF(t,e){var n=e.layout,r=e.layoutId;return T.has(t)||t.startsWith("origin")||(n||void 0!==r)&&(!!rH[t]||"opacity"===t)}var oE=function(t,e){return e&&"number"==typeof t?e.transform(t):t};function oT(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function oV(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oT(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oT(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var oR=oV(oV({},tf),{},{transform:Math.round});function oC(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function oM(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oC(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oC(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var oB=oM(oM({borderWidth:tF,borderTopWidth:tF,borderRightWidth:tF,borderBottomWidth:tF,borderLeftWidth:tF,borderRadius:tF,radius:tF,borderTopLeftRadius:tF,borderTopRightRadius:tF,borderBottomRightRadius:tF,borderBottomLeftRadius:tF,width:tF,maxWidth:tF,height:tF,maxHeight:tF,top:tF,right:tF,bottom:tF,left:tF,padding:tF,paddingTop:tF,paddingRight:tF,paddingBottom:tF,paddingLeft:tF,margin:tF,marginTop:tF,marginRight:tF,marginBottom:tF,marginLeft:tF,backgroundPositionX:tF,backgroundPositionY:tF},{rotate:tS,rotateX:tS,rotateY:tS,rotateZ:tS,scale:th,scaleX:th,scaleY:th,scaleZ:th,skew:tS,skewX:tS,skewY:tS,distance:tF,translateX:tF,translateY:tF,translateZ:tF,x:tF,y:tF,z:tF,perspective:tF,transformPerspective:tF,opacity:td,originX:tV,originY:tV,originZ:tF}),{},{zIndex:oR,fillOpacity:td,strokeOpacity:td,numOctaves:oR}),o_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},oL=E.length;function oI(t,e,n){var r=t.style,i=t.vars,o=t.transformOrigin,a=!1,s=!1;for(var u in e){var c=e[u];if(T.has(u)){a=!0;continue}if(to(u)){i[u]=c;continue}var l=oE(c,oB[u]);u.startsWith("origin")?(s=!0,o[u]=l):r[u]=l}if(!e.transform&&(a||n?r.transform=function(t,e,n){for(var r="",i=!0,o=0;o<oL;o++){var a=E[o],s=t[a];if(void 0!==s){var u=!0;if(!(u="number"==typeof s?s===+!!a.startsWith("scale"):0===parseFloat(s))||n){var c=oE(s,oB[a]);if(!u){i=!1;var l=o_[a]||a;r+="".concat(l,"(").concat(c,") ")}n&&(e[a]=c)}}}return r=r.trim(),n?r=n(e,i?"":r):i&&(r="none"),r}(e,t.transform,n):r.transform&&(r.transform="none")),s){var f=o.originX,d=o.originY,h=o.originZ;r.transformOrigin="".concat(void 0===f?"50%":f," ").concat(void 0===d?"50%":d," ").concat(void 0===h?0:h)}}var oU=function(){return{style:{},transform:{},transformOrigin:{},vars:{}}};function oN(t,e,n){for(var r in e)Y(e[r])||oF(r,n)||(t[r]=e[r])}var oZ={offset:"stroke-dashoffset",array:"stroke-dasharray"},oW={offset:"strokeDashoffset",array:"strokeDasharray"},oq=["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"];function oz(t,e,n,r,i){var o,a,s=e.attrX,u=e.attrY,c=e.attrScale,l=e.pathLength,f=e.pathSpacing,d=e.pathOffset;if(oI(t,(0,h.A)(e,oq),r),n){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};var p=t.attrs,v=t.style;p.transform&&(v.transform=p.transform,delete p.transform),(v.transform||p.transformOrigin)&&(v.transformOrigin=null!=(o=p.transformOrigin)?o:"50% 50%",delete p.transformOrigin),v.transform&&(v.transformBox=null!=(a=null==i?void 0:i.transformBox)?a:"fill-box",delete p.transformBox),void 0!==s&&(p.x=s),void 0!==u&&(p.y=u),void 0!==c&&(p.scale=c),void 0!==l&&function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;var o=i?oZ:oW;t[o.offset]=tF.transform(-r);var a=tF.transform(e),s=tF.transform(n);t[o.array]="".concat(a," ").concat(s)}(p,l,void 0===f?1:f,void 0===d?0:d,!1)}function oY(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function oK(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oY(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oY(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var oX=function(){return oK(oK({},oU()),{},{attrs:{}})},oH=function(t){return"string"==typeof t&&"svg"===t.toLowerCase()};function oG(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function o$(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oG(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oG(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var oJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function oQ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||oJ.has(t)}var o0=function(t){return!oQ(t)};try{!function(t){t&&(o0=function(e){return e.startsWith("on")?!oQ(e):t(e)})}(require("@emotion/is-prop-valid").default)}catch(t){}var o1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function o2(t){if("string"!=typeof t||t.includes("-"));else if(o1.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}function o3(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function o5(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o3(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o3(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var o9=["transitionEnd","transition"],o8=function(t){return function(e,n){var r,i=(0,rZ.useContext)(ov),o=(0,rZ.useContext)(rW),a=function(){var n,r;return n=t.scrapeMotionValuesFromProps,r=t.createRenderState,{latestValues:function(t,e,n,r){var i={},o=r(t,{});for(var a in o)i[a]=r5(o[a]);var s=t.initial,u=t.animate,c=oy(t),l=om(t);e&&l&&!c&&!1!==t.inherit&&(void 0===s&&(s=e.initial),void 0===u&&(u=e.animate));var f=!!n&&!1===n.initial,p=(f=f||!1===s)?u:s;if(p&&"boolean"!=typeof p&&!d(p))for(var v=Array.isArray(p)?p:[p],y=0;y<v.length;y++){var g=m(t,v[y]);if(g){var b=g.transitionEnd,O=(g.transition,(0,h.A)(g,o9));for(var A in O){var x=O[A];if(Array.isArray(x)){var P=f?x.length-1:0;x=x[P]}null!==x&&(i[A]=x)}for(var w in b)i[w]=b[w]}}return i}(e,i,o,n),renderState:r()}};return n?a():(null===(r=(0,rZ.useRef)(null)).current&&(r.current=a()),r.current)}};function o4(t,e,n){var r,i=t.style,o={};for(var a in i)(Y(i[a])||e.style&&Y(e.style[a])||oF(a,t)||(null==n||null==(r=n.getValue(a))?void 0:r.liveStyle)!==void 0)&&(o[a]=i[a]);return o}var o6={useVisualState:o8({scrapeMotionValuesFromProps:o4,createRenderState:oU})};function o7(t,e,n){var r=o4(t,e,n);for(var i in t)(Y(t[i])||Y(e[i]))&&(r[-1!==E.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}var at={useVisualState:o8({scrapeMotionValuesFromProps:o7,createRenderState:oX})};function ae(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function an(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ae(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function ar(){return(ar="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=(0,f.A)(t)););return t}(t,e);if(r){var i=Object.getOwnPropertyDescriptor(r,e);return i.get?i.get.call(arguments.length<3?t:n):i.value}}).apply(this,arguments)}var ai=function(t){return function(e){return e.test(t)}},ao=[tf,tF,tD,tS,tT,tE,{test:function(t){return"auto"===t},parse:function(t){return t}}],aa=function(t){return ao.find(ai(t))},as=function(t){return/^\x2D?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)$/.test(t)},au=/^var\(\x2D\x2D(?:([\x2D0-9A-Z_a-z]+)|([\x2D0-9A-Z_a-z]+), ?([ #%\(\),-\.0-9A-Za-z]+))\)/,ac=function(t){return/^0(?:[\0-\x08\x0E-\x1F!-\x2D\/-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+$/.test(t)};function al(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function af(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?al(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):al(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var ad=new Set(["brightness","contrast","saturate","opacity"]);function ah(t){var e=t.slice(0,-1).split("("),n=(0,v.A)(e,2),r=n[0],i=n[1];if("drop-shadow"===r)return t;var o=i.match(tv)||[],a=(0,v.A)(o,1)[0];if(!a)return t;var s=i.replace(a,""),u=+!!ad.has(r);return a!==i&&(u*=100),r+"("+u+s+")"}var ap=/\b([\x2Da-z]*)\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\)/g,av=af(af({},tW),{},{getAnimatableNone:function(t){var e=t.match(ap);return e?e.map(ah).join(" "):t}});function ay(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function am(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ay(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ay(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var ag=am(am({},oB),{},{color:tC,backgroundColor:tC,outlineColor:tC,fill:tC,stroke:tC,borderColor:tC,borderTopColor:tC,borderRightColor:tC,borderBottomColor:tC,borderLeftColor:tC,filter:av,WebkitFilter:av}),ab=function(t){return ag[t]};function aO(t,e){var n=ab(t);return n!==av&&(n=tW),n.getAnimatableNone?n.getAnimatableNone(e):void 0}function aA(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var ax=new Set(["auto","none","0"]),aP=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(t,e,i,o,a){return(0,s.A)(this,r),n.call(this,t,e,i,o,a,!0)}return(0,u.A)(r,[{key:"readKeyframes",value:function(){var t=this.unresolvedKeyframes,e=this.element,n=this.name;if(e&&e.current){ar((0,f.A)(r.prototype),"readKeyframes",this).call(this);for(var i=0;i<t.length;i++){var o=t[i];if("string"==typeof o&&ts(o=o.trim())){var a=function t(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;tr(r<=4,'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.'));var i=function(t){var e=au.exec(t);if(!e)return[,];var n=(0,v.A)(e,4),r=n[1],i=n[2],o=n[3];return["--".concat(null!=r?r:i),o]}(e),o=(0,v.A)(i,2),a=o[0],s=o[1];if(a){var u=window.getComputedStyle(n).getPropertyValue(a);if(u){var c=u.trim();return as(c)?parseFloat(c):c}return ts(s)?t(s,n,r+1):s}}(o,e.current);void 0!==a&&(t[i]=a),i===t.length-1&&(this.finalKeyframe=o)}}if(this.resolveNoneKeyframes(),V.has(n)&&2===t.length){var s=(0,v.A)(t,2),u=s[0],c=s[1],l=aa(u),d=aa(c);if(l!==d)if(e$(l)&&e$(d))for(var h=0;h<t.length;h++){var p=t[h];"string"==typeof p&&(t[h]=parseFloat(p))}else e0[n]&&(this.needsMeasurement=!0)}}}},{key:"resolveNoneKeyframes",value:function(){for(var t,e=this.unresolvedKeyframes,n=this.name,r=[],i=0;i<e.length;i++)(null===e[i]||("number"==typeof(t=e[i])?0===t:null===t||"none"===t||"0"===t||ac(t)))&&r.push(i);r.length&&function(t,e,n){for(var r=0,i=void 0;r<t.length&&!i;){var o=t[r];"string"==typeof o&&!ax.has(o)&&tI(o).values.length&&(i=t[r]),r++}if(i&&n){var a,s=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return aA(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return aA(t,e)}}(t))){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}(e);try{for(s.s();!(a=s.n()).done;)t[a.value]=aO(n,i)}catch(t){s.e(t)}finally{s.f()}}}(e,r,n)}},{key:"measureInitialState",value:function(){var t=this.element,e=this.unresolvedKeyframes,n=this.name;if(t&&t.current){"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=e0[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;var r=e[e.length-1];void 0!==r&&t.getValue(n,r).jump(r,!1)}}},{key:"measureEndState",value:function(){var t,e=this.element,n=this.name,r=this.unresolvedKeyframes;if(e&&e.current){var i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);var o=r.length-1,a=r[o];r[o]=e0[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),null!=(t=this.removedTransforms)&&t.length&&this.removedTransforms.forEach(function(t){var n=(0,v.A)(t,2),r=n[0],i=n[1];e.getValue(r).set(i)}),this.resolveNoneKeyframes()}}}]),r}(e4),aw=[].concat((0,p.A)(ao),[tC,tW]),ak={current:null},aj={current:!1},aS=new WeakMap,aD=["willChange"];function aF(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function aE(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?aF(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):aF(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var aT=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],aV=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.KeyframeResolver=aP,t}return(0,u.A)(r,[{key:"sortInstanceNodePosition",value:function(t,e){return 2&t.compareDocumentPosition(e)?1:-1}},{key:"getBaseTargetFromProps",value:function(t,e){return t.style?t.style[e]:void 0}},{key:"removeValueFromRenderState",value:function(t,e){var n=e.vars,r=e.style;delete n[t],delete r[t]}},{key:"handleChildMotionValue",value:function(){var t=this;this.childSubscription&&(this.childSubscription(),delete this.childSubscription);var e=this.props.children;Y(e)&&(this.childSubscription=e.on("change",function(e){t.current&&(t.current.textContent="".concat(e))}))}}]),r}(function(){function t(e){var n=this,r=e.parent,i=e.props,o=e.presenceContext,a=e.reducedMotionConfig,u=e.blockInitialAnimation,c=e.visualState,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,s.A)(this,t),this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=e4,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=function(){return n.notify("Update",n.latestValues)},this.render=function(){n.current&&(n.triggerBuild(),n.renderInstance(n.current,n.renderState,n.props.style,n.projection))},this.renderScheduledAt=0,this.scheduleRender=function(){var t=_.now();n.renderScheduledAt<t&&(n.renderScheduledAt=t,j.render(n.render,!1,!0))};var f=c.latestValues,d=c.renderState;this.latestValues=f,this.baseTarget=aE({},f),this.initialValues=i.initial?aE({},f):{},this.renderState=d,this.parent=r,this.props=i,this.presenceContext=o,this.depth=r?r.depth+1:0,this.reducedMotionConfig=a,this.options=l,this.blockInitialAnimation=!!u,this.isControllingVariants=oy(i),this.isVariantNode=om(i),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(r&&r.current);var p=this.scrapeMotionValuesFromProps(i,{},this),v=(p.willChange,(0,h.A)(p,aD));for(var y in v){var m=v[y];void 0!==f[y]&&Y(m)&&m.set(f[y],!1)}}return(0,u.A)(t,[{key:"scrapeMotionValuesFromProps",value:function(t,e,n){return{}}},{key:"mount",value:function(t){var e=this;this.current=t,aS.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(function(t,n){return e.bindToMotionValue(n,t)}),aj.current||function(){if(aj.current=!0,window.matchMedia){var t=window.matchMedia("(prefers-reduced-motion)"),e=function(){return ak.current=t.matches};t.addListener(e),e()}else ak.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ak.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}},{key:"unmount",value:function(){for(var t in this.projection&&this.projection.unmount(),S(this.notifyUpdate),S(this.render),this.valueSubscriptions.forEach(function(t){return t()}),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(var e in this.features){var n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}},{key:"bindToMotionValue",value:function(t,e){var n,r=this;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();var i=T.has(t);i&&this.onBindTransform&&this.onBindTransform();var o=e.on("change",function(e){r.latestValues[t]=e,r.props.onUpdate&&j.preRender(r.notifyUpdate),i&&r.projection&&(r.projection.isTransformDirty=!0)}),a=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,function(){o(),a(),n&&n(),e.owner&&e.stop()})}},{key:"sortNodePosition",value:function(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}},{key:"updateFeatures",value:function(){var t="animation";for(t in oO){var e=oO[t];if(e){var n=e.isEnabled,r=e.Feature;if(!this.features[t]&&r&&n(this.props)&&(this.features[t]=new r(this)),this.features[t]){var i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}}},{key:"triggerBuild",value:function(){this.build(this.renderState,this.latestValues,this.props)}},{key:"measureViewportBox",value:function(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ri()}},{key:"getStaticValue",value:function(t){return this.latestValues[t]}},{key:"setStaticValue",value:function(t,e){this.latestValues[t]=e}},{key:"update",value:function(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(var n=0;n<aT.length;n++){var r=aT[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);var i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,n){for(var r in e){var i=e[r],o=n[r];if(Y(i))t.addValue(r,i);else if(Y(o))t.addValue(r,N(i,{owner:t}));else if(o!==i)if(t.hasValue(r)){var a=t.getValue(r);!0===a.liveStyle?a.jump(i):a.hasAnimated||a.set(i)}else{var s=t.getStaticValue(r);t.addValue(r,N(void 0!==s?s:i,{owner:t}))}}for(var u in n)void 0===e[u]&&t.removeValue(u);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}},{key:"getProps",value:function(){return this.props}},{key:"getVariant",value:function(t){return this.props.variants?this.props.variants[t]:void 0}},{key:"getDefaultTransition",value:function(){return this.props.transition}},{key:"getTransformPagePoint",value:function(){return this.props.transformPagePoint}},{key:"getClosestVariantNode",value:function(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}},{key:"addVariantChild",value:function(t){var e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),function(){return e.variantChildren.delete(t)}}},{key:"addValue",value:function(t,e){var n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}},{key:"removeValue",value:function(t){this.values.delete(t);var e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}},{key:"hasValue",value:function(t){return this.values.has(t)}},{key:"getValue",value:function(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];var n=this.values.get(t);return void 0===n&&void 0!==e&&(n=N(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}},{key:"readValue",value:function(t,e){var n,r,i=void 0===this.latestValues[t]&&this.current?null!=(r=this.getBaseTargetFromProps(this.props,t))?r:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(as(i)||ac(i))?i=parseFloat(i):(n=i,!aw.find(ai(n))&&tW.test(e)&&(i=aO(t,e))),this.setBaseTarget(t,Y(i)?i.get():i)),Y(i)?i.get():i}},{key:"setBaseTarget",value:function(t,e){this.baseTarget[t]=e}},{key:"getBaseTarget",value:function(t){var e=this.props.initial;if("string"==typeof e||"object"==typeof e){var n,r,i=m(this.props,e,null==(r=this.presenceContext)?void 0:r.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;var o=this.getBaseTargetFromProps(this.props,t);return void 0===o||Y(o)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:o}},{key:"on",value:function(t,e){return this.events[t]||(this.events[t]=new M),this.events[t].add(e)}},{key:"notify",value:function(t){if(this.events[t]){for(var e,n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];(e=this.events[t]).notify.apply(e,r)}}}]),t}());function aR(t,e,n,r){var i=e.style,o=e.vars;for(var a in Object.assign(t.style,i,r&&r.getProjectionStyles(n)),o)t.style.setProperty(a,o[a])}var aC=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.type="html",t.renderInstance=aR,t}return(0,u.A)(r,[{key:"readValueFromInstance",value:function(t,e){if(T.has(e)){var n;return null!=(n=this.projection)&&n.isProjecting?eK(e):eH(t,e)}var r=window.getComputedStyle(t),i=(to(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}},{key:"measureInstanceViewportBox",value:function(t,e){return ry(t,e.transformPagePoint)}},{key:"build",value:function(t,e,n){oI(t,e,n.transformTemplate)}},{key:"scrapeMotionValuesFromProps",value:function(t,e,n){return o4(t,e,n)}}]),r}(aV),aM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),aB=function(t){(0,c.A)(r,t);var e,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=(0,f.A)(r);return t=e?Reflect.construct(n,arguments,(0,f.A)(this).constructor):n.apply(this,arguments),(0,l.A)(this,t)});function r(){var t;return(0,s.A)(this,r),t=n.apply(this,arguments),t.type="svg",t.isSVGTag=!1,t.measureInstanceViewportBox=ri,t}return(0,u.A)(r,[{key:"getBaseTargetFromProps",value:function(t,e){return t[e]}},{key:"readValueFromInstance",value:function(t,e){if(T.has(e)){var n=ab(e);return n&&n.default||0}return e=aM.has(e)?e:X(e),t.getAttribute(e)}},{key:"scrapeMotionValuesFromProps",value:function(t,e,n){return o7(t,e,n)}},{key:"build",value:function(t,e,n){oz(t,e,this.isSVGTag,n.transformTemplate,n.style)}},{key:"renderInstance",value:function(t,e,n,r){for(var i in aR(t,e,void 0,r),e.attrs)t.setAttribute(aM.has(i)?i:X(i),e.attrs[i])}},{key:"mount",value:function(t){this.isSVGTag=oH(t.tagName),ar((0,f.A)(r.prototype),"mount",this).call(this,t)}}]),r}(aV);function a_(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function aL(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?a_(Object(n),!0).forEach(function(e){(0,a.A)(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):a_(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var aI=function(t){if("undefined"==typeof Proxy)return t;var e=new Map;return new Proxy(function(){return t.apply(void 0,arguments)},{get:function(n,r){return"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))}})}((r=aL(aL(aL(aL({},{animation:{Feature:nG},exit:{Feature:nJ}}),{inView:{Feature:od},tap:{Feature:oi},focus:{Feature:i8},hover:{Feature:i9}}),{pan:{Feature:rL},drag:{Feature:rB,ProjectionNode:iQ,MeasureLayout:rQ}}),{layout:{ProjectionNode:iQ,MeasureLayout:rQ}}),i=function(t,e){return o2(t)?new aB(e):new aC(e,{allowProjection:t!==rZ.Fragment})},function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1},n=e.forwardMotionProps;return function(t){var e,n,r=t.preloadedFeatures,i=t.createVisualElement,o=t.useRender,a=t.useVisualState,s=t.Component;function u(t,e){var n,r,u,c,l=oD(oD(oD({},(0,rZ.useContext)(op)),t),{},{layoutId:(n=t.layoutId,(r=(0,rZ.useContext)(rq).id)&&void 0!==n?r+"-"+n:n)}),f=l.isStatic,d=(D=(S=function(t,e){if(oy(t)){var n=t.initial,r=t.animate;return{initial:!1===n||nL(n)?n:void 0,animate:nL(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,rZ.useContext)(ov))).initial,F=S.animate,(0,rZ.useMemo)(function(){return{initial:D,animate:F}},[og(D),og(F)])),h=a(t,f);if(!f){k=0,j=0,(0,rZ.useContext)(oh).strict;var p,v,y,m,g,b,O,A,x,P,w,k,j,S,D,F,E,T,V,R,C,M,B,_,L,I,U,N,Z,W,q,z,Y=function(t){var e=oO.drag,n=oO.layout;if(!e&&!n)return{};var r=oD(oD({},e),n);return{MeasureLayout:null!=e&&e.isEnabled(t)||null!=n&&n.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(l);c=Y.MeasureLayout,E=i,T=Y.ProjectionNode,B=(0,rZ.useContext)(ov).visualElement,_=(0,rZ.useContext)(oh),L=(0,rZ.useContext)(rW),I=(0,rZ.useContext)(op).reducedMotion,U=(0,rZ.useRef)(null),E=E||_.renderer,!U.current&&E&&(U.current=E(s,{visualState:h,parent:B,props:l,presenceContext:L,blockInitialAnimation:!!L&&!1===L.initial,reducedMotionConfig:I})),N=U.current,Z=(0,rZ.useContext)(rz),N&&!N.projection&&T&&("html"===N.type||"svg"===N.type)&&(p=U.current,v=l,y=T,m=Z,g=v.layoutId,b=v.layout,O=v.drag,A=v.dragConstraints,x=v.layoutScroll,P=v.layoutRoot,w=v.layoutCrossfade,p.projection=new y(p.latestValues,v["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(p.parent)),p.projection.setOptions({layoutId:g,layout:b,alwaysMeasureLayout:!!O||A&&rg(A),visualElement:p,animationType:"string"==typeof b?b:"both",initialPromotionConfig:m,crossfade:w,layoutScroll:x,layoutRoot:P})),W=(0,rZ.useRef)(!1),(0,rZ.useInsertionEffect)(function(){N&&W.current&&N.update(l,L)}),q=l[H],z=(0,rZ.useRef)(!!q&&!(null!=(V=(R=window).MotionHandoffIsComplete)&&V.call(R,q))&&(null==(C=(M=window).MotionHasOptimisedAnimation)?void 0:C.call(M,q))),oj(function(){N&&(W.current=!0,window.MotionIsMounted=!0,N.updateFeatures(),rN.render(N.render),z.current&&N.animationState&&N.animationState.animateChanges())}),(0,rZ.useEffect)(function(){N&&(!z.current&&N.animationState&&N.animationState.animateChanges(),z.current&&(queueMicrotask(function(){var t,e;null==(t=(e=window).MotionHandoffMarkAsComplete)||t.call(e,q)}),z.current=!1))}),d.visualElement=N}return(0,rI.jsxs)(ov.Provider,{value:d,children:[c&&d.visualElement?(0,rI.jsx)(c,oD({visualElement:d.visualElement},l)):null,o(s,t,(u=d.visualElement,(0,rZ.useCallback)(function(t){t&&h.onMount&&h.onMount(t),u&&(t?u.mount(t):u.unmount()),e&&("function"==typeof e?e(t):rg(e)&&(e.current=t))},[u])),h,f,d.visualElement)]})}r&&function(t){for(var e in t)oO[e]=ow(ow({},oO[e]),t[e])}(r),u.displayName="motion.".concat("string"==typeof s?s:"create(".concat(null!=(e=null!=(n=s.displayName)?n:s.name)?e:"",")"));var c=(0,rZ.forwardRef)(u);return c[ok]=s,c}(an(an({},o2(t)?at:o6),{},{preloadedFeatures:r,useRender:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(e,n,r,i,o){var a=i.latestValues,s=(o2(e)?function(t,e,n,r){var i=(0,rZ.useMemo)(function(){var n=oX();return oz(n,e,oH(r),t.transformTemplate,t.style),o$(o$({},n.attrs),{},{style:o$({},n.style)})},[e]);if(t.style){var o={};oN(o,t.style,t),i.style=o$(o$({},o),i.style)}return i}:function(t,e){var n,r,i={},o=(oN(n={},t.style||{},t),Object.assign(n,(r=t.transformTemplate,(0,rZ.useMemo)(function(){var t=oU();return oI(t,e,r),Object.assign({},t.vars,t.style)},[e]))),n);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=o,i})(n,a,o,e),u=function(t,e,n){var r={};for(var i in t)("values"!==i||"object"!=typeof t.values)&&(o0(i)||!0===n&&oQ(i)||!e&&!oQ(i)||t.draggable&&i.startsWith("onDrag"))&&(r[i]=t[i]);return r}(n,"string"==typeof e,t),c=e!==rZ.Fragment?o5(o5(o5({},u),s),{},{ref:r}):{},l=n.children,f=(0,rZ.useMemo)(function(){return Y(l)?l.get():l},[l]);return(0,rZ.createElement)(e,o5(o5({},c),{},{children:f}))}}(n),createVisualElement:i,Component:t}))}))}}]);
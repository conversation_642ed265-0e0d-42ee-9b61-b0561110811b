"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5601],{15601:(e,t,r)=>{var n=r(43081),o=r(83095),u=r(26097),c=r(95835),a=r(32249),f=r(37936),i=r(83922),p=r(56191);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return j}});var l=r(64252),y=r(37876),b=l._(r(14232)),d=r(15615);function O(e){return h.apply(this,arguments)}function h(){return(h=p(n.mark(function e(t){var r,o,u;return n.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.Component,o=t.ctx,e.next=3,(0,d.loadGetInitialProps)(r,o);case 3:return u=e.sent,e.abrupt("return",{pageProps:u});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}var j=function(e){a(n,e);var t,r=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=i(n);return e=t?Reflect.construct(r,arguments,i(this).constructor):r.apply(this,arguments),f(this,e)});function n(){return u(this,n),r.apply(this,arguments)}return c(n,[{key:"render",value:function(){var e=this.props,t=e.Component,r=e.pageProps;return(0,y.jsx)(t,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){o(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},r))}}]),n}(b.default.Component);j.origGetInitialProps=O,j.getInitialProps=O,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);
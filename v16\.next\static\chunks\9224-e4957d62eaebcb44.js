"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9224],{2775:(e,r,t)=>{t.d(r,{Iv:()=>E,Og:()=>y,Q1:()=>b,QU:()=>F,_W:()=>O,_p:()=>_,as:()=>M,kO:()=>w,oE:()=>m,qJ:()=>p,xq:()=>C,yK:()=>v,yf:()=>D});var a=t(10631),n=t(37711),o=t(33311),s=t(28295),c=t.n(s),i=t(55564),u=t(66430);function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function d(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){(0,n.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function p(e,r){return f.apply(this,arguments)}function f(){return(f=(0,o.A)(c().mark(function e(r,t){var a,n,o,s,l;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.iF)();case 3:if(n=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",null);case 8:return e.next=10,i.N.from("colecciones_flashcards").insert([{titulo:r,descripcion:t,user_id:n.id}]).select();case 10:if(s=(o=e.sent).data,!(l=o.error)){e.next=16;break}return console.error("Error al crear colecci\xf3n de flashcards:",l),e.abrupt("return",null);case 16:return e.abrupt("return",(null==s||null==(a=s[0])?void 0:a.id)||null);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error al crear colecci\xf3n de flashcards:",e.t0),e.abrupt("return",null);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function m(){return h.apply(this,arguments)}function h(){return(h=(0,o.A)(c().mark(function e(){var r,t,a,n,s,l,p;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.iF)();case 3:if(t=(r=e.sent).user,!(a=r.error)){e.next=9;break}return console.error("Error al obtener usuario:",a),e.abrupt("return",[]);case 9:if(t){e.next=12;break}return console.error("No hay usuario autenticado"),e.abrupt("return",[]);case 12:return e.next=14,i.N.from("colecciones_flashcards").select("*").eq("user_id",t.id).order("creado_en",{ascending:!1});case 14:if(s=(n=e.sent).data,!(l=n.error)){e.next=20;break}return console.error("Error al obtener colecciones de flashcards:",l),e.abrupt("return",[]);case 20:if(!(!s||0===s.length)){e.next=22;break}return e.abrupt("return",[]);case 22:return e.next=24,Promise.all(s.map(function(){var e=(0,o.A)(c().mark(function e(r){var t,a,n,o,s,u;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i.N.from("flashcards").select("id").eq("coleccion_id",r.id);case 3:if(a=(t=e.sent).data,!(n=t.error)){e.next=9;break}return console.error("Error al contar flashcards para colecci\xf3n",r.id,":",n),e.abrupt("return",d(d({},r),{},{numero_flashcards:0,pendientes_hoy:0}));case 9:return e.next=11,i.N.from("flashcards").select("\n              id,\n              progreso_flashcards!inner(\n                proxima_revision,\n                estado\n              )\n            ").eq("coleccion_id",r.id).lte("progreso_flashcards.proxima_revision",new Date().toISOString());case 11:return s=(o=e.sent).data,u=o.error?0:(null==s?void 0:s.length)||0,e.abrupt("return",d(d({},r),{},{numero_flashcards:(null==a?void 0:a.length)||0,pendientes_hoy:u}));case 18:return e.prev=18,e.t0=e.catch(0),console.error("Error al procesar colecci\xf3n",r.id,":",e.t0),e.abrupt("return",d(d({},r),{},{numero_flashcards:0,pendientes_hoy:0}));case 22:case"end":return e.stop()}},e,null,[[0,18]])}));return function(r){return e.apply(this,arguments)}}()));case 24:return p=e.sent,e.abrupt("return",p);case 28:return e.prev=28,e.t0=e.catch(0),console.error("Error general al obtener colecciones de flashcards:",e.t0),e.abrupt("return",[]);case 32:case"end":return e.stop()}},e,null,[[0,28]])}))).apply(this,arguments)}function b(e){return x.apply(this,arguments)}function x(){return(x=(0,o.A)(c().mark(function e(r){var t,a,n;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.N.from("flashcards").select("*").eq("coleccion_id",r).order("creado_en",{ascending:!0});case 2:if(a=(t=e.sent).data,!(n=t.error)){e.next=8;break}return console.error("Error al obtener flashcards:",n),e.abrupt("return",[]);case 8:return e.abrupt("return",a||[]);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}function v(e){return g.apply(this,arguments)}function g(){return(g=(0,o.A)(c().mark(function e(r){var t,a,n;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.N.from("flashcards").insert(r).select();case 2:if(a=(t=e.sent).data,!(n=t.error)){e.next=8;break}return console.error("Error al guardar flashcards:",n),e.abrupt("return",null);case 8:return e.abrupt("return",(null==a?void 0:a.map(function(e){return e.id}))||null);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}function y(e){return j.apply(this,arguments)}function j(){return(j=(0,o.A)(c().mark(function e(r){var t,a,n,o,s,u;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b(r);case 2:return t=e.sent,e.next=5,i.N.from("progreso_flashcards").select("*").in("flashcard_id",t.map(function(e){return e.id}));case 5:if(n=(a=e.sent).data,!(o=a.error)){e.next=11;break}return console.error("Error al obtener progreso de flashcards:",o),e.abrupt("return",[]);case 11:return u=new Date((s=new Date).getFullYear(),s.getMonth(),s.getDate()),e.abrupt("return",t.map(function(e){var r=null==n?void 0:n.find(function(r){return r.flashcard_id===e.id});if(!r)return d(d({},e),{},{debeEstudiar:!0});var t=new Date(r.proxima_revision),a=new Date(t.getFullYear(),t.getMonth(),t.getDate())<=u;return d(d({},e),{},{debeEstudiar:a,progreso:{factor_facilidad:r.factor_facilidad,intervalo:r.intervalo,repeticiones:r.repeticiones,estado:r.estado,proxima_revision:r.proxima_revision}})}));case 14:case"end":return e.stop()}},e)}))).apply(this,arguments)}function w(e){return N.apply(this,arguments)}function N(){return(N=(0,o.A)(c().mark(function e(r){var t,a,n,o,s,u,l,p,f=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=f.length>1&&void 0!==f[1]?f[1]:10,e.prev=1,e.next=4,y(r);case 4:return n=(a=e.sent).map(function(e){return e.id}),e.next=8,i.N.from("historial_revisiones").select("flashcard_id, dificultad").in("flashcard_id",n);case 8:if(s=(o=e.sent).data,!(u=o.error)){e.next=14;break}return console.error("Error al obtener historial de revisiones:",u),e.abrupt("return",a.slice(0,t));case 14:return l=new Map,null==s||s.forEach(function(e){var r=l.get(e.flashcard_id)||{dificil:0,total:0};r.total++,"dificil"===e.dificultad&&r.dificil++,l.set(e.flashcard_id,r)}),p=a.map(function(e){var r=l.get(e.id),t=r?r.dificil/r.total:0;return d(d({},e),{},{ratioDificultad:t})}).sort(function(e,r){return r.ratioDificultad-e.ratioDificultad}).slice(0,t),e.abrupt("return",p);case 20:return e.prev=20,e.t0=e.catch(1),console.error("Error al obtener flashcards m\xe1s dif\xedciles:",e.t0),e.abrupt("return",[]);case 24:case"end":return e.stop()}},e,null,[[1,20]])}))).apply(this,arguments)}function _(e){return k.apply(this,arguments)}function k(){return(k=(0,o.A)(c().mark(function e(r){var t,n,o,s=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=s.length>1&&void 0!==s[1]?s[1]:10,e.prev=1,e.next=4,y(r);case 4:return n=e.sent,o=(0,a.A)(n).sort(function(){return Math.random()-.5}).slice(0,t),e.abrupt("return",o);case 9:return e.prev=9,e.t0=e.catch(1),console.error("Error al obtener flashcards aleatorias:",e.t0),e.abrupt("return",[]);case 13:case"end":return e.stop()}},e,null,[[1,9]])}))).apply(this,arguments)}function O(e){return A.apply(this,arguments)}function A(){return(A=(0,o.A)(c().mark(function e(r){var t,a,n,o,s,u,l,p,f=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=f.length>1&&void 0!==f[1]?f[1]:10,e.prev=1,e.next=4,y(r);case 4:return n=(a=e.sent).map(function(e){return e.id}),e.next=8,i.N.from("historial_revisiones").select("flashcard_id, fecha").in("flashcard_id",n).order("fecha",{ascending:!1});case 8:if(s=(o=e.sent).data,!(u=o.error)){e.next=14;break}return console.error("Error al obtener \xfaltimas revisiones:",u),e.abrupt("return",a.slice(0,t));case 14:return l=new Map,null==s||s.forEach(function(e){l.has(e.flashcard_id)||l.set(e.flashcard_id,e.fecha)}),p=a.map(function(e){var r=l.get(e.id);return d(d({},e),{},{ultimaRevision:new Date(r||0)})}).sort(function(e,r){return e.ultimaRevision.getTime()-r.ultimaRevision.getTime()}).slice(0,t),e.abrupt("return",p);case 20:return e.prev=20,e.t0=e.catch(1),console.error("Error al obtener flashcards no recientes:",e.t0),e.abrupt("return",[]);case 24:case"end":return e.stop()}},e,null,[[1,20]])}))).apply(this,arguments)}function E(e,r){return S.apply(this,arguments)}function S(){return(S=(0,o.A)(c().mark(function e(r,t){var a,n,o=arguments;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=o.length>2&&void 0!==o[2]?o[2]:10,e.prev=1,e.next=4,y(r);case 4:return n=e.sent.filter(function(e){return e.progreso?e.progreso.estado===t:"nuevo"===t}).slice(0,a),e.abrupt("return",n);case 9:return e.prev=9,e.t0=e.catch(1),console.error("Error al obtener flashcards por estado:",e.t0),e.abrupt("return",[]);case 13:case"end":return e.stop()}},e,null,[[1,9]])}))).apply(this,arguments)}function D(e,r){return P.apply(this,arguments)}function P(){return(P=(0,o.A)(c().mark(function e(r,t){var a,n,o,s,u,l,d,p,f,m,h,b,x,v;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a=2.5,n=1,o=0,s="nuevo",u=!1,e.next=8,i.N.from("progreso_flashcards").select("factor_facilidad, intervalo, repeticiones, estado").eq("flashcard_id",r).single();case 8:if(d=(l=e.sent).data,!l.error&&d&&(a=d.factor_facilidad||2.5,n=d.intervalo||1,o=d.repeticiones||0,s=d.estado||"nuevo",u=!0),p=a,f=n,m=o,h=s,"dificil"===t?(p=Math.max(1.3,a-.3),m=0,f=1,h="aprendiendo"):(m++,"normal"===t?p=a-.15:"facil"===t&&(p=a+.1),p=Math.max(1.3,Math.min(2.5,p)),1===m?(f=1,h="aprendiendo"):2===m?(f=6,h="repasando"):h=(f=Math.round(n*p))>30?"aprendido":"repasando"),(x=new Date(b=new Date)).setDate(x.getDate()+f),v=null,!u){e.next=29;break}return e.next=24,i.N.from("progreso_flashcards").update({factor_facilidad:p,intervalo:f,repeticiones:m,estado:h,ultima_revision:b.toISOString(),proxima_revision:x.toISOString()}).eq("flashcard_id",r);case 24:v=e.sent.error,e.next=34;break;case 29:return e.next=31,i.N.from("progreso_flashcards").insert({flashcard_id:r,factor_facilidad:p,intervalo:f,repeticiones:m,estado:h,ultima_revision:b.toISOString(),proxima_revision:x.toISOString()});case 31:v=e.sent.error;case 34:if(!v){e.next=37;break}return console.error("Error al guardar progreso:",v),e.abrupt("return",!1);case 37:return e.next=39,i.N.from("historial_revisiones").insert({flashcard_id:r,dificultad:t,factor_facilidad:p,intervalo:f,repeticiones:m,fecha:b.toISOString()});case 39:return e.sent.error,e.abrupt("return",!0);case 45:return e.prev=45,e.t0=e.catch(0),e.abrupt("return",!1);case 48:case"end":return e.stop()}},e,null,[[0,45]])}))).apply(this,arguments)}function C(e,r,t){return T.apply(this,arguments)}function T(){return(T=(0,o.A)(c().mark(function e(r,t,a){return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i.N.from("flashcards").update({pregunta:t,respuesta:a,actualizado_en:new Date().toISOString()}).eq("id",r);case 3:if(!e.sent.error){e.next=7;break}return e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(0),e.abrupt("return",!1);case 13:case"end":return e.stop()}},e,null,[[0,10]])}))).apply(this,arguments)}function F(e){return I.apply(this,arguments)}function I(){return(I=(0,o.A)(c().mark(function e(r){var t,a,n;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i.N.from("progreso_flashcards").delete().eq("flashcard_id",r);case 3:if(!e.sent.error){e.next=7;break}return e.abrupt("return",!1);case 7:return e.next=9,i.N.from("historial_revisiones").delete().eq("flashcard_id",r);case 9:if(!e.sent.error){e.next=13;break}return e.abrupt("return",!1);case 13:return e.next=15,i.N.from("flashcards").delete({count:"exact"}).eq("id",r);case 15:if(a=(t=e.sent).error,n=t.count,!a){e.next=20;break}return e.abrupt("return",!1);case 20:if(0!==n){e.next=22;break}return e.abrupt("return",!1);case 22:return e.abrupt("return",!0);case 25:return e.prev=25,e.t0=e.catch(0),e.abrupt("return",!1);case 28:case"end":return e.stop()}},e,null,[[0,25]])}))).apply(this,arguments)}function M(e){return q.apply(this,arguments)}function q(){return(q=(0,o.A)(c().mark(function e(r){var t,a,n,o,s,l,d;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,u.iF)();case 3:if(t=e.sent.user){e.next=7;break}return e.abrupt("return",!1);case 7:return e.next=9,i.N.from("flashcards").select("id").eq("coleccion_id",r);case 9:if(n=(a=e.sent).data,!a.error){e.next=14;break}return e.abrupt("return",!1);case 14:if(!((o=(null==n?void 0:n.map(function(e){return e.id}))||[]).length>0)){e.next=34;break}return e.next=18,i.N.from("progreso_flashcards").delete().in("flashcard_id",o);case 18:if(!e.sent.error){e.next=22;break}return e.abrupt("return",!1);case 22:return e.next=24,i.N.from("historial_revisiones").delete().in("flashcard_id",o);case 24:if(!e.sent.error){e.next=28;break}return e.abrupt("return",!1);case 28:return e.next=30,i.N.from("flashcards").delete().eq("coleccion_id",r);case 30:if(!e.sent.error){e.next=34;break}return e.abrupt("return",!1);case 34:return e.next=36,i.N.from("colecciones_flashcards").delete({count:"exact"}).eq("id",r).eq("user_id",t.id);case 36:if(l=(s=e.sent).error,d=s.count,!l){e.next=41;break}return e.abrupt("return",!1);case 41:if(0!==d){e.next=43;break}return e.abrupt("return",!1);case 43:return e.abrupt("return",!0);case 46:return e.prev=46,e.t0=e.catch(0),e.abrupt("return",!1);case 49:case"end":return e.stop()}},e,null,[[0,46]])}))).apply(this,arguments)}},5929:(e,r,t)=>{t.d(r,{wU:()=>l,yV:()=>i});var a=t(33311),n=t(28295),o=t.n(n),s=t(55564),c=t(2775);function i(e){return u.apply(this,arguments)}function u(){return(u=(0,a.A)(o().mark(function e(r){var t,a,n,i,u,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.Q1)(r);case 2:if(0!==(t=e.sent).length){e.next=5;break}return e.abrupt("return",{total:0,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0});case 5:return e.next=7,s.N.from("progreso_flashcards").select("*").in("flashcard_id",t.map(function(e){return e.id}));case 7:if(n=(a=e.sent).data,!(i=a.error)){e.next=13;break}return console.error("Error al obtener progreso de flashcards:",i),e.abrupt("return",{total:t.length,nuevas:t.length,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:t.length});case 13:return u={total:t.length,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0},l=new Date,t.forEach(function(e){var r=null==n?void 0:n.find(function(r){return r.flashcard_id===e.id});if(r){switch(r.estado){case"nuevo":u.nuevas++;break;case"aprendiendo":u.aprendiendo++;break;case"repasando":u.repasando++;break;case"aprendido":u.aprendidas++}var t=new Date(r.proxima_revision);new Date(t.getFullYear(),t.getMonth(),t.getDate())<=new Date(l.getFullYear(),l.getMonth(),l.getDate())&&u.paraHoy++}else u.nuevas++,u.paraHoy++}),e.abrupt("return",u);case 17:case"end":return e.stop()}},e)}))).apply(this,arguments)}function l(e){return d.apply(this,arguments)}function d(){return(d=(0,a.A)(o().mark(function e(r){var t,a,n,i,u,l,d,p,f,m;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,c.Q1)(r);case 3:if(0!==(t=e.sent).length){e.next=6;break}return e.abrupt("return",{totalSesiones:0,totalRevisiones:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]});case 6:return a=t.map(function(e){return e.id}),e.next=9,s.N.from("historial_revisiones").select("*").in("flashcard_id",a).order("fecha",{ascending:!0});case 9:if(i=(n=e.sent).data,!(u=n.error)){e.next=15;break}return console.error("Error al obtener revisiones:",u),e.abrupt("return",null);case 15:return e.next=17,s.N.from("progreso_flashcards").select("*").in("flashcard_id",a);case 17:return(l=e.sent).data,(d=l.error)&&console.error("Error al obtener progreso:",d),p={totalSesiones:0,totalRevisiones:i?i.length:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]},i&&i.length>0&&(i.forEach(function(e){"dificil"===e.dificultad?p.distribucionDificultad.dificil++:"normal"===e.dificultad?p.distribucionDificultad.normal++:"facil"===e.dificultad&&p.distribucionDificultad.facil++}),f=new Set,i.forEach(function(e){var r=new Date(e.fecha).toISOString().split("T")[0];f.add(r)}),p.totalSesiones=f.size,m=new Map,t.forEach(function(e){m.set(e.id,{dificil:0,normal:0,facil:0,total:0})}),i.forEach(function(e){var r=m.get(e.flashcard_id);r&&("dificil"===e.dificultad?r.dificil++:"normal"===e.dificultad?r.normal++:"facil"===e.dificultad&&r.facil++,r.total++)}),p.tarjetasMasDificiles=t.map(function(e){var r=m.get(e.id)||{dificil:0,normal:0,facil:0,total:0};return{id:e.id,pregunta:e.pregunta,dificil:r.dificil,normal:r.normal,facil:r.facil,totalRevisiones:r.total}}).filter(function(e){return e.totalRevisiones>0}).sort(function(e,r){var t=e.totalRevisiones>0?e.dificil/e.totalRevisiones:0;return(r.totalRevisiones>0?r.dificil/r.totalRevisiones:0)-t}).slice(0,10)),e.abrupt("return",p);case 26:return e.prev=26,e.t0=e.catch(0),console.error("Error al calcular estad\xedsticas detalladas:",e.t0),e.abrupt("return",null);case 30:case"end":return e.stop()}},e,null,[[0,26]])}))).apply(this,arguments)}},11555:(e,r,t)=>{t.d(r,{IE:()=>c,qk:()=>u,qo:()=>s});var a=t(33311),n=t(28295),o=t.n(n),s={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function c(e){return s[e]||null}function i(e,r){var t=c(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}function u(e){return l.apply(this,arguments)}function l(){return(l=(0,a.A)(o().mark(function e(r){var t,a;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/api/user/plan");case 3:if((t=e.sent).ok){e.next=7;break}return console.error("Error obteniendo plan del usuario"),e.abrupt("return",i("free",r));case 7:return e.next=9,t.json();case 9:return a=e.sent.plan||"free",e.abrupt("return",i(a,r));case 15:return e.prev=15,e.t0=e.catch(0),console.error("Error verificando acceso a caracter\xedstica:",e.t0),e.abrupt("return",i("free",r));case 19:case"end":return e.stop()}},e,null,[[0,15]])}))).apply(this,arguments)}},17863:(e,r,t)=>{t.d(r,{$S:()=>l,d7:()=>p,fF:()=>i});var a=t(33311),n=t(28295),o=t.n(n),s=t(66618),c=t(57759);function i(e){return u.apply(this,arguments)}function u(){return(u=(0,a.A)(o().mark(function e(r){var t,a,n,i,u,l,d,p,f;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,c.iF)();case 3:if(a=(t=e.sent).user,n=t.error,!(!a||n)){e.next=8;break}return e.abrupt("return",null);case 8:return e.next=10,s.N.from("temarios").select("id").eq("id",r).eq("user_id",a.id).single();case 10:if(u=(i=e.sent).data,!(l=i.error)){e.next=19;break}if("PGRST116"!==l.code){e.next=17;break}return console.log("Temario no encontrado:",r),e.abrupt("return",null);case 17:return console.error("Error al verificar temario:",l),e.abrupt("return",null);case 19:if(u){e.next=22;break}return console.log("Temario no encontrado:",r),e.abrupt("return",null);case 22:return e.next=24,s.N.from("planes_estudios").select("*").eq("user_id",a.id).eq("temario_id",r).eq("activo",!0).single();case 24:if(p=(d=e.sent).data,!(f=d.error)){e.next=32;break}if("PGRST116"!==f.code){e.next=30;break}return e.abrupt("return",null);case 30:return console.error("Error al obtener plan activo:",f),e.abrupt("return",null);case 32:return e.abrupt("return",p);case 35:return e.prev=35,e.t0=e.catch(0),console.error("Error al obtener plan activo:",e.t0),e.abrupt("return",null);case 39:case"end":return e.stop()}},e,null,[[0,35]])}))).apply(this,arguments)}function l(e){return d.apply(this,arguments)}function d(){return(d=(0,a.A)(o().mark(function e(r){var t,a,n,i,u,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,c.iF)();case 3:if(a=(t=e.sent).user,n=t.error,!(!a||n)){e.next=8;break}return e.abrupt("return",[]);case 8:return e.next=10,s.N.from("progreso_plan_estudios").select("*").eq("plan_id",r).eq("user_id",a.id).order("semana_numero",{ascending:!0}).order("creado_en",{ascending:!0});case 10:if(u=(i=e.sent).data,!(l=i.error)){e.next=16;break}return console.error("Error al obtener progreso del plan:",l),e.abrupt("return",[]);case 16:return e.abrupt("return",u||[]);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error al obtener progreso del plan:",e.t0),e.abrupt("return",[]);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function p(e,r,t,a,n,o,s,c,i){return f.apply(this,arguments)}function f(){return(f=(0,a.A)(o().mark(function e(r,t,a,n,i,u,l,d,p){var f,m,h,b,x,v;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,c.iF)();case 3:if(m=(f=e.sent).user,h=f.error,!(!m||h)){e.next=8;break}return e.abrupt("return",!1);case 8:return e.next=10,s.N.from("progreso_plan_estudios").select("id").eq("plan_id",r).eq("user_id",m.id).eq("semana_numero",t).eq("dia_nombre",a).eq("tarea_titulo",n).single();case 10:if(!(b=e.sent.data)){e.next=22;break}return e.next=15,s.N.from("progreso_plan_estudios").update({completado:u,fecha_completado:u?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:d,calificacion:p,actualizado_en:new Date().toISOString()}).eq("id",b.id);case 15:if(!(x=e.sent.error)){e.next=20;break}return console.error("Error al actualizar progreso:",x),e.abrupt("return",!1);case 20:e.next=29;break;case 22:return e.next=24,s.N.from("progreso_plan_estudios").insert([{plan_id:r,user_id:m.id,semana_numero:t,dia_nombre:a,tarea_titulo:n,tarea_tipo:i,completado:u,fecha_completado:u?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:d,calificacion:p}]);case 24:if(!(v=e.sent.error)){e.next=29;break}return console.error("Error al crear progreso:",v),e.abrupt("return",!1);case 29:return e.abrupt("return",!0);case 32:return e.prev=32,e.t0=e.catch(0),console.error("Error al guardar progreso de tarea:",e.t0),e.abrupt("return",!1);case 36:case"end":return e.stop()}},e,null,[[0,32]])}))).apply(this,arguments)}},25519:(e,r,t)=>{t.d(r,{Pk:()=>h,u9:()=>f,vD:()=>d});var a=t(37711),n=t(33311),o=t(28295),s=t.n(o),c=t(66618),i=t(57759);function u(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?u(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return p.apply(this,arguments)}function p(){return(p=(0,n.A)(s().mark(function e(r){var t,a,n,o,u,l,d,p,f;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(a=(t=e.sent).user,n=t.error,!(!a||n)){e.next=9;break}return console.log("No hay usuario autenticado o error:",n),e.abrupt("return",!1);case 9:return e.next=11,c.N.from("temarios").select("id").eq("id",r).eq("user_id",a.id).single();case 11:if(u=(o=e.sent).data,!(l=o.error)){e.next=19;break}if("PGRST116"!==l.code){e.next=17;break}return e.abrupt("return",!1);case 17:return console.error("Error al verificar temario:",l),e.abrupt("return",!1);case 19:if(u){e.next=21;break}return e.abrupt("return",!1);case 21:return e.next=23,c.N.from("planificacion_usuario").select("id").eq("user_id",a.id).eq("temario_id",r).eq("completado",!0).limit(1);case 23:if(p=(d=e.sent).data,!(f=d.error)){e.next=29;break}return console.error("Error al verificar planificaci\xf3n:",f),e.abrupt("return",!1);case 29:return e.abrupt("return",p&&p.length>0);case 32:return e.prev=32,e.t0=e.catch(0),console.error("Error al verificar planificaci\xf3n:",e.t0),e.abrupt("return",!1);case 36:case"end":return e.stop()}},e,null,[[0,32]])}))).apply(this,arguments)}function f(e){return m.apply(this,arguments)}function m(){return(m=(0,n.A)(s().mark(function e(r){var t,a,n,o,u,l;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(a=(t=e.sent).user,n=t.error,!(!a||n)){e.next=8;break}return e.abrupt("return",null);case 8:return e.next=10,c.N.from("planificacion_usuario").select("*").eq("user_id",a.id).eq("temario_id",r).single();case 10:if(u=(o=e.sent).data,!(l=o.error)){e.next=18;break}if("PGRST116"!==l.code){e.next=16;break}return e.abrupt("return",null);case 16:return console.error("Error al obtener planificaci\xf3n:",l),e.abrupt("return",null);case 18:return e.abrupt("return",u);case 21:return e.prev=21,e.t0=e.catch(0),console.error("Error al obtener planificaci\xf3n:",e.t0),e.abrupt("return",null);case 25:case"end":return e.stop()}},e,null,[[0,21]])}))).apply(this,arguments)}function h(e,r){return b.apply(this,arguments)}function b(){return(b=(0,n.A)(s().mark(function e(r,t){var a,n,o,u,d,p,m,h,b,x;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(n=(a=e.sent).user,o=a.error,!(!n||o)){e.next=9;break}return console.error("No hay usuario autenticado"),e.abrupt("return",null);case 9:return e.next=11,f(r);case 11:if(!(u=e.sent)){e.next=24;break}return e.next=15,c.N.from("planificacion_usuario").update(l(l({},t),{},{completado:!0,actualizado_en:new Date().toISOString()})).eq("id",u.id).select().single();case 15:if(p=(d=e.sent).data,!(m=d.error)){e.next=21;break}return console.error("Error al actualizar planificaci\xf3n:",m),e.abrupt("return",null);case 21:return e.abrupt("return",p.id);case 24:return e.next=26,c.N.from("planificacion_usuario").insert([l(l({user_id:n.id,temario_id:r},t),{},{completado:!0})]).select().single();case 26:if(b=(h=e.sent).data,!(x=h.error)){e.next=32;break}return console.error("Error al crear planificaci\xf3n:",x),e.abrupt("return",null);case 32:return e.abrupt("return",b.id);case 33:e.next=39;break;case 35:return e.prev=35,e.t0=e.catch(0),console.error("Error al guardar planificaci\xf3n:",e.t0),e.abrupt("return",null);case 39:case"end":return e.stop()}},e,null,[[0,35]])}))).apply(this,arguments)}},33821:(e,r,t)=>{t.d(r,{B$:()=>k,Il:()=>S,Se:()=>j,cN:()=>A,cm:()=>x,jg:()=>f,oS:()=>N,r5:()=>h,sW:()=>g,xv:()=>P,yr:()=>d});var a=t(37711),n=t(33311),o=t(28295),s=t.n(o),c=t(66618),i=t(57759);function u(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?u(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(){return p.apply(this,arguments)}function p(){return(p=(0,n.A)(s().mark(function e(){var r,t,a,n,o,u,l,d,p;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(t=(r=e.sent).user,a=r.error,!(!t||a)){e.next=8;break}return e.abrupt("return",!1);case 8:return e.next=10,c.N.from("temarios").select("id").eq("user_id",t.id).limit(1);case 10:if(o=(n=e.sent).data,!(u=n.error)){e.next=18;break}if(!("PGRST116"===u.code||null!=(l=u.message)&&l.includes("relation")||null!=(d=u.message)&&d.includes("does not exist"))){e.next=16;break}return e.abrupt("return",!1);case 16:return console.error("Error al verificar temario en Supabase:",u),e.abrupt("return",!1);case 18:return p=o&&o.length>0,e.abrupt("return",p);case 22:return e.prev=22,e.t0=e.catch(0),console.error("Error general al verificar temario:",e.t0),e.abrupt("return",!1);case 26:case"end":return e.stop()}},e,null,[[0,22]])}))).apply(this,arguments)}function f(){return m.apply(this,arguments)}function m(){return(m=(0,n.A)(s().mark(function e(){var r,t,a,n,o,u;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(t=(r=e.sent).user,a=r.error,!(!t||a)){e.next=8;break}return e.abrupt("return",null);case 8:return e.next=10,c.N.from("temarios").select("*").eq("user_id",t.id).single();case 10:if(o=(n=e.sent).data,!(u=n.error)){e.next=18;break}if("PGRST116"!==u.code){e.next=16;break}return e.abrupt("return",null);case 16:return console.error("Error al obtener temario en Supabase:",u),e.abrupt("return",null);case 18:return e.abrupt("return",o);case 21:return e.prev=21,e.t0=e.catch(0),console.error("Error general al obtener temario:",e.t0),e.abrupt("return",null);case 25:case"end":return e.stop()}},e,null,[[0,21]])}))).apply(this,arguments)}function h(e,r,t){return b.apply(this,arguments)}function b(){return(b=(0,n.A)(s().mark(function e(r,t,a){var n,o,u,l,d,p;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(o=(n=e.sent).user,u=n.error,!(!o||u)){e.next=9;break}return console.error("No hay usuario autenticado o error:",u),e.abrupt("return",null);case 9:return e.next=11,c.N.from("temarios").insert([{titulo:r,descripcion:t,tipo:a,user_id:o.id}]).select().single();case 11:if(d=(l=e.sent).data,!(p=l.error)){e.next=17;break}return console.error("Error al crear temario:",p),e.abrupt("return",null);case 17:return e.abrupt("return",d.id);case 20:return e.prev=20,e.t0=e.catch(0),console.error("Error al crear temario:",e.t0),e.abrupt("return",null);case 24:case"end":return e.stop()}},e,null,[[0,20]])}))).apply(this,arguments)}function x(e){return v.apply(this,arguments)}function v(){return(v=(0,n.A)(s().mark(function e(r){var t,a,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.N.from("temas").select("*").eq("temario_id",r).order("orden",{ascending:!0});case 3:if(a=(t=e.sent).data,!(n=t.error)){e.next=9;break}return console.error("Error al obtener temas:",n),e.abrupt("return",[]);case 9:return e.abrupt("return",a||[]);case 12:return e.prev=12,e.t0=e.catch(0),console.error("Error al obtener temas:",e.t0),e.abrupt("return",[]);case 16:case"end":return e.stop()}},e,null,[[0,12]])}))).apply(this,arguments)}function g(e,r){return y.apply(this,arguments)}function y(){return(y=(0,n.A)(s().mark(function e(r,t){var a,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a=t.map(function(e){return l(l({},e),{},{temario_id:r})}),e.next=4,c.N.from("temas").insert(a);case 4:if(!(n=e.sent.error)){e.next=9;break}return console.error("Error al crear temas:",n),e.abrupt("return",!1);case 9:return e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(0),console.error("Error al crear temas:",e.t0),e.abrupt("return",!1);case 16:case"end":return e.stop()}},e,null,[[0,12]])}))).apply(this,arguments)}function j(e,r,t){return w.apply(this,arguments)}function w(){return(w=(0,n.A)(s().mark(function e(r,t,a){var n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.N.from("temarios").update({titulo:t,descripcion:a,actualizado_en:new Date().toISOString()}).eq("id",r);case 3:if(!(n=e.sent.error)){e.next=8;break}return console.error("Error al actualizar temario:",n),e.abrupt("return",!1);case 8:return e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(0),console.error("Error al actualizar temario:",e.t0),e.abrupt("return",!1);case 15:case"end":return e.stop()}},e,null,[[0,11]])}))).apply(this,arguments)}function N(e,r,t){return _.apply(this,arguments)}function _(){return(_=(0,n.A)(s().mark(function e(r,t,a){var n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.N.from("temas").update({titulo:t,descripcion:a,actualizado_en:new Date().toISOString()}).eq("id",r);case 3:if(!(n=e.sent.error)){e.next=8;break}return console.error("Error al actualizar tema:",n),e.abrupt("return",!1);case 8:return e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(0),console.error("Error al actualizar tema:",e.t0),e.abrupt("return",!1);case 15:case"end":return e.stop()}},e,null,[[0,11]])}))).apply(this,arguments)}function k(e){return O.apply(this,arguments)}function O(){return(O=(0,n.A)(s().mark(function e(r){var t;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.N.from("temas").delete().eq("id",r);case 3:if(!(t=e.sent.error)){e.next=8;break}return console.error("Error al eliminar tema:",t),e.abrupt("return",!1);case 8:return e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(0),console.error("Error al eliminar tema:",e.t0),e.abrupt("return",!1);case 15:case"end":return e.stop()}},e,null,[[0,11]])}))).apply(this,arguments)}function A(e,r){return E.apply(this,arguments)}function E(){return(E=(0,n.A)(s().mark(function e(r,t){var a,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a={completado:t,actualizado_en:new Date().toISOString()},t?a.fecha_completado=new Date().toISOString():a.fecha_completado=null,e.next=5,c.N.from("temas").update(a).eq("id",r);case 5:if(!(n=e.sent.error)){e.next=10;break}return console.error("Error al actualizar estado del tema:",n),e.abrupt("return",!1);case 10:return e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(0),console.error("Error al actualizar estado del tema:",e.t0),e.abrupt("return",!1);case 17:case"end":return e.stop()}},e,null,[[0,13]])}))).apply(this,arguments)}function S(e){return D.apply(this,arguments)}function D(){return(D=(0,n.A)(s().mark(function e(r){var t,a,n,o,i,u;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.N.from("temas").select("completado").eq("temario_id",r);case 3:if(a=(t=e.sent).data,!(n=t.error)){e.next=9;break}return console.error("Error al obtener estad\xedsticas del temario:",n),e.abrupt("return",null);case 9:return o=a.length,i=a.filter(function(e){return e.completado}).length,u=o>0?i/o*100:0,e.abrupt("return",{totalTemas:o,temasCompletados:i,porcentajeCompletado:u});case 15:return e.prev=15,e.t0=e.catch(0),console.error("Error al obtener estad\xedsticas del temario:",e.t0),e.abrupt("return",null);case 19:case"end":return e.stop()}},e,null,[[0,15]])}))).apply(this,arguments)}function P(e){return C.apply(this,arguments)}function C(){return(C=(0,n.A)(s().mark(function e(r){var t;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.N.from("temarios").delete().eq("id",r);case 3:if(!(t=e.sent.error)){e.next=8;break}return console.error("Error al eliminar temario:",t),e.abrupt("return",!1);case 8:return e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(0),console.error("Error al eliminar temario:",e.t0),e.abrupt("return",!1);case 15:case"end":return e.stop()}},e,null,[[0,11]])}))).apply(this,arguments)}},47493:(e,r,t)=>{t.d(r,{Gl:()=>g,Kj:()=>h,Lx:()=>d,OA:()=>x,_4:()=>u,dd:()=>N,hg:()=>f,oC:()=>j});var a=t(3243),n=t(33311),o=t(28295),s=t.n(o),c=t(55564),i=t(66430);function u(e,r,t){return l.apply(this,arguments)}function l(){return(l=(0,n.A)(s().mark(function e(r,t,a){var n,o,u,l,d,p;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("\uD83D\uDCDD Creando nuevo test:",r),e.next=4,(0,i.iF)();case 4:if(u=e.sent.user){e.next=9;break}return console.error("❌ No hay usuario autenticado para crear test"),e.abrupt("return",null);case 9:return console.log("\uD83D\uDC64 Usuario autenticado:",u.id),e.next=12,c.N.from("tests").insert([{titulo:r,descripcion:t,documentos_ids:a,user_id:u.id}]).select();case 12:if(d=(l=e.sent).data,!(p=l.error)){e.next=18;break}return console.error("❌ Error al crear test:",p),e.abrupt("return",null);case 18:return console.log("✅ Test creado exitosamente:",null==d||null==(n=d[0])?void 0:n.id),e.abrupt("return",(null==d||null==(o=d[0])?void 0:o.id)||null);case 22:return e.prev=22,e.t0=e.catch(0),console.error("\uD83D\uDCA5 Error inesperado al crear test:",e.t0),e.abrupt("return",null);case 26:case"end":return e.stop()}},e,null,[[0,22]])}))).apply(this,arguments)}function d(){return p.apply(this,arguments)}function p(){return(p=(0,n.A)(s().mark(function e(){var r,t,a,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(r=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",[]);case 8:return e.next=10,c.N.from("tests").select("*").eq("user_id",r.id).order("creado_en",{ascending:!1});case 10:if(a=(t=e.sent).data,!(n=t.error)){e.next=16;break}return console.error("Error al obtener tests:",n),e.abrupt("return",[]);case 16:return e.abrupt("return",a||[]);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error al obtener tests:",e.t0),e.abrupt("return",[]);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function f(e){return m.apply(this,arguments)}function m(){return(m=(0,n.A)(s().mark(function e(r){var t,a,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.N.from("preguntas_test").select("*").eq("test_id",r);case 2:if(a=(t=e.sent).data,!(n=t.error)){e.next=8;break}return console.error("Error al obtener preguntas de test:",n),e.abrupt("return",[]);case 8:return e.abrupt("return",a||[]);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}function h(e){return b.apply(this,arguments)}function b(){return(b=(0,n.A)(s().mark(function e(r){var t,a,n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",r);case 2:if(a=(t=e.sent).count,!(n=t.error)){e.next=8;break}return console.error("Error al obtener conteo de preguntas:",n),e.abrupt("return",0);case 8:return e.abrupt("return",a||0);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}function x(e){return v.apply(this,arguments)}function v(){return(v=(0,n.A)(s().mark(function e(r){var t;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.N.from("preguntas_test").insert(r);case 2:if(!(t=e.sent.error)){e.next=7;break}return console.error("Error al guardar preguntas de test:",t),e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function g(e,r,t,a){return y.apply(this,arguments)}function y(){return(y=(0,n.A)(s().mark(function e(r,t,a,n){var o;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.N.from("estadisticas_test").insert([{test_id:r,pregunta_id:t,respuesta_seleccionada:a,es_correcta:n,fecha_respuesta:new Date().toISOString()}]);case 2:if(!(o=e.sent.error)){e.next=7;break}return console.error("Error al registrar respuesta de test:",o),e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function j(){return w.apply(this,arguments)}function w(){return(w=(0,n.A)(s().mark(function e(){var r,t,a,n,o,i,u,l;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.N.from("estadisticas_test").select("*");case 2:if(t=(r=e.sent).data,!(a=r.error)){e.next=8;break}return console.error("Error al obtener estad\xedsticas de tests:",a),e.abrupt("return",{totalTests:0,totalPreguntas:0,totalRespuestasCorrectas:0,totalRespuestasIncorrectas:0,porcentajeAcierto:0});case 8:return n=new Set((null==t?void 0:t.map(function(e){return e.test_id}))||[]),o=new Set((null==t?void 0:t.map(function(e){return e.pregunta_id}))||[]),i=(null==t?void 0:t.filter(function(e){return e.es_correcta}).length)||0,u=((null==t?void 0:t.length)||0)-i,l=t&&t.length>0?Math.round(i/t.length*100):0,e.abrupt("return",{totalTests:n.size,totalPreguntas:o.size,totalRespuestasCorrectas:i,totalRespuestasIncorrectas:u,porcentajeAcierto:l});case 14:case"end":return e.stop()}},e)}))).apply(this,arguments)}function N(e){return _.apply(this,arguments)}function _(){return(_=(0,n.A)(s().mark(function e(r){var t,n,o,i,u,l,d,p,f,m,h;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.N.from("estadisticas_test").select("*").eq("test_id",r);case 2:if(n=(t=e.sent).data,!(o=t.error)){e.next=8;break}return console.error("Error al obtener estad\xedsticas del test:",o),e.abrupt("return",{testId:r,totalPreguntas:0,totalCorrectas:0,totalIncorrectas:0,porcentajeAcierto:0,fechasRealizacion:[],preguntasMasFalladas:[]});case 8:return e.next=10,c.N.from("preguntas_test").select("*").eq("test_id",r);case 10:return i=e.sent.data,u=(null==n?void 0:n.filter(function(e){return e.es_correcta}).length)||0,l=((null==n?void 0:n.length)||0)-u,d=n&&n.length>0?Math.round(u/n.length*100):0,p=new Set,null==n||n.forEach(function(e){var r=new Date(e.fecha_respuesta);p.add("".concat(r.getDate(),"/").concat(r.getMonth()+1,"/").concat(r.getFullYear()))}),f=Array.from(p),m=new Map,null==n||n.forEach(function(e){var r=m.get(e.pregunta_id)||{fallos:0,aciertos:0};e.es_correcta?r.aciertos++:r.fallos++,m.set(e.pregunta_id,r)}),h=Array.from(m.entries()).map(function(e){var r,t=(0,a.A)(e,2),n=t[0],o=t[1];return{preguntaId:n,totalFallos:o.fallos,totalAciertos:o.aciertos,pregunta:(null==i||null==(r=i.find(function(e){return e.id===n}))?void 0:r.pregunta)||"Desconocida"}}).sort(function(e,r){return r.totalFallos-e.totalFallos}).slice(0,5),e.abrupt("return",{testId:r,totalPreguntas:(null==i?void 0:i.length)||0,totalCorrectas:u,totalIncorrectas:l,porcentajeAcierto:d,fechasRealizacion:f,preguntasMasFalladas:h});case 22:case"end":return e.stop()}},e)}))).apply(this,arguments)}},52750:(e,r,t)=>{t.d(r,{A:()=>c}),t(12115);var a=t(73329),n=t.n(a),o=t(75780),s=t(95155);function c(e){var r=e.feature,t=e.featureDescription,a=e.benefits,c=void 0===a?[]:a,i=e.className,u=c.length>0?c:["Acceso ilimitado a todas las funcionalidades","Generaci\xf3n de contenido sin l\xedmites","Soporte prioritario","Nuevas funcionalidades en primicia"],l={ai_tutor_chat:{name:"Chat con IA",description:"Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas."},study_planning:{name:"Planificaci\xf3n de Estudios",description:"Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos."},summary_a1_a2:{name:"Res\xfamenes Avanzados",description:"Genera res\xfamenes inteligentes y estructurados de tus documentos de estudio."}}[r]||{name:r,description:t||"Esta funcionalidad avanzada te ayudar\xe1 a mejorar tu preparaci\xf3n."};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4 ".concat(void 0===i?"":i),children:(0,s.jsx)("div",{className:"max-w-2xl w-full",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(o.F5$,{className:"w-10 h-10"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:l.name}),(0,s.jsx)("p",{className:"text-blue-100 text-lg leading-relaxed",children:l.description})]}),(0,s.jsxs)("div",{className:"px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6",children:[(0,s.jsx)(o.usP,{className:"w-4 h-4 mr-2"}),"Funcionalidad Premium"]}),(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Actualiza tu plan para acceder"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"Esta funcionalidad est\xe1 disponible para usuarios con planes de pago. Actualiza ahora y desbloquea todo el potencial de OposiAI."})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"\xbfQu\xe9 obtienes al actualizar?"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map(function(e,r){return(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,s.jsx)(o.YrT,{className:"w-4 h-4 text-green-600"})}),(0,s.jsx)("span",{className:"text-gray-700",children:e})]},r)})})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(n(),{href:"/upgrade-plan",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,s.jsx)(o.ei4,{className:"w-5 h-5 mr-2"}),"Actualizar Plan"]}),(0,s.jsx)(n(),{href:"/app",className:"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors",children:"Volver al Dashboard"})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfTienes preguntas? ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Cont\xe1ctanos"})]})})]})]})})})}},57759:(e,r,t)=>{t.d(r,{iF:()=>i});var a=t(33311),n=t(28295),o=t.n(n),s=t(66618);function c(){return(c=_asyncToGenerator(_regeneratorRuntime.mark(function e(){var r,t,a;return _regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,supabase.auth.getSession();case 3:if(t=(r=e.sent).data,!(a=r.error)){e.next=10;break}if("Auth session missing!"!==a.message){e.next=9;break}return e.abrupt("return",{session:null,error:null});case 9:return e.abrupt("return",{session:null,error:a.message});case 10:return e.abrupt("return",{session:t.session,error:null});case 13:return e.prev=13,e.t0=e.catch(0),e.abrupt("return",{session:null,error:"Ha ocurrido un error inesperado al obtener la sesi\xf3n"});case 16:case"end":return e.stop()}},e,null,[[0,13]])}))).apply(this,arguments)}function i(){return u.apply(this,arguments)}function u(){return(u=(0,a.A)(o().mark(function e(){var r,t,a;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,s.N.auth.getUser();case 3:if(t=(r=e.sent).data.user,!(a=r.error)){e.next=10;break}if("Auth session missing!"!==a.message){e.next=9;break}return e.abrupt("return",{user:null,error:null});case 9:return e.abrupt("return",{user:null,error:a.message});case 10:return e.abrupt("return",{user:t,error:null});case 13:return e.prev=13,e.t0=e.catch(0),e.abrupt("return",{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"});case 16:case"end":return e.stop()}},e,null,[[0,13]])}))).apply(this,arguments)}},74100:(e,r,t)=>{t.d(r,{C9:()=>j,CM:()=>h,QE:()=>g,Sl:()=>x,Yp:()=>c,fW:()=>d,sj:()=>u,sq:()=>N,vW:()=>f});var a=t(33311),n=t(28295),o=t.n(n),s=t(55564);function c(e){return i.apply(this,arguments)}function i(){return(i=(0,a.A)(o().mark(function e(r){var t,a,n,c,i,u,l=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=l.length>1&&void 0!==l[1]&&l[1],e.prev=1,e.next=4,s.N.auth.getUser();case 4:if(n=e.sent.data.user){e.next=9;break}return console.error("No hay usuario autenticado para crear conversaci\xf3n"),e.abrupt("return",null);case 9:if(!t){e.next=12;break}return e.next=12,h();case 12:return e.next=14,s.N.from("conversaciones").insert([{titulo:r,activa:t,user_id:n.id}]).select();case 14:if(i=(c=e.sent).data,!(u=c.error)){e.next=20;break}return console.error("Error al crear conversaci\xf3n:",u),e.abrupt("return",null);case 20:return e.abrupt("return",(null==i||null==(a=i[0])?void 0:a.id)||null);case 23:return e.prev=23,e.t0=e.catch(1),console.error("Error inesperado al crear conversaci\xf3n:",e.t0),e.abrupt("return",null);case 27:case"end":return e.stop()}},e,null,[[1,23]])}))).apply(this,arguments)}function u(){return l.apply(this,arguments)}function l(){return(l=(0,a.A)(o().mark(function e(){var r,t,a,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,s.N.auth.getUser();case 3:if(r=e.sent.data.user){e.next=8;break}return console.error("No hay usuario autenticado para obtener conversaciones"),e.abrupt("return",[]);case 8:return e.next=10,s.N.from("conversaciones").select("*").eq("user_id",r.id).order("actualizado_en",{ascending:!1});case 10:if(a=(t=e.sent).data,!(n=t.error)){e.next=16;break}return console.error("Error al obtener conversaciones:",n),e.abrupt("return",[]);case 16:return e.abrupt("return",a||[]);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error inesperado al obtener conversaciones:",e.t0),e.abrupt("return",[]);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function d(e,r){return p.apply(this,arguments)}function p(){return(p=(0,a.A)(o().mark(function e(r,t){var a;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.N.from("conversaciones").update({titulo:t,actualizado_en:new Date().toISOString()}).eq("id",r);case 2:if(!(a=e.sent.error)){e.next=7;break}return console.error("Error al actualizar conversaci\xf3n:",a),e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function f(e){return m.apply(this,arguments)}function m(){return(m=(0,a.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,h();case 3:return e.next=5,s.N.from("conversaciones").update({activa:!0,actualizado_en:new Date().toISOString()}).eq("id",r);case 5:if(!(t=e.sent.error)){e.next=10;break}return console.error("Error al activar conversaci\xf3n:",t),e.abrupt("return",!1);case 10:return e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(0),console.error("Error inesperado al activar conversaci\xf3n:",e.t0),e.abrupt("return",!1);case 17:case"end":return e.stop()}},e,null,[[0,13]])}))).apply(this,arguments)}function h(){return b.apply(this,arguments)}function b(){return(b=(0,a.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,s.N.auth.getUser();case 3:if(r=e.sent.data.user){e.next=8;break}return console.error("No hay usuario autenticado para desactivar conversaciones"),e.abrupt("return",!1);case 8:return e.next=10,s.N.from("conversaciones").update({activa:!1}).eq("user_id",r.id).eq("activa",!0);case 10:if(!(t=e.sent.error)){e.next=15;break}return console.error("Error al desactivar todas las conversaciones:",t),e.abrupt("return",!1);case 15:return e.abrupt("return",!0);case 18:return e.prev=18,e.t0=e.catch(0),console.error("Error inesperado al desactivar conversaciones:",e.t0),e.abrupt("return",!1);case 22:case"end":return e.stop()}},e,null,[[0,18]])}))).apply(this,arguments)}function x(){return v.apply(this,arguments)}function v(){return(v=(0,a.A)(o().mark(function e(){var r,t,a,n,c,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,s.N.auth.getUser();case 3:if(r=e.sent.data.user){e.next=8;break}return console.warn("No hay usuario autenticado para obtener conversaci\xf3n activa"),e.abrupt("return",null);case 8:return e.next=10,s.N.from("conversaciones").select("*").eq("user_id",r.id);case 10:return(t=e.sent).data,(a=t.error)&&console.error("Error al obtener todas las conversaciones:",a),e.next=16,s.N.from("conversaciones").select("*").eq("user_id",r.id).eq("activa",!0).limit(1);case 16:if(c=(n=e.sent).data,!(i=n.error)){e.next=24;break}if(!("406"===i.code||i.message.includes("406"))){e.next=22;break}return e.abrupt("return",null);case 22:return console.error("Error al obtener conversaci\xf3n activa:",i),e.abrupt("return",null);case 24:return e.abrupt("return",c&&c.length>0?c[0]:null);case 27:return e.prev=27,e.t0=e.catch(0),console.error("Error inesperado al obtener conversaci\xf3n activa:",e.t0),e.abrupt("return",null);case 31:case"end":return e.stop()}},e,null,[[0,27]])}))).apply(this,arguments)}function g(e){return y.apply(this,arguments)}function y(){return(y=(0,a.A)(o().mark(function e(r){var t,a,n,c,i,u,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,s.N.from("conversaciones").select("id").eq("id",r.conversacion_id).single();case 3:if((a=e.sent).data,!(n=a.error)){e.next=9;break}return console.error("Error al verificar la conversaci\xf3n:",n),e.abrupt("return",null);case 9:return e.next=11,s.N.from("mensajes").insert([r]).select();case 11:if(i=(c=e.sent).data,!(u=c.error)){e.next=17;break}return console.error("Error al guardar mensaje:",u),e.abrupt("return",null);case 17:return e.next=19,s.N.from("conversaciones").update({actualizado_en:new Date().toISOString()}).eq("id",r.conversacion_id);case 19:return(l=e.sent.error)&&console.error("Error al actualizar la fecha de la conversaci\xf3n:",l),e.abrupt("return",(null==i||null==(t=i[0])?void 0:t.id)||null);case 25:return e.prev=25,e.t0=e.catch(0),console.error("Error inesperado al guardar mensaje:",e.t0),e.abrupt("return",null);case 29:case"end":return e.stop()}},e,null,[[0,25]])}))).apply(this,arguments)}function j(e){return w.apply(this,arguments)}function w(){return(w=(0,a.A)(o().mark(function e(r){var t,a,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.N.from("mensajes").select("*").eq("conversacion_id",r).order("timestamp",{ascending:!0});case 2:if(a=(t=e.sent).data,!(n=t.error)){e.next=8;break}return console.error("Error al obtener mensajes:",n),e.abrupt("return",[]);case 8:return e.abrupt("return",a||[]);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}function N(e){return _.apply(this,arguments)}function _(){return(_=(0,a.A)(o().mark(function e(r){var t,a,n,c,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,s.N.auth.getUser();case 3:if(t=e.sent.data.user){e.next=8;break}return console.error("No hay usuario autenticado para eliminar conversaci\xf3n"),e.abrupt("return",!1);case 8:return e.next=10,s.N.from("mensajes").delete().eq("conversacion_id",r);case 10:if(!(a=e.sent.error)){e.next=15;break}return console.error("Error al eliminar mensajes de la conversaci\xf3n:",a),e.abrupt("return",!1);case 15:return e.next=17,s.N.from("conversaciones").delete({count:"exact"}).eq("id",r).eq("user_id",t.id);case 17:if(c=(n=e.sent).error,i=n.count,!c){e.next=23;break}return console.error("Error al eliminar conversaci\xf3n:",c),e.abrupt("return",!1);case 23:if(0!==i){e.next=25;break}return e.abrupt("return",!1);case 25:return e.abrupt("return",!0);case 28:return e.prev=28,e.t0=e.catch(0),console.error("Error inesperado al eliminar conversaci\xf3n:",e.t0),e.abrupt("return",!1);case 32:case"end":return e.stop()}},e,null,[[0,28]])}))).apply(this,arguments)}},76538:(e,r,t)=>{t.d(r,{w:()=>m,x:()=>p});var a=t(33311),n=t(28295),o=t.n(n),s=t(55564),c=t(66430),i=t(2775),u=t(47493),l=t(5929);function d(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,a=Array(r);t<r;t++)a[t]=e[t];return a}function p(){return f.apply(this,arguments)}function f(){return(f=(0,a.A)(o().mark(function e(){var r,t,n,d,p,f,m,h,b,x,v,g;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,c.iF)();case 3:if(r=e.sent.user){e.next=7;break}throw Error("Usuario no autenticado");case 7:return e.next=9,s.N.from("documentos").select("id").eq("user_id",r.id);case 9:return t=e.sent.data,e.next=13,(0,i.oE)();case 13:return n=e.sent,e.next=16,(0,u.Lx)();case 16:return d=e.sent,e.next=19,(0,u.oC)();case 19:return p=e.sent,f=0,m=0,h=0,b=0,x=0,e.next=27,Promise.all(n.map(function(){var e=(0,a.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,l.yV)(r.id);case 2:return t=e.sent,f+=t.total,m+=t.paraHoy,h+=t.nuevas,b+=t.aprendiendo,x+=t.repasando,e.abrupt("return",{id:r.id,titulo:r.titulo,fechaCreacion:r.creado_en,paraHoy:t.paraHoy});case 9:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}()));case 27:return v=e.sent.sort(function(e,r){return new Date(r.fechaCreacion).getTime()-new Date(e.fechaCreacion).getTime()}).slice(0,5),g=d.map(function(e){return{id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,numeroPreguntas:e.numero_preguntas||0}}).slice(0,5),e.abrupt("return",{totalDocumentos:(null==t?void 0:t.length)||0,totalColeccionesFlashcards:n.length,totalTests:d.length,totalFlashcards:f,flashcardsParaHoy:m,flashcardsNuevas:h,flashcardsAprendiendo:b,flashcardsRepasando:x,testsRealizados:p.totalTests,porcentajeAcierto:p.porcentajeAcierto,coleccionesRecientes:v,testsRecientes:g});case 33:return e.prev=33,e.t0=e.catch(0),console.error("Error al obtener estad\xedsticas del dashboard:",e.t0),e.abrupt("return",{totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]});case 37:case"end":return e.stop()}},e,null,[[0,33]])}))).apply(this,arguments)}function m(){return h.apply(this,arguments)}function h(){return(h=(0,a.A)(o().mark(function e(){var r,t,a,n,u,l,p,f,m,h,b,x,v,g,y=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=y.length>0&&void 0!==y[0]?y[0]:10,e.prev=1,e.next=4,(0,c.iF)();case 4:if(e.sent.user){e.next=8;break}return e.abrupt("return",[]);case 8:return e.next=10,(0,i.oE)();case 10:if(0!==(t=e.sent).length){e.next=13;break}return e.abrupt("return",[]);case 13:return(a=new Date).setHours(23,59,59,999),e.next=17,s.N.from("progreso_flashcards").select("flashcard_id, proxima_revision, estado").lte("proxima_revision",a.toISOString()).order("proxima_revision",{ascending:!0}).limit(r);case 17:if(u=(n=e.sent).data,!(l=n.error)){e.next=23;break}return console.error("Error al obtener progreso de flashcards:",l),e.abrupt("return",[]);case 23:if(!(!u||0===u.length)){e.next=25;break}return e.abrupt("return",[]);case 25:return p=u.map(function(e){return e.flashcard_id}),e.next=28,s.N.from("flashcards").select("id, pregunta, coleccion_id").in("id",p);case 28:if(m=(f=e.sent).data,!(h=f.error)){e.next=34;break}return console.error("Error al obtener flashcards:",h),e.abrupt("return",[]);case 34:b=[],x=function(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return d(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return d(e,r)}}(e))){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:n}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return s=e.done,e},e:function(e){c=!0,o=e},f:function(){try{s||null==t.return||t.return()}finally{if(c)throw o}}}}(u),e.prev=36,g=o().mark(function e(){var r,a,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=v.value,(a=null==m?void 0:m.find(function(e){return e.id===r.flashcard_id}))&&(n=t.find(function(e){return e.id===a.coleccion_id}))&&b.push({id:a.id,pregunta:a.pregunta,coleccionTitulo:n.titulo,coleccionId:n.id,proximaRevision:r.proxima_revision,estado:r.estado||"nuevo"});case 3:case"end":return e.stop()}},e)}),x.s();case 39:if((v=x.n()).done){e.next=43;break}return e.delegateYield(g(),"t0",41);case 41:e.next=39;break;case 43:e.next=48;break;case 45:e.prev=45,e.t1=e.catch(36),x.e(e.t1);case 48:return e.prev=48,x.f(),e.finish(48);case 51:return e.abrupt("return",b);case 54:return e.prev=54,e.t2=e.catch(1),console.error("Error al obtener pr\xf3ximas flashcards:",e.t2),e.abrupt("return",[]);case 58:case"end":return e.stop()}},e,null,[[1,54],[36,45,48,51]])}))).apply(this,arguments)}},86520:(e,r,t)=>{t.d(r,{A:()=>m});var a=t(37711),n=t(33311),o=t(28295),s=t.n(o),c=t(12115),i=t(36343),u=t(87925),l=t(95155);function d(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function p(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?d(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):d(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var f=(0,c.forwardRef)(function(e,r){var t,a=e.onSelectionChange,o=(0,c.useState)([]),d=o[0],f=o[1],m=(0,c.useState)([]),h=m[0],b=m[1],x=(0,c.useState)(!0),v=x[0],g=x[1],y=(t=(0,n.A)(s().mark(function e(){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return g(!0),e.prev=1,e.next=4,(0,u.R1)();case 4:f(e.sent),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error al cargar documentos:",e.t0);case 11:return e.prev=11,g(!1),e.finish(11);case 14:case"end":return e.stop()}},e,null,[[1,8,11,14]])})),function(){return t.apply(this,arguments)});(0,c.useEffect)(function(){y()},[]),(0,c.useImperativeHandle)(r,function(){return{recargarDocumentos:y}});var j=d.map(function(e){return{value:e.id,label:"".concat(e.numero_tema?"Tema ".concat(e.numero_tema,": "):"").concat(e.titulo," ").concat(e.categoria?"(".concat(e.categoria,")"):"")}});return(0,l.jsxs)("div",{className:"mb-3",children:[(0,l.jsx)("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Selecciona los documentos para consultar:"}),(0,l.jsx)(i.Ay,{instanceId:"document-selector",isMulti:!0,isLoading:v,options:j,className:"basic-multi-select",classNamePrefix:"select",placeholder:"Selecciona uno o m\xe1s documentos...",noOptionsMessage:function(){return"No hay documentos disponibles"},onChange:function(e){b(e||[]),a(e.map(function(e){return d.find(function(r){return r.id===e.value})}).filter(Boolean))},value:h,styles:{control:function(e){return p(p({},e),{},{minHeight:"36px",fontSize:"14px"})},multiValue:function(e){return p(p({},e),{},{fontSize:"12px"})},placeholder:function(e){return p(p({},e),{},{fontSize:"14px"})}}}),0===h.length&&(0,l.jsx)("p",{className:"text-red-500 text-xs italic mt-0.5",children:"Debes seleccionar al menos un documento"})]})});f.displayName="DocumentSelector";let m=f},87925:(e,r,t)=>{t.d(r,{vW:()=>x.vW,fW:()=>x.fW,xq:()=>v.xq,qJ:()=>v.qJ,Yp:()=>x.Yp,_4:()=>y._4,CM:()=>x.CM,sq:()=>x.sq,Q3:()=>h,hE:()=>f,yK:()=>v.yK,QE:()=>x.QE,OA:()=>y.OA,oE:()=>v.oE,Sl:()=>x.Sl,sj:()=>x.sj,R1:()=>d,yV:()=>g.yV,wU:()=>g.wU,oC:()=>y.oC,dd:()=>y.dd,C9:()=>x.C9,hg:()=>y.hg,Kj:()=>y.Kj,Lx:()=>y.Lx,Gl:()=>y.Gl});var a=t(55564),n=t(37711),o=t(33311),s=t(28295),c=t.n(s),i=t(66430);function u(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?u(Object(t),!0).forEach(function(r){(0,n.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(){return p.apply(this,arguments)}function p(){return(p=(0,o.A)(c().mark(function e(){var r,t,n,o;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(r=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",[]);case 8:return e.next=10,a.N.from("documentos").select("*").eq("user_id",r.id).order("numero_tema",{ascending:!0});case 10:if(n=(t=e.sent).data,!(o=t.error)){e.next=16;break}return console.error("Error al obtener documentos:",o),e.abrupt("return",[]);case 16:return e.abrupt("return",n||[]);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error al obtener documentos:",e.t0),e.abrupt("return",[]);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function f(e){return m.apply(this,arguments)}function m(){return(m=(0,o.A)(c().mark(function e(r){var t,n,o,s,u,d;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,i.iF)();case 3:if(n=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",null);case 8:return o=l(l({},r),{},{user_id:n.id,tipo_original:r.tipo_original}),e.next=11,a.N.from("documentos").insert([o]).select();case 11:if(u=(s=e.sent).data,!(d=s.error)){e.next=17;break}return console.error("Error al guardar documento:",d),e.abrupt("return",null);case 17:return e.abrupt("return",(null==u||null==(t=u[0])?void 0:t.id)||null);case 20:return e.prev=20,e.t0=e.catch(0),console.error("Error al guardar documento:",e.t0),e.abrupt("return",null);case 24:case"end":return e.stop()}},e,null,[[0,20]])}))).apply(this,arguments)}function h(e){return b.apply(this,arguments)}function b(){return(b=(0,o.A)(c().mark(function e(r){var t,n,o,s;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("\uD83D\uDDD1️ Iniciando eliminaci\xf3n de documento:",r),e.next=4,(0,i.iF)();case 4:if(t=e.sent.user){e.next=9;break}return console.error("❌ No hay usuario autenticado para eliminar documento"),e.abrupt("return",!1);case 9:return console.log("\uD83D\uDC64 Usuario autenticado:",t.id),console.log("\uD83D\uDCC4 Eliminando documento ID:",r),e.next=13,a.N.from("documentos").delete({count:"exact"}).eq("id",r).eq("user_id",t.id);case 13:if(o=(n=e.sent).error,s=n.count,!o){e.next=19;break}return console.error("❌ Error al eliminar documento de Supabase:",o),e.abrupt("return",!1);case 19:if(console.log("✅ Documento eliminado exitosamente. Filas afectadas:",s),0!==s){e.next=23;break}return console.warn("⚠️ No se elimin\xf3 ning\xfan documento. Posibles causas: documento no existe o no pertenece al usuario"),e.abrupt("return",!1);case 23:return e.abrupt("return",!0);case 26:return e.prev=26,e.t0=e.catch(0),console.error("\uD83D\uDCA5 Error inesperado al eliminar documento:",e.t0),e.abrupt("return",!1);case 30:case"end":return e.stop()}},e,null,[[0,26]])}))).apply(this,arguments)}var x=t(74100),v=t(2775),g=t(5929),y=t(47493);t(76538)},96116:(e,r,t)=>{t.d(r,{A:()=>W});var a=t(37711),n=t(10631),o=t(33311),s=t(28295),c=t.n(s),i=t(12115),u=t(75780),l=t(17863),d=t(1448);function p(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function f(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?p(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):p(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={firstDayOfWeek:1,locale:"es-ES",dateFormat:"YYYY-MM-DD",displayFormat:"DD/MM/YYYY"},h=["Lunes","Martes","Mi\xe9rcoles","Jueves","Viernes","S\xe1bado","Domingo"],b=["LU","MA","MI","JU","VI","SA","DO"],x=["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"];function v(e){if(!e||"string"!=typeof e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return null;var r=new Date(e+"T00:00:00.000Z");if(isNaN(r.getTime()))return null;var t=r.getUTCFullYear(),a=String(r.getUTCMonth()+1).padStart(2,"0"),n=String(r.getUTCDate()).padStart(2,"0");return"".concat(t,"-").concat(a,"-").concat(n)!==e?null:r}function g(e){if(!e||isNaN(e.getTime()))return"";var r=e.getFullYear(),t=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0");return"".concat(r,"-").concat(t,"-").concat(a)}function y(e,r){var t,a=v(e);if(!a)return null;var n=(t=h.findIndex(function(e){return e.toLowerCase()===r.toLowerCase()}))>=0?t:-1;if(-1===n)return null;var o=new Date(a);return o.setDate(a.getDate()+n),o}function j(e,r){return new Date(e,r,1)}function w(e){var r;return r=new Date,!!e&&!!r&&e.getFullYear()===r.getFullYear()&&e.getMonth()===r.getMonth()&&e.getDate()===r.getDate()}function N(e,r,t){return e.getFullYear()===r&&e.getMonth()===t}function _(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return k(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return k(e,r)}}(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:n}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return s=e.done,e},e:function(e){c=!0,o=e},f:function(){try{s||null==t.return||t.return()}finally{if(c)throw o}}}}function k(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,a=Array(r);t<r;t++)a[t]=e[t];return a}function O(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function A(e,r){var t=g(e),a=r.mapaDias.get(t);return(null==a?void 0:a.tareas)||[]}function E(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function S(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?E(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):E(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var D="oposiciones-ia-calendario-prefs";function P(){try{var e,r=localStorage.getItem(D);if(!r)return T();var t=JSON.parse(r);return{mesActual:"number"==typeof t.mesActual&&t.mesActual>=0&&t.mesActual<=11?t.mesActual:void 0,yearActual:"number"==typeof t.yearActual&&t.yearActual>=2020&&t.yearActual<=2030?t.yearActual:void 0,fechaSeleccionada:"string"==typeof t.fechaSeleccionada&&(e=t.fechaSeleccionada)&&"string"==typeof e&&!isNaN(new Date(e).getTime())&&e.includes("T")?t.fechaSeleccionada:void 0,calendarioExpandido:"boolean"!=typeof t.calendarioExpandido||t.calendarioExpandido,primerDiaSemana:0===t.primerDiaSemana||1===t.primerDiaSemana?t.primerDiaSemana:1,vistaTamaño:["compacto","normal","grande"].includes(t.vistaTamaño)?t.vistaTamaño:"normal"}}catch(e){return console.warn("Error al leer preferencias del calendario:",e),T()}}function C(e){try{var r=P(),t=S(S({},r),e);localStorage.setItem(D,JSON.stringify(t))}catch(e){console.warn("Error al guardar preferencias del calendario:",e)}}function T(){var e=new Date;return{mesActual:e.getMonth(),yearActual:e.getFullYear(),fechaSeleccionada:void 0,calendarioExpandido:!0,primerDiaSemana:1,vistaTamaño:"normal"}}function F(e){var r;r=e?e.toISOString():void 0,C((0,a.A)({},"fechaSeleccionada",r))}function I(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function M(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?I(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):I(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var q=t(95155);let R=function(e){var r,t,o,s,c,l,d,p,h,k,E,I,R,z,Y,H,U,G,L,W,V,J,K,Q,$,Z,B,X,ee,er,et,ea,en,eo,es=e.plan,ec=e.progresoPlan,ei=e.fechaSeleccionada,eu=e.onFechaSeleccionada,el=e.onMesChanged,ed=e.className,ep=void 0===ed?"":ed,ef=(d=(t=(r=(0,i.useState)(T))[0],o=r[1],(0,i.useEffect)(function(){o(P())},[]),s=(0,i.useCallback)(function(e){o(function(r){var t=S(S({},r),e);return C(e),t})},[]),c=(0,i.useCallback)(function(e,r){s((0,a.A)({},e,r))},[s]),l={preferences:t,updatePreferences:s,updatePreference:c,clearPreferences:function(){try{localStorage.removeItem(D)}catch(e){console.warn("Error al limpiar preferencias del calendario:",e)}o(T())}}).preferences,p=l.updatePreference,k=(h=(0,i.useState)(function(){try{var e=!1,r={};["plan-calendario-mes","plan-calendario-year","plan-calendario-expandido"].forEach(function(t){var a=localStorage.getItem(t);if(null!==a){switch(e=!0,t){case"plan-calendario-mes":var n=parseInt(a);!isNaN(n)&&n>=0&&n<=11&&(r.mesActual=n);break;case"plan-calendario-year":var o=parseInt(a);!isNaN(o)&&o>=2020&&o<=2030&&(r.yearActual=o);break;case"plan-calendario-expandido":r.calendarioExpandido="true"===a}localStorage.removeItem(t)}}),e&&(C(r),console.log("Preferencias del calendario migradas exitosamente"))}catch(e){console.warn("Error al migrar preferencias antiguas:",e)}void 0!==d.primerDiaSemana&&(t=d.primerDiaSemana,m=f(f({},m),{},{firstDayOfWeek:t}));var t,a,n,o=new Date,s=function(){var e=P();if(!e.fechaSeleccionada)return null;try{var r=new Date(e.fechaSeleccionada);return isNaN(r.getTime())?null:r}catch(e){return null}}();return{yearActual:null!=(a=d.yearActual)?a:o.getFullYear(),mesActual:null!=(n=d.mesActual)?n:o.getMonth(),fechaSeleccionada:ei||s||null,fechasCalendario:[],diasCalendario:[]}}))[0],E=h[1],R=(I=(0,i.useState)(!1))[0],z=I[1],H=(Y=(0,i.useState)(null))[0],U=Y[1],G=(0,i.useRef)(es),L=(0,i.useRef)(ec),W=G.current!==es,V=JSON.stringify(L.current)!==JSON.stringify(ec),(0,i.useEffect)(function(){G.current=es,L.current=ec},[es,ec]),J=(0,i.useMemo)(function(){if(!es||!W&&!V&&G.current&&L.current)return null;try{z(!0),U(null);var e=function(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=[],s=new Map,c=function(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?O(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):O(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}({incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!1},t);if(!e||!e.semanas||!Array.isArray(e.semanas)){return o.push("Plan de estudios inv\xe1lido o sin semanas"),u=o,{datosPlan:{fechaInicio:new Date,fechaFin:new Date,totalSemanas:0,mapaDias:new Map,rangoFechas:{minYear:new Date().getFullYear(),maxYear:new Date().getFullYear(),minMonth:new Date().getMonth(),maxMonth:new Date().getMonth()}},estadisticas:{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:u}}if(c.validarFechas){var i=function(e){var r=[],t=[],a=[];if(!e.semanas||!Array.isArray(e.semanas))return r.push("El plan no contiene semanas v\xe1lidas"),{esValido:!1,errores:r,advertencias:t,fechasProblematicas:a};var n,o=null,s=_(e.semanas);try{for(s.s();!(n=s.n()).done;){var c=n.value;if(!c||"number"!=typeof c.numero){r.push("Semana inv\xe1lida encontrada");continue}var i=v(c.fechaInicio),u=v(c.fechaFin);if(!i){r.push("Fecha de inicio inv\xe1lida en semana ".concat(c.numero,": ").concat(c.fechaInicio)),a.push({semana:c.numero,fecha:c.fechaInicio,problema:"Fecha de inicio inv\xe1lida"});continue}if(!u){r.push("Fecha de fin inv\xe1lida en semana ".concat(c.numero,": ").concat(c.fechaFin)),a.push({semana:c.numero,fecha:c.fechaFin,problema:"Fecha de fin inv\xe1lida"});continue}u<=i&&r.push("La fecha de fin debe ser posterior a la de inicio en semana ".concat(c.numero)),o&&i<o&&t.push("La semana ".concat(c.numero," comienza antes de que termine la anterior")),o=u}}catch(e){s.e(e)}finally{s.f()}return{esValido:0===r.length,errores:r,advertencias:t,fechasProblematicas:a}}(e);i.esValido||o.push.apply(o,(0,n.A)(i.errores))}var u,l,d=null,p=null,f=_(e.semanas);try{for(f.s();!(l=f.n()).done;){var m=l.value;if(!m||"number"!=typeof m.numero){o.push("Semana inv\xe1lida encontrada");continue}var h=v(m.fechaInicio),b=v(m.fechaFin);if(!h||!b){o.push("Fechas inv\xe1lidas en semana ".concat(m.numero));continue}if((!d||h<d)&&(d=h),(!p||b>p)&&(p=b),m.dias&&Array.isArray(m.dias)){var x,j=_(m.dias);try{for(j.s();!(x=j.n()).done;){var N=x.value,k=function(e,r,t,a){if(!e||!e.dia)return{error:"D\xeda inv\xe1lido en semana ".concat(r.numero)};var n,o,s,c=y(r.fechaInicio,e.dia);if(!c)return{error:"No se pudo calcular la fecha para ".concat(e.dia," en semana ").concat(r.numero)};var i=[];if(e.tareas&&Array.isArray(e.tareas)){var u,l=_(e.tareas);try{for(l.s();!(u=l.n()).done;)if(function(){var a=u.value;if(!a||!a.titulo)return 1;var n=t.find(function(t){return t.semana_numero===r.numero&&t.dia_nombre===e.dia&&t.tarea_titulo===a.titulo});i.push({tarea:a,semanaNumero:r.numero,diaNombre:e.dia,completada:(null==n?void 0:n.completado)||!1,fechaCompletado:null==n?void 0:n.fecha_completado})}())continue}catch(e){l.e(e)}finally{l.f()}}a.ordenarTareasPorTipo&&i.sort(function(e,r){var t={estudio:0,repaso:1,practica:2,evaluacion:3};return(t[e.tarea.tipo]||99)-(t[r.tarea.tipo]||99)});var d=i.length,p=i.filter(function(e){return e.completada}).length,f=d>0?p/d*100:0,m=(n=c,o=d,s=p,w(n)?"hoy":0===o?"normal":0===s?"con-tareas":s===o?"completado":"parcial");return{diaCalendario:{fecha:c,dia:c.getDate(),estaEnMesActual:!0,esHoy:w(c),estado:m,tareas:i,totalTareas:d,tareasCompletadas:p,porcentajeCompletado:f}}}(N,m,r,c);if(k.error){o.push(k.error);continue}if(k.diaCalendario){var A=g(k.diaCalendario.fecha);s.set(A,k.diaCalendario),k.diaCalendario.totalTareas}}}catch(e){j.e(e)}finally{j.f()}}}}catch(e){f.e(e)}finally{f.f()}return{datosPlan:{fechaInicio:d||new Date,fechaFin:p||new Date,totalSemanas:e.semanas.length,mapaDias:s,rangoFechas:function(e,r){if(!e||!r){var t=new Date;return{minYear:t.getFullYear(),maxYear:t.getFullYear(),minMonth:t.getMonth(),maxMonth:t.getMonth()}}return{minYear:e.getFullYear(),maxYear:r.getFullYear(),minMonth:e.getMonth(),maxMonth:r.getMonth()}}(d,p)},estadisticas:c.calcularEstadisticas?function(e,r){var t,a=0,n=0,o=0,s=0,c=_(e.values());try{for(c.s();!(t=c.n()).done;){var i=t.value;i.totalTareas>0&&(o++,a+=i.totalTareas,n+=i.tareasCompletadas,i.tareasCompletadas===i.totalTareas&&s++)}}catch(e){c.e(e)}finally{c.f()}return{totalTareas:a,tareasCompletadas:n,porcentajeGeneral:a>0?n/a*100:0,diasConTareas:o,diasCompletados:s}}(s,0):{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:o}}(es,ec,{incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!0});return e.errores.length>0&&(console.warn("Errores al procesar el plan:",e.errores),U(e.errores[0])),e.datosPlan}catch(e){return U(e instanceof Error?e.message:"Error desconocido al procesar el plan"),console.error("Error al procesar plan para calendario:",e),null}finally{z(!1)}},[es,ec,W,V]),K=(0,i.useMemo)(function(){return function(e,r){for(var t,a=[],n=j(e,r),o=new Date(e,r+1,0),s=(t=j(e,r).getDay(),1===m.firstDayOfWeek?0===t?6:t-1:t),c=s-1;c>=0;c--){var i=new Date(n);i.setDate(i.getDate()-(c+1)),a.push(i)}for(var u=1;u<=o.getDate();u++)a.push(new Date(e,r,u));for(var l=42-a.length,d=1;d<=l;d++){var p=new Date(o);p.setDate(p.getDate()+d),a.push(p)}return a}(k.yearActual,k.mesActual)},[k.yearActual,k.mesActual]),Q=(0,i.useMemo)(function(){return J?K.map(function(e){var r=g(e),t=J.mapaDias.get(r);return t?M(M({},t),{},{estaEnMesActual:N(e,k.yearActual,k.mesActual),esHoy:w(e)}):{fecha:e,dia:e.getDate(),estaEnMesActual:N(e,k.yearActual,k.mesActual),esHoy:w(e),estado:w(e)?"hoy":N(e,k.yearActual,k.mesActual)?"normal":"fuera-mes",tareas:[],totalTareas:0,tareasCompletadas:0,porcentajeCompletado:0}}):[]},[K,J,k.yearActual,k.mesActual]),(0,i.useEffect)(function(){E(function(e){return M(M({},e),{},{fechasCalendario:K,diasCalendario:Q})})},[K,Q]),$=(0,i.useCallback)(function(e){E(function(r){var t,a,n,o,s="anterior"===e?(t=r.yearActual,0===(a=r.mesActual)?{year:t-1,month:11}:{year:t,month:a-1}):(n=r.yearActual,11===(o=r.mesActual)?{year:n+1,month:0}:{year:n,month:o+1}),c=s.year,i=s.month;return p("yearActual",c),p("mesActual",i),M(M({},r),{},{yearActual:c,mesActual:i})})},[p]),Z=(0,i.useCallback)(function(e,r){p("yearActual",e),p("mesActual",r),E(function(t){return M(M({},t),{},{yearActual:e,mesActual:r})})},[p]),B=(0,i.useCallback)(function(e){F(e),E(function(r){return M(M({},r),{},{fechaSeleccionada:e})})},[]),X=(0,i.useCallback)(function(){var e=new Date;p("yearActual",e.getFullYear()),p("mesActual",e.getMonth()),F(e),E(function(r){return M(M({},r),{},{yearActual:e.getFullYear(),mesActual:e.getMonth(),fechaSeleccionada:e})})},[p]),ee=(0,i.useCallback)(function(e){return J?A(e,J):[]},[J]),er=(0,i.useCallback)(function(e){var r,t;return J?(r=g(e),(null==(t=J.mapaDias.get(r))?void 0:t.estado)||"normal"):"normal"},[J]),et=(0,i.useCallback)(function(e){if(!J)return!1;var r=g(e);return void 0!==J.mapaDias.get(r)||e>=J.fechaInicio&&e<=J.fechaFin},[J]),ea=(0,i.useMemo)(function(){var e=new Date(k.yearActual,k.mesActual);return"".concat(x[e.getMonth()]," ").concat(k.yearActual)},[k.yearActual,k.mesActual]),en=(0,i.useMemo)(function(){return k.fechaSeleccionada&&J?A(k.fechaSeleccionada,J):[]},[k.fechaSeleccionada,J]),eo=(0,i.useMemo)(function(){if(!k.fechaSeleccionada||!J)return null;var e=g(k.fechaSeleccionada),r=J.mapaDias.get(e);return r?{total:r.totalTareas,completadas:r.tareasCompletadas,porcentaje:r.porcentajeCompletado}:null},[k.fechaSeleccionada,J]),{estadoCalendario:k,datosPlan:J,isLoading:R,error:H,navegarMes:$,irAMes:Z,seleccionarFecha:B,irAHoy:X,obtenerTareasDelDia:ee,obtenerEstadoDia:er,esFechaSeleccionable:et,tituloMes:ea,tareasDelDiaSeleccionado:en,estadisticasDelDia:eo}),em=ef.estadoCalendario,eh=ef.isLoading,eb=ef.error,ex=ef.navegarMes,ev=ef.irAHoy,eg=ef.tituloMes,ey=ef.esFechaSeleccionable,ej=function(e){ey(e.fecha)&&eu(e.fecha)},ew=function(e){ex(e),el&&el(em.yearActual,em.mesActual)},eN=function(e,r){("Enter"===e.key||" "===e.key)&&(e.preventDefault(),ej(r))},e_=function(e,r){("Enter"===e.key||" "===e.key)&&(e.preventDefault(),r())},ek=function(e){var r=["relative","aspect-square","flex","items-center","justify-center","text-xs","sm:text-sm","font-medium","cursor-pointer","calendario-day-hover","calendario-estado-transition","rounded-none","sm:rounded-lg","border","border-transparent","min-h-[2.5rem]","sm:min-h-[3rem]"];switch(e.estaEnMesActual?r.push("text-gray-700","hover:text-gray-900"):r.push("text-gray-300","hover:text-gray-400"),e.estado){case"hoy":r.push("bg-blue-100","text-blue-900","border-blue-300","font-bold","ring-2","ring-blue-400","ring-opacity-50","calendario-pulso");break;case"con-tareas":r.push("bg-orange-50","text-orange-800","border-orange-200","hover:bg-orange-100","hover:border-orange-300");break;case"completado":r.push("bg-green-50","text-green-800","border-green-200","hover:bg-green-100","hover:border-green-300");break;case"parcial":r.push("bg-yellow-50","text-yellow-800","border-yellow-200","hover:bg-yellow-100","hover:border-yellow-300");break;case"normal":e.estaEnMesActual&&r.push("hover:bg-gray-50","hover:border-gray-200")}return ei&&e.fecha.getTime()===ei.getTime()&&r.push("ring-2","ring-blue-500","ring-opacity-75","bg-blue-50","border-blue-300"),ey(e.fecha)||r.push("cursor-not-allowed","opacity-50"),r.join(" ")},eO=function(e){if(0===e.totalTareas)return null;var r=e.porcentajeCompletado,t="bg-orange-400";return 100===r?t="bg-green-400":r>0&&(t="bg-yellow-400"),(0,q.jsx)("div",{className:"absolute bottom-1 right-1",children:(0,q.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t),title:"".concat(e.tareasCompletadas,"/").concat(e.totalTareas," tareas completadas")})})};return eb?(0,q.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(ep),children:[(0,q.jsxs)("div",{className:"flex items-center text-red-800",children:[(0,q.jsx)(u.wIk,{className:"w-5 h-5 mr-2"}),(0,q.jsx)("span",{className:"font-medium",children:"Error en el calendario"})]}),(0,q.jsx)("p",{className:"text-red-600 text-sm mt-1",children:eb})]}):(0,q.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-fade-in ".concat(ep),children:[(0,q.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,q.jsxs)("div",{className:"flex items-center justify-between",children:[(0,q.jsx)("button",{onClick:function(){return ew("anterior")},onKeyDown:function(e){return e_(e,function(){return ew("anterior")})},className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes anterior",tabIndex:0,children:(0,q.jsx)(u.irw,{className:"w-5 h-5 text-gray-600"})}),(0,q.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,q.jsx)(u.wIk,{className:"w-4 h-4 text-gray-600 hidden sm:block"}),(0,q.jsx)("h3",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:eg})]}),(0,q.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,q.jsx)("button",{onClick:ev,onKeyDown:function(e){return e_(e,ev)},className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",title:"Ir a hoy","aria-label":"Ir a hoy",tabIndex:0,children:(0,q.jsx)(u.V5Y,{className:"w-4 h-4 text-gray-600"})}),(0,q.jsx)("button",{onClick:function(){return ew("siguiente")},onKeyDown:function(e){return e_(e,function(){return ew("siguiente")})},className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes siguiente",tabIndex:0,children:(0,q.jsx)(u.fOo,{className:"w-5 h-5 text-gray-600"})})]})]})}),(0,q.jsx)("div",{className:"grid grid-cols-7 bg-gray-100 border-b border-gray-200",children:b.map(function(e){return(0,q.jsx)("div",{className:"py-1 sm:py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wide",children:e},e)})}),(0,q.jsx)("div",{className:"grid grid-cols-7 gap-0",children:eh?Array.from({length:42},function(e,r){return(0,q.jsx)("div",{className:"aspect-square flex items-center justify-center border-r border-b border-gray-100 last:border-r-0",children:(0,q.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded animate-pulse"})},r)}):em.diasCalendario.map(function(e,r){return(0,q.jsx)("div",{className:"border-r border-b border-gray-100 last:border-r-0 ".concat(5===Math.floor(r/7)?"border-b-0":""),children:(0,q.jsxs)("button",{onClick:function(){return ej(e)},onKeyDown:function(r){return eN(r,e)},className:"".concat(ek(e)," focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"),disabled:!ey(e.fecha),tabIndex:ey(e.fecha)?0:-1,"aria-label":"".concat(e.dia," de ").concat(eg).concat(e.totalTareas>0?", ".concat(e.totalTareas," tareas"):"").concat(e.esHoy?", hoy":"").concat("completado"===e.estado?", completado":"parcial"===e.estado?", parcialmente completado":"con-tareas"===e.estado?", con tareas pendientes":""),"aria-pressed":ei&&e.fecha.getTime()===ei.getTime()?"true":"false",children:[e.dia,eO(e)]})},r)})}),(0,q.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 border-t border-gray-200",children:(0,q.jsxs)("div",{className:"flex items-center justify-center space-x-2 sm:space-x-4 text-xs text-gray-600",children:[(0,q.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,q.jsx)("div",{className:"w-2 h-2 bg-orange-400 rounded-full"}),(0,q.jsx)("span",{className:"hidden sm:inline",children:"Pendientes"}),(0,q.jsx)("span",{className:"sm:hidden",children:"Pend."})]}),(0,q.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,q.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),(0,q.jsx)("span",{className:"hidden sm:inline",children:"Parcial"}),(0,q.jsx)("span",{className:"sm:hidden",children:"Parc."})]}),(0,q.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,q.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,q.jsx)("span",{className:"hidden sm:inline",children:"Completado"}),(0,q.jsx)("span",{className:"sm:hidden",children:"Comp."})]})]})})]})},z=function(e){var r=e.fecha,t=e.tareas,a=e.isLoading,n=void 0!==a&&a,o=e.onTareaClick,s=e.className,c=void 0===s?"":s,l=function(e){switch(e){case"estudio":return(0,q.jsx)(u.H9b,{className:"w-4 h-4"});case"repaso":return(0,q.jsx)(u.jTZ,{className:"w-4 h-4"});case"practica":return(0,q.jsx)(u.aze,{className:"w-4 h-4"});case"evaluacion":return(0,q.jsx)(u.x_j,{className:"w-4 h-4"});default:return(0,q.jsx)(u.Ohp,{className:"w-4 h-4"})}},d=function(e){switch(e){case"estudio":return"text-blue-600 bg-blue-50 border-blue-200";case"repaso":return"text-green-600 bg-green-50 border-green-200";case"practica":return"text-purple-600 bg-purple-50 border-purple-200";case"evaluacion":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},p=function(e){switch(e){case"estudio":return"Estudio";case"repaso":return"Repaso";case"practica":return"Pr\xe1ctica";case"evaluacion":return"Evaluaci\xf3n";default:return"Tarea"}},f=i.useMemo(function(){if(!t.length)return null;var e=t.filter(function(e){return e.completada}).length,r=t.length,a=Math.round(e/r*100);return{completadas:e,total:r,porcentaje:a}},[t]),h=function(e){o&&o(e)},b=function(e,r){("Enter"===e.key||" "===e.key)&&(e.preventDefault(),h(r))};return r?(0,q.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-slide-in ".concat(c),children:[(0,q.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,q.jsxs)("div",{className:"flex items-center justify-between",children:[(0,q.jsxs)("div",{children:[(0,q.jsx)("h4",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:!r||isNaN(r.getTime())?"":r.toLocaleDateString(m.locale,{day:"2-digit",month:"2-digit",year:"numeric"})}),f&&(0,q.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600",children:[f.completadas," de ",f.total," tareas",(0,q.jsx)("span",{className:"hidden sm:inline",children:" completadas"})]})]}),f&&(0,q.jsx)("div",{className:"text-right",children:(0,q.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(100===f.porcentaje?"bg-green-100 text-green-800":f.porcentaje>0?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:[f.porcentaje,"%"]})})]})}),(0,q.jsx)("div",{className:"p-3 sm:p-4",children:n?(0,q.jsx)("div",{className:"space-y-3",children:Array.from({length:3},function(e,r){return(0,q.jsx)("div",{className:"animate-pulse",children:(0,q.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,q.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded"}),(0,q.jsxs)("div",{className:"flex-1",children:[(0,q.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,q.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},r)})}):0===t.length?(0,q.jsxs)("div",{className:"text-center py-6",children:[(0,q.jsx)(u.y3G,{className:"w-8 h-8 mx-auto mb-2 text-gray-400"}),(0,q.jsx)("p",{className:"text-sm text-gray-500",children:"No hay tareas programadas"}),(0,q.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"para este d\xeda"})]}):(0,q.jsx)("div",{className:"space-y-3",children:t.map(function(e,r){return(0,q.jsx)("div",{className:"border rounded-lg p-3 calendario-estado-transition ".concat(o?"cursor-pointer hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 calendario-day-hover":""," ").concat(e.completada?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-gray-300"),onClick:function(){return h(e)},onKeyDown:function(r){return b(r,e)},tabIndex:o?0:-1,role:o?"button":void 0,"aria-label":"Tarea: ".concat(e.tarea.titulo).concat(e.completada?", completada":", pendiente"),children:(0,q.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,q.jsx)("div",{className:"flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center ".concat(e.completada?"bg-green-500 border-green-500":"border-gray-300"),children:e.completada&&(0,q.jsx)(u.YrT,{className:"w-3 h-3 text-white"})}),(0,q.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,q.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,q.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ".concat(d(e.tarea.tipo)),children:[l(e.tarea.tipo),(0,q.jsx)("span",{className:"ml-1",children:p(e.tarea.tipo)})]}),e.tarea.duracionEstimada&&(0,q.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,q.jsx)(u.Ohp,{className:"w-3 h-3 mr-1"}),e.tarea.duracionEstimada]})]}),(0,q.jsx)("h5",{className:"font-medium text-sm ".concat(e.completada?"text-green-800 line-through":"text-gray-900"),children:e.tarea.titulo}),e.tarea.descripcion&&(0,q.jsx)("p",{className:"text-xs mt-1 ".concat(e.completada?"text-green-600":"text-gray-600"),children:e.tarea.descripcion}),(0,q.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,q.jsxs)("div",{className:"text-xs text-gray-500",children:["Semana ",e.semanaNumero," • ",e.diaNombre]}),e.completada&&e.fechaCompletado&&(0,q.jsx)("div",{className:"text-xs text-green-600",children:"✓ Completada"})]})]})]})},r)})})}),t.length>0&&!n&&(0,q.jsx)("div",{className:"bg-gray-50 px-4 py-2 border-t border-gray-200",children:(0,q.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,q.jsxs)("span",{children:[t.filter(function(e){return e.completada}).length," completadas"]}),(0,q.jsxs)("span",{children:[t.filter(function(e){return!e.completada}).length," pendientes"]})]})})]}):(0,q.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 ".concat(c),children:(0,q.jsxs)("div",{className:"text-center text-gray-500",children:[(0,q.jsx)(u.wIk,{className:"w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400"}),(0,q.jsx)("p",{className:"text-xs sm:text-sm",children:"Selecciona un d\xeda en el calendario"}),(0,q.jsx)("p",{className:"text-xs text-gray-400 mt-1 hidden sm:block",children:"para ver las tareas programadas"})]})})},Y=function(e){var r=e.isOpen,t=e.onClose,a=e.plan,n=e.progresoPlan,o=e.fechaSeleccionada,s=e.onFechaSeleccionada,c=e.tareasDelDia,l=e.onTareaClick;return((0,i.useEffect)(function(){var e=function(e){"Escape"===e.key&&t()};return r&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),function(){document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[r,t]),r)?(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden calendario-modal-overlay",onClick:t,"aria-hidden":"true"}),(0,q.jsx)("div",{className:"fixed inset-0 z-50 lg:hidden",children:(0,q.jsx)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:(0,q.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-t-lg sm:rounded-lg text-left overflow-hidden shadow-xl calendario-modal-content sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,q.jsxs)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between",children:[(0,q.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Calendario del Plan"}),(0,q.jsx)("button",{onClick:t,className:"p-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Cerrar calendario",children:(0,q.jsx)(u.yGN,{className:"w-5 h-5 text-gray-600"})})]}),(0,q.jsxs)("div",{className:"bg-white px-4 py-4 space-y-4 max-h-[70vh] overflow-y-auto",children:[(0,q.jsx)(R,{plan:a,progresoPlan:n,fechaSeleccionada:o,onFechaSeleccionada:s}),o&&(0,q.jsx)(z,{fecha:o,tareas:c,onTareaClick:function(e){l&&l(e),t()}})]}),(0,q.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-t border-gray-200",children:(0,q.jsx)("button",{onClick:t,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Cerrar"})})]})})})]}):null};function H(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return U(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return U(e,r)}}(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:n}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return s=e.done,e},e:function(e){c=!0,o=e},f:function(){try{s||null==t.return||t.return()}finally{if(c)throw o}}}}function U(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,a=Array(r);t<r;t++)a[t]=e[t];return a}function G(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function L(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?G(Object(t),!0).forEach(function(r){(0,a.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):G(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let W=function(e){var r,t,a=e.plan,s=e.temarioId,p=(0,i.useState)([]),f=p[0],m=p[1],h=(0,i.useState)(null),b=h[0],x=h[1],v=(0,i.useState)(!0),j=v[0],w=v[1],N=(0,i.useState)(null),_=N[0],k=N[1],O=(0,i.useState)(!1),A=O[0],E=O[1],S=(0,i.useRef)({});(0,i.useEffect)(function(){D()},[s]);var D=(r=(0,o.A)(c().mark(function e(){var r;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,l.fF)(s);case 3:if(r=e.sent){e.next=10;break}return console.warn("No se encontr\xf3 plan activo para el temario:",s),x(null),m([]),w(!1),e.abrupt("return");case 10:return x(r.id),e.next=13,(0,l.$S)(r.id);case 13:m(e.sent),e.next=22;break;case 17:e.prev=17,e.t0=e.catch(0),console.error("Error al cargar progreso:",e.t0),x(null),m([]);case 22:return e.prev=22,w(!1),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[0,17,22,25]])})),function(){return r.apply(this,arguments)}),P=(t=(0,o.A)(c().mark(function e(r,t,a){var o,s;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(b){e.next=3;break}return d.oR.error("No se pudo identificar el plan de estudios"),e.abrupt("return");case 3:return e.prev=3,s=!(null!=(o=f.find(function(e){return e.semana_numero===t&&e.dia_nombre===a&&e.tarea_titulo===r.titulo}))&&o.completado),e.next=8,(0,l.d7)(b,t,a,r.titulo,r.tipo,s);case 8:e.sent?(m(function(e){var o=e.findIndex(function(e){return e.semana_numero===t&&e.dia_nombre===a&&e.tarea_titulo===r.titulo});if(!(o>=0))return[].concat((0,n.A)(e),[{id:"temp-".concat(Date.now()),plan_id:b,user_id:"",semana_numero:t,dia_nombre:a,tarea_titulo:r.titulo,tarea_tipo:r.tipo,completado:s,fecha_completado:s?new Date().toISOString():void 0,creado_en:new Date().toISOString(),actualizado_en:new Date().toISOString()}]);var c=(0,n.A)(e);return c[o]=L(L({},c[o]),{},{completado:s,fecha_completado:s?new Date().toISOString():void 0}),c}),d.oR.success(s?"Tarea completada":"Tarea marcada como pendiente")):d.oR.error("Error al actualizar el progreso"),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(3),console.error("Error al actualizar tarea:",e.t0),d.oR.error("Error al actualizar el progreso");case 16:case"end":return e.stop()}},e,null,[[3,12]])})),function(e,r,a){return t.apply(this,arguments)}),C=function(e,r,t){return f.some(function(a){return a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo&&a.completado})},T=function(e){if(k(e),a&&a.semanas){var r,t=H(a.semanas);try{for(t.s();!(r=t.n()).done;){var n,o=r.value,s=H(o.dias||[]);try{for(s.s();!(n=s.n()).done;){var c=n.value,i=y(o.fechaInicio,c.dia);if(i&&g(i)===g(e)){var u=S.current[o.numero];u&&u.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"});return}}}catch(e){s.e(e)}finally{s.f()}}}catch(e){t.e(e)}finally{t.f()}}},F=function(){if(!_||!a||!a.semanas)return[];var e,r=[],t=H(a.semanas);try{for(t.s();!(e=t.n()).done;)!function(){var t,a=e.value,n=H(a.dias||[]);try{for(n.s();!(t=n.n()).done;)!function(){var e=t.value,n=y(a.fechaInicio,e.dia);if(n&&g(n)===g(_)){var o,s=H(e.tareas||[]);try{for(s.s();!(o=s.n()).done;)!function(){var t,n=o.value,s=C(n,a.numero,e.dia);r.push({tarea:n,semanaNumero:a.numero,diaNombre:e.dia,completada:s,fechaCompletado:null==(t=f.find(function(r){return r.semana_numero===a.numero&&r.dia_nombre===e.dia&&r.tarea_titulo===n.titulo}))?void 0:t.fecha_completado})}()}catch(e){s.e(e)}finally{s.f()}}}()}catch(e){n.e(e)}finally{n.f()}}()}catch(e){t.e(e)}finally{t.f()}return r},I=function(e){var r=S.current[e.semanaNumero];r&&r.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})},M=function(){if(!a||!a.semanas||!Array.isArray(a.semanas))return{completadas:0,total:0,porcentaje:0};var e=a.semanas.reduce(function(e,r){return r&&r.dias&&Array.isArray(r.dias)?e+r.dias.reduce(function(e,r){return r&&r.tareas&&Array.isArray(r.tareas)?e+r.tareas.length:e},0):e},0),r=f.filter(function(e){return e.completado}).length;return{completadas:r,total:e,porcentaje:e>0?Math.round(r/e*100):0}}();return j?(0,q.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,q.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,q.jsx)("span",{className:"ml-3 text-gray-600",children:"Cargando progreso..."})]}):a?s&&""!==s.trim()?(0,q.jsxs)("div",{className:"space-y-6",children:[(0,q.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,q.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Introducci\xf3n"}),(0,q.jsx)("p",{className:"text-blue-800",children:a.introduccion||"Introducci\xf3n no disponible"})]}),(0,q.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4",children:[(0,q.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,q.jsx)("h3",{className:"font-semibold text-gray-900",children:"Progreso General"}),(0,q.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[M.porcentaje,"%"]})]}),(0,q.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,q.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(M.porcentaje,"%")}})}),(0,q.jsxs)("p",{className:"text-sm text-gray-600",children:[M.completadas," de ",M.total," tareas completadas"]})]}),a.resumen&&(0,q.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,q.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,q.jsxs)("div",{className:"flex items-center",children:[(0,q.jsx)(u.Ohp,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,q.jsxs)("div",{children:[(0,q.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Total"}),(0,q.jsx)("p",{className:"font-semibold",children:a.resumen.tiempoTotalEstudio||"No disponible"})]})]})}),(0,q.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,q.jsxs)("div",{className:"flex items-center",children:[(0,q.jsx)(u.H9b,{className:"w-5 h-5 text-green-600 mr-2"}),(0,q.jsxs)("div",{children:[(0,q.jsx)("p",{className:"text-sm text-gray-600",children:"Temas"}),(0,q.jsx)("p",{className:"font-semibold",children:a.resumen.numeroTemas||"No disponible"})]})]})}),(0,q.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,q.jsxs)("div",{className:"flex items-center",children:[(0,q.jsx)(u.x_j,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,q.jsxs)("div",{children:[(0,q.jsx)("p",{className:"text-sm text-gray-600",children:"Estudio Nuevo"}),(0,q.jsx)("p",{className:"font-semibold",children:a.resumen.duracionEstudioNuevo||"No disponible"})]})]})}),(0,q.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,q.jsxs)("div",{className:"flex items-center",children:[(0,q.jsx)(u.jTZ,{className:"w-5 h-5 text-orange-600 mr-2"}),(0,q.jsxs)("div",{children:[(0,q.jsx)("p",{className:"text-sm text-gray-600",children:"Repaso Final"}),(0,q.jsx)("p",{className:"font-semibold",children:a.resumen.duracionRepasoFinal||"No disponible"})]})]})})]}),a.semanas&&a.semanas.length>0&&(0,q.jsxs)("div",{className:"space-y-6",children:[(0,q.jsxs)("div",{className:"flex items-center justify-between",children:[(0,q.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,q.jsx)(u.wIk,{className:"w-5 h-5 mr-2"}),"Cronograma Semanal"]}),(0,q.jsxs)("button",{onClick:function(){return E(!0)},className:"lg:hidden flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",children:[(0,q.jsx)(u.wIk,{className:"w-4 h-4 mr-2"}),"Ver Calendario"]})]}),(0,q.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,q.jsx)("div",{className:"lg:col-span-8 space-y-6",children:a.semanas.map(function(e,r){return(0,q.jsxs)("div",{ref:function(r){S.current[e.numero]=r},className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,q.jsxs)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:[(0,q.jsxs)("div",{className:"flex items-center justify-between",children:[(0,q.jsxs)("h4",{className:"text-lg font-semibold text-gray-900",children:["Semana ",(null==e?void 0:e.numero)||"N/A"]}),(0,q.jsxs)("span",{className:"text-sm text-gray-600",children:[(null==e?void 0:e.fechaInicio)||"N/A"," - ",(null==e?void 0:e.fechaFin)||"N/A"]})]}),(0,q.jsx)("p",{className:"text-gray-700 mt-2",children:(null==e?void 0:e.objetivoPrincipal)||"Objetivo no especificado"})]}),(0,q.jsx)("div",{className:"p-6 space-y-4",children:e.dias&&Array.isArray(e.dias)?e.dias.map(function(r,t){return(0,q.jsxs)("div",{className:"border border-gray-100 rounded-lg p-4",children:[(0,q.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,q.jsx)("h5",{className:"font-semibold text-gray-900",children:(null==r?void 0:r.dia)||"D\xeda no especificado"}),(0,q.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded",children:[(null==r?void 0:r.horas)||0,"h"]})]}),(0,q.jsx)("div",{className:"space-y-2",children:r.tareas&&Array.isArray(r.tareas)?r.tareas.map(function(t,a){var n=C(t,e.numero,r.dia);return(0,q.jsxs)("div",{className:"flex items-start p-3 rounded-lg border transition-all cursor-pointer ".concat(n?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-blue-300"),onClick:function(){return P(t,e.numero,r.dia)},children:[(0,q.jsx)("div",{className:"flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ".concat(n?"bg-green-500 border-green-500":"border-gray-300 hover:border-blue-400"),children:n&&(0,q.jsx)(u.YrT,{className:"w-3 h-3 text-white"})}),(0,q.jsxs)("div",{className:"flex-1",children:[(0,q.jsx)("h6",{className:"font-medium ".concat(n?"text-green-800 line-through":"text-gray-900"),children:(null==t?void 0:t.titulo)||"Tarea sin t\xedtulo"}),(null==t?void 0:t.descripcion)&&(0,q.jsx)("p",{className:"text-sm mt-1 ".concat(n?"text-green-700":"text-gray-600"),children:t.descripcion}),(0,q.jsxs)("div",{className:"flex items-center mt-2 space-x-3",children:[(0,q.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat((null==t?void 0:t.tipo)==="estudio"?"bg-blue-100 text-blue-800":(null==t?void 0:t.tipo)==="repaso"?"bg-yellow-100 text-yellow-800":(null==t?void 0:t.tipo)==="practica"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:(null==t?void 0:t.tipo)||"general"}),(0,q.jsx)("span",{className:"text-xs text-gray-500",children:(null==t?void 0:t.duracionEstimada)||"No especificado"})]})]})]},a)}):(0,q.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay tareas disponibles"})})]},t)}):(0,q.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay d\xedas disponibles"})})]},r)})}),(0,q.jsxs)("div",{className:"hidden lg:block lg:col-span-4 space-y-4",children:[(0,q.jsx)(R,{plan:a,progresoPlan:f,fechaSeleccionada:_,onFechaSeleccionada:T,className:"sticky top-4"}),(0,q.jsx)(z,{fecha:_,tareas:F(),onTareaClick:I,className:"sticky top-4"})]})]})]}),(0,q.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,q.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"Estrategia de Repasos"}),(0,q.jsx)("p",{className:"text-yellow-800",children:"string"==typeof a.estrategiaRepasos?a.estrategiaRepasos:a.estrategiaRepasos&&"object"==typeof a.estrategiaRepasos&&a.estrategiaRepasos.descripcion||"Estrategia de repasos no disponible"})]}),(0,q.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,q.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"Pr\xf3ximos Pasos y Consejos"}),(0,q.jsx)("p",{className:"text-purple-800",children:"string"==typeof a.proximosPasos?a.proximosPasos:a.proximosPasos&&"object"==typeof a.proximosPasos&&a.proximosPasos.descripcion||"Pr\xf3ximos pasos no disponibles"})]}),(0,q.jsx)(Y,{isOpen:A,onClose:function(){return E(!1)},plan:a,progresoPlan:f,fechaSeleccionada:_,onFechaSeleccionada:T,tareasDelDia:F(),onTareaClick:I})]}):(0,q.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,q.jsx)("div",{className:"text-center",children:(0,q.jsx)("p",{className:"text-gray-600",children:"ID de temario no v\xe1lido"})})}):(0,q.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,q.jsx)("div",{className:"text-center",children:(0,q.jsx)("p",{className:"text-gray-600",children:"No se pudo cargar el plan de estudios"})})})}}}]);
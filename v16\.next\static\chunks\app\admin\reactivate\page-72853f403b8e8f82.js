(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[979],{3243:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(69940);function n(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var s,n,a,l,i=[],o=!0,d=!1;try{if(a=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;o=!1}else for(;!(o=(s=a.call(t)).done)&&(i.push(s.value),i.length!==r);o=!0);}catch(e){d=!0,n=e}finally{try{if(!o&&null!=t.return&&(l=t.return(),Object(l)!==l))return}finally{if(d)throw n}}return i}}(e,r)||(0,s.A)(e,r)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},10631:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(78321),n=t(69940);function a(e){return function(e){if(Array.isArray(e))return(0,s.A)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,n.A)(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},21234:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(33311),n=t(28295),a=t.n(n),l=t(12115),i=t(1448),o=t(95155);function d(){var e,r,t,n,d,c=(0,l.useState)(""),u=c[0],m=c[1],x=(0,l.useState)(""),f=x[0],h=x[1],v=(0,l.useState)(""),b=v[0],p=v[1],y=(0,l.useState)(!1),j=y[0],g=y[1],N=(0,l.useState)(null),S=N[0],w=N[1],A=(e=(0,s.A)(a().mark(function e(r){var t,s;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r.preventDefault(),!(!u&&!f&&!b)){e.next=4;break}return i.oR.error("Proporciona al menos un identificador (Session ID, Email o User ID)"),e.abrupt("return");case 4:return g(!0),w(null),e.prev=6,e.next=9,fetch("/api/admin/reactivate-user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:u||void 0,email:f||void 0,userId:b||void 0})});case 9:return t=e.sent,e.next=12,t.json();case 12:s=e.sent,t.ok?(i.oR.success("Usuario reactivado exitosamente"),w(s),m(""),h(""),p("")):(i.oR.error(s.error||"Error reactivando usuario"),w({error:s.error})),e.next=21;break;case 16:e.prev=16,e.t0=e.catch(6),console.error("Error:",e.t0),i.oR.error("Error de conexi\xf3n"),w({error:"Error de conexi\xf3n"});case 21:return e.prev=21,g(!1),e.finish(21);case 24:case"end":return e.stop()}},e,null,[[6,16,21,24]])})),function(r){return e.apply(this,arguments)});return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,o.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,o.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reactivar Usuario"}),(0,o.jsx)("p",{className:"text-gray-600 mt-2",children:"Herramienta administrativa para reactivar usuarios cuando el webhook de Stripe falla."})]}),(0,o.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Uso Administrativo"}),(0,o.jsxs)("div",{className:"mt-2 text-sm text-yellow-700",children:[(0,o.jsx)("p",{children:"Esta herramienta debe usarse solo cuando:"}),(0,o.jsxs)("ul",{className:"list-disc list-inside mt-1",children:[(0,o.jsx)("li",{children:"El webhook de Stripe fall\xf3"}),(0,o.jsx)("li",{children:"El pago est\xe1 confirmado en Stripe"}),(0,o.jsx)("li",{children:"El usuario no recibi\xf3 su email de activaci\xf3n"})]})]})]})]})}),(0,o.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"sessionId",className:"block text-sm font-medium text-gray-700",children:"Stripe Session ID"}),(0,o.jsx)("input",{type:"text",id:"sessionId",value:u,onChange:function(e){return m(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"cs_test_...",disabled:j}),(0,o.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"ID de la sesi\xf3n de checkout de Stripe (recomendado)"})]}),(0,o.jsx)("div",{className:"text-center text-gray-500 text-sm",children:"- O -"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email del Usuario"}),(0,o.jsx)("input",{type:"email",id:"email",value:f,onChange:function(e){return h(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:j})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"userId",className:"block text-sm font-medium text-gray-700",children:"User ID de Supabase"}),(0,o.jsx)("input",{type:"text",id:"userId",value:b,onChange:function(e){return p(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"uuid-del-usuario",disabled:j})]}),(0,o.jsx)("button",{type:"submit",disabled:j,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:j?"Reactivando...":"Reactivar Usuario"})]}),S&&(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Resultado"}),S.success?(0,o.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Usuario reactivado exitosamente"}),(0,o.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,o.jsxs)("ul",{className:"list-disc list-inside",children:[(0,o.jsxs)("li",{children:["User ID: ",null==(r=S.data)?void 0:r.userId]}),(null==(t=S.data)?void 0:t.transactionId)&&(0,o.jsxs)("li",{children:["Transaction ID: ",S.data.transactionId]}),(0,o.jsxs)("li",{children:["Email enviado: ",null!=(n=S.data)&&n.emailSent?"S\xed":"No"]}),(0,o.jsxs)("li",{children:["Perfil actualizado: ",null!=(d=S.data)&&d.profileUpdated?"S\xed":"No"]})]})})]})]})}):(0,o.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error en la reactivaci\xf3n"}),(0,o.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,o.jsx)("p",{children:S.error})})]})]})})]})]})})})}},29889:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(94348);function n(e){var r=function(e,r){if("object"!==(0,s.A)(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!==(0,s.A)(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===(0,s.A)(r)?r:String(r)}},33311:(e,r,t)=>{"use strict";function s(e,r,t,s,n,a,l){try{var i=e[a](l),o=i.value}catch(e){t(e);return}i.done?r(o):Promise.resolve(o).then(s,n)}function n(e){return function(){var r=this,t=arguments;return new Promise(function(n,a){var l=e.apply(r,t);function i(e){s(l,n,a,i,o,"next",e)}function o(e){s(l,n,a,i,o,"throw",e)}i(void 0)})}}t.d(r,{A:()=>n})},37711:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(29889);function n(e,r,t){return(r=(0,s.A)(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}},61408:(e,r,t)=>{Promise.resolve().then(t.bind(t,21234))},69940:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(78321);function n(e,r){if(e){if("string"==typeof e)return(0,s.A)(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return(0,s.A)(e,r)}}},78321:(e,r,t)=>{"use strict";function s(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,s=Array(r);t<r;t++)s[t]=e[t];return s}t.d(r,{A:()=>s})},94348:(e,r,t)=>{"use strict";function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.d(r,{A:()=>s})}},e=>{var r=r=>e(e.s=r);e.O(0,[1448,8441,6891,7358],()=>r(61408)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4466],{25410:(e,r,t)=>{Promise.resolve().then(t.bind(t,88216))},88216:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>rM});var n=t(3243),s=t(33311),a=t(28295),o=t.n(a),i=t(12115),c=t(6066),l=t(75780),d=t(86520),u=t(10631),m=t(37711),x=t(87925),p=t(1448),h=t(95155);function f(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function g(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?f(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):f(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function b(e){var r,t,n,a=e.onSelectConversation,c=e.conversacionActualId,d=e.onConversationDeleted,u=(0,i.useState)([]),m=u[0],f=u[1],b=(0,i.useState)(!0),v=b[0],j=b[1],y=(0,i.useState)(""),N=y[0],w=y[1],k=(0,i.useState)(null),E=k[0],C=k[1],S=(0,i.useState)(null),O=S[0],A=S[1],P=(0,i.useState)(""),D=P[0],T=P[1],_=(0,i.useState)(null),R=_[0],z=_[1],I=(r=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return j(!0),e.prev=1,e.next=4,(0,x.sj)();case 4:f(e.sent),w(""),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("Error al cargar conversaciones:",e.t0),w("No se pudieron cargar las conversaciones");case 13:return e.prev=13,j(!1),e.finish(13);case 16:case"end":return e.stop()}},e,null,[[1,9,13,16]])})),function(){return r.apply(this,arguments)});(0,i.useEffect)(function(){I()},[]),(0,i.useEffect)(function(){c&&I()},[c]);var F=function(e){var r=new Date(e),t=Math.floor((new Date().getTime()-r.getTime())/864e5);return 0===t?r.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"}):1===t?"Ayer":t<7?"Hace ".concat(t," d\xedas"):r.toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"2-digit"})},M=function(e){return e.titulo?e.titulo:"Conversaci\xf3n del ".concat(F(e.creado_en))},L=(t=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!E){e.next=2;break}return e.abrupt("return");case 2:return C(r),e.prev=3,t=p.oR.loading("Eliminando conversaci\xf3n..."),e.next=7,(0,x.sq)(r);case 7:e.sent?(p.oR.success("Conversaci\xf3n eliminada exitosamente",{id:t}),f(function(e){return e.filter(function(e){return e.id!==r})}),r===c&&(null==d||d())):p.oR.error("Error al eliminar la conversaci\xf3n",{id:t}),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(3),console.error("Error al eliminar conversaci\xf3n:",e.t0),p.oR.error("Error al eliminar la conversaci\xf3n",{id:t});case 15:return e.prev=15,C(null),z(null),e.finish(15);case 19:case"end":return e.stop()}},e,null,[[3,11,15,19]])})),function(e){return t.apply(this,arguments)}),G=function(e){A(e.id),T(M(e))},q=function(){A(null),T("")},H=(n=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(D.trim()){e.next=3;break}return p.oR.error("El t\xedtulo no puede estar vac\xedo"),e.abrupt("return");case 3:return e.prev=3,t=p.oR.loading("Actualizando t\xedtulo..."),e.next=7,(0,x.fW)(r,D.trim());case 7:e.sent?(p.oR.success("T\xedtulo actualizado exitosamente",{id:t}),f(function(e){return e.map(function(e){return e.id===r?g(g({},e),{},{titulo:D.trim()}):e})}),A(null),T("")):p.oR.error("Error al actualizar el t\xedtulo",{id:t}),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(3),console.error("Error al actualizar t\xedtulo:",e.t0),p.oR.error("Error al actualizar el t\xedtulo",{id:t});case 15:case"end":return e.stop()}},e,null,[[3,11]])})),function(e){return n.apply(this,arguments)});return(0,h.jsxs)("div",{className:"w-64 bg-gray-50 border border-gray-200 rounded-lg flex flex-col h-full",children:[(0,h.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Conversaciones"}),(0,h.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[m.length," conversaci\xf3n",1!==m.length?"es":""]})]}),(0,h.jsx)("div",{className:"flex-1 overflow-y-auto",children:v?(0,h.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Cargando conversaciones..."})]}):N?(0,h.jsx)("div",{className:"p-4",children:(0,h.jsx)("div",{className:"text-red-500 text-sm",children:N})}):0===m.length?(0,h.jsx)("div",{className:"p-4",children:(0,h.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"No hay conversaciones guardadas"})}):(0,h.jsx)("div",{className:"divide-y divide-gray-200",children:m.map(function(e){return(0,h.jsxs)("div",{className:"p-4 hover:bg-gray-100 transition-colors ".concat(c===e.id?"bg-blue-50 border-r-2 border-blue-500":""),children:[(0,h.jsx)("div",{className:"flex items-start justify-between mb-2",children:(0,h.jsx)("div",{className:"flex-1 min-w-0",children:O===e.id?(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)("input",{type:"text",value:D,onChange:function(e){return T(e.target.value)},className:"flex-1 text-sm font-medium bg-white border border-gray-300 rounded px-2 py-1",onKeyDown:function(r){"Enter"===r.key?H(e.id):"Escape"===r.key&&q()},autoFocus:!0}),(0,h.jsx)("button",{onClick:function(){return H(e.id)},className:"text-green-600 hover:text-green-800",children:(0,h.jsx)(l.YrT,{size:16})}),(0,h.jsx)("button",{onClick:q,className:"text-gray-600 hover:text-gray-800",children:(0,h.jsx)(l.yGN,{size:16})})]}):(0,h.jsx)("h4",{className:"font-medium text-gray-800 truncate cursor-pointer hover:text-blue-600",onClick:function(){return a(e.id)},title:M(e),children:M(e)})})}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("div",{className:"text-xs text-gray-500",children:F(e.actualizado_en)}),O!==e.id&&(0,h.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,h.jsx)("button",{onClick:function(){return G(e)},className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Renombrar conversaci\xf3n",children:(0,h.jsx)(l.Pj4,{size:14})}),R===e.id?(0,h.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,h.jsx)("button",{onClick:function(){return L(e.id)},disabled:E===e.id,className:"p-1 text-red-600 hover:text-red-800 transition-colors",title:"Confirmar eliminaci\xf3n",children:(0,h.jsx)(l.YrT,{size:14})}),(0,h.jsx)("button",{onClick:function(){return z(null)},className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"Cancelar",children:(0,h.jsx)(l.yGN,{size:14})})]}):(0,h.jsx)("button",{onClick:function(){return z(e.id)},disabled:E===e.id,className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Eliminar conversaci\xf3n",children:(0,h.jsx)(l.IXo,{size:14})})]})]}),c===e.id&&(0,h.jsx)("div",{className:"text-xs text-blue-600 mt-1 font-medium",children:"Conversaci\xf3n actual"})]},e.id)})})})]})}var v=t(21410),j=t(53552),y=t(35431),N=y.z.object({pregunta:y.z.string().min(1,"La pregunta es obligatoria").max(500,"M\xe1ximo 500 caracteres"),documentos:y.z.array(y.z.object({id:y.z.string().optional(),titulo:y.z.string().min(1),contenido:y.z.string().min(1),categoria:y.z.string().optional().nullable(),numero_tema:y.z.union([y.z.number().int().positive(),y.z.string(),y.z.null(),y.z.undefined()]).optional(),creado_en:y.z.string().optional(),actualizado_en:y.z.string().optional(),user_id:y.z.string().optional(),tipo_original:y.z.string().optional()})).min(1,"Debes seleccionar al menos un documento")}),w=y.z.object({peticion:y.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres")}),k=y.z.object({peticion:y.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:y.z.number().min(1,"M\xednimo 1 pregunta").max(50,"M\xe1ximo 50 preguntas").default(10)}),E=y.z.object({peticion:y.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:y.z.number().min(1,"M\xednimo 1 flashcard").max(30,"M\xe1ximo 30 flashcards").default(10)}),C=t(4001),S=t(11555),O=t(52750);function A(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function P(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?A(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):A(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function D(e){var r,t,n,a,c=e.documentosSeleccionados,l=(0,i.useState)([]),d=l[0],m=l[1],p=(0,i.useState)(!1),f=p[0],g=p[1],y=(0,i.useState)(""),w=y[0],k=y[1],E=(0,i.useState)(null),A=E[0],D=E[1],T=(0,i.useState)(!1),_=T[0],R=T[1],z=(0,i.useRef)(null),I=(0,C.A)().user,F=(0,i.useState)(null),M=F[0],L=F[1],G=(0,v.mN)({resolver:(0,j.u)(N),defaultValues:{pregunta:"",documentos:c}}),q=G.register,H=G.handleSubmit,U=G.formState.errors,V=G.reset,B=G.setValue;(0,i.useEffect)(function(){B("documentos",c.map(function(e){return P(P({},e),{},{categoria:e.categoria||null,numero_tema:void 0!==e.numero_tema&&null!==e.numero_tema?"string"==typeof e.numero_tema?parseInt(e.numero_tema,10):e.numero_tema:void 0,id:e.id||void 0,creado_en:e.creado_en||void 0,actualizado_en:e.actualizado_en||void 0,user_id:e.user_id||void 0,tipo_original:e.tipo_original||void 0})}))},[c,B]),(0,i.useEffect)(function(){var e;(e=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!I){e.next=5;break}return e.next=3,(0,S.qk)("ai_tutor_chat");case 3:L(e.sent?"paid":"free");case 5:case"end":return e.stop()}},e)})),function(){return e.apply(this,arguments)})()},[I]),(0,i.useEffect)(function(){var e,r=(e=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,x.Sl)();case 3:if(!(r=e.sent)){e.next=10;break}return D(r.id),e.next=8,$(r.id);case 8:e.next=12;break;case 10:m([]),D(null);case 12:e.next=19;break;case 14:e.prev=14,e.t0=e.catch(0),console.warn("No se pudo cargar la conversaci\xf3n activa (esto es normal para usuarios nuevos):",e.t0),m([]),D(null);case 19:case"end":return e.stop()}},e,null,[[0,14]])})),function(){return e.apply(this,arguments)});"paid"===M&&r()},[M]),(0,i.useEffect)(function(){z.current&&(z.current.scrollTop=z.current.scrollHeight)},[d]);var $=(r=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,g(!0),e.next=4,(0,x.vW)(r);case 4:return e.next=6,(0,x.C9)(r);case 6:m(e.sent.map(function(e){return{id:e.id,tipo:e.tipo,contenido:e.contenido,timestamp:new Date(e.timestamp)}})),D(r),k(""),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("Error al cargar la conversaci\xf3n:",e.t0),k("No se pudo cargar la conversaci\xf3n");case 17:return e.prev=17,g(!1),e.finish(17);case 20:case"end":return e.stop()}},e,null,[[0,13,17,20]])})),function(e){return r.apply(this,arguments)}),W=(t=(0,s.A)(o().mark(function e(r,t){var n,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,R(!0),n=t||A){e.next=21;break}if("usuario"!==r.tipo){e.next=17;break}return s="Conversaci\xf3n: ".concat(r.contenido.substring(0,50)).concat(r.contenido.length>50?"...":""),e.next=8,(0,x.Yp)(s,!0);case 8:if(a=e.sent){e.next=11;break}throw Error("No se pudo crear la conversaci\xf3n");case 11:return D(a),e.next=14,(0,x.QE)({conversacion_id:a,tipo:r.tipo,contenido:r.contenido});case 14:return e.abrupt("return",a);case 17:throw console.error("❌ ERROR CR\xcdTICO: Intentando guardar mensaje de IA sin conversaci\xf3n activa"),Error("No se puede guardar un mensaje de IA sin una conversaci\xf3n activa");case 19:e.next=30;break;case 21:return e.next=23,(0,x.Sl)();case 23:if(!(!(i=e.sent)||i.id!==n)){e.next=27;break}return e.next=27,(0,x.vW)(n);case 27:return e.next=29,(0,x.QE)({conversacion_id:n,tipo:r.tipo,contenido:r.contenido});case 29:return e.abrupt("return",n);case 30:e.next=36;break;case 32:return e.prev=32,e.t0=e.catch(0),console.error("Error al guardar el mensaje:",e.t0),e.abrupt("return",null);case 36:return e.prev=36,R(!1),e.finish(36);case 39:case"end":return e.stop()}},e,null,[[0,32,36,39]])})),function(e,r){return t.apply(this,arguments)}),Y=(n=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,g(!0),e.next=4,(0,x.CM)();case 4:m([]),D(null),k(""),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Error al iniciar nueva conversaci\xf3n:",e.t0);case 12:return e.prev=12,g(!1),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[0,9,12,15]])})),function(){return n.apply(this,arguments)}),K=(a=(0,s.A)(o().mark(function e(r){var t,n,s,a,i,l,p,h,f,b;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return g(!0),k(""),t={tipo:"usuario",contenido:r.pregunta,timestamp:new Date},m(function(e){return[].concat((0,u.A)(e),[t])}),g(!0),k(""),V({pregunta:"",documentos:c}),n=null,e.prev=8,e.next=11,W(t);case 11:return n=e.sent,e.next=14,fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pregunta:t.contenido,documentos:r.documentos})});case 14:return s=e.sent,e.next=17,s.json();case 17:if(a=e.sent,i="",i=a.result?"string"==typeof a.result?a.result:JSON.stringify(a.result):a.error?"string"==typeof a.error?a.error:JSON.stringify(a.error):"Error desconocido al obtener respuesta de la IA.",n){e.next=23;break}throw console.error("❌ ERROR: No se pudo obtener el ID de conversaci\xf3n para guardar la respuesta de la IA"),Error("No se pudo guardar la respuesta: conversaci\xf3n no encontrada");case 23:return A!==n&&D(n),l={tipo:"ia",contenido:i,timestamp:new Date},m(function(e){return[].concat((0,u.A)(e),[l])}),e.next=28,W(l,n);case 28:if(!(0===d.length&&n)){e.next=32;break}return p="Conversaci\xf3n: ".concat(r.pregunta.substring(0,50)).concat(r.pregunta.length>50?"...":""),e.next=32,(0,x.fW)(n,p);case 32:case 47:e.next=52;break;case 34:if(e.prev=34,e.t0=e.catch(8),console.error("Error al obtener respuesta:",e.t0),h="Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, int\xe9ntalo de nuevo.",e.t0 instanceof Error&&(h=e.t0.message.includes("API key")?"Error de configuraci\xf3n: La clave de API de Gemini no est\xe1 configurada correctamente.":e.t0.message.includes("network")||e.t0.message.includes("fetch")?"Error de conexi\xf3n: No se pudo conectar con el servicio de IA. Verifica tu conexi\xf3n a internet.":e.t0.message.includes("quota")||e.t0.message.includes("limit")?"Se ha alcanzado el l\xedmite de uso del servicio de IA. Int\xe9ntalo m\xe1s tarde.":"Error: ".concat(e.t0.message)),k(h),f={tipo:"ia",contenido:h,timestamp:new Date},m(function(e){return[].concat((0,u.A)(e),[f])}),e.prev=42,!(b=n||A)){e.next=47;break}return e.next=47,W(f,b);case 49:e.prev=49,e.t1=e.catch(42),console.error("Error al guardar mensaje de error en DB:",e.t1);case 52:return e.prev=52,g(!1),e.finish(52);case 55:case"end":return e.stop()}},e,null,[[8,34,52,55],[42,49]])})),function(e){return a.apply(this,arguments)});return"free"===M?(0,h.jsx)("div",{className:"mt-6",children:(0,h.jsx)(O.A,{feature:"ai_tutor_chat",benefits:["Chat ilimitado con IA especializada","Respuestas personalizadas a tus documentos","Historial completo de conversaciones","Explicaciones detalladas y ejemplos"],className:"h-[600px]"})}):(0,h.jsxs)("div",{className:"mt-6 flex h-[600px] gap-6",children:[(0,h.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,h.jsx)("div",{className:"flex justify-start mb-4",children:(0,h.jsxs)("button",{type:"button",onClick:Y,className:"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center",disabled:f,children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Nueva conversaci\xf3n"]})}),(0,h.jsx)("div",{ref:z,className:"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50",style:{height:"calc(100% - 180px)"},children:0===d.length?(0,h.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,h.jsx)("p",{children:"Selecciona documentos y haz una pregunta para comenzar la conversaci\xf3n."})}):(0,h.jsxs)("div",{className:"space-y-4",children:[d.map(function(e,r){return(0,h.jsx)("div",{className:"flex ".concat("usuario"===e.tipo?"justify-end":"justify-start"),children:(0,h.jsxs)("div",{className:"max-w-[80%] p-3 rounded-lg ".concat("usuario"===e.tipo?"bg-blue-500 text-white rounded-br-none":"bg-white border border-gray-300 rounded-bl-none"),children:[(0,h.jsx)("div",{className:"whitespace-pre-wrap",children:e.contenido}),(0,h.jsx)("div",{className:"text-xs mt-1 text-right ".concat("usuario"===e.tipo?"text-blue-100":"text-gray-500"),children:e.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},e.id||r)}),f&&(0,h.jsx)("div",{className:"flex justify-start",children:(0,h.jsx)("div",{className:"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none",children:(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full"}),(0,h.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full",style:{animationDelay:"0.2s"}}),(0,h.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full",style:{animationDelay:"0.4s"}})]})})}),_&&(0,h.jsx)("div",{className:"text-xs text-gray-500 text-center py-1",children:"Guardando conversaci\xf3n..."})]})}),(0,h.jsxs)("form",{onSubmit:H(K),className:"mt-auto",children:[w&&(0,h.jsx)("div",{className:"text-red-500 text-sm mb-2",children:w}),(0,h.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:c.length>0?(0,h.jsxs)("span",{className:"text-green-600",children:["✓ ",c.length," documento",1!==c.length?"s":""," seleccionado",1!==c.length?"s":""]}):(0,h.jsx)("span",{className:"text-red-600",children:"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas."})}),(0,h.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,h.jsxs)("div",{className:"flex-grow",children:[(0,h.jsx)("textarea",P(P({id:"pregunta",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2},q("pregunta")),{},{placeholder:"Escribe tu pregunta sobre los documentos seleccionados...",disabled:f,onKeyDown:function(e){"Enter"!==e.key||e.shiftKey||(e.preventDefault(),H(K)())}})),U.pregunta&&(0,h.jsx)("p",{className:"text-red-500 text-xs mt-1",children:U.pregunta.message}),(0,h.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Presiona Enter para enviar, Shift+Enter para nueva l\xednea"})]}),(0,h.jsx)("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed",disabled:f||0===c.length,title:0===c.length?"Selecciona al menos un documento para hacer una pregunta":"Enviar pregunta",children:f?(0,h.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,h.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,h.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,h.jsx)("svg",{className:"h-5 w-5 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,h.jsx)("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})})})]})]})]}),(0,h.jsx)(b,{onSelectConversation:$,conversacionActualId:A,onConversationDeleted:function(){m([]),D(null),k("")}})]})}var T=t(73329),_=t.n(T);function R(e){var r,t=e.className,n=void 0===t?"":t,a=(0,i.useState)(null),c=a[0],d=a[1],u=(0,i.useState)(!0),m=u[0],x=u[1];(0,i.useEffect)(function(){p()},[]);var p=(r=(0,s.A)(o().mark(function e(){var r,t,n,s,a,i,c,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,x(!0),e.next=4,fetch("/api/user/plan");case 4:if(!(r=e.sent).ok){e.next=27;break}return e.next=8,r.json();case 8:if("free"!==(t=e.sent).plan){e.next=24;break}return e.next=12,fetch("/api/auth/free-account-status");case 12:if(!(n=e.sent).ok){e.next=20;break}return e.next=16,n.json();case 16:(s=e.sent).success&&s.isFreeAccount&&s.status?(a=s.status.usageCount||{},i=s.status.limits||{},d({current:c="number"==typeof a.documents?a.documents:0,limit:l="number"==typeof i.documents?i.documents:1,remaining:Math.max(0,l-c),isAtLimit:c>=l,plan:"free"})):d(null),e.next=22;break;case 20:console.error("Error fetching free account status:",n.status),d(null);case 22:e.next=25;break;case 24:d({current:0,limit:-1,remaining:-1,isAtLimit:!1,plan:t.plan||"paid"});case 25:e.next=28;break;case 27:d(null);case 28:e.next=34;break;case 30:e.prev=30,e.t0=e.catch(0),console.error("Error cargando estado de l\xedmites:",e.t0),d(null);case 34:return e.prev=34,x(!1),e.finish(34);case 37:case"end":return e.stop()}},e,null,[[0,30,34,37]])})),function(){return r.apply(this,arguments)});if(m)return(0,h.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 ".concat(n),children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"}),(0,h.jsx)("span",{children:"Cargando l\xedmites..."})]});if(!c)return null;if("paid"===c.plan||-1===c.limit)return(0,h.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-green-600 ".concat(n),children:[(0,h.jsx)(l.jH2,{className:"w-4 h-4"}),(0,h.jsx)("span",{children:"Documentos ilimitados"})]});var f=c.current/c.limit*100;return(0,h.jsxs)("div",{className:"space-y-2 ".concat(n),children:[(0,h.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)(l.jH2,{className:"w-4 h-4 text-gray-500"}),(0,h.jsxs)("span",{className:"text-gray-700",children:["Documentos: ",c.current,"/",c.limit]})]}),c.isAtLimit&&(0,h.jsxs)("div",{className:"flex items-center space-x-1 text-red-600",children:[(0,h.jsx)(l.y3G,{className:"w-4 h-4"}),(0,h.jsx)("span",{className:"text-xs font-medium",children:"L\xedmite alcanzado"})]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,h.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(c.isAtLimit?"bg-red-500":f>80?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(Math.min(f,100),"%")}})}),c.isAtLimit?(0,h.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,h.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,h.jsx)(l.y3G,{className:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"}),(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"L\xedmite de documentos alcanzado"}),(0,h.jsxs)("p",{className:"text-xs text-red-700 mt-1",children:["Has alcanzado el l\xedmite de ",c.limit," documento(s) para el plan gratuito."]}),(0,h.jsxs)(_(),{href:"/upgrade-plan",className:"inline-flex items-center mt-2 px-3 py-1 bg-red-600 text-white text-xs font-medium rounded hover:bg-red-700 transition-colors",children:[(0,h.jsx)(l.ei4,{className:"w-3 h-3 mr-1"}),"Actualizar Plan"]})]})]})}):0===c.remaining?(0,h.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,h.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,h.jsx)(l.y3G,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("p",{className:"text-sm text-yellow-800 font-medium",children:"\xdaltimo documento disponible"}),(0,h.jsx)("p",{className:"text-xs text-yellow-700 mt-1",children:"Este es tu \xfaltimo documento disponible en el plan gratuito."})]})]})}):(0,h.jsxs)("div",{className:"text-xs text-gray-600",children:["Te quedan ",c.remaining," documento(s) disponible(s)"]})]})}function z(e){var r,t=e.onSuccess,n=(0,i.useState)(""),a=n[0],c=n[1],l=(0,i.useState)(""),d=l[0],u=l[1],m=(0,i.useState)(""),f=m[0],g=m[1],b=(0,i.useState)(""),v=b[0],j=b[1],y=(0,i.useState)(null),N=y[0],w=y[1],k=(0,i.useRef)(null),E=(0,i.useState)(!1),C=E[0],S=E[1],O=(0,i.useState)({texto:"",tipo:""}),A=O[0],P=O[1],D=(r=(0,s.A)(o().mark(function e(r){var n,s,i,l,m,h;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r.preventDefault(),S(!0),P({texto:"",tipo:""}),n=void 0,!N){e.next=44;break}return n=p.Ay.loading("Subiendo ".concat(N.name,"...")),(s=new FormData).append("file",N),s.append("titulo",a),f&&s.append("categoria",f),v&&s.append("numero_tema",v),e.prev=11,e.next=14,fetch("/api/document/upload",{method:"POST",body:s});case 14:if(!(i=e.sent).ok){e.next=29;break}return e.next=18,i.json();case 18:l=e.sent,p.Ay.success('Documento "'.concat(N.name,'" subido y procesado con ID: ').concat(l.documentId,"."),{id:n}),c(""),u(""),g(""),j(""),w(null),k.current&&(k.current.value=""),t&&t(),e.next=33;break;case 29:return e.next=31,i.json();case 31:m=e.sent,403===i.status&&m.needsUpgrade?p.Ay.error("".concat(m.error,": ").concat(m.reason),{id:n,duration:6e3}):p.Ay.error("Error al subir archivo: ".concat(m.error||i.statusText),{id:n});case 33:e.next=39;break;case 35:e.prev=35,e.t0=e.catch(11),console.error("Error en la subida del archivo:",e.t0),p.Ay.error("Error de conexi\xf3n o inesperado al subir el archivo.",{id:n});case 39:return e.prev=39,S(!1),e.finish(39);case 42:e.next=64;break;case 44:if(!(!a.trim()||!d.trim())){e.next=48;break}return P({texto:"El t\xedtulo y el contenido son obligatorios si no se selecciona un archivo.",tipo:"error"}),S(!1),e.abrupt("return");case 48:return n=p.Ay.loading("Guardando documento manualmente..."),e.prev=49,h={titulo:a,contenido:d,categoria:f||void 0,numero_tema:v?parseInt(v):void 0},e.next=53,(0,x.hE)(h);case 53:e.sent?(p.Ay.success("Documento guardado manualmente correctamente.",{id:n}),c(""),u(""),g(""),j(""),t&&t()):p.Ay.error("Error al guardar el documento manualmente.",{id:n}),e.next=61;break;case 57:e.prev=57,e.t1=e.catch(49),console.error("Error al guardar documento manualmente:",e.t1),p.Ay.error("Ha ocurrido un error al guardar el documento manualmente.",{id:n});case 61:return e.prev=61,S(!1),e.finish(61);case 64:case"end":return e.stop()}},e,null,[[11,35,39,42],[49,57,61,64]])})),function(e){return r.apply(this,arguments)});return(0,h.jsxs)("div",{className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4",children:[(0,h.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Subir nuevo documento"}),(0,h.jsx)(R,{className:"mb-6"}),(0,h.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"titulo",className:"block text-gray-700 text-sm font-bold mb-2",children:"T\xedtulo:"}),(0,h.jsx)("input",{type:"text",id:"titulo",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:a,onChange:function(e){return c(e.target.value)},placeholder:"T\xedtulo del documento (se autocompleta con el nombre del archivo)",disabled:C,required:!0})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"categoria",className:"block text-gray-700 text-sm font-bold mb-2",children:"Categor\xeda:"}),(0,h.jsxs)("select",{id:"categoria",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:f,onChange:function(e){return g(e.target.value)},disabled:C,children:[(0,h.jsx)("option",{value:"",children:"Seleccionar categor\xeda"}),(0,h.jsx)("option",{value:"tema",children:"Tema"}),(0,h.jsx)("option",{value:"anexo",children:"Anexo"}),(0,h.jsx)("option",{value:"resumen",children:"Resumen"})]})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"numeroTema",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de tema:"}),(0,h.jsx)("input",{type:"number",id:"numeroTema",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:v,onChange:function(e){return j(e.target.value)},placeholder:"Opcional",min:"1",disabled:C})]})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"contenido",className:"block text-gray-700 text-sm font-bold mb-2",children:"Contenido (manual o previsualizaci\xf3n de .txt):"}),(0,h.jsx)("textarea",{id:"contenido",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:10,value:d,onChange:function(e){return u(e.target.value)},placeholder:"Escribe o pega el contenido aqu\xed, o selecciona un archivo .txt para previsualizarlo. Para PDFs, el contenido se extraer\xe1 autom\xe1ticamente.",disabled:C})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"archivo",className:"block text-gray-700 text-sm font-bold mb-2",children:"O sube un archivo (.txt o .pdf):"}),(0,h.jsx)("input",{type:"file",id:"archivo",ref:k,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",onChange:function(e){var r,t=null==(r=e.target.files)?void 0:r[0];if(P({texto:"",tipo:""}),!t)return void w(null);if(t.size>5242880){p.Ay.error("El archivo es demasiado grande. El tama\xf1o m\xe1ximo es ".concat(5,"MB.")),k.current&&(k.current.value=""),w(null);return}if(w(t),c(t.name),"text/plain"===t.type){var n=new FileReader;n.onload=function(e){var r;null!=(r=e.target)&&r.result?(u(e.target.result),p.Ay.success("Archivo TXT le\xeddo y listo para vista previa.")):(p.Ay.error("Error al leer el archivo TXT."),u(""))},n.onerror=function(){p.Ay.error("Error al leer el archivo TXT."),u("")},n.readAsText(t)}else"application/pdf"===t.type?(u("El contenido se extraer\xe1 del PDF al guardar. Puedes editar el t\xedtulo si es necesario."),p.Ay.success("Archivo PDF seleccionado. El contenido se procesar\xe1 en el servidor.")):(u("Este tipo de archivo no tiene previsualizaci\xf3n. El contenido se procesar\xe1 en el servidor si es compatible."),(0,p.Ay)("Archivo ".concat(t.name," seleccionado. El tipo no es previsualizable.")))},accept:".txt,.pdf",disabled:C}),(0,h.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Solo archivos .txt o .pdf. M\xe1ximo ",5,"MB."]})]}),A.texto&&(0,h.jsx)("div",{className:"p-3 rounded ".concat("error"===A.tipo?"bg-red-100 text-red-700":"bg-green-100 text-green-700"),children:A.texto}),(0,h.jsx)("div",{children:(0,h.jsx)("button",{type:"submit",className:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:C,children:C?"Guardando...":"Guardar documento"})})]})]})}var I=t(57644),F=function(){var e,r,t,n,a,c,l=(0,I.M)(),d=l.addTask,u=l.updateTask,m=l.getTasksByType,x=(0,i.useCallback)((e=(0,s.A)(o().mark(function e(r,t,n){var s,a,i,c,l,m,x,p,h,f,g;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=n.peticion,a=n.contextos,i=n.cantidad,c=n.onComplete,l=n.onError,m=d({type:r,title:s.length>50?"".concat(s.substring(0,50),"..."):s}),e.prev=2,u(m,{status:"processing"}),console.log("\uD83D\uDE80 Iniciando generaci\xf3n de ".concat(r,":"),{action:t,peticion:s,contextos:null==a?void 0:a.length,cantidad:i}),e.next=7,fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:t,peticion:s,contextos:a,cantidad:i})});case 7:if(x=e.sent,console.log("\uD83D\uDCE1 Respuesta recibida para ".concat(r,":"),x.status,x.ok),x.ok){e.next=15;break}return e.next=12,x.text();case 12:throw p=e.sent,console.error("❌ Error en la API para ".concat(r,":"),x.status,p),Error("Error en la API: ".concat(x.status," - ").concat(p));case 15:return e.next=17,x.json();case 17:return h=e.sent,console.log("✅ Resultado obtenido para ".concat(r,":"),h),f=h.result,u(m,{status:"completed",result:f,progress:100}),c&&setTimeout(function(){return c(f)},0),e.abrupt("return",m);case 25:throw e.prev=25,e.t0=e.catch(2),g=e.t0 instanceof Error?e.t0.message:"Error desconocido",u(m,{status:"error",error:g}),l&&setTimeout(function(){return l(g)},0),e.t0;case 31:case"end":return e.stop()}},e,null,[[2,25]])})),function(r,t,n){return e.apply(this,arguments)}),[d,u]),p=(0,i.useCallback)((r=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",x("mapa-mental","generarMapaMental",r));case 1:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)}),[x]),h=(0,i.useCallback)((t=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",x("test","generarTest",r));case 1:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)}),[x]),f=(0,i.useCallback)((n=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",x("flashcards","generarFlashcards",r));case 1:case"end":return e.stop()}},e)})),function(e){return n.apply(this,arguments)}),[x]),g=(0,i.useCallback)((a=(0,s.A)(o().mark(function e(r){var t,n,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.documento,n=r.instrucciones,s=r.onComplete,a=r.onError,i={action:"generarResumen",peticion:"".concat(t.titulo,"|").concat(t.categoria||"","|").concat(t.numero_tema||"","|").concat(n),contextos:[t.contenido]},e.abrupt("return",x("resumen","generarResumen",{peticion:i.peticion,contextos:i.contextos,onComplete:s,onError:a}));case 3:case"end":return e.stop()}},e)})),function(e){return a.apply(this,arguments)}),[x]);return{generateMapaMental:p,generateTest:h,generateFlashcards:f,generatePlanEstudios:(0,i.useCallback)((c=(0,s.A)(o().mark(function e(r){var t,n,s,a,i,c,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.temarioId,n=r.onComplete,s=r.onError,a=d({type:"plan-estudios",title:"Generando plan de estudios personalizado"}),e.prev=2,u(a,{status:"processing"}),e.next=6,fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generarPlanEstudios",peticion:t,contextos:[]})});case 6:if((i=e.sent).ok){e.next=9;break}throw Error("Error en la API: ".concat(i.status));case 9:return e.next=11,i.json();case 11:return c=e.sent.result,u(a,{status:"completed",result:c,progress:100}),n&&setTimeout(function(){return n(c)},0),e.abrupt("return",a);case 18:throw e.prev=18,e.t0=e.catch(2),l=e.t0 instanceof Error?e.t0.message:"Error desconocido",u(a,{status:"error",error:l}),s&&setTimeout(function(){return s(l)},0),e.t0;case 24:case"end":return e.stop()}},e,null,[[2,18]])})),function(e){return c.apply(this,arguments)}),[d,u]),generateResumen:g,isGenerating:(0,i.useCallback)(function(e){return m(e).some(function(e){return"pending"===e.status||"processing"===e.status})},[m]),getActiveTask:(0,i.useCallback)(function(e){return m(e).find(function(e){return"pending"===e.status||"processing"===e.status})},[m])}},M=function(e){var r=e.taskType,t=e.onResult,n=e.onError,s=(0,I.M)().tasks,a=(0,i.useState)(new Set),o=a[0],c=a[1],l=(0,i.useRef)(t),d=(0,i.useRef)(n);return l.current=t,d.current=n,(0,i.useEffect)(function(){var e=s.filter(function(e){return e.type===r&&"completed"===e.status&&!o.has(e.id)&&e.result}),t=s.filter(function(e){return e.type===r&&"error"===e.status&&!o.has(e.id)&&e.error});if(e.length>0){var n=e[e.length-1];c(function(e){var r=new Set(e);return r.add(n.id),r}),l.current&&setTimeout(function(){var e;null==(e=l.current)||e.call(l,n.result)},0)}if(t.length>0){var a=t[t.length-1];c(function(e){var r=new Set(e);return r.add(a.id),r}),d.current&&setTimeout(function(){var e;null==(e=d.current)||e.call(d,a.error)},0)}},[s,r,o]),{resetProcessed:(0,i.useCallback)(function(){c(new Set)},[])}};function L(e){var r=e.className,t=(0,i.useState)(!1),n=t[0],s=t[1];return(0,h.jsxs)("div",{className:"relative ".concat(void 0===r?"":r),children:[(0,h.jsxs)("button",{onClick:function(){return s(!n)},className:"inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors",title:"Ayuda sobre mapas mentales",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})}),"\xbfC\xf3mo usar?"]}),n&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:function(){return s(!1)}}),(0,h.jsx)("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 sm:w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 max-h-80 sm:max-h-96 overflow-y-auto",children:(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsx)("h3",{className:"font-semibold text-gray-800",children:"Gu\xeda de Mapas Mentales"}),(0,h.jsx)("button",{onClick:function(){return s(!1)},className:"text-gray-400 hover:text-gray-600",children:(0,h.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,h.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDCDD Generar"}),(0,h.jsx)("p",{className:"text-xs",children:"Describe el mapa mental basado en tus documentos."})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDD0D Vista Previa"}),(0,h.jsx)("p",{className:"text-xs",children:"Revisa el resultado antes de expandir."})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDDA5️ Pantalla Completa"}),(0,h.jsx)("p",{className:"text-xs",children:"Bot\xf3n azul para mejor visualizaci\xf3n."})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"⌨️ Controles"}),(0,h.jsxs)("ul",{className:"list-disc list-inside space-y-0.5 ml-2 text-xs",children:[(0,h.jsxs)("li",{children:[(0,h.jsx)("kbd",{className:"px-1 py-0.5 bg-gray-100 rounded text-xs",children:"ESC"})," para salir"]}),(0,h.jsx)("li",{children:"Clic fuera para cerrar"}),(0,h.jsx)("li",{children:"Zoom y pan en el mapa"}),(0,h.jsx)("li",{children:"Clic en nodos para expandir"})]})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDCBE Descargar"}),(0,h.jsx)("p",{className:"text-xs",children:"Guarda como archivo HTML interactivo."})]})]}),(0,h.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,h.jsxs)("p",{className:"text-xs text-gray-500",children:["\uD83D\uDCA1 ",(0,h.jsx)("strong",{children:"Consejo:"})," Los mapas son interactivos con zoom y navegaci\xf3n."]})})]})})]})]})}function G(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function q(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?G(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):G(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function H(){var e,r,t,n,a,c,l,d,u,m,x,p,h,f,g,b,v=(x=(r=(e=(0,C.A)()).user,t=e.isLoading,a=(n=(0,i.useState)({isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan",loading:!0,error:null}))[0],c=n[1],l=(0,i.useCallback)((0,s.A)(o().mark(function e(){var n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(!r||t)){e.next=3;break}return c(function(e){return q(q({},e),{},{loading:!1})}),e.abrupt("return");case 3:return e.prev=3,c(function(e){return q(q({},e),{},{loading:!0,error:null})}),e.next=7,fetch("/api/auth/free-account-status");case 7:return n=e.sent,e.next=10,n.json();case 10:if(s=e.sent,n.ok){e.next=16;break}if(404!==n.status){e.next=15;break}return c({isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan",loading:!1,error:null}),e.abrupt("return");case 15:throw Error(s.error||"Error obteniendo estado");case 16:c({isFreeAccount:s.isFreeAccount,status:s.status,alerts:s.alerts||[],usageWarnings:s.usageWarnings||[],recommendations:s.recommendations||[],upgradeUrl:s.upgradeUrl||"/upgrade-plan",loading:!1,error:null}),e.next=23;break;case 19:e.prev=19,e.t0=e.catch(3),console.error("Error obteniendo estado de cuenta gratuita:",e.t0),c(function(r){return q(q({},r),{},{loading:!1,error:e.t0 instanceof Error?e.t0.message:"Error desconocido"})});case 23:case"end":return e.stop()}},e,null,[[3,19]])})),[r,t]),d=(0,i.useCallback)(function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return!a.isFreeAccount||!a.status||!!a.status.isActive&&(a.status.usage[e]||0)+r<=(a.status.limits[e]||0)},[a.isFreeAccount,a.status]),u=(0,i.useCallback)((0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l();case 2:case"end":return e.stop()}},e)})),[l]),(0,i.useEffect)(function(){l()},[l]),m=q(q({},a),{},{refresh:u,canPerformAction:d})).isFreeAccount,p=m.status,h=m.canPerformAction,g=(0,i.useCallback)((f=(0,s.A)(o().mark(function e(r){var t,n,s,a,i,c,l=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=l.length>1&&void 0!==l[1]?l[1]:1,n=l.length>2?l[2]:void 0,x){e.next=13;break}return e.prev=3,e.next=6,n();case 6:return s=e.sent,e.abrupt("return",{success:!0,result:s});case 10:return e.prev=10,e.t0=e.catch(3),e.abrupt("return",{success:!1,error:e.t0 instanceof Error?e.t0.message:"Error desconocido"});case 13:if(h(r,t)){e.next=19;break}if(a=(null==p?void 0:p.usage[r])||0,i=(null==p?void 0:p.limits[r])||0,null!=p&&p.isActive){e.next=18;break}return e.abrupt("return",{success:!1,error:"Tu cuenta gratuita ha expirado. Actualiza tu plan para continuar."});case 18:return e.abrupt("return",{success:!1,error:"Has alcanzado el l\xedmite de ".concat(r," (").concat(a,"/").concat(i,"). Actualiza tu plan para continuar.")});case 19:return e.prev=19,e.next=22,n();case 22:return c=e.sent,e.abrupt("return",{success:!0,result:c});case 26:return e.prev=26,e.t1=e.catch(19),e.abrupt("return",{success:!1,error:e.t1 instanceof Error?e.t1.message:"Error desconocido"});case 29:case"end":return e.stop()}},e,null,[[3,10],[19,26]])})),function(e){return f.apply(this,arguments)}),[x,p,h]),{isFreeAccount:x,status:p,validateAndExecute:g,canPerformAction:h}).validateAndExecute;return{executeWithGuard:(b=(0,s.A)(o().mark(function e(r,t){var n,s=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=s.length>2&&void 0!==s[2]?s[2]:1,e.abrupt("return",v(r,n,t));case 2:case"end":return e.stop()}},e)})),function(e,r){return b.apply(this,arguments)})}}function U(){var e=(0,i.useState)("free"),r=e[0],t=e[1],n=(0,i.useState)(!0),a=n[0],c=n[1],l=(0,i.useState)(null),d=l[0],u=l[1],m=(0,C.A)().user;return(0,i.useEffect)(function(){var e;(e=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(m){e.next=4;break}return t("free"),c(!1),e.abrupt("return");case 4:return e.prev=4,c(!0),u(null),e.next=9,fetch("/api/user/plan");case 9:if((r=e.sent).ok){e.next=12;break}throw Error("Error obteniendo plan del usuario");case 12:return e.next=14,r.json();case 14:t(e.sent.plan||"free"),e.next=23;break;case 18:e.prev=18,e.t0=e.catch(4),console.error("Error fetching user plan:",e.t0),u(e.t0 instanceof Error?e.t0.message:"Error desconocido"),t("free");case 23:return e.prev=23,c(!1),e.finish(23);case 26:case"end":return e.stop()}},e,null,[[4,18,23,26]])})),function(){return e.apply(this,arguments)})()},[m]),{plan:r,isLoading:a,error:d}}function V(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function B(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?V(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):V(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function $(e){var r,t=e.documentosSeleccionados,n=(0,i.useState)(null),a=n[0],c=n[1],d=(0,i.useState)(!1),u=d[0],m=d[1],x=(0,i.useState)(!1);x[0],x[1];var f=F(),g=f.generateMapaMental,b=f.isGenerating,y=f.getActiveTask;(0,I.M)().getTask;var N=H().executeWithGuard;(0,C.A)().user;var k=U(),E=k.plan,O=k.isLoading,A=y("mapa-mental"),P=b("mapa-mental");M({taskType:"mapa-mental",onResult:function(e){c(e),p.oR.success("\xa1Mapa mental generado exitosamente!")},onError:function(e){p.oR.error("Error al generar mapa mental: ".concat(e))}});var D=(0,v.mN)({resolver:(0,j.u)(w),defaultValues:{peticion:""}}),T=D.register,R=D.handleSubmit,z=D.formState.errors;(0,i.useEffect)(function(){var e=function(e){"Escape"===e.key&&u&&m(!1)};return u?(document.addEventListener("keydown",e),document.body.style.overflow="hidden"):document.body.style.overflow="unset",function(){document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[u]);var G=(r=(0,s.A)(o().mark(function e(r){var n,a;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.map(function(e){return e.contenido}),console.log("\uD83D\uDDFA️ Iniciando generaci\xf3n de mapa mental:",{peticion:r.peticion,documentos:t.length,contextosLength:n.length}),e.next=4,N("mindMaps",(0,s.A)(o().mark(function e(){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g({peticion:r.peticion,contextos:n});case 2:return console.log("✅ Tarea de mapa mental creada:",t=e.sent),e.abrupt("return",t);case 5:case"end":return e.stop()}},e)})),1);case 4:(a=e.sent).success?p.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):(console.error("❌ Error al generar mapa mental:",a.error),p.oR.error(a.error||"Error al iniciar la generaci\xf3n del mapa mental"));case 6:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)}),q=function(){if(a){var e=new Blob([a],{type:"text/html"}),r=URL.createObjectURL(e),t=document.createElement("a");t.href=r,t.download="mapa-mental.html",document.body.appendChild(t),t.click(),setTimeout(function(){document.body.removeChild(t),URL.revokeObjectURL(r)},0)}},V=function(){m(!1)};return(0,h.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("h2",{className:"text-xl font-bold",children:"Generador de Mapas Mentales"}),(0,h.jsx)(L,{})]}),!O&&"free"===E&&(0,h.jsx)("div",{className:"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg",children:(0,h.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,h.jsx)(l.F5$,{className:"w-5 h-5 text-purple-600 mt-0.5"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"font-medium text-purple-900",children:"L\xedmites del Plan Gratuito"}),(0,h.jsxs)("p",{className:"text-sm text-purple-700 mt-1",children:["M\xe1ximo ",S.qo.free.limits.mindMapsForTrial," mapas mentales durante el per\xedodo de prueba. Para generar mapas mentales ilimitados,",(0,h.jsx)(_(),{href:"/upgrade-plan",className:"font-medium underline hover:text-purple-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,h.jsxs)("form",{onSubmit:R(G),className:"space-y-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe el mapa mental que deseas generar:"}),(0,h.jsx)("input",B(B({id:"peticion",type:"text"},T("peticion")),{},{disabled:P,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Ej: Genera un mapa mental sobre los conceptos principales del tema 1"})),z.peticion&&(0,h.jsx)("span",{className:"text-red-500 text-sm",children:z.peticion.message}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 un mapa mental basado en los documentos seleccionados y tu petici\xf3n."})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",disabled:P||0===t.length,children:P?"Generando en segundo plano...":"Generar Mapa Mental"}),A&&(0,h.jsxs)("div",{className:"text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg",children:[(0,h.jsx)("span",{className:"font-medium",children:"Generando:"})," ",A.title]})]})]}),P&&(0,h.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,h.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-blue-800 font-medium",children:"Generando mapa mental en segundo plano"}),(0,h.jsx)("p",{className:"text-blue-600 text-sm",children:"Puedes continuar usando otras funciones de la aplicaci\xf3n"})]})]})}),a&&(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Vista previa:"}),(0,h.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg border overflow-hidden",style:{maxHeight:"500px"},children:(0,h.jsx)("iframe",{srcDoc:a,title:"Vista previa del mapa mental",className:"w-full h-96 border-0",sandbox:"allow-scripts allow-same-origin"})}),(0,h.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,h.jsx)("p",{className:"text-sm text-gray-500",children:"Vista previa limitada. Usa pantalla completa o descarga para mejor experiencia."}),(0,h.jsxs)("div",{className:"flex gap-2",children:[(0,h.jsxs)("button",{type:"button",onClick:function(){m(!0)},className:"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z",clipRule:"evenodd"})}),"Pantalla Completa"]}),(0,h.jsxs)("button",{type:"button",onClick:q,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Descargar Mapa Mental"]})]})]})]}),u&&a&&(0,h.jsxs)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center animate-fadeIn",children:[(0,h.jsxs)("div",{className:"relative w-full h-full max-w-none max-h-none p-4 animate-scaleIn",children:[(0,h.jsxs)("div",{className:"absolute top-4 left-4 right-4 z-10 flex justify-between items-center bg-white bg-opacity-90 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg",children:[(0,h.jsx)("div",{className:"flex items-center space-x-4",children:(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Mapa Mental - Vista Completa"})}),(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsxs)("button",{onClick:q,className:"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm flex items-center",title:"Descargar mapa mental",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Descargar"]}),(0,h.jsxs)("button",{onClick:V,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm flex items-center",title:"Cerrar pantalla completa",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Cerrar"]})]})]}),(0,h.jsx)("div",{className:"w-full h-full pt-16 pb-4",children:(0,h.jsx)("iframe",{srcDoc:a,title:"Mapa mental en pantalla completa",className:"w-full h-full border-0 rounded-lg shadow-2xl bg-white",sandbox:"allow-scripts allow-same-origin"})})]}),(0,h.jsx)("div",{className:"absolute inset-0 -z-10",onClick:V,"aria-label":"Cerrar pantalla completa"})]})]})}function W(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function Y(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?W(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):W(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function K(e){var r,t,n,a=e.documentosSeleccionados,c=(0,i.useState)(""),d=c[0],u=c[1],m=(0,i.useState)(""),f=m[0],g=m[1],b=(0,i.useState)([]),y=b[0],N=b[1],w=(0,i.useState)(!1),k=w[0],O=w[1],A=(0,i.useState)(0),P=A[0],D=A[1],T=(0,i.useState)(!1),R=T[0],z=T[1],L=(0,i.useState)([]),G=L[0],q=L[1],V=(0,i.useState)("nueva"),B=V[0],$=V[1],W=(0,i.useState)(!1),K=(W[0],W[1]),X=(0,i.useState)(""),J=X[0],Z=X[1],Q=F(),ee=Q.generateFlashcards,er=Q.isGenerating,et=Q.getActiveTask;(0,I.M)().getTask;var en=H().executeWithGuard;(0,C.A)().user;var es=U(),ea=es.plan,eo=es.isLoading;et("flashcards");var ei=er("flashcards");M({taskType:"flashcards",onResult:function(e){N(e),p.oR.success("\xa1Flashcards generadas exitosamente!")},onError:function(e){p.oR.error("Error al generar flashcards: ".concat(e))}});var ec=(0,v.mN)({resolver:(0,j.u)(E),defaultValues:{peticion:"",cantidad:10}}),el=ec.register,ed=ec.handleSubmit,eu=ec.formState.errors;(0,i.useEffect)(function(){em()},[]);var em=(r=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return K(!0),e.prev=1,e.next=4,(0,x.oE)();case 4:q(e.sent),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error al cargar colecciones:",e.t0),p.oR.error("No se pudieron cargar las colecciones existentes.");case 12:return e.prev=12,K(!1),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[1,8,12,15]])})),function(){return r.apply(this,arguments)}),ex=(t=(0,s.A)(o().mark(function e(r){var t,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.map(function(e){return e.contenido}),N([]),O(!1),e.next=5,en("flashcards",(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ee({peticion:r.peticion,contextos:t,cantidad:r.cantidad});case 2:return d||u("Flashcards: ".concat(r.peticion.substring(0,50)).concat(r.peticion.length>50?"...":"")),e.abrupt("return",!0);case 4:case"end":return e.stop()}},e)})),r.cantidad);case 5:(n=e.sent).success?p.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):p.oR.error(n.error||"Error al iniciar la generaci\xf3n de flashcards");case 7:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)}),ep=(n=(0,s.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==y.length){e.next=3;break}return Z("No hay flashcards para guardar"),e.abrupt("return");case 3:if(!("nueva"===B&&!d.trim())){e.next=6;break}return Z("Por favor, proporciona un t\xedtulo para la nueva colecci\xf3n"),e.abrupt("return");case 6:if("nueva"===B||""!==B){e.next=9;break}return Z("Por favor, selecciona una colecci\xf3n existente"),e.abrupt("return");case 9:if(Z(""),e.prev=10,"nueva"!==B){e.next=19;break}return e.next=14,(0,x.qJ)(d,f);case 14:if(r=e.sent){e.next=17;break}throw Error("No se pudo crear la colecci\xf3n");case 17:e.next=20;break;case 19:r=B;case 20:return t=y.map(function(e){return{coleccion_id:r,pregunta:e.pregunta,respuesta:e.respuesta}}),e.next=23,(0,x.yK)(t);case 23:if(e.sent){e.next=26;break}throw Error("No se pudieron guardar las flashcards");case 26:if(O(!0),"nueva"!==B){e.next=30;break}return e.next=30,em();case 30:e.next=36;break;case 32:e.prev=32,e.t0=e.catch(10),console.error("Error al guardar las flashcards:",e.t0),Z("Ha ocurrido un error al guardar las flashcards. Por favor, int\xe9ntalo de nuevo.");case 36:case"end":return e.stop()}},e,null,[[10,32]])})),function(){return n.apply(this,arguments)}),eh=function(){z(!R)};return(0,h.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,h.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Generador de Flashcards"}),!eo&&"free"===ea&&(0,h.jsx)("div",{className:"mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,h.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,h.jsx)(l.F5$,{className:"w-5 h-5 text-orange-600 mt-0.5"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"font-medium text-orange-900",children:"L\xedmites del Plan Gratuito"}),(0,h.jsxs)("p",{className:"text-sm text-orange-700 mt-1",children:["M\xe1ximo ",S.qo.free.limits.flashcardsForTrial," flashcards durante el per\xedodo de prueba. Para generar flashcards ilimitadas,",(0,h.jsx)(_(),{href:"/upgrade-plan",className:"font-medium underline hover:text-orange-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,h.jsxs)("form",{onSubmit:ed(ex),className:"space-y-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe las flashcards que deseas generar:"}),(0,h.jsx)("textarea",Y(Y({id:"peticion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:3},el("peticion")),{},{placeholder:"Ej: Genera flashcards sobre los conceptos principales del tema 1",disabled:ei})),eu.peticion&&(0,h.jsx)("span",{className:"text-red-500 text-sm",children:eu.peticion.message}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 flashcards basadas en los documentos seleccionados y tu petici\xf3n."})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"cantidad",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de flashcards:"}),(0,h.jsx)("input",Y(Y({id:"cantidad",type:"number",min:"1",max:"30",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},el("cantidad",{valueAsNumber:!0})),{},{disabled:ei})),eu.cantidad&&(0,h.jsx)("span",{className:"text-red-500 text-sm",children:eu.cantidad.message}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica cu\xe1ntas flashcards quieres generar (entre 1 y 30)."})]}),J&&(0,h.jsx)("div",{className:"text-red-500 text-sm",children:J}),(0,h.jsx)("div",{children:(0,h.jsx)("button",{type:"submit",className:"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:ei||0===a.length,children:ei?"Generando...":"Generar Flashcards"})})]}),ei&&(0,h.jsxs)("div",{className:"mt-4 text-center",children:[(0,h.jsx)("p",{className:"text-gray-600",children:"Generando flashcards, por favor espera..."}),(0,h.jsx)("div",{className:"mt-2 flex justify-center",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})})]}),y.length>0&&(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Flashcards generadas (",y.length,")"]}),!k&&(0,h.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,h.jsx)("h4",{className:"font-medium mb-2",children:"Guardar flashcards"}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"tipoColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"\xbfD\xf3nde quieres guardar estas flashcards?"}),(0,h.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{type:"radio",id:"nuevaColeccion",name:"tipoColeccion",value:"nueva",checked:"nueva"===B,onChange:function(){return $("nueva")},className:"mr-2",disabled:ei}),(0,h.jsx)("label",{htmlFor:"nuevaColeccion",className:"text-sm text-gray-700",children:"Crear nueva colecci\xf3n"})]}),(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{type:"radio",id:"coleccionExistente",name:"tipoColeccion",value:"existente",checked:"nueva"!==B,onChange:function(){G.length>0?$(G[0].id):$("")},className:"mr-2",disabled:ei||0===G.length}),(0,h.jsxs)("label",{htmlFor:"coleccionExistente",className:"text-sm text-gray-700",children:["A\xf1adir a una colecci\xf3n existente",0===G.length&&(0,h.jsx)("span",{className:"text-gray-500 ml-2",children:"(No hay colecciones disponibles)"})]})]})]})]}),"nueva"===B&&(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"tituloColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo de la nueva colecci\xf3n:"}),(0,h.jsx)("input",{type:"text",id:"tituloColeccion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:d,onChange:function(e){return u(e.target.value)},disabled:ei})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"descripcionColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional):"}),(0,h.jsx)("textarea",{id:"descripcionColeccion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,value:f,onChange:function(e){return g(e.target.value)},disabled:ei})]})]}),"nueva"!==B&&G.length>0&&(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"coleccionExistenteSelect",className:"block text-sm font-medium text-gray-700 mb-1",children:"Selecciona una colecci\xf3n:"}),(0,h.jsx)("select",{id:"coleccionExistenteSelect",className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:B,onChange:function(e){return $(e.target.value)},disabled:ei,children:G.map(function(e){return(0,h.jsx)("option",{value:e.id,children:e.titulo},e.id)})})]}),(0,h.jsx)("div",{className:"mt-4",children:(0,h.jsx)("button",{type:"button",onClick:ep,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:!1,children:"Guardar flashcards"})})]}),k&&(0,h.jsxs)("div",{className:"bg-green-100 text-green-800 p-4 rounded-lg mb-6",children:[(0,h.jsx)("p",{className:"font-medium",children:"nueva"===B?"\xa1Nueva colecci\xf3n creada correctamente!":"\xa1Flashcards a\xf1adidas a la colecci\xf3n correctamente!"}),(0,h.jsxs)("p",{className:"text-sm mt-1",children:["Puedes acceder a ","nueva"===B?"ella":"las flashcards",' desde la secci\xf3n de "Mis Flashcards".']})]}),(0,h.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mb-4",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("button",{onClick:function(){P>0&&(D(P-1),z(!1))},disabled:0===P,className:"p-2 rounded-full ".concat(0===P?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,h.jsxs)("span",{className:"text-gray-600",children:[P+1," de ",y.length]}),(0,h.jsx)("button",{onClick:function(){P<y.length-1&&(D(P+1),z(!1))},disabled:P===y.length-1,className:"p-2 rounded-full ".concat(P===y.length-1?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,h.jsx)("div",{className:"min-h-[200px] flex items-center justify-center cursor-pointer",onClick:eh,children:(0,h.jsx)("div",{className:"text-center p-4 w-full",children:R?(0,h.jsxs)("div",{children:[(0,h.jsx)("div",{className:"font-semibold text-lg mb-2",children:y[P].pregunta}),(0,h.jsx)("div",{className:"border-t pt-4 text-left whitespace-pre-wrap",children:y[P].respuesta})]}):(0,h.jsx)("div",{className:"font-semibold text-lg",children:y[P].pregunta})})}),(0,h.jsx)("div",{className:"mt-4 text-center",children:(0,h.jsx)("button",{onClick:eh,className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:R?"Ocultar respuesta":"Mostrar respuesta"})})]}),(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsx)("h4",{className:"font-medium mb-2",children:"Todas las flashcards:"}),(0,h.jsx)("div",{className:"space-y-2",children:y.map(function(e,r){return(0,h.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ".concat(r===P?"border-blue-500 bg-blue-50":""),onClick:function(){D(r),z(!1)},children:(0,h.jsx)("p",{className:"font-medium",children:e.pregunta})},r)})})]})]})]})}var X=t(55564),J=t(66430);function Z(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function Q(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?Z(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Z(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function ee(){return(ee=(0,s.A)(o().mark(function e(){var r,t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,J.iF)();case 3:if(r=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",[]);case 8:return e.next=10,X.N.from("resumenes").select("*").eq("user_id",r.id).order("creado_en",{ascending:!1});case 10:if(n=(t=e.sent).data,!(s=t.error)){e.next=16;break}return console.error("Error al obtener res\xfamenes:",s),e.abrupt("return",[]);case 16:return e.abrupt("return",n||[]);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error al obtener res\xfamenes:",e.t0),e.abrupt("return",[]);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function er(e){return et.apply(this,arguments)}function et(){return(et=(0,s.A)(o().mark(function e(r){var t,n,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,J.iF)();case 3:if(t=e.sent.user){e.next=8;break}return console.log("No hay usuario autenticado para verificar resumen"),e.abrupt("return",!1);case 8:return console.log("Verificando si existe resumen para documento ".concat(r," y usuario ").concat(t.id)),e.next=11,X.N.from("resumenes").select("id").eq("user_id",t.id).eq("documento_id",r).maybeSingle();case 11:if(s=(n=e.sent).data,!(a=n.error)){e.next=17;break}return console.error("Error al verificar resumen existente:",a),e.abrupt("return",!1);case 17:return i=!!s,console.log("Resultado verificaci\xf3n resumen: ".concat(i?"existe":"no existe")),e.abrupt("return",i);case 22:return e.prev=22,e.t0=e.catch(0),console.error("Error al verificar resumen existente:",e.t0),e.abrupt("return",!1);case 26:case"end":return e.stop()}},e,null,[[0,22]])}))).apply(this,arguments)}function en(){return(en=(0,s.A)(o().mark(function e(r,t,n,s){var a,i,c,l,d;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,J.iF)();case 3:if(a=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado para guardar resumen"),e.abrupt("return",null);case 8:return console.log("Intentando guardar resumen para documento ".concat(r," y usuario ").concat(a.id)),e.next=11,er(r);case 11:if(!e.sent){e.next=15;break}return console.error("Ya existe un resumen para este documento"),e.abrupt("return",null);case 15:return console.log("Datos del resumen a insertar:",Q(Q({},i={user_id:a.id,documento_id:r,titulo:t.trim(),contenido:n.trim(),instrucciones:(null==s?void 0:s.trim())||null}),{},{contenido:"".concat(i.contenido.substring(0,100),"...")})),e.next=19,X.N.from("resumenes").insert([i]).select().single();case 19:if(l=(c=e.sent).data,!(d=c.error)){e.next=25;break}return console.error("Error al guardar resumen en Supabase:",d),e.abrupt("return",null);case 25:if(null!=l&&l.id){e.next=28;break}return console.error("No se recibi\xf3 ID del resumen guardado"),e.abrupt("return",null);case 28:return console.log("Resumen guardado exitosamente con ID: ".concat(l.id)),e.abrupt("return",l.id);case 32:return e.prev=32,e.t0=e.catch(0),console.error("Error al guardar resumen:",e.t0),e.abrupt("return",null);case 36:case"end":return e.stop()}},e,null,[[0,32]])}))).apply(this,arguments)}function es(){return(es=(0,s.A)(o().mark(function e(r){var t,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,J.iF)();case 3:if(t=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",!1);case 8:return e.next=10,X.N.from("resumenes").delete().eq("id",r).eq("user_id",t.id);case 10:if(!(n=e.sent.error)){e.next=15;break}return console.error("Error al eliminar resumen:",n),e.abrupt("return",!1);case 15:return e.abrupt("return",!0);case 18:return e.prev=18,e.t0=e.catch(0),console.error("Error al eliminar resumen:",e.t0),e.abrupt("return",!1);case 22:case"end":return e.stop()}},e,null,[[0,18]])}))).apply(this,arguments)}function ea(){return(ea=(0,s.A)(o().mark(function e(r,t){var n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,J.iF)();case 3:if(n=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado para guardar resumen editado"),e.abrupt("return",!1);case 8:return console.log("Guardando versi\xf3n editada del resumen ".concat(r)),e.next=11,X.N.from("resumenes").update({contenido_editado:t.trim(),editado:!0,fecha_edicion:new Date().toISOString(),actualizado_en:new Date().toISOString()}).eq("id",r).eq("user_id",n.id);case 11:if(!(s=e.sent.error)){e.next=16;break}return console.error("Error al guardar resumen editado en Supabase:",s),e.abrupt("return",!1);case 16:return console.log("✅ Resumen editado guardado exitosamente"),e.abrupt("return",!0);case 20:return e.prev=20,e.t0=e.catch(0),console.error("Error al guardar resumen editado:",e.t0),e.abrupt("return",!1);case 24:case"end":return e.stop()}},e,null,[[0,20]])}))).apply(this,arguments)}function eo(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function ei(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?eo(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):eo(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var ec=y.z.object({instrucciones:y.z.string().min(10,"Las instrucciones deben tener al menos 10 caracteres")});function el(e){var r,t=e.documentosSeleccionados,n=e.onSummaryGenerated,a=(0,i.useState)(null),c=a[0],l=a[1],d=(0,i.useState)(0),u=d[0],m=d[1],x=F(),f=x.generateResumen,g=x.isGenerating,b=x.getActiveTask,y=(0,v.mN)({resolver:(0,j.u)(ec),defaultValues:{instrucciones:"Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y did\xe1ctica."}}),N=y.register,w=y.handleSubmit,k=y.formState.errors,E=y.reset,C=1===t.length,S=t[0],O=S?S?S.titulo&&0!==S.titulo.trim().length?S.contenido&&0!==S.contenido.trim().length?S.contenido.trim().length<50?{valido:!1,error:"El contenido del documento es demasiado corto para generar un resumen \xfatil"}:{valido:!0}:{valido:!1,error:"El documento debe tener contenido"}:{valido:!1,error:"El documento debe tener un t\xedtulo"}:{valido:!1,error:"No se ha proporcionado ning\xfan documento"}:{valido:!1,error:"No hay documento seleccionado"},A=b("resumen"),P=g("resumen");(0,i.useEffect)(function(){(null==A?void 0:A.status)==="completed"&&A.result?(l(A.result),m(0)):(null==A?void 0:A.status)==="error"&&m(0)},[A]);var D=(r=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(!C||!S||!O.valido)){e.next=3;break}return p.oR.error("No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado."),e.abrupt("return");case 3:return e.prev=3,console.log("\uD83D\uDE80 Iniciando generaci\xf3n de resumen..."),m(S&&S.contenido?Math.max(15,Math.min(120,Math.ceil(S.contenido.split(/\s+/).length/100))):30),console.log("\uD83D\uDD0D Verificando si ya existe resumen..."),e.next=9,er(S.id);case 9:if(!e.sent){e.next=14;break}return console.log("⚠️ Ya existe un resumen para este documento"),p.oR.error("Ya existe un resumen para este documento. Solo se permite un resumen por tema."),e.abrupt("return");case 14:return console.log("✅ No existe resumen previo, continuando..."),e.next=17,f({documento:S,instrucciones:r.instrucciones,onComplete:function(){var e=(0,s.A)(o().mark(function e(t){var s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("✅ Resumen generado, guardando en Supabase..."),e.next=3,function(e,r,t,n){return en.apply(this,arguments)}(S.id,"Resumen: ".concat(S.titulo),t,r.instrucciones);case 3:(s=e.sent)?(console.log("✅ Resumen guardado exitosamente con ID:",s),null==n||n(s),E()):(console.error("❌ Error al guardar el resumen - no se recibi\xf3 ID"),p.oR.error("Error al guardar el resumen en la base de datos"));case 5:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}(),onError:function(e){console.error("❌ Error en generaci\xf3n de resumen:",e),m(0)}});case 17:e.next=25;break;case 19:e.prev=19,e.t0=e.catch(3),console.error("Error al iniciar generaci\xf3n de resumen:",e.t0),t=e.t0 instanceof Error?e.t0.message:"Error al generar el resumen",p.oR.error(t),m(0);case 25:case"end":return e.stop()}},e,null,[[3,19]])})),function(e){return r.apply(this,arguments)});return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,h.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"\uD83D\uDCC4 Generaci\xf3n de Res\xfamenes"}),C?O.valido?(0,h.jsxs)("div",{className:"text-blue-700",children:[(0,h.jsx)("p",{className:"font-medium",children:"✅ Documento seleccionado:"}),(0,h.jsxs)("p",{className:"text-sm mt-1",children:[(0,h.jsx)("strong",{children:S.titulo}),S.numero_tema&&" (Tema ".concat(S.numero_tema,")")]}),(0,h.jsxs)("p",{className:"text-xs mt-1 text-blue-600",children:["Contenido: ~",S.contenido.split(/\s+/).length," palabras"]})]}):(0,h.jsxs)("div",{className:"text-red-700",children:[(0,h.jsx)("p",{className:"font-medium",children:"⚠️ Documento no v\xe1lido"}),(0,h.jsx)("p",{className:"text-sm mt-1",children:O.error})]}):(0,h.jsxs)("div",{className:"text-red-700",children:[(0,h.jsx)("p",{className:"font-medium",children:"⚠️ Selecci\xf3n incorrecta"}),(0,h.jsx)("p",{className:"text-sm mt-1",children:0===t.length?"Debes seleccionar exactamente un documento para generar un resumen.":"Tienes ".concat(t.length," documentos seleccionados. Solo se permite generar un resumen por tema.")})]})]}),C&&O.valido&&(0,h.jsxs)("form",{onSubmit:w(D),className:"space-y-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"instrucciones",className:"block text-gray-700 text-sm font-bold mb-2",children:"Instrucciones para el resumen:"}),(0,h.jsx)("textarea",ei(ei({id:"instrucciones"},N("instrucciones")),{},{disabled:P,rows:4,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical",placeholder:"Describe c\xf3mo quieres que sea el resumen..."})),k.instrucciones&&(0,h.jsx)("span",{className:"text-red-500 text-sm",children:k.instrucciones.message}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen."})]}),(0,h.jsx)("button",{type:"submit",disabled:P,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:P?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,h.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,h.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Generando resumen...",u>0&&" (~".concat(u,"s)")]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,h.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"}),(0,h.jsx)("path",{fillRule:"evenodd",d:"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z",clipRule:"evenodd"})]}),"Generar Resumen"]})}),P&&(0,h.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,h.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,h.jsx)("strong",{children:"⏳ Generando resumen..."}),(0,h.jsx)("br",{}),"La IA est\xe1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar ",u>0?"aproximadamente ".concat(u," segundos"):"unos momentos",".",(0,h.jsx)("br",{}),(0,h.jsx)("em",{children:"Puedes navegar a otras pesta\xf1as mientras se genera."})]})})]}),c&&(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"\uD83D\uDCCB Resumen Generado"}),(0,h.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,h.jsx)("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:c.replace(/\n/g,"<br />")}})}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"✅ Resumen guardado exitosamente. Puedes acceder a \xe9l desde la secci\xf3n de res\xfamenes."})]})]})}function ed(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function eu(e){var r,t,n,a=e.refreshTrigger,c=(0,i.useState)([]),d=c[0],u=c[1],m=(0,i.useState)(!0),x=m[0],f=m[1],g=(0,i.useState)(null),b=g[0],v=g[1],j=(0,i.useState)(!1),y=j[0],N=j[1],w=(0,i.useState)(null),k=w[0],E=w[1],C=(0,i.useState)(!1);C[0],C[1];var S=(0,i.useState)("editada"),O=S[0],A=S[1],P=(r=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,f(!0),e.next=4,function(){return ee.apply(this,arguments)}();case 4:u(e.sent),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error al cargar res\xfamenes:",e.t0),p.oR.error("Error al cargar los res\xfamenes");case 12:return e.prev=12,f(!1),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[0,8,12,15]])})),function(){return r.apply(this,arguments)});(0,i.useEffect)(function(){P()},[a]);var D=(t=(0,s.A)(o().mark(function e(r,t){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(confirm('\xbfEst\xe1s seguro de que quieres eliminar el resumen "'.concat(t,'"?'))){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,function(e){return es.apply(this,arguments)}(r);case 5:if(!e.sent){e.next=12;break}return p.oR.success("Resumen eliminado exitosamente"),e.next=10,P();case 10:e.next=13;break;case 12:p.oR.error("Error al eliminar el resumen");case 13:e.next=19;break;case 15:e.prev=15,e.t0=e.catch(2),console.error("Error al eliminar resumen:",e.t0),p.oR.error("Error al eliminar el resumen");case 19:case"end":return e.stop()}},e,null,[[2,15]])})),function(e,r){return t.apply(this,arguments)}),T=function(e){v(e),e.editado&&e.contenido_editado?A("editada"):A("original"),N(!0)},_=(n=(0,s.A)(o().mark(function e(r){var t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(confirm('\xbfEst\xe1s seguro de que quieres editar el resumen "'.concat(r.titulo,'"? Esta acci\xf3n crear\xe1 una versi\xf3n condensada del resumen original.'))){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,E(r.id),p.oR.loading("Editando resumen con IA...",{id:"editing-summary"}),t=r.contenido_editado||r.contenido,e.next=8,fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"editarResumen",contextos:[t]})});case 8:if((n=e.sent).ok){e.next=11;break}throw Error("Error en la respuesta del servidor");case 11:return e.next=13,n.json();case 13:if((s=e.sent).result){e.next=16;break}throw Error("No se recibi\xf3 resultado de la edici\xf3n");case 16:return e.next=18,function(e,r){return ea.apply(this,arguments)}(r.id,s.result);case 18:if(!e.sent){e.next=25;break}return p.oR.success("Resumen editado exitosamente",{id:"editing-summary"}),e.next=23,P();case 23:e.next=26;break;case 25:throw Error("Error al guardar el resumen editado");case 26:e.next=32;break;case 28:e.prev=28,e.t0=e.catch(2),console.error("Error al editar resumen:",e.t0),p.oR.error("Error al editar el resumen",{id:"editing-summary"});case 32:return e.prev=32,E(null),e.finish(32);case 35:case"end":return e.stop()}},e,null,[[2,28,32,35]])})),function(e){return n.apply(this,arguments)}),R=function(e){try{var r,t="";y&&b&&b.id===e.id?(r="editada"===O&&b.contenido_editado?b.contenido_editado:b.contenido,t="editada"===O&&b.contenido_editado?" (Versi\xf3n Editada)":""):(r=e.contenido_editado||e.contenido,t=e.contenido_editado?" (Versi\xf3n Editada)":"");var n=function(e,r,t){var n,s=(e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n")).split(/\n\s*\n/),a="",o=function(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return ed(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ed(e,r)}}(e))){t&&(e=t);var n=0,s=function(){};return{s:s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){i=!0,a=e},f:function(){try{o||null==t.return||t.return()}finally{if(i)throw a}}}}(s);try{for(o.s();!(n=o.n()).done;){var i=n.value;if(i=i.trim()){var c=i.replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/^\* (.*$)/gim,"<li>$1</li>").replace(/^\+ (.*$)/gim,"<li>$1</li>").replace(/\n/g," ");c.includes("<li>")?(c=c.replace(/(<li>.*?<\/li>)/g,function(e){return"<ul>".concat(e,"</ul>")}),a+=c):c.startsWith("<h1>")||c.startsWith("<h2>")||c.startsWith("<h3>")||c.startsWith("<blockquote>")?a+=c:a+="<p>".concat(c,"</p>")}}}catch(e){o.e(e)}finally{o.f()}var l="";if(r&&(l+="<h1>".concat(r,"</h1>")),t){if(l+='<div class="metadata">',t.createdAt){var d=new Date(t.createdAt).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});l+="<p><strong>Fecha de creaci\xf3n:</strong> ".concat(d,"</p>")}t.author&&(l+="<p><strong>Autor:</strong> ".concat(t.author,"</p>")),t.instructions&&(l+="<p><strong>Instrucciones utilizadas:</strong> ".concat(t.instructions,"</p>")),l+="</div>"}return l+=a}(r,e.titulo+t,{createdAt:e.creado_en,instructions:e.instrucciones||void 0,author:"OposiAI"}),s=window.open("","_blank");s&&(s.document.write("\n          <html>\n            <head>\n              <title>".concat(e.titulo,"</title>\n              <style>\n                * { box-sizing: border-box; }\n                body {\n                  font-family: Arial, sans-serif;\n                  margin: 20mm;\n                  line-height: 1.6;\n                  color: #333;\n                  font-size: 12px;\n                }\n                h1 {\n                  color: #2563eb;\n                  border-bottom: 2px solid #2563eb;\n                  padding-bottom: 10px;\n                  margin: 0 0 20px 0;\n                  font-size: 24px;\n                  text-align: left;\n                }\n                h2 {\n                  color: #1e40af;\n                  border-bottom: 1px solid #cbd5e1;\n                  padding-bottom: 5px;\n                  margin: 25px 0 15px 0;\n                  font-size: 20px;\n                  text-align: left;\n                }\n                h3 {\n                  color: #1e40af;\n                  margin: 20px 0 10px 0;\n                  font-size: 16px;\n                  text-align: left;\n                }\n                p {\n                  margin: 12px 0;\n                  text-align: justify;\n                  text-justify: inter-word;\n                  hyphens: auto;\n                  word-wrap: break-word;\n                }\n                strong {\n                  color: #1e40af;\n                  font-weight: bold;\n                }\n                em {\n                  font-style: italic;\n                  color: #64748b;\n                }\n                ul, ol {\n                  margin: 12px 0;\n                  padding-left: 20px;\n                }\n                li {\n                  margin: 6px 0;\n                  text-align: justify;\n                }\n                blockquote {\n                  margin: 15px 0;\n                  padding: 12px 15px;\n                  background-color: #f8fafc;\n                  border-left: 4px solid #2563eb;\n                  font-style: italic;\n                  text-align: justify;\n                }\n                .metadata {\n                  font-size: 11px;\n                  color: #64748b;\n                  margin-bottom: 25px;\n                  padding: 12px;\n                  background-color: #f8fafc;\n                  border-radius: 5px;\n                  border: 1px solid #e2e8f0;\n                }\n                .metadata p {\n                  margin: 4px 0;\n                  text-align: left;\n                }\n                @media print {\n                  body {\n                    margin: 20mm;\n                    font-size: 12px;\n                  }\n                  .metadata {\n                    background-color: #f9f9f9;\n                    border: 1px solid #ddd;\n                  }\n                  blockquote {\n                    background-color: #f9f9f9;\n                  }\n                }\n              </style>\n            </head>\n            <body>\n              ").concat(n,"\n            </body>\n          </html>\n        ")),s.document.close(),s.focus(),s.print())}catch(e){console.error("Error al imprimir:",e),p.oR.error("Error al preparar la impresi\xf3n")}},z=function(e){return new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})};return x?(0,h.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,h.jsx)("span",{className:"ml-2 text-gray-600",children:"Cargando res\xfamenes..."})]}):(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"\uD83D\uDCDA Mis Res\xfamenes"}),(0,h.jsxs)("button",{onClick:P,className:"text-blue-600 hover:text-blue-800 text-sm flex items-center",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,h.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})}),"Actualizar"]})]}),(0,h.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)("div",{className:"flex-shrink-0",children:(0,h.jsx)(l.Pj4,{className:"h-5 w-5 text-blue-600 mt-0.5"})}),(0,h.jsxs)("div",{className:"ml-3",children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-blue-800",children:"✨ Funci\xf3n de Edici\xf3n con IA"}),(0,h.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Puedes usar la IA para condensar y refinar tus res\xfamenes. La edici\xf3n mantiene toda la informaci\xf3n esencial mientras elimina redundancias, creando un texto m\xe1s conciso y estructurado (3.200-3.800 palabras). El resumen original se conserva siempre."})]})]})}),0===d.length?(0,h.jsxs)("div",{className:"text-center py-8 bg-gray-50 rounded-lg border border-gray-200",children:[(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,h.jsx)("p",{className:"text-gray-600 mb-2",children:"No tienes res\xfamenes creados"}),(0,h.jsx)("p",{className:"text-sm text-gray-500",children:"Selecciona un documento y genera tu primer resumen para empezar a estudiar de manera m\xe1s eficiente."})]}):(0,h.jsx)("div",{className:"grid gap-4",children:d.map(function(e){return(0,h.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,h.jsxs)("div",{className:"flex justify-between items-start",children:[(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,h.jsx)("h4",{className:"font-medium text-gray-900",children:e.titulo}),e.editado&&(0,h.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"✨ Editado"})]}),(0,h.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Creado: ",z(e.creado_en),e.editado&&e.fecha_edicion&&(0,h.jsxs)("span",{className:"ml-2 text-green-600",children:["• Editado: ",z(e.fecha_edicion)]})]}),e.instrucciones&&(0,h.jsxs)("p",{className:"text-xs text-gray-500 mb-2 italic",children:['"',e.instrucciones.substring(0,100),e.instrucciones.length>100?"...":"",'"']}),(0,h.jsxs)("p",{className:"text-xs text-gray-400",children:["Contenido original: ~",e.contenido.split(/\s+/).length," palabras",e.contenido_editado&&(0,h.jsxs)("span",{className:"ml-2 text-green-600",children:["• Versi\xf3n editada: ~",e.contenido_editado.split(/\s+/).length," palabras"]})]})]}),(0,h.jsxs)("div",{className:"flex flex-wrap gap-2 ml-4",children:[(0,h.jsxs)("button",{onClick:function(){return T(e)},className:"flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Ver resumen completo",children:[(0,h.jsx)(l.Vap,{className:"w-3 h-3"}),"Ver"]}),(0,h.jsxs)("button",{onClick:function(){return _(e)},disabled:k===e.id,className:"flex items-center gap-1 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Editar resumen con IA",children:[(0,h.jsx)(l.Pj4,{className:"w-3 h-3"}),k===e.id?"Editando...":"Editar"]}),(0,h.jsxs)("button",{onClick:function(){return R(e)},className:"flex items-center gap-1 bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Imprimir resumen",children:[(0,h.jsx)(l.Mvz,{className:"w-3 h-3"}),"Imprimir"]}),(0,h.jsxs)("button",{onClick:function(){return D(e.id,e.titulo)},className:"flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Eliminar resumen",children:[(0,h.jsx)(l.IXo,{className:"w-3 h-3"}),"Eliminar"]})]})]})},e.id)})}),y&&b&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:function(){return N(!1)}}),(0,h.jsxs)("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-11/12 max-w-4xl max-h-5/6 bg-white rounded-lg shadow-xl z-50 overflow-hidden",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-gray-200",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:b.titulo}),(0,h.jsx)("button",{onClick:function(){return N(!1)},className:"text-gray-400 hover:text-gray-600",children:(0,h.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),b.editado&&b.contenido_editado&&(0,h.jsxs)("div",{className:"p-4 border-b border-gray-200 flex space-x-2",children:[(0,h.jsx)("button",{onClick:function(){return A("editada")},className:"px-3 py-1 rounded text-xs font-medium transition-colors\n                    ".concat("editada"===O?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),title:"Versi\xf3n concisa refinada por IA",children:"✨ Versi\xf3n Editada"}),(0,h.jsx)("button",{onClick:function(){return A("original")},className:"px-3 py-1 rounded text-xs font-medium transition-colors\n                    ".concat("original"===O?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),title:"Primer borrador generado por IA",children:"\uD83D\uDCDC Versi\xf3n Original"})]}),(0,h.jsxs)("div",{className:"p-4 overflow-y-auto max-h-96",children:[(0,h.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,h.jsxs)("p",{children:[(0,h.jsx)("strong",{children:"Creado:"})," ",z(b.creado_en)]}),b.editado&&b.fecha_edicion&&(0,h.jsxs)("p",{children:[(0,h.jsx)("strong",{children:"Editado:"})," ",z(b.fecha_edicion)]}),b.instrucciones&&(0,h.jsxs)("p",{children:[(0,h.jsx)("strong",{children:"Instrucciones:"})," ",b.instrucciones]}),b.editado&&b.contenido_editado&&(0,h.jsx)("div",{className:"mt-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs",children:"editada"===O?(0,h.jsx)("p",{className:"text-green-800 font-medium",children:"✨ Mostrando versi\xf3n editada por IA (condensada y refinada)."}):(0,h.jsx)("p",{className:"text-blue-800 font-medium",children:"\uD83D\uDCDC Mostrando versi\xf3n original."})})]}),(0,h.jsx)("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:("editada"===O&&b.contenido_editado?b.contenido_editado:b.contenido).replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/\n/g,"<br />")}})]}),(0,h.jsxs)("div",{className:"p-4 border-t border-gray-200 flex justify-between items-center",children:[(0,h.jsx)("div",{className:"flex gap-2",children:(0,h.jsxs)("button",{onClick:function(){return R(b)},className:"flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors",children:[(0,h.jsx)(l.Mvz,{className:"w-4 h-4"}),"Imprimir Versi\xf3n Actual"]})}),(0,h.jsx)("button",{onClick:function(){return N(!1)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors",children:"Cerrar"})]})]})]})]})}function em(e){var r,t,n=e.onDocumentDeleted,a=(0,i.useState)([]),c=a[0],d=a[1],u=(0,i.useState)(!0),m=u[0],f=u[1],g=(0,i.useState)(null),b=g[0],v=g[1],j=(0,i.useState)(null),y=j[0],N=j[1],w=(r=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return f(!0),e.prev=1,e.next=4,(0,x.R1)();case 4:d(e.sent),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error al cargar documentos:",e.t0),p.Ay.error("Error al cargar documentos");case 12:return e.prev=12,f(!1),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[1,8,12,15]])})),function(){return r.apply(this,arguments)});(0,i.useEffect)(function(){w()},[]);var k=(t=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return v(r),e.prev=1,t=p.Ay.loading("Eliminando documento..."),e.next=5,(0,x.Q3)(r);case 5:e.sent?(p.Ay.success("Documento eliminado exitosamente",{id:t}),d(function(e){return e.filter(function(e){return e.id!==r})}),null==n||n()):p.Ay.error("Error al eliminar el documento",{id:t}),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("Error al eliminar documento:",e.t0),p.Ay.error("Error al eliminar el documento",{id:t});case 13:return e.prev=13,v(null),N(null),e.finish(13);case 17:case"end":return e.stop()}},e,null,[[1,9,13,17]])})),function(e){return t.apply(this,arguments)});return m?(0,h.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,h.jsx)(l.jTZ,{className:"animate-spin text-blue-500 mr-2"}),(0,h.jsx)("span",{children:"Cargando documentos..."})]}):0===c.length?(0,h.jsxs)("div",{className:"text-center p-8 text-gray-500",children:[(0,h.jsx)(l.jH2,{className:"mx-auto text-4xl mb-4"}),(0,h.jsx)("p",{children:"No hay documentos subidos a\xfan."}),(0,h.jsx)("p",{className:"text-sm",children:"Sube tu primer documento para comenzar."})]}):(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:["Gestionar Documentos (",c.length,")"]}),(0,h.jsx)("div",{className:"space-y-3",children:c.map(function(e){return(0,h.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,h.jsxs)("div",{className:"flex items-start justify-between",children:[(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.jH2,{className:"text-blue-500 mr-2 flex-shrink-0"}),(0,h.jsx)("h4",{className:"font-medium text-gray-900 truncate",children:e.titulo}),e.numero_tema&&(0,h.jsxs)("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:["Tema ",e.numero_tema]}),e.categoria&&(0,h.jsx)("span",{className:"ml-2 px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e.categoria})]}),(0,h.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,h.jsxs)("p",{children:["Subido: ",new Date(e.creado_en).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]}),(0,h.jsxs)("p",{children:["Caracteres: ",e.contenido.length.toLocaleString()]}),e.tipo_original&&(0,h.jsxs)("p",{children:["Tipo: ",e.tipo_original.toUpperCase()]})]})]}),(0,h.jsx)("button",{onClick:function(){return N(e.id)},disabled:b===e.id,className:"ml-4 p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50",title:"Eliminar documento",children:b===e.id?(0,h.jsx)(l.jTZ,{className:"animate-spin"}):(0,h.jsx)(l.IXo,{})})]})},e.id)})}),y&&(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-4",children:[(0,h.jsx)(l.eHT,{className:"text-red-500 mr-3"}),(0,h.jsx)("h3",{className:"text-lg font-semibold",children:"Confirmar eliminaci\xf3n"})]}),(0,h.jsx)("p",{className:"text-gray-600 mb-6",children:"\xbfEst\xe1s seguro de que quieres eliminar este documento? Esta acci\xf3n no se puede deshacer."}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,h.jsx)("button",{onClick:function(){return N(null)},className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"}),(0,h.jsx)("button",{onClick:function(){return k(y)},className:"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors",children:"Eliminar"})]})]})})]})}var ex=t(2775),ep=t(5929);let eh=function(e){var r=e.colecciones,t=e.coleccionSeleccionada,n=e.onSeleccionarColeccion,s=e.onEliminarColeccion,a=e.isLoading,o=e.deletingId;if(a)return(0,h.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(0===r.length)return(0,h.jsxs)("div",{className:"text-center p-8 border-2 border-dashed border-gray-300 rounded-lg",children:[(0,h.jsx)(l._Y7,{className:"mx-auto text-6xl text-gray-400 mb-4"}),(0,h.jsx)("p",{className:"text-gray-500 text-lg",children:"No hay colecciones de flashcards disponibles."}),(0,h.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Crea una nueva colecci\xf3n para empezar a estudiar."})]});var i=function(e,r){e.stopPropagation(),s(r)};return(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:r.map(function(e){return(0,h.jsxs)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors flex flex-col justify-between ".concat((null==t?void 0:t.id)===e.id?"border-orange-500 bg-orange-50 shadow-lg":"border-gray-200 hover:bg-gray-50"),onClick:function(){return n(e)},children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"font-semibold text-lg mb-2",children:e.titulo}),e.descripcion&&(0,h.jsx)("p",{className:"text-sm text-gray-600 mb-2 break-words",children:e.descripcion}),(0,h.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,h.jsxs)("p",{className:"text-sm text-gray-500",children:["Flashcards: ","number"==typeof e.numero_flashcards?e.numero_flashcards:"N/A"]}),"number"==typeof e.pendientes_hoy&&e.pendientes_hoy>0&&(0,h.jsxs)("span",{className:"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full",children:[e.pendientes_hoy," para hoy"]})]}),(0,h.jsxs)("p",{className:"text-xs text-gray-400",children:["Creada: ",new Date(e.creado_en).toLocaleDateString("es-ES")]})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,h.jsx)("button",{onClick:function(r){r.stopPropagation(),n(e)},className:"bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50",children:"Estudiar"}),(0,h.jsxs)("button",{onClick:function(r){return i(r,e.id)},disabled:o===e.id,className:"bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 disabled:opacity-50",title:"Eliminar colecci\xf3n",children:[o===e.id?(0,h.jsx)(l.jTZ,{size:14,className:"animate-spin mr-2"}):(0,h.jsx)(l.IXo,{size:14,className:"mr-2"}),"Eliminar"]})]})]},e.id)})})},ef=function(e){var r=e.coleccion,t=e.flashcards,n=e.estadisticas,s=e.isLoading,a=e.onStartStudy,o=e.onShowStudyOptions,i=e.onShowStatistics,c=e.onEditFlashcard,d=e.onDeleteFlashcard,u=e.deletingFlashcardId;return(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-xl font-semibold mb-4",children:r.titulo}),n&&(0,h.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[(0,h.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Estad\xedsticas"}),(0,h.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-2 text-sm",children:[(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"font-semibold text-blue-600",children:n.total}),(0,h.jsx)("div",{className:"text-gray-500",children:"Total"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"font-semibold text-orange-600",children:n.paraHoy}),(0,h.jsx)("div",{className:"text-gray-500",children:"Para hoy"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"font-semibold text-gray-600",children:n.nuevas}),(0,h.jsx)("div",{className:"text-gray-500",children:"Nuevas"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"font-semibold text-yellow-600",children:n.aprendiendo}),(0,h.jsx)("div",{className:"text-gray-500",children:"Aprendiendo"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"font-semibold text-green-600",children:n.aprendidas}),(0,h.jsx)("div",{className:"text-gray-500",children:"Aprendidas"})]})]})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6",children:[(0,h.jsx)("button",{onClick:a,className:"bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Estudiar (".concat(n?n.paraHoy:0," para hoy)")}),(0,h.jsx)("button",{onClick:o,className:"bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Opciones de estudio"}),(0,h.jsx)("button",{onClick:i,className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Ver estad\xedsticas"})]}),s?(0,h.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):0===t.length?(0,h.jsx)("div",{className:"text-center p-4",children:(0,h.jsx)("p",{className:"text-gray-500",children:"No hay flashcards en esta colecci\xf3n."})}):(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:t.map(function(e,r){return(0,h.jsxs)("div",{className:"p-3 border rounded-lg ".concat(e.debeEstudiar?"border-orange-300 bg-orange-50":"border-gray-200"),children:[(0,h.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,h.jsx)("p",{className:"font-medium flex-1 pr-2",children:e.pregunta}),(0,h.jsxs)("div",{className:"flex items-center space-x-1 flex-shrink-0",children:[c&&(0,h.jsx)("button",{onClick:function(){return c(e)},className:"p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded",title:"Editar flashcard",children:(0,h.jsx)(l.WXf,{size:14})}),d&&(0,h.jsx)("button",{onClick:function(){return d(e.id)},disabled:u===e.id,className:"p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded disabled:opacity-50",title:"Eliminar flashcard",children:u===e.id?(0,h.jsx)("div",{className:"animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-red-600"}):(0,h.jsx)(l.IXo,{size:14})})]})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsxs)("p",{className:"text-xs text-gray-500",children:["Tarjeta ",r+1]}),e.progreso&&(0,h.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("nuevo"===e.progreso.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.progreso.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.progreso.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:e.progreso.estado})]})]},e.id)})})]})},eg=function(e){var r=e.isOpen,t=e.onClose,n=e.onSelectStudyType,s=(e.estadisticas,e.isLoading),a=void 0!==s&&s,o=[{tipo:"dificiles",label:"M\xe1s dif\xedciles",descripcion:"Tarjetas que has marcado como dif\xedciles m\xe1s frecuentemente",icon:(0,h.jsx)(l.lrG,{className:"text-red-600"}),color:"red-600",bgColor:"bg-red-100",hoverBgColor:"hover:bg-red-200"},{tipo:"aleatorias",label:"Aleatorias",descripcion:"Selecci\xf3n aleatoria de tarjetas de la colecci\xf3n",icon:(0,h.jsx)(l.jTZ,{className:"text-purple-600"}),color:"purple-600",bgColor:"bg-purple-100",hoverBgColor:"hover:bg-purple-200"},{tipo:"no-recientes",label:"No estudiadas recientemente",descripcion:"Tarjetas que no has revisado en mucho tiempo",icon:(0,h.jsx)(l.Ohp,{className:"text-orange-600"}),color:"orange-600",bgColor:"bg-orange-100",hoverBgColor:"hover:bg-orange-200"},{tipo:"nuevas",label:"Nuevas",descripcion:"Tarjetas que nunca has estudiado",icon:(0,h.jsx)(l.D1A,{className:"text-green-600"}),color:"green-600",bgColor:"bg-green-100",hoverBgColor:"hover:bg-green-200"},{tipo:"aprendiendo",label:"En aprendizaje",descripcion:"Tarjetas que est\xe1s aprendiendo actualmente",icon:(0,h.jsx)(l.TwU,{className:"text-yellow-600"}),color:"yellow-600",bgColor:"bg-yellow-100",hoverBgColor:"hover:bg-yellow-200"}];return r?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold",children:"Opciones de Estudio"}),(0,h.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("p",{className:"text-gray-600 mb-3",children:"Elige el tipo de estudio que prefieras. Cada opci\xf3n te permitir\xe1 enfocar tu aprendizaje de manera diferente:"}),(0,h.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,h.jsxs)("p",{className:"text-sm text-blue-800 font-medium",children:["ℹ️ Importante: Estos estudios adicionales son complementarios y ",(0,h.jsx)("strong",{children:"no afectan al algoritmo de repetici\xf3n espaciada"}),'. Para el estudio oficial que cuenta para tu progreso, usa el bot\xf3n "Estudiar" principal.']})})]}),(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:o.map(function(e){return(0,h.jsx)("button",{onClick:function(){return n(e.tipo)},className:"p-4 border rounded-lg text-left transition-all duration-200 ".concat(e.hoverBgColor," ").concat(e.bgColor," border-gray-200 hover:border-gray-300"),disabled:a,children:(0,h.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,h.jsx)("div",{className:"flex-shrink-0 mt-1",children:e.icon}),(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("h4",{className:"font-medium text-".concat(e.color," mb-1"),children:e.label}),(0,h.jsx)("p",{className:"text-sm text-gray-600",children:e.descripcion})]})]})},e.tipo)})}),(0,h.jsx)("div",{className:"mt-6 flex justify-end space-x-3",children:(0,h.jsx)("button",{onClick:t,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancelar"})})]})}):null};var eb=t(38628),ev=t(94042);let ej=function(e){var r,t,n=e.flashcards,s=e.activeIndex,a=e.respondiendo,o=e.onRespuesta,c=e.onNavigate,d=e.onVolver,u=e.onReiniciarProgreso,m=e.onVerHistorial,x=n[s],p=(0,i.useState)(!1),f=p[0],g=p[1];(0,i.useEffect)(function(){g(!1)},[s]);var b=function(e){!(e&&e.target.closest("button"))&&(a||g(function(e){return!e}))},v=function(e,r){e.stopPropagation(),a||o(r)},j=function(e,r){e.stopPropagation(),a||r()};return x?(0,h.jsxs)("div",{className:"flex flex-col items-center w-full",children:[(0,h.jsxs)("div",{className:"w-full flex justify-between items-center mb-4 px-2",children:[(0,h.jsxs)("button",{onClick:d,className:"flex items-center text-sm text-gray-600 hover:text-gray-900 p-2 rounded-md hover:bg-gray-100 transition-colors",disabled:a,children:[(0,h.jsx)(eb.NEn,{className:"mr-1"})," Volver"]}),(0,h.jsxs)("div",{className:"text-sm text-gray-500",children:[s+1," / ",n.length]})]}),(0,h.jsx)("div",{className:"w-full max-w-2xl mx-auto",children:(0,h.jsx)("div",{className:"relative w-full h-[24rem] sm:h-[28rem] perspective-1000",onClick:function(){return b()},children:(0,h.jsxs)(ev.P.div,{className:"absolute w-full h-full transform-style-3d",animate:{rotateY:180*!!f},transition:{duration:.6},children:[(0,h.jsxs)("div",{className:"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"text-xs text-gray-400 mb-2",children:[(null==(r=x.progreso)?void 0:r.estado)&&(0,h.jsx)("span",{className:"px-2 py-0.5 rounded-full font-medium ".concat("nuevo"===x.progreso.estado?"bg-blue-100 text-blue-700":"aprendiendo"===x.progreso.estado?"bg-yellow-100 text-yellow-700":"repasando"===x.progreso.estado?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"),children:x.progreso.estado.charAt(0).toUpperCase()+x.progreso.estado.slice(1)}),!(null!=(t=x.progreso)&&t.estado)&&(0,h.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium text-xs",children:"Nuevo"})]}),(0,h.jsx)("div",{className:"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px]",children:(0,h.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-center text-gray-800 break-words",children:x.pregunta})})]}),(0,h.jsx)("div",{className:"text-center mt-4",children:(0,h.jsx)("button",{onClick:function(e){e.stopPropagation(),b()},className:"bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg text-sm transition-colors",disabled:a,children:"Mostrar respuesta"})})]}),(0,h.jsxs)("div",{className:"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between rotate-y-180",children:[(0,h.jsx)("div",{className:"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px] overflow-y-auto",children:(0,h.jsx)("p",{className:"text-base sm:text-lg text-center text-gray-700 whitespace-pre-wrap break-words transform-none",children:x.respuesta})}),(0,h.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,h.jsx)("p",{className:"text-center text-sm font-medium text-gray-600",children:"\xbfQu\xe9 tal te ha resultado?"}),(0,h.jsxs)("div",{className:"flex justify-around space-x-2 sm:space-x-3",children:[(0,h.jsxs)("button",{onClick:function(e){return v(e,"dificil")},disabled:a,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-colors disabled:opacity-50",children:[(0,h.jsx)(l.rxb,{className:"mb-1 text-xl"})," ",(0,h.jsx)("span",{className:"text-xs font-medium",children:"Dif\xedcil"})]}),(0,h.jsxs)("button",{onClick:function(e){return v(e,"normal")},disabled:a,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 text-yellow-600 transition-colors disabled:opacity-50",children:[(0,h.jsx)(l.YrT,{className:"mb-1 text-xl"})," ",(0,h.jsx)("span",{className:"text-xs font-medium",children:"Normal"})]}),(0,h.jsxs)("button",{onClick:function(e){return v(e,"facil")},disabled:a,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-600 transition-colors disabled:opacity-50",children:[(0,h.jsx)(l.Ydy,{className:"mb-1 text-xl"})," ",(0,h.jsx)("span",{className:"text-xs font-medium",children:"F\xe1cil"})]})]}),(u||m)&&(0,h.jsxs)("div",{className:"flex justify-center space-x-4 pt-2 text-xs",children:[u&&(0,h.jsxs)("button",{onClick:function(e){return j(e,function(){return u(x.id)})},disabled:a,className:"text-gray-500 hover:text-gray-700 underline flex items-center",children:[(0,h.jsx)(l.VI6,{size:12,className:"mr-1"})," Reiniciar"]}),m&&(0,h.jsxs)("button",{onClick:function(e){return j(e,function(){return m(x.id)})},disabled:a,className:"text-blue-500 hover:text-blue-700 underline flex items-center",children:[(0,h.jsx)(l.lrG,{size:12,className:"mr-1"})," Ver Historial"]})]})]})]})]})})}),(0,h.jsxs)("div",{className:"w-full max-w-2xl mx-auto flex justify-between mt-6 px-2",children:[(0,h.jsxs)("button",{onClick:function(){return c("prev")},disabled:0===s||a,className:"flex items-center text-sm p-2 rounded-md transition-colors ".concat(0===s||a?"text-gray-400 cursor-not-allowed":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,h.jsx)(eb.NEn,{className:"mr-1"})," Anterior"]}),(0,h.jsxs)("button",{onClick:function(){return c("next")},disabled:s===n.length-1||a,className:"flex items-center text-sm p-2 rounded-md transition-colors ".concat(s===n.length-1||a?"text-gray-400 cursor-not-allowed":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:["Siguiente ",(0,h.jsx)(eb.Lqc,{className:"ml-1"})]})]})]}):(0,h.jsxs)("div",{className:"flex flex-col items-center justify-center h-96 text-gray-500",children:["Cargando tarjeta...",(0,h.jsxs)("button",{onClick:d,className:"mt-4 flex items-center text-blue-600 hover:text-blue-800",children:[(0,h.jsx)(eb.NEn,{className:"mr-1"})," Volver"]})]})};function ey(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eN(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ey(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ey(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function ew(e){var r,t,n=e.flashcard,a=e.isOpen,c=e.onClose,d=e.onSave,u=(0,i.useState)(""),m=u[0],f=u[1],g=(0,i.useState)(""),b=g[0],v=g[1],j=(0,i.useState)(!1),y=j[0],N=j[1];(0,i.useEffect)(function(){n&&(f(n.pregunta),v(n.respuesta))},[n]);var w=(r=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(!m.trim()||!b.trim())){e.next=3;break}return p.Ay.error("La pregunta y respuesta no pueden estar vac\xedas"),e.abrupt("return");case 3:return N(!0),e.prev=4,r=p.Ay.loading("Guardando cambios..."),e.next=8,(0,x.xq)(n.id,m.trim(),b.trim());case 8:e.sent?(p.Ay.success("Flashcard actualizada exitosamente",{id:r}),d(eN(eN({},n),{},{pregunta:m.trim(),respuesta:b.trim()})),c()):p.Ay.error("Error al actualizar la flashcard",{id:r}),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(4),console.error("Error al actualizar flashcard:",e.t0),p.Ay.error("Error al actualizar la flashcard",{id:r});case 16:return e.prev=16,N(!1),e.finish(16);case 19:case"end":return e.stop()}},e,null,[[4,12,16,19]])})),function(){return r.apply(this,arguments)}),k=function(){f(n.pregunta),v(n.respuesta),c()};return a?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Editar Flashcard"}),(0,h.jsx)("button",{onClick:k,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:y,children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"p-6 space-y-6",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pregunta"}),(0,h.jsx)("textarea",{value:m,onChange:function(e){return f(e.target.value)},className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Escribe la pregunta aqu\xed...",disabled:y})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Respuesta"}),(0,h.jsx)("textarea",{value:b,onChange:function(e){return v(e.target.value)},className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:6,placeholder:"Escribe la respuesta aqu\xed...",disabled:y})]}),(null==(t=n.progreso)?void 0:t.estado)&&(0,h.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Estado actual"}),(0,h.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,h.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("nuevo"===n.progreso.estado?"bg-blue-100 text-blue-800":"aprendiendo"===n.progreso.estado?"bg-yellow-100 text-yellow-800":"repasando"===n.progreso.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:n.progreso.estado}),(0,h.jsxs)("span",{children:["Repeticiones: ",n.progreso.repeticiones]}),(0,h.jsxs)("span",{children:["Intervalo: ",n.progreso.intervalo," d\xedas"]})]})]})]}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200",children:[(0,h.jsx)("button",{onClick:k,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",disabled:y,children:"Cancelar"}),(0,h.jsx)("button",{onClick:w,disabled:y||!m.trim()||!b.trim(),className:"px-4 py-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:y?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.TwU,{className:"animate-spin mr-2"}),"Guardando..."]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.Bc_,{className:"mr-2"}),"Guardar cambios"]})})]})]})}):null}let ek=function(e){var r,t=e.onClose,n=(0,i.useState)(null),a=n[0],c=n[1],d=(0,i.useState)(!0),u=d[0],m=d[1],p=(0,i.useState)(""),f=p[0],g=p[1];(0,i.useEffect)(function(){b()},[]);var b=(r=(0,s.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,m(!0),e.next=4,(0,x.oE)();case 4:if(0!==(r=e.sent).length){e.next=8;break}return c({totalColecciones:0,totalFlashcards:0,totalNuevas:0,totalAprendiendo:0,totalRepasando:0,totalAprendidas:0,totalParaHoy:0,coleccionesConMasActividad:[]}),e.abrupt("return");case 8:return e.next=10,Promise.all(r.map(function(){var e=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,x.yV)(r.id);case 2:return t=e.sent,e.abrupt("return",{coleccion:r,estadisticas:t});case 4:case"end":return e.stop()}},e)}));return function(r){return e.apply(this,arguments)}}()));case 10:t=e.sent,c({totalColecciones:r.length,totalFlashcards:t.reduce(function(e,r){return e+r.estadisticas.total},0),totalNuevas:t.reduce(function(e,r){return e+r.estadisticas.nuevas},0),totalAprendiendo:t.reduce(function(e,r){return e+r.estadisticas.aprendiendo},0),totalRepasando:t.reduce(function(e,r){return e+r.estadisticas.repasando},0),totalAprendidas:t.reduce(function(e,r){return e+r.estadisticas.aprendidas},0),totalParaHoy:t.reduce(function(e,r){return e+r.estadisticas.paraHoy},0),coleccionesConMasActividad:t.map(function(e){return{id:e.coleccion.id,titulo:e.coleccion.titulo,totalRevisiones:e.estadisticas.total,paraHoy:e.estadisticas.paraHoy}}).sort(function(e,r){return r.paraHoy-e.paraHoy}).slice(0,5)}),e.next=19;break;case 15:e.prev=15,e.t0=e.catch(0),console.error("Error al cargar estad\xedsticas generales:",e.t0),g("No se pudieron cargar las estad\xedsticas generales");case 19:return e.prev=19,m(!1),e.finish(19);case 22:case"end":return e.stop()}},e,null,[[0,15,19,22]])})),function(){return r.apply(this,arguments)});return u?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:(0,h.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})})})}):f?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("h2",{className:"text-xl font-bold",children:"Error"}),(0,h.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsx)("div",{className:"text-red-500",children:f})]})}):a?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsx)("h2",{className:"text-2xl font-bold",children:"Estad\xedsticas Generales de Flashcards"}),(0,h.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,h.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.D1A,{className:"text-blue-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Total Colecciones"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:a.totalColecciones})]}),(0,h.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.x_j,{className:"text-green-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Total Flashcards"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-green-700",children:a.totalFlashcards})]}),(0,h.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.Ohp,{className:"text-orange-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Para Estudiar Hoy"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-orange-700",children:a.totalParaHoy})]}),(0,h.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.TPq,{className:"text-purple-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Aprendidas"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-purple-700",children:a.totalAprendidas})]})]}),(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Distribuci\xf3n por Estado"}),(0,h.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a.totalNuevas}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"Nuevas"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:a.totalAprendiendo}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"Aprendiendo"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:a.totalRepasando}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"Repasando"})]}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.totalAprendidas}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"Aprendidas"})]})]})})]}),a.coleccionesConMasActividad.length>0&&(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Colecciones con M\xe1s Actividad"}),(0,h.jsx)("div",{className:"space-y-3",children:a.coleccionesConMasActividad.map(function(e,r){return(0,h.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsxs)("div",{className:"text-lg font-bold text-blue-600 mr-3",children:["#",r+1]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,h.jsxs)("p",{className:"text-sm text-gray-600",children:[e.totalRevisiones," flashcards total"]})]})]}),(0,h.jsxs)("div",{className:"text-right",children:[(0,h.jsx)("div",{className:"text-lg font-bold text-orange-600",children:e.paraHoy}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"para hoy"})]})]})},e.id)})})]}),(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Resumen de Progreso"}),(0,h.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[a.totalFlashcards>0?Math.round(a.totalAprendidas/a.totalFlashcards*100):0,"%"]}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"Progreso General"})]}),(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[a.totalFlashcards>0?Math.round((a.totalAprendidas+a.totalRepasando)/a.totalFlashcards*100):0,"%"]}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"En Proceso"})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:a.totalParaHoy}),(0,h.jsx)("div",{className:"text-sm text-gray-600",children:"Pendientes Hoy"})]})]})})]}),(0,h.jsx)("div",{className:"flex justify-end",children:(0,h.jsx)("button",{onClick:t,className:"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Cerrar"})})]})}):null};function eE(e){var r=e.coleccionId,t=e.onClose,n=(0,i.useState)(null),a=n[0],c=n[1],l=(0,i.useState)(!0),d=l[0],u=l[1],m=(0,i.useState)(""),p=m[0],f=m[1],g=(0,i.useState)("general"),b=g[0],v=g[1];(0,i.useEffect)(function(){var e;(e=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return u(!0),e.prev=1,e.next=4,(0,x.wU)(r);case 4:c(e.sent),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error al cargar estad\xedsticas:",e.t0),f("No se pudieron cargar las estad\xedsticas detalladas");case 12:return e.prev=12,u(!1),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[1,8,12,15]])})),function(){return e.apply(this,arguments)})()},[r]);var j=function(e,r){return 0===r?0:Math.round(e/r*100)};return(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col",children:[(0,h.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,h.jsx)("h2",{className:"text-xl font-bold",children:"Estad\xedsticas detalladas de estudio"}),(0,h.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,h.jsx)("div",{className:"border-b",children:(0,h.jsxs)("div",{className:"flex",children:[(0,h.jsx)("button",{onClick:function(){return v("general")},className:"px-4 py-2 font-medium ".concat("general"===b?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"),children:"General"}),(0,h.jsx)("button",{onClick:function(){return v("progreso")},className:"px-4 py-2 font-medium ".concat("progreso"===b?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"),children:"Progreso"}),(0,h.jsx)("button",{onClick:function(){return v("dificiles")},className:"px-4 py-2 font-medium ".concat("dificiles"===b?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"),children:"Tarjetas dif\xedciles"})]})}),(0,h.jsx)("div",{className:"p-4 overflow-y-auto flex-grow",children:d?(0,h.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})}):p?(0,h.jsx)("div",{className:"text-red-500 text-center py-4",children:p}):a?(0,h.jsxs)(h.Fragment,{children:["general"===b&&(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,h.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Sesiones de estudio"}),(0,h.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:a.totalSesiones})]}),(0,h.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Total de revisiones"}),(0,h.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:a.totalRevisiones})]}),(0,h.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Promedio por sesi\xf3n"}),(0,h.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:a.totalSesiones>0?Math.round(a.totalRevisiones/a.totalSesiones):0})]})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Distribuci\xf3n de respuestas"}),(0,h.jsxs)("div",{className:"space-y-2",children:[(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,h.jsx)("span",{className:"text-sm font-medium",children:"Dif\xedcil"}),(0,h.jsxs)("span",{className:"text-sm font-medium",children:[a.distribucionDificultad.dificil," (",j(a.distribucionDificultad.dificil,a.totalRevisiones),"%)"]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,h.jsx)("div",{className:"bg-red-500 h-2.5 rounded-full",style:{width:"".concat(j(a.distribucionDificultad.dificil,a.totalRevisiones),"%")}})})]}),(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,h.jsx)("span",{className:"text-sm font-medium",children:"Normal"}),(0,h.jsxs)("span",{className:"text-sm font-medium",children:[a.distribucionDificultad.normal," (",j(a.distribucionDificultad.normal,a.totalRevisiones),"%)"]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,h.jsx)("div",{className:"bg-yellow-500 h-2.5 rounded-full",style:{width:"".concat(j(a.distribucionDificultad.normal,a.totalRevisiones),"%")}})})]}),(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,h.jsx)("span",{className:"text-sm font-medium",children:"F\xe1cil"}),(0,h.jsxs)("span",{className:"text-sm font-medium",children:[a.distribucionDificultad.facil," (",j(a.distribucionDificultad.facil,a.totalRevisiones),"%)"]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,h.jsx)("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:"".concat(j(a.distribucionDificultad.facil,a.totalRevisiones),"%")}})})]})]})]})]}),"progreso"===b&&(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Progreso a lo largo del tiempo"}),0===a.progresoTiempo.length?(0,h.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos de progreso disponibles"}):(0,h.jsx)("div",{className:"overflow-x-auto",children:(0,h.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,h.jsx)("thead",{className:"bg-gray-50",children:(0,h.jsxs)("tr",{children:[(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nuevas"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aprendiendo"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Repasando"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aprendidas"})]})}),(0,h.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.progresoTiempo.map(function(e,r){return(0,h.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:new Date(e.fecha).toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric"})}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,h.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:e.nuevas})}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,h.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800",children:e.aprendiendo})}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,h.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800",children:e.repasando})}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,h.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:e.aprendidas})})]},r)})})]})})]}),"dificiles"===b&&(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Tarjetas m\xe1s dif\xedciles"}),0===a.tarjetasMasDificiles.length?(0,h.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos suficientes para determinar las tarjetas m\xe1s dif\xedciles"}):(0,h.jsx)("div",{className:"space-y-4",children:a.tarjetasMasDificiles.map(function(e){return(0,h.jsxs)("div",{className:"border rounded-lg p-4 hover:bg-gray-50",children:[(0,h.jsx)("p",{className:"font-medium mb-2",children:e.pregunta}),(0,h.jsxs)("div",{className:"flex space-x-4 text-sm",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,h.jsxs)("span",{children:["Dif\xedcil: ",e.dificil]})]}),(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"}),(0,h.jsxs)("span",{children:["Normal: ",e.normal]})]}),(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,h.jsxs)("span",{children:["F\xe1cil: ",e.facil]})]})]}),(0,h.jsx)("div",{className:"mt-2",children:(0,h.jsxs)("div",{className:"w-full bg-gray-200 rounded-full h-1.5 flex",children:[(0,h.jsx)("div",{className:"bg-red-500 h-1.5 rounded-l-full",style:{width:"".concat(j(e.dificil,e.totalRevisiones),"%")}}),(0,h.jsx)("div",{className:"bg-yellow-500 h-1.5",style:{width:"".concat(j(e.normal,e.totalRevisiones),"%")}}),(0,h.jsx)("div",{className:"bg-green-500 h-1.5 rounded-r-full",style:{width:"".concat(j(e.facil,e.totalRevisiones),"%")}})]})})]},e.id)})})]})]}):(0,h.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos estad\xedsticos disponibles"})})]})})}function eC(){var e,r,t,n,a,c,d,m=(0,i.useState)([]),x=m[0],p=m[1],f=(0,i.useState)(null),g=f[0],b=f[1],v=(0,i.useState)([]),j=v[0],y=v[1],N=(0,i.useState)(!0),w=N[0],k=N[1],E=(0,i.useState)(!0),C=E[0],S=E[1],O=(0,i.useState)(""),A=O[0],P=O[1],D=(0,i.useState)(0),T=D[0],_=D[1],R=(0,i.useState)(null),z=R[0],I=R[1],F=(0,i.useState)(!1),M=F[0],L=F[1],G=(0,i.useState)(null),q=G[0],H=G[1],U=(0,i.useState)(!1),V=U[0],B=U[1],$=(0,i.useState)(!1),W=$[0],Y=$[1],K=(0,i.useState)(!1),X=K[0],J=K[1],Z=(0,i.useState)(!1),Q=Z[0],ee=Z[1],er=(0,i.useState)(!1),et=er[0],en=er[1],es=(0,i.useState)(null),ea=es[0],eo=es[1],ei=(0,i.useState)(null),ec=ei[0],el=ei[1];(0,i.useEffect)(function(){var e;(e=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return S(!0),e.prev=1,e.next=4,(0,ex.oE)();case 4:p(e.sent),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error al cargar colecciones:",e.t0),P("No se pudieron cargar las colecciones de flashcards");case 12:return e.prev=12,S(!1),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[1,8,12,15]])})),function(){return e.apply(this,arguments)})()},[]);var ed=(e=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return k(!0),P(""),b(r),_(0),B(!1),e.prev=5,e.next=8,(0,ex.Og)(r.id);case 8:return t=e.sent,y((0,u.A)(t).sort(function(e,r){return e.debeEstudiar&&!r.debeEstudiar?-1:!e.debeEstudiar&&r.debeEstudiar?1:0})),e.next=13,(0,ep.yV)(r.id);case 13:I(e.sent),e.next=21;break;case 17:e.prev=17,e.t0=e.catch(5),console.error("Error al cargar flashcards:",e.t0),P("No se pudieron cargar las flashcards de esta colecci\xf3n");case 21:return e.prev=21,k(!1),e.finish(21);case 24:case"end":return e.stop()}},e,null,[[5,17,21,24]])})),function(r){return e.apply(this,arguments)}),eu=(r=(0,s.A)(o().mark(function e(){var r,t,n=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.length>0&&void 0!==n[0]?n[0]:"programadas",k(!0),e.prev=2,!g){e.next=61;break}t=[],e.t0=r,e.next="programadas"===e.t0?8:"dificiles"===e.t0?13:"aleatorias"===e.t0?17:"no-recientes"===e.t0?21:"nuevas"===e.t0?25:"aprendiendo"===e.t0?29:"repasando"===e.t0?33:"aprendidas"===e.t0?37:41;break;case 8:return e.next=10,(0,ex.Og)(g.id);case 10:return t=e.sent.filter(function(e){return e.debeEstudiar}),e.abrupt("break",45);case 13:return e.next=15,(0,ex.kO)(g.id,20);case 15:case 19:case 23:case 27:case 31:case 35:case 39:return t=e.sent,e.abrupt("break",45);case 17:return e.next=19,(0,ex._p)(g.id,20);case 21:return e.next=23,(0,ex._W)(g.id,20);case 25:return e.next=27,(0,ex.Iv)(g.id,"nuevo",20);case 29:return e.next=31,(0,ex.Iv)(g.id,"aprendiendo",20);case 33:return e.next=35,(0,ex.Iv)(g.id,"repasando",20);case 37:return e.next=39,(0,ex.Iv)(g.id,"aprendido",20);case 41:return e.next=43,(0,ex.Og)(g.id);case 43:t=e.sent.filter(function(e){return e.debeEstudiar});case 45:return e.next=47,(0,ep.yV)(g.id);case 47:if(I(e.sent),0!==t.length){e.next=57;break}if("programadas"!==r){e.next=55;break}return alert('No hay flashcards programadas para estudiar hoy. Puedes usar "Opciones de estudio" para elegir otro tipo de repaso.'),e.abrupt("return");case 55:return alert("No hay flashcards disponibles para el tipo de estudio seleccionado."),e.abrupt("return");case 57:y(t),B(!0),_(0),Y(!1);case 61:e.next=66;break;case 63:e.prev=63,e.t1=e.catch(2),console.error("Error al iniciar modo estudio:",e.t1);case 66:return e.prev=66,k(!1),e.finish(66);case 69:case"end":return e.stop()}},e,null,[[2,63,66,69]])})),function(){return r.apply(this,arguments)}),em=(t=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(B(!1),!g){e.next=17;break}return e.prev=2,e.next=5,(0,ex.Og)(g.id);case 5:return r=e.sent,y((0,u.A)(r).sort(function(e,r){return e.debeEstudiar&&!r.debeEstudiar?-1:!e.debeEstudiar&&r.debeEstudiar?1:0})),e.next=10,(0,ep.yV)(g.id);case 10:I(e.sent),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(2),console.error("Error al recargar datos:",e.t0);case 17:case"end":return e.stop()}},e,null,[[2,14]])})),function(){return t.apply(this,arguments)}),eb=(n=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(j[T]){e.next=2;break}return e.abrupt("return");case 2:return L(!0),e.prev=3,e.next=6,(0,ex.yf)(j[T].id,r);case 6:if(e.sent){e.next=9;break}throw Error("Error al registrar la respuesta");case 9:if(!(T>=j.length-1)){e.next=15;break}return alert("\xa1Has completado la sesi\xf3n de estudio!"),e.next=13,em();case 13:e.next=16;break;case 15:_(T+1);case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("Error al actualizar progreso:",e.t0),P("Error al guardar tu respuesta. Por favor, int\xe9ntalo de nuevo.");case 22:return e.prev=22,L(!1),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[3,18,22,25]])})),function(e){return n.apply(this,arguments)}),ev=(a=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta colecci\xf3n? Esta acci\xf3n no se puede deshacer.")){e.next=2;break}return e.abrupt("return");case 2:return H(r),e.prev=3,e.next=6,(0,ex.as)(r);case 6:if(!e.sent){e.next=15;break}return e.next=10,(0,ex.oE)();case 10:p(e.sent),(null==g?void 0:g.id)===r&&(b(null),y([]),I(null)),e.next=16;break;case 15:P("No se pudo eliminar la colecci\xf3n");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("Error al eliminar colecci\xf3n:",e.t0),P("Error al eliminar la colecci\xf3n");case 22:return e.prev=22,H(null),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[3,18,22,25]])})),function(e){return a.apply(this,arguments)}),ey=(c=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\xf3n no se puede deshacer.")){e.next=2;break}return e.abrupt("return");case 2:return el(r),e.prev=3,e.next=6,(0,ex.QU)(r);case 6:if(!e.sent){e.next=20;break}if(!g){e.next=18;break}return e.next=11,(0,ex.Og)(g.id);case 11:return t=e.sent,y((0,u.A)(t).sort(function(e,r){return e.debeEstudiar&&!r.debeEstudiar?-1:!e.debeEstudiar&&r.debeEstudiar?1:0})),e.next=16,(0,ep.yV)(g.id);case 16:I(e.sent);case 18:e.next=21;break;case 20:P("No se pudo eliminar la flashcard");case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(3),console.error("Error al eliminar flashcard:",e.t0),P("Error al eliminar la flashcard");case 27:return e.prev=27,el(null),e.finish(27);case 30:case"end":return e.stop()}},e,null,[[3,23,27,30]])})),function(e){return c.apply(this,arguments)}),eN=(d=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:y(function(e){return e.map(function(e){return e.id===r.id?r:e})});case 1:case"end":return e.stop()}},e)})),function(e){return d.apply(this,arguments)});return(0,h.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[A&&(0,h.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:A}),V?(0,h.jsx)(ej,{flashcards:j,activeIndex:T,respondiendo:M,onRespuesta:eb,onNavigate:function(e){"next"===e&&T<j.length-1?_(T+1):"prev"===e&&T>0&&_(T-1)},onVolver:em}):(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsx)("h2",{className:"text-2xl font-bold",children:"Mis Flashcards"}),(0,h.jsxs)("button",{onClick:function(){return ee(!0)},className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,h.jsx)(l.vQY,{className:"mr-2"})," Estad\xedsticas Generales"]})]}),g?(0,h.jsxs)("div",{children:[(0,h.jsx)("button",{onClick:function(){return b(null)},className:"mb-4 text-blue-600 hover:text-blue-800 flex items-center",children:"← Volver a mis colecciones"}),(0,h.jsx)(ef,{coleccion:g,flashcards:j,estadisticas:z,isLoading:w,onStartStudy:function(){return eu("programadas")},onShowStudyOptions:function(){return Y(!0)},onShowStatistics:function(){return J(!0)},onEditFlashcard:function(e){eo(e),en(!0)},onDeleteFlashcard:ey,deletingFlashcardId:ec})]}):(0,h.jsx)(eh,{colecciones:x,coleccionSeleccionada:g,onSeleccionarColeccion:ed,onEliminarColeccion:ev,isLoading:C,deletingId:q})]}),(0,h.jsx)(eg,{isOpen:W,onClose:function(){return Y(!1)},onSelectStudyType:eu,estadisticas:z,isLoading:w}),X&&g&&(0,h.jsx)(eE,{coleccionId:g.id,onClose:function(){return J(!1)}}),Q&&(0,h.jsx)(ek,{onClose:function(){return ee(!1)}}),ea&&(0,h.jsx)(ew,{flashcard:ea,isOpen:et,onClose:function(){en(!1),eo(null)},onSave:eN})]})}function eS(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eO(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?eS(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):eS(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function eA(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function eP(e){var r,t,n,a,c=e.documentosSeleccionados,d=(0,i.useState)(""),u=d[0],m=d[1],f=(0,i.useState)(""),g=f[0],b=f[1],y=(0,i.useState)([]),N=y[0],w=y[1],E=(0,i.useState)(!1),O=E[0],A=E[1],P=(0,i.useState)(0),D=P[0],T=P[1],R=(0,i.useState)(!1),z=R[0],L=R[1],G=(0,i.useState)([]),q=G[0],V=G[1],B=(0,i.useState)("nuevo"),$=B[0],W=B[1],Y=(0,i.useState)(!1),K=(Y[0],Y[1]),X=(0,i.useState)(""),J=X[0],Z=X[1],Q=F(),ee=Q.generateTest,er=Q.isGenerating,et=Q.getActiveTask;(0,I.M)().getTask;var en=H().executeWithGuard;(0,C.A)().user;var es=U(),ea=es.plan,eo=es.isLoading;et("test");var ei=er("test");M({taskType:"test",onResult:function(e){w(e),p.oR.success("\xa1Test generado exitosamente!")},onError:function(e){p.oR.error("Error al generar test: ".concat(e))}});var ec=(0,v.mN)({resolver:(0,j.u)(k),defaultValues:{peticion:"",cantidad:10}}),el=ec.register,ed=ec.handleSubmit,eu=ec.formState.errors;(0,i.useEffect)(function(){em()},[]);var em=(r=(0,s.A)(o().mark(function e(){var r,t,n,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return K(!0),e.prev=1,e.next=4,(0,x.Lx)();case 4:r=e.sent,t=[],n=function(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return eA(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return eA(e,r)}}(e))){t&&(e=t);var n=0,s=function(){};return{s:s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){i=!0,a=e},f:function(){try{o||null==t.return||t.return()}finally{if(i)throw a}}}}(r),e.prev=7,n.s();case 9:if((s=n.n()).done){e.next=17;break}return a=s.value,e.next=13,(0,x.Kj)(a.id);case 13:i=e.sent,t.push(eO(eO({},a),{},{numPreguntas:i}));case 15:e.next=9;break;case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(7),n.e(e.t0);case 22:return e.prev=22,n.f(),e.finish(22);case 25:V(t),e.next=32;break;case 28:e.prev=28,e.t1=e.catch(1),console.error("Error al cargar tests:",e.t1),p.oR.error("No se pudieron cargar los tests existentes.");case 32:return e.prev=32,K(!1),e.finish(32);case 35:case"end":return e.stop()}},e,null,[[1,28,32,35],[7,19,22,25]])})),function(){return r.apply(this,arguments)}),ex=(t=(0,s.A)(o().mark(function e(r){var t,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.map(function(e){return e.contenido}),w([]),A(!1),e.next=5,en("tests",(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ee({peticion:r.peticion,contextos:t,cantidad:r.cantidad});case 2:return u||m("Test: ".concat(r.peticion.substring(0,50)).concat(r.peticion.length>50?"...":"")),e.abrupt("return",!0);case 4:case"end":return e.stop()}},e)})),r.cantidad);case 5:(n=e.sent).success?p.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):p.oR.error(n.error||"Error al iniciar la generaci\xf3n del test");case 7:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)}),ep=(n=(0,s.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==N.length){e.next=3;break}return Z("No hay preguntas para guardar"),e.abrupt("return");case 3:if(!("nuevo"===$&&!u.trim())){e.next=6;break}return Z("Por favor, proporciona un t\xedtulo para el nuevo test"),e.abrupt("return");case 6:if("nuevo"===$||""!==$){e.next=9;break}return Z("Por favor, selecciona un test existente"),e.abrupt("return");case 9:if(Z(""),e.prev=10,"nuevo"!==$){e.next=19;break}return e.next=14,(0,x._4)(u,g,c.map(function(e){return e.id}));case 14:if(r=e.sent){e.next=17;break}throw Error("No se pudo crear el test");case 17:e.next=20;break;case 19:r=$;case 20:return t=N.map(function(e){return{test_id:r,pregunta:e.pregunta,opcion_a:e.opciones.a,opcion_b:e.opciones.b,opcion_c:e.opciones.c,opcion_d:e.opciones.d,respuesta_correcta:e.respuesta_correcta}}),e.next=23,(0,x.OA)(t);case 23:if(e.sent){e.next=26;break}throw Error("No se pudieron guardar las preguntas");case 26:if(A(!0),"nuevo"!==$){e.next=30;break}return e.next=30,em();case 30:setTimeout(function(){w([]),A(!1),m(""),b(""),W("nuevo"),T(0),L(!1),p.oR.success("Test guardado correctamente. Puedes generar uno nuevo.")},3e3),e.next=37;break;case 33:e.prev=33,e.t0=e.catch(10),console.error("Error al guardar las preguntas:",e.t0),Z("Ha ocurrido un error al guardar las preguntas. Por favor, int\xe9ntalo de nuevo.");case 37:case"end":return e.stop()}},e,null,[[10,33]])})),function(){return n.apply(this,arguments)});return(0,h.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,h.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Generador de Tests"}),!eo&&"free"===ea&&(0,h.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,h.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,h.jsx)(l.F5$,{className:"w-5 h-5 text-blue-600 mt-0.5"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"font-medium text-blue-900",children:"L\xedmites del Plan Gratuito"}),(0,h.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["M\xe1ximo ",S.qo.free.limits.testsForTrial," preguntas de test durante el per\xedodo de prueba. Para generar tests ilimitados,",(0,h.jsx)(_(),{href:"/upgrade-plan",className:"font-medium underline hover:text-blue-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,h.jsxs)("form",{onSubmit:ed(ex),className:"space-y-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe el test que deseas generar:"}),(0,h.jsx)("textarea",eO(eO({id:"peticion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:3},el("peticion")),{},{placeholder:"Ej: Genera un test sobre los conceptos principales del tema 1",disabled:ei})),eu.peticion&&(0,h.jsx)("span",{className:"text-red-500 text-sm",children:eu.peticion.message}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 preguntas de test basadas en los documentos seleccionados y tu petici\xf3n."})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"cantidad",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de preguntas:"}),(0,h.jsx)("input",eO(eO({id:"cantidad",type:"number",min:"1",max:"50",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"},el("cantidad",{valueAsNumber:!0})),{},{disabled:ei})),eu.cantidad&&(0,h.jsx)("span",{className:"text-red-500 text-sm",children:eu.cantidad.message}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica cu\xe1ntas preguntas quieres generar (entre 1 y 50)."})]}),J&&(0,h.jsx)("div",{className:"text-red-500 text-sm",children:J}),(0,h.jsx)("div",{children:(0,h.jsx)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:ei||0===c.length,children:ei?"Generando...":"Generar Test"})})]}),ei&&(0,h.jsxs)("div",{className:"mt-4 text-center",children:[(0,h.jsx)("p",{className:"text-gray-600",children:"Generando test, por favor espera..."}),(0,h.jsx)("div",{className:"mt-2 flex justify-center",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})})]}),N.length>0&&(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsxs)("h3",{className:"text-lg font-semibold",children:["Test generado (",N.length," preguntas)"]}),(0,h.jsx)("button",{onClick:function(){w([]),A(!1),m(""),b(""),W("nuevo"),T(0),L(!1)},className:"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline text-sm",title:"Limpiar test generado",children:"Limpiar"})]}),!O&&(0,h.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,h.jsx)("h4",{className:"font-medium mb-2",children:"Guardar preguntas de test"}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("label",{htmlFor:"tipoTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"\xbfD\xf3nde quieres guardar estas preguntas?"}),(0,h.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{type:"radio",id:"nuevoTest",name:"tipoTest",value:"nuevo",checked:"nuevo"===$,onChange:function(){return W("nuevo")},className:"mr-2",disabled:ei}),(0,h.jsx)("label",{htmlFor:"nuevoTest",className:"text-sm text-gray-700",children:"Crear nuevo test"})]}),(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{type:"radio",id:"testExistente",name:"tipoTest",value:"existente",checked:"nuevo"!==$,onChange:function(){q.length>0?W(q[0].id):W("")},className:"mr-2",disabled:ei||0===q.length}),(0,h.jsxs)("label",{htmlFor:"testExistente",className:"text-sm text-gray-700",children:["A\xf1adir a un test existente",0===q.length&&(0,h.jsx)("span",{className:"text-gray-500 ml-2",children:"(No hay tests disponibles)"})]})]})]})]}),"nuevo"===$&&(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"tituloTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo del nuevo test:"}),(0,h.jsx)("input",{type:"text",id:"tituloTest",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:u,onChange:function(e){return m(e.target.value)},disabled:ei})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"descripcionTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional):"}),(0,h.jsx)("textarea",{id:"descripcionTest",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,value:g,onChange:function(e){return b(e.target.value)},disabled:ei})]})]}),"nuevo"!==$&&q.length>0&&(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"testExistenteSelect",className:"block text-sm font-medium text-gray-700 mb-1",children:"Selecciona un test:"}),(0,h.jsx)("select",{id:"testExistenteSelect",className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:$,onChange:function(e){return W(e.target.value)},disabled:ei,children:q.map(function(e){return(0,h.jsxs)("option",{value:e.id,children:[e.titulo," ",e.numPreguntas?"(".concat(e.numPreguntas," preguntas)"):""]},e.id)})})]}),(0,h.jsx)("div",{className:"mt-4",children:(0,h.jsx)("button",{type:"button",onClick:ep,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:!1,children:"Guardar preguntas"})})]}),O&&(0,h.jsxs)("div",{className:"bg-green-100 text-green-800 p-4 rounded-lg mb-6",children:[(0,h.jsx)("p",{className:"font-medium",children:"nuevo"===$?"\xa1Nuevo test creado correctamente!":"\xa1Preguntas a\xf1adidas al test correctamente!"}),(0,h.jsxs)("p",{className:"text-sm mt-1",children:["Puedes acceder a ","nuevo"===$?"\xe9l":"las preguntas",' desde la secci\xf3n de "Mis Tests".']})]}),(0,h.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mb-4",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("button",{onClick:function(){D>0&&(T(D-1),L(!1))},disabled:0===D,className:"p-2 rounded-full ".concat(0===D?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,h.jsxs)("span",{className:"text-gray-600",children:["Pregunta ",D+1," de ",N.length]}),(0,h.jsx)("button",{onClick:function(){D<N.length-1&&(T(D+1),L(!1))},disabled:D===N.length-1,className:"p-2 rounded-full ".concat(D===N.length-1?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,h.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,h.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,h.jsx)("div",{className:"min-h-[300px]",children:(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("h4",{className:"font-semibold text-lg mb-4",children:null==(a=N[D])?void 0:a.pregunta}),(0,h.jsx)("div",{className:"space-y-3 mt-6",children:["a","b","c","d"].map(function(e){var r;return(0,h.jsx)("div",{className:"p-3 border rounded-lg ".concat(z&&N[D].respuesta_correcta===e?"bg-green-100 border-green-500":"hover:bg-gray-50"),children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ".concat(z&&N[D].respuesta_correcta===e?"bg-green-500 text-white":"bg-gray-200 text-gray-700"),children:e.toUpperCase()}),(0,h.jsx)("div",{className:"flex-grow",children:null==(r=N[D])?void 0:r.opciones[e]})]})},e)})})]})}),(0,h.jsx)("div",{className:"mt-4 text-center",children:(0,h.jsx)("button",{onClick:function(){L(!z)},className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:z?"Ocultar respuesta":"Mostrar respuesta"})})]}),(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsx)("h4",{className:"font-medium mb-2",children:"Todas las preguntas:"}),(0,h.jsx)("div",{className:"space-y-2",children:N.map(function(e,r){return(0,h.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ".concat(r===D?"border-indigo-500 bg-indigo-50":""),onClick:function(){T(r),L(!1)},children:(0,h.jsxs)("p",{className:"font-medium",children:[r+1,". ",e.pregunta]})},r)})})]})]})]})}let eD=function(e){var r=e.estadisticas,t=e.onClose;return(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsx)("h2",{className:"text-2xl font-bold",children:"Estad\xedsticas Generales de Tests"}),(0,h.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,h.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.TPq,{className:"text-blue-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Tests Realizados"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:r.totalTests})]}),(0,h.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.YrT,{className:"text-green-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Respuestas Correctas"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-green-700",children:r.totalRespuestasCorrectas})]}),(0,h.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.yGN,{className:"text-red-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Respuestas Incorrectas"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-red-700",children:r.totalRespuestasIncorrectas})]}),(0,h.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.ARf,{className:"text-purple-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Porcentaje de Acierto"})]}),(0,h.jsxs)("p",{className:"text-3xl font-bold text-purple-700",children:[r.porcentajeAcierto.toFixed(1),"%"]})]})]})]})})},eT=function(e){var r=e.estadisticas,t=e.testTitulo,n=e.onClose;return(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsxs)("h2",{className:"text-2xl font-bold",children:["Estad\xedsticas del Test: ",t]}),(0,h.jsx)("button",{onClick:n,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,h.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.Ohp,{className:"text-blue-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Veces Realizado"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:r.fechasRealizacion.length})]}),(0,h.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.YrT,{className:"text-green-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Respuestas Correctas"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-green-700",children:r.totalCorrectas})]}),(0,h.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.yGN,{className:"text-red-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Respuestas Incorrectas"})]}),(0,h.jsx)("p",{className:"text-3xl font-bold text-red-700",children:r.totalIncorrectas})]}),(0,h.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.ARf,{className:"text-purple-600 mr-2 text-xl"}),(0,h.jsx)("h4",{className:"font-semibold",children:"Porcentaje de Acierto"})]}),(0,h.jsxs)("p",{className:"text-3xl font-bold text-purple-700",children:[r.porcentajeAcierto.toFixed(1),"%"]})]})]}),r.preguntasMasFalladas.length>0&&(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Preguntas con M\xe1s Fallos"}),(0,h.jsx)("div",{className:"space-y-3",children:r.preguntasMasFalladas.map(function(e,r){return(0,h.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,h.jsx)("div",{className:"flex justify-between items-start",children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsxs)("div",{className:"text-lg font-bold text-red-600 mr-3",children:["#",r+1]}),(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("p",{className:"font-medium text-gray-900",children:e.pregunta}),(0,h.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm",children:[(0,h.jsxs)("span",{className:"text-red-600",children:[(0,h.jsx)(l.yGN,{className:"inline mr-1"})," ",e.totalFallos," fallos"]}),(0,h.jsxs)("span",{className:"text-green-600",children:[(0,h.jsx)(l.YrT,{className:"inline mr-1"})," ",e.totalAciertos," aciertos"]})]})]})]})})},e.preguntaId)})})]})]})})};var e_=t(66618),eR=t(57759);function ez(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function eI(){return(eI=(0,s.A)(o().mark(function e(){var r,t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,eR.iF)();case 3:if(r=e.sent.user){e.next=8;break}return console.error("No hay usuario autenticado"),e.abrupt("return",[]);case 8:return e.next=10,e_.N.from("tests").select("*").eq("user_id",r.id).order("creado_en",{ascending:!1});case 10:if(n=(t=e.sent).data,!(s=t.error)){e.next=16;break}return console.error("Error al obtener tests:",s),e.abrupt("return",[]);case 16:return e.abrupt("return",n||[]);case 19:return e.prev=19,e.t0=e.catch(0),console.error("Error al obtener tests:",e.t0),e.abrupt("return",[]);case 23:case"end":return e.stop()}},e,null,[[0,19]])}))).apply(this,arguments)}function eF(){return(eF=(0,s.A)(o().mark(function e(r){var t,n,s,a,i,c,l,d;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,t=[],n=function(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return ez(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ez(e,r)}}(e))){t&&(e=t);var n=0,s=function(){};return{s:s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){i=!0,a=e},f:function(){try{o||null==t.return||t.return()}finally{if(i)throw a}}}}(r),e.prev=3,n.s();case 5:if((s=n.n()).done){e.next=19;break}if(!((a=s.value).cantidad>0)){e.next=17;break}return e.next=10,e_.N.from("preguntas_test").select("*").eq("test_id",a.testId);case 10:if(c=(i=e.sent).data,!(l=i.error)){e.next=16;break}return console.error("Error al obtener preguntas del test ".concat(a.testId,":"),l),e.abrupt("continue",17);case 16:c&&c.length>0&&(d=c.sort(function(){return Math.random()-.5}).slice(0,a.cantidad),t.push.apply(t,(0,u.A)(d)));case 17:e.next=5;break;case 19:e.next=24;break;case 21:e.prev=21,e.t0=e.catch(3),n.e(e.t0);case 24:return e.prev=24,n.f(),e.finish(24);case 27:return e.abrupt("return",t.sort(function(){return Math.random()-.5}));case 30:return e.prev=30,e.t1=e.catch(0),console.error("Error al obtener preguntas de repaso:",e.t1),e.abrupt("return",[]);case 34:case"end":return e.stop()}},e,null,[[0,30],[3,21,24,27]])}))).apply(this,arguments)}function eM(){return(eM=(0,s.A)(o().mark(function e(r){var t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,e_.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",r);case 2:if(n=(t=e.sent).count,!(s=t.error)){e.next=8;break}return console.error("Error al obtener conteo de preguntas:",s),e.abrupt("return",0);case 8:return e.abrupt("return",n||0);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}function eL(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eG(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?eL(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):eL(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function eq(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function eH(e){var r,t,n=e.onIniciarRepaso,a=e.onCancelar,c=(0,i.useState)([]),d=c[0],u=c[1],m=(0,i.useState)([]),x=m[0],f=m[1],g=(0,i.useState)(!1),b=g[0],v=g[1],j=(0,i.useState)(!1),y=j[0],N=j[1];(0,i.useEffect)(function(){w()},[]);var w=(r=(0,s.A)(o().mark(function e(){var r,t,n,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return v(!0),e.prev=1,e.next=4,function(){return eI.apply(this,arguments)}();case 4:r=e.sent,t=[],n=function(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return eq(e,void 0);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return eq(e,r)}}(e))){t&&(e=t);var n=0,s=function(){};return{s:s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){i=!0,a=e},f:function(){try{o||null==t.return||t.return()}finally{if(i)throw a}}}}(r),e.prev=7,n.s();case 9:if((s=n.n()).done){e.next=17;break}return a=s.value,e.next=13,function(e){return eM.apply(this,arguments)}(a.id);case 13:(i=e.sent)>0&&t.push(eG(eG({},a),{},{numPreguntas:i}));case 15:e.next=9;break;case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(7),n.e(e.t0);case 22:return e.prev=22,n.f(),e.finish(22);case 25:u(t),f(t.map(function(e){return{testId:e.id,cantidad:0,maxPreguntas:e.numPreguntas}})),e.next=34;break;case 30:e.prev=30,e.t1=e.catch(1),console.error("Error al cargar tests:",e.t1),p.oR.error("No se pudieron cargar los tests.");case 34:return e.prev=34,v(!1),e.finish(34);case 37:case"end":return e.stop()}},e,null,[[1,30,34,37],[7,19,22,25]])})),function(){return r.apply(this,arguments)}),k=function(e,r){f(function(t){return t.map(function(t){return t.testId===e?eG(eG({},t),{},{cantidad:Math.max(0,Math.min(r,t.maxPreguntas))}):t})})},E=(t=(0,s.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==(r=x.filter(function(e){return e.cantidad>0})).length){e.next=4;break}return p.oR.error("Selecciona al menos una pregunta de alg\xfan test."),e.abrupt("return");case 4:return N(!0),e.prev=5,e.next=8,function(e){return eF.apply(this,arguments)}(r);case 8:if(0!==(t=e.sent).length){e.next=12;break}return p.oR.error("No se pudieron obtener preguntas para el repaso."),e.abrupt("return");case 12:r.reduce(function(e,r){return e+r.cantidad},0),p.oR.success("Test de repaso creado con ".concat(t.length," preguntas de ").concat(r.length," test(s)")),n(t,r),e.next=21;break;case 17:e.prev=17,e.t0=e.catch(5),console.error("Error al generar test de repaso:",e.t0),p.oR.error("Error al generar el test de repaso.");case 21:return e.prev=21,N(!1),e.finish(21);case 24:case"end":return e.stop()}},e,null,[[5,17,21,24]])})),function(){return t.apply(this,arguments)}),C=x.reduce(function(e,r){return e+r.cantidad},0),S=x.filter(function(e){return e.cantidad>0}).length;return b?(0,h.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"}),(0,h.jsx)("span",{className:"ml-2 text-gray-600",children:"Cargando tests..."})]}):0===d.length?(0,h.jsxs)("div",{className:"text-center py-8",children:[(0,h.jsx)(l.NLe,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,h.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No hay tests disponibles"}),(0,h.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Primero necesitas crear algunos tests con preguntas para poder hacer un repaso."}),(0,h.jsx)("button",{onClick:a,className:"mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600",children:"Volver"})]}):(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h2",{className:"text-xl font-bold",children:"Configurar Test de Repaso"}),(0,h.jsx)("p",{className:"text-gray-600",children:"Selecciona cu\xe1ntas preguntas quieres de cada test para crear tu repaso personalizado."})]}),(0,h.jsx)("button",{onClick:a,className:"p-2 text-gray-500 hover:text-gray-700",title:"Cancelar",children:(0,h.jsx)(l.yGN,{className:"h-6 w-6"})})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center bg-gray-50 p-4 rounded-lg",children:[(0,h.jsxs)("div",{className:"flex gap-2",children:[(0,h.jsx)("button",{onClick:function(){f(function(e){return e.map(function(e){return eG(eG({},e),{},{cantidad:e.maxPreguntas})})})},className:"px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200",children:"Seleccionar todas"}),(0,h.jsx)("button",{onClick:function(){f(function(e){return e.map(function(e){return eG(eG({},e),{},{cantidad:0})})})},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:"Limpiar todo"})]}),(0,h.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,h.jsx)("strong",{children:C})," preguntas de ",(0,h.jsx)("strong",{children:S})," test(s)"]})]}),(0,h.jsx)("div",{className:"space-y-4",children:d.map(function(e){var r=x.find(function(r){return r.testId===e.id});return r?(0,h.jsxs)("div",{className:"border rounded-lg p-4 bg-white",children:[(0,h.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("h3",{className:"font-medium text-gray-900",children:e.titulo}),e.descripcion&&(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.descripcion}),(0,h.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Creado: ",new Date(e.creado_en).toLocaleDateString()]})]}),(0,h.jsxs)("div",{className:"text-sm text-gray-500",children:[e.numPreguntas," pregunta",1!==e.numPreguntas?"s":""," disponible",1!==e.numPreguntas?"s":""]})]}),(0,h.jsxs)("div",{className:"flex items-center gap-4",children:[(0,h.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Preguntas a incluir:"}),(0,h.jsxs)("div",{className:"flex items-center gap-2",children:[(0,h.jsx)("input",{type:"range",min:"0",max:r.maxPreguntas,value:r.cantidad,onChange:function(r){return k(e.id,parseInt(r.target.value))},className:"flex-1"}),(0,h.jsx)("input",{type:"number",min:"0",max:r.maxPreguntas,value:r.cantidad,onChange:function(r){return k(e.id,parseInt(r.target.value)||0)},className:"w-16 px-2 py-1 border rounded text-center text-sm"}),(0,h.jsxs)("span",{className:"text-sm text-gray-500",children:["/ ",r.maxPreguntas]})]})]})]},e.id):null})}),(0,h.jsx)("div",{className:"flex justify-center pt-4",children:(0,h.jsx)("button",{onClick:E,disabled:0===C||y,className:"flex items-center px-6 py-3 rounded-lg font-medium ".concat(0===C||y?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-indigo-600 text-white hover:bg-indigo-700"),children:y?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.jTZ,{className:"mr-2 h-5 w-5 animate-spin"}),"Generando repaso..."]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.aze,{className:"mr-2 h-5 w-5"}),"Iniciar Repaso (",C," preguntas)"]})})})]})}function eU(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eV(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?eU(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):eU(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function eB(e){var r,t,n,s=e.preguntas,a=(e.configuracion,e.onFinalizar),o=e.onCancelar,c=(0,i.useState)(0),d=c[0],u=c[1],x=(0,i.useState)({}),p=x[0],f=x[1],g=(0,i.useState)(null),b=g[0],v=g[1],j=(0,i.useState)(Date.now()),y=j[0],N=j[1],w=(0,i.useState)({}),k=w[0],E=w[1],C=(0,i.useState)(!1),S=C[0],O=C[1],A=(0,i.useState)(null),P=A[0],D=A[1];(0,i.useEffect)(function(){N(Date.now())},[]),(0,i.useEffect)(function(){var e;v(p[null==(e=s[d])?void 0:e.id]||null)},[d,p]);var T=s[d],_=function(e){if(!S){if(v(e),y){var r=Date.now(),t=r-y;E(function(e){return eV(eV({},e),{},(0,m.A)({},s[d].id,t))}),N(r)}f(function(r){return eV(eV({},r),{},(0,m.A)({},s[d].id,e))})}},R=function(){O(!0);var e=0,r=0,t=0,n=0,o=[];s.forEach(function(s){var a=p[s.id],i=k[s.id]||0;a?"blank"===a?t++:a===s.respuesta_correcta?e++:r++:t++,o.push({preguntaId:s.id,respuestaSeleccionada:a||"blank",esCorrecta:a===s.respuesta_correcta,tiempoRespuesta:i}),n+=i});var i=e/s.length*100;D({correctas:e,incorrectas:r,enBlanco:t,porcentaje:i,tiempoTotal:n}),a({totalPreguntas:s.length,respuestasCorrectas:e,respuestasIncorrectas:r,respuestasEnBlanco:t,tiempoTotal:n,porcentajeAcierto:i,respuestas:o})},z=(d+1)/s.length*100,I=Math.floor((Date.now()-y)/1e3);return(0,h.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,h.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("h2",{className:"text-xl font-bold",children:"Test de Repaso"}),(0,h.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,h.jsxs)("div",{className:"flex items-center gap-1",children:[(0,h.jsx)(l.x_j,{className:"h-4 w-4"}),d+1," de ",s.length]}),!S&&(0,h.jsxs)("div",{className:"flex items-center gap-1",children:[(0,h.jsx)(l.Ohp,{className:"h-4 w-4"}),Math.floor(I/60),":",(I%60).toString().padStart(2,"0")]})]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,h.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(z,"%")}})})]}),S&&P&&(0,h.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-4",children:[(0,h.jsx)("h4",{className:"font-semibold text-lg mb-3",children:"Resultados del Test"}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,h.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Correctas"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-green-700",children:P.correctas})]}),(0,h.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Incorrectas"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-red-700",children:P.incorrectas})]}),(0,h.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"En Blanco"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-yellow-700",children:P.enBlanco})]}),(0,h.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Porcentaje"}),(0,h.jsxs)("p",{className:"text-2xl font-bold text-blue-700",children:[P.porcentaje.toFixed(1),"%"]})]}),(0,h.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Tiempo Total"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(t=Math.floor((r=P.tiempoTotal)/6e4),n=Math.floor(r%6e4/1e3),"".concat(t,":").concat(n.toString().padStart(2,"0")))})]})]}),(0,h.jsx)("div",{className:"mt-4 flex justify-center",children:(0,h.jsx)("button",{onClick:function(){u(0),f({}),v(null),N(Date.now()),E({}),O(!1),D(null)},className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded",children:"Realizar de nuevo"})})]}),(0,h.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-6",children:T.pregunta}),(0,h.jsxs)("div",{className:"space-y-3",children:[["a","b","c","d"].map(function(e){var r,t=b===e;return(0,h.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer transition-all ".concat((r=e,S?r===T.respuesta_correcta?"bg-green-100 border-green-500 text-green-800":r===b&&r!==T.respuesta_correcta?"bg-red-100 border-red-500 text-red-800":"bg-gray-50":b===r?"bg-indigo-100 border-indigo-500":"hover:bg-gray-50 cursor-pointer")),onClick:function(){return!S&&_(e)},children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ".concat(S&&e===T.respuesta_correcta?"bg-green-500 text-white":S&&t&&e!==T.respuesta_correcta?"bg-red-500 text-white":t?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.toUpperCase()}),(0,h.jsx)("div",{className:"flex-grow",children:T["opcion_".concat(e)]}),S?e===T.respuesta_correcta?(0,h.jsx)(l.YrT,{className:"text-green-600"}):e===b&&e!==T.respuesta_correcta?(0,h.jsx)(l.yGN,{className:"text-red-600"}):null:null]})},e)}),!S&&(0,h.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,h.jsxs)("div",{className:"p-3 border rounded-lg cursor-pointer flex items-center ".concat("blank"===b?"bg-yellow-50 border-yellow-300":"hover:bg-gray-50 border-gray-300"),onClick:function(){return _("blank")},children:[(0,h.jsx)("input",{type:"checkbox",checked:"blank"===b,onChange:function(){return _("blank")},className:"mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded",onClick:function(e){return e.stopPropagation()}}),(0,h.jsx)("span",{className:"text-sm text-gray-600",children:"Marque si quiere dejar en blanco"})]})})]})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsxs)("button",{onClick:function(){d>0&&u(function(e){return e-1})},disabled:0===d,className:"flex items-center gap-2 px-4 py-2 rounded-lg ".concat(0===d?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:[(0,h.jsx)(l.irw,{className:"h-4 w-4"}),"Anterior"]}),(0,h.jsxs)("div",{className:"flex gap-2",children:[(0,h.jsx)("button",{onClick:o,className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancelar"}),!S&&(0,h.jsxs)("button",{onClick:function(){b||_("blank"),d<s.length-1?u(function(e){return e+1}):R()},className:"bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700",children:[d===s.length-1?"Finalizar Test":"Siguiente",d<s.length-1&&(0,h.jsx)(l.fOo,{className:"ml-1"})]}),S&&d<s.length-1&&(0,h.jsxs)("button",{onClick:function(){return u(function(e){return e+1})},className:"text-indigo-600 hover:text-indigo-800 flex items-center",children:["Siguiente ",(0,h.jsx)(l.fOo,{className:"ml-1"})]})]})]}),!S&&!b&&(0,h.jsx)("div",{className:"text-center text-gray-500 text-sm",children:"Selecciona una respuesta para continuar"})]})}function e$(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eW(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?e$(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):e$(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function eY(){var e,r,t,n,a,c,d,u,p,f,g=(0,i.useState)([]),b=g[0],v=g[1],j=(0,i.useState)(null),y=j[0],N=j[1],w=(0,i.useState)([]),k=w[0],E=w[1],C=(0,i.useState)(0),S=C[0],O=C[1],A=(0,i.useState)(null),P=A[0],D=A[1],T=(0,i.useState)({}),_=T[0],R=T[1],z=(0,i.useState)(!1),I=z[0],F=z[1],M=(0,i.useState)(null),L=M[0],G=M[1],q=(0,i.useState)(null),H=q[0],U=q[1],V=(0,i.useState)({}),B=V[0],$=V[1],W=(0,i.useState)(!0),Y=W[0],K=W[1],X=(0,i.useState)(""),J=X[0],Z=X[1],Q=(0,i.useState)(null),ee=Q[0],er=Q[1],et=(0,i.useState)(null),en=et[0],es=et[1],ea=(0,i.useState)("lista"),eo=ea[0],ei=ea[1],ec=(0,i.useState)([]),el=ec[0],ed=ec[1],eu=(0,i.useState)([]),em=eu[0],ex=eu[1];(0,i.useEffect)(function(){ep()},[]),(0,i.useEffect)(function(){y&&eh(y.id)},[y]),(0,i.useEffect)(function(){k.length>0&&!I&&U(Date.now())},[k]);var ep=(e=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return K(!0),Z(""),e.prev=2,e.next=5,(0,x.Lx)();case 5:return r=e.sent,e.next=8,Promise.all(r.map(function(){var e=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,x.Kj)(r.id);case 3:return t=e.sent,e.abrupt("return",eW(eW({},r),{},{numero_preguntas:t}));case 7:return e.prev=7,e.t0=e.catch(0),console.error("Error al obtener conteo para test ".concat(r.id,":"),e.t0),e.abrupt("return",eW(eW({},r),{},{numero_preguntas:void 0}));case 11:case"end":return e.stop()}},e,null,[[0,7]])}));return function(r){return e.apply(this,arguments)}}()));case 8:v(e.sent),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(2),console.error("Error al cargar tests:",e.t0),Z("No se pudieron cargar los tests. Por favor, int\xe9ntalo de nuevo.");case 16:return e.prev=16,K(!1),e.finish(16);case 19:case"end":return e.stop()}},e,null,[[2,12,16,19]])})),function(){return e.apply(this,arguments)}),eh=(r=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return K(!0),e.prev=1,e.next=4,(0,x.hg)(r);case 4:E(e.sent),O(0),D(null),R({}),F(!1),G(null),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(1),console.error("Error al cargar preguntas:",e.t0),Z("No se pudieron cargar las preguntas del test. Por favor, int\xe9ntalo de nuevo.");case 17:return e.prev=17,K(!1),e.finish(17);case 20:case"end":return e.stop()}},e,null,[[1,13,17,20]])})),function(e){return r.apply(this,arguments)}),ef=function(e){if(!I){if(D(e),H){var r=Date.now(),t=r-H;$(function(e){return eW(eW({},e),{},(0,m.A)({},k[S].id,t))}),U(r)}R(function(r){return eW(eW({},r),{},(0,m.A)({},k[S].id,e))})}},eg=(t=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(P||ef("blank"),!(S<k.length-1)){e.next=8;break}return e.next=4,eb();case 4:O(S+1),D(null),e.next=11;break;case 8:return e.next=10,eb();case 10:ev();case 11:case"end":return e.stop()}},e)})),function(){return t.apply(this,arguments)}),eb=(n=(0,s.A)(o().mark(function e(){var r,t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(y){e.next=2;break}return e.abrupt("return");case 2:return r=k[S],n="blank"!==(t=P||"blank")&&t===r.respuesta_correcta,s="blank"===t?"x":t,e.prev=6,e.next=9,(0,x.Gl)(y.id,r.id,s,n);case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(6),console.error("Error al guardar estad\xedstica:",e.t0);case 14:case"end":return e.stop()}},e,null,[[6,11]])})),function(){return n.apply(this,arguments)}),ev=function(){F(!0);var e=0,r=0,t=0,n=0;k.forEach(function(s){var a=_[s.id];a?"blank"===a?t++:a===s.respuesta_correcta?e++:r++:t++,n+=B[s.id]||0});var s=e/k.length*100;G({correctas:e,incorrectas:r,enBlanco:t,porcentaje:s,tiempoTotal:n})},ej=(a=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,x.oC)();case 3:er(e.sent),ei("estadisticas-generales"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error al cargar estad\xedsticas generales:",e.t0),Z("No se pudieron cargar las estad\xedsticas generales. Por favor, int\xe9ntalo de nuevo.");case 12:case"end":return e.stop()}},e,null,[[0,8]])})),function(){return a.apply(this,arguments)}),ey=(c=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,x.dd)(r);case 3:es(e.sent),ei("estadisticas-test"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error al cargar estad\xedsticas del test:",e.t0),Z("No se pudieron cargar las estad\xedsticas del test. Por favor, int\xe9ntalo de nuevo.");case 12:case"end":return e.stop()}},e,null,[[0,8]])})),function(e){return c.apply(this,arguments)});return(0,h.jsxs)("div",{className:"container mx-auto p-4",children:[J&&(0,h.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:J}),(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsx)("h2",{className:"text-2xl font-bold",children:"Mis Tests"}),(0,h.jsxs)("div",{className:"flex gap-2",children:[(0,h.jsxs)("button",{onClick:function(){ei("repaso-config")},className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline",children:[(0,h.jsx)(l.jTZ,{className:"mr-2"})," Test de Repaso"]}),(0,h.jsxs)("button",{onClick:ej,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline",children:[(0,h.jsx)(l.vQY,{className:"mr-2"})," Estad\xedsticas Generales"]})]})]}),"lista"===eo&&(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:b.map(function(e){return(0,h.jsxs)("div",{className:"border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col justify-between",onClick:function(){return N(e)},children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"font-semibold text-lg mb-2",children:e.titulo}),e.descripcion&&(0,h.jsx)("p",{className:"text-sm text-gray-600 mb-2 break-words",children:e.descripcion}),(0,h.jsxs)("p",{className:"text-sm text-gray-500 mb-1",children:["Preguntas: ","number"==typeof e.numero_preguntas?e.numero_preguntas:"Cargando..."]}),(0,h.jsxs)("p",{className:"text-xs text-gray-400",children:["Creado: ",new Date(e.creado_en).toLocaleDateString("es-ES")]})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,h.jsx)("button",{onClick:function(r){r.stopPropagation(),N(e),ei("test")},className:"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:"Realizar Test"}),(0,h.jsxs)("button",{onClick:function(r){r.stopPropagation(),ey(e.id),ei("estadisticas-test")},className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50",children:[(0,h.jsx)(l.eXT,{className:"mr-2"})," Estad\xedsticas"]})]})]},e.id)})}),"estadisticas-generales"===eo&&ee&&(0,h.jsx)(eD,{estadisticas:{totalTests:ee.totalTests,totalRespuestasCorrectas:ee.totalRespuestasCorrectas,totalRespuestasIncorrectas:ee.totalRespuestasIncorrectas,porcentajeAcierto:ee.porcentajeAcierto},onClose:function(){return ei("lista")}}),"estadisticas-test"===eo&&en&&(0,h.jsx)(eT,{estadisticas:{totalPreguntas:en.totalPreguntas,totalCorrectas:en.totalCorrectas,totalIncorrectas:en.totalIncorrectas,porcentajeAcierto:en.porcentajeAcierto,fechasRealizacion:en.fechasRealizacion,preguntasMasFalladas:en.preguntasMasFalladas},testTitulo:(null==(p=b.find(function(e){return e.id===en.testId}))?void 0:p.titulo)||"Test",onClose:function(){return ei("lista")}}),"repaso-config"===eo&&(0,h.jsx)(eH,{onIniciarRepaso:function(e,r){ed(e),ex(r),ei("repaso-test")},onCancelar:function(){return ei("lista")}}),"repaso-test"===eo&&el.length>0&&(0,h.jsx)(eB,{preguntas:el,configuracion:em,onFinalizar:function(e){console.log("Test de repaso finalizado:",e)},onCancelar:function(){return ei("lista")}}),"test"===eo&&y&&k.length>0&&(0,h.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mt-4",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,h.jsx)("h3",{className:"text-xl font-bold",children:y.titulo}),(0,h.jsx)("button",{onClick:function(){return ei("lista")},className:"bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded",children:"Volver"})]}),I&&L&&(0,h.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-4 mb-6",children:[(0,h.jsx)("h4",{className:"font-semibold text-lg mb-3",children:"Resultados del Test"}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,h.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Correctas"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-green-700",children:L.correctas})]}),(0,h.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Incorrectas"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-red-700",children:L.incorrectas})]}),(0,h.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"En Blanco"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-yellow-700",children:L.enBlanco})]}),(0,h.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Porcentaje"}),(0,h.jsxs)("p",{className:"text-2xl font-bold text-blue-700",children:[L.porcentaje.toFixed(1),"%"]})]}),(0,h.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,h.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Tiempo Total"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(u=Math.floor((d=Math.floor(L.tiempoTotal/1e3))/60),"".concat(u,":").concat((d%60).toString().padStart(2,"0")))})]})]}),(0,h.jsx)("div",{className:"mt-4 flex justify-center",children:(0,h.jsx)("button",{onClick:function(){O(0),D(null),R({}),F(!1),G(null),U(Date.now()),$({})},className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded",children:"Realizar de nuevo"})})]}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,h.jsxs)("span",{className:"text-gray-600",children:["Pregunta ",S+1," de ",k.length]}),!I&&(0,h.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,h.jsx)(l.Ohp,{className:"inline mr-1"})," Tiempo por pregunta"]})]}),(0,h.jsx)("div",{className:"h-2 bg-gray-200 rounded-full mb-4",children:(0,h.jsx)("div",{className:"h-2 bg-indigo-600 rounded-full",style:{width:"".concat((S+1)/k.length*100,"%")}})})]}),(0,h.jsx)("div",{className:"min-h-[300px]",children:(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("h4",{className:"font-semibold text-lg mb-6",children:null==(f=k[S])?void 0:f.pregunta}),(0,h.jsxs)("div",{className:"space-y-3 mt-6",children:[["a","b","c","d"].map(function(e){var r,t=I&&e===k[S].respuesta_correcta,n=I&&P===e&&e!==k[S].respuesta_correcta,s=P===e;return(0,h.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer ".concat(t?"bg-green-100 border-green-500":n?"bg-red-100 border-red-500":s?"bg-indigo-100 border-indigo-500":"hover:bg-gray-50"),onClick:function(){return!I&&ef(e)},children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ".concat(t?"bg-green-500 text-white":n?"bg-red-500 text-white":s?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.toUpperCase()}),(0,h.jsx)("div",{className:"flex-grow",children:null==(r=k[S])?void 0:r["opcion_".concat(e)]})]})},e)}),!I&&(0,h.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,h.jsxs)("div",{className:"p-3 border rounded-lg cursor-pointer flex items-center ".concat("blank"===P?"bg-yellow-50 border-yellow-300":"hover:bg-gray-50 border-gray-300"),onClick:function(){return ef("blank")},children:[(0,h.jsx)("input",{type:"checkbox",checked:"blank"===P,onChange:function(){return ef("blank")},className:"mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded",onClick:function(e){return e.stopPropagation()}}),(0,h.jsx)("span",{className:"text-sm text-gray-600",children:"Marque si quiere dejar en blanco"})]})})]})]})}),(0,h.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,h.jsxs)("button",{onClick:function(){S>0&&(O(S-1),D(_[k[S-1].id]||null))},disabled:0===S,className:"flex items-center ".concat(0===S?"text-gray-400 cursor-not-allowed":"text-indigo-600 hover:text-indigo-800"),children:[(0,h.jsx)(l.irw,{className:"mr-1"})," Anterior"]}),!I&&(0,h.jsxs)("button",{onClick:eg,className:"bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700",children:[S===k.length-1?"Finalizar Test":"Siguiente",S<k.length-1&&(0,h.jsx)(l.fOo,{className:"ml-1"})]}),I&&S<k.length-1&&(0,h.jsxs)("button",{onClick:eg,className:"text-indigo-600 hover:text-indigo-800 flex items-center",children:["Siguiente ",(0,h.jsx)(l.fOo,{className:"ml-1"})]})]})]}),Y&&(0,h.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})}),!Y&&0===b.length&&"lista"===eo&&(0,h.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded flex items-start",children:[(0,h.jsx)(l.eHT,{className:"mr-2 mt-1 flex-shrink-0"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"font-medium",children:"No hay tests disponibles"}),(0,h.jsx)("p",{className:"text-sm mt-1",children:'Genera nuevos tests desde la secci\xf3n "Generar Tests".'})]})]})]})}var eK=t(76538),eX=t(33821);function eJ(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eZ(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?eJ(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):eJ(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var eQ=[{id:"a1_2019_junta",nombre:"Cuerpo Superior Facultativo - Inform\xe1tica (A1.2019)",descripcion:"Temario completo para oposiciones del Cuerpo Superior Facultativo, opci\xf3n Inform\xe1tica de la Junta de Andaluc\xeda",cuerpo:"CUERPO SUPERIOR FACULTATIVO, OPCI\xd3N INFORM\xc1TICA (A1.2019)",archivo:"a1_2019_junta.md"},{id:"c1_junta",nombre:"Cuerpo General de Administrativos (C1.1000)",descripcion:"Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andaluc\xeda",cuerpo:"CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)",archivo:"c1_junta.md"},{id:"c2_estado",nombre:"Cuerpo General Auxiliar del Estado (C2)",descripcion:"Temario para oposiciones del Cuerpo General Auxiliar del Estado",cuerpo:"CUERPO GENERAL AUXILIAR DEL ESTADO (C2)",archivo:"c2_estado.md"},{id:"c2_junta",nombre:"Cuerpo General Auxiliar - Junta de Andaluc\xeda (C2)",descripcion:"Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andaluc\xeda",cuerpo:"CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUC\xcdA (C2)",archivo:"c2_junta.md"}];function e0(e){return e1.apply(this,arguments)}function e1(){return(e1=(0,s.A)(o().mark(function e(r){var t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t=eQ.find(function(e){return e.id===r})){e.next=5;break}return console.error("Temario predefinido no encontrado:",r),e.abrupt("return",null);case 5:return e.next=7,fetch("/temarios/".concat(t.archivo));case 7:if((n=e.sent).ok){e.next=11;break}return console.error("Error al cargar archivo de temario:",n.status),e.abrupt("return",null);case 11:return e.next=13,n.text();case 13:return s=function(e){for(var r=[],t=e.split("\n"),n=0;n<t.length;n++){var s=t[n].trim(),a=s.match(/^Tema\s+(\d+)\.\s*(.+)$/);if(a||(a=s.match(/^(\d+)\.\s*(.+)$/)),a){var o=parseInt(a[1]),i=a[2].trim();i.length>10&&!i.match(/^[IVX]+\s*$/)&&r.push({numero:o,titulo:i,descripcion:i.length>100?i.substring(0,100)+"...":i})}}return r}(e.sent),e.abrupt("return",eZ(eZ({},t),{},{temas:s}));case 18:return e.prev=18,e.t0=e.catch(0),console.error("Error al cargar temario predefinido:",e.t0),e.abrupt("return",null);case 22:case"end":return e.stop()}},e,null,[[0,18]])}))).apply(this,arguments)}function e2(){return(e2=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,e0(r);case 3:if(t=e.sent){e.next=6;break}return e.abrupt("return",null);case 6:return e.abrupt("return",{totalTemas:t.temas.length,tipoTemario:"Temario Completo Predefinido",cuerpo:t.cuerpo});case 9:return e.prev=9,e.t0=e.catch(0),console.error("Error al obtener estad\xedsticas:",e.t0),e.abrupt("return",null);case 13:case"end":return e.stop()}},e,null,[[0,9]])}))).apply(this,arguments)}function e4(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function e3(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?e4(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):e4(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let e5=function(e){var r,t=e.onSeleccionar,n=e.onVolver,a=(0,i.useState)([]),c=a[0],d=a[1],u=(0,i.useState)(""),x=u[0],f=u[1],g=(0,i.useState)(null),b=g[0];g[1];var v=(0,i.useState)(null),j=v[0],y=v[1],N=(0,i.useState)({}),w=N[0],k=N[1];(0,i.useEffect)(function(){E()},[]),(0,i.useEffect)(function(){d(function(e){if(!e.trim())return eQ;var r=e.toLowerCase();return eQ.filter(function(e){return e.nombre.toLowerCase().includes(r)||e.descripcion.toLowerCase().includes(r)||e.cuerpo.toLowerCase().includes(r)})}(x))},[x]);var E=function(){var e;d(eQ),eQ.forEach((e=(0,s.A)(o().mark(function e(r){var t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,function(e){return e2.apply(this,arguments)}(r.id);case 2:(t=e.sent)&&k(function(e){return e3(e3({},e),{},(0,m.A)({},r.id,t))});case 4:case"end":return e.stop()}},e)})),function(r){return e.apply(this,arguments)}))},C=(r=(0,s.A)(o().mark(function e(r){var n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return y(r),e.prev=1,e.next=4,e0(r);case 4:(n=e.sent)?(t(n),p.oR.success("Temario predefinido cargado exitosamente")):p.oR.error("Error al cargar el temario predefinido"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error al cargar temario:",e.t0),p.oR.error("Error al cargar el temario predefinido");case 12:return e.prev=12,y(null),e.finish(12);case 15:case"end":return e.stop()}},e,null,[[1,8,12,15]])})),function(e){return r.apply(this,arguments)}),S=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150;return e.length<=r?e:e.substring(0,r)+"..."};return(0,h.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,h.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,h.jsxs)("div",{className:"mb-8",children:[(0,h.jsx)("button",{onClick:n,className:"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4 flex items-center",children:"← Volver a la selecci\xf3n"}),(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Seleccionar Temario Predefinido"}),(0,h.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Elige uno de nuestros temarios oficiales predefinidos"}),(0,h.jsx)("p",{className:"text-gray-500",children:"Estos temarios est\xe1n basados en convocatorias oficiales y contienen todos los temas necesarios"})]})]}),(0,h.jsx)("div",{className:"mb-6",children:(0,h.jsxs)("div",{className:"relative max-w-md mx-auto",children:[(0,h.jsx)(l.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,h.jsx)("input",{type:"text",placeholder:"Buscar por cuerpo, nivel o descripci\xf3n...",value:x,onChange:function(e){return f(e.target.value)},className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,h.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:c.map(function(e){var r=w[e.id],t=j===e.id;return(0,h.jsx)("div",{className:"bg-white rounded-xl shadow-sm border-2 transition-all duration-200 hover:shadow-md h-full flex flex-col ".concat(b===e.id?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-blue-300"),children:(0,h.jsxs)("div",{className:"p-6 flex flex-col h-full",children:[(0,h.jsxs)("div",{className:"flex-grow",children:[(0,h.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3",children:(0,h.jsx)(l.H9b,{className:"w-6 h-6 text-blue-600"})}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"font-semibold text-gray-900 text-sm leading-tight",children:e.nombre}),(0,h.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.cuerpo})]})]})}),(0,h.jsx)("p",{className:"text-sm text-gray-600 mb-4 leading-relaxed",children:S(e.descripcion)}),r&&(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,h.jsx)("span",{className:"text-gray-600",children:"Total de temas:"}),(0,h.jsx)("span",{className:"font-semibold text-gray-900",children:r.totalTemas})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between text-sm mt-1",children:[(0,h.jsx)("span",{className:"text-gray-600",children:"Tipo:"}),(0,h.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded",children:"Completo"})]})]})]}),(0,h.jsx)("button",{onClick:function(){return C(e.id)},disabled:t,className:"w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center ".concat(t?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800"),children:t?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.TwU,{className:"w-4 h-4 mr-2 animate-spin"}),"Cargando..."]}):(0,h.jsxs)(h.Fragment,{children:["Seleccionar Temario",(0,h.jsx)(l.dyV,{className:"w-4 h-4 ml-2"})]})})]})},e.id)})}),0===c.length&&(0,h.jsxs)("div",{className:"text-center py-12",children:[(0,h.jsx)(l.S8s,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No se encontraron temarios"}),(0,h.jsx)("p",{className:"text-gray-600",children:"Intenta con otros t\xe9rminos de b\xfasqueda o revisa la ortograf\xeda"})]}),(0,h.jsx)("div",{className:"mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)(l.S8s,{className:"w-5 h-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,h.jsxs)("div",{className:"text-blue-800",children:[(0,h.jsx)("h4",{className:"font-semibold mb-2",children:"Sobre los temarios predefinidos"}),(0,h.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,h.jsx)("li",{children:"• Basados en convocatorias oficiales reales"}),(0,h.jsx)("li",{children:"• Incluyen todos los temas necesarios para la oposici\xf3n"}),(0,h.jsx)("li",{children:"• Optimizados para usar con las funciones de IA de la plataforma"}),(0,h.jsx)("li",{children:"• Se pueden personalizar despu\xe9s de la importaci\xf3n"})]})]})]})})]})})};function e6(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function e8(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?e6(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):e6(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let e7=function(e){var r,t=e.onComplete,n=(0,i.useState)("seleccion"),a=n[0],c=n[1],d=(0,i.useState)(null),x=d[0],f=d[1],g=(0,i.useState)(""),b=g[0],v=g[1],j=(0,i.useState)(""),y=j[0],N=j[1],w=(0,i.useState)([{numero:1,titulo:"",descripcion:""}]),k=w[0],E=w[1],C=(0,i.useState)(null),S=C[0],O=C[1],A=(0,i.useState)(!1),P=A[0],D=A[1],T=function(e){f(e),"predefinido"===e?c("predefinidos"):c("configuracion")},_=function(e){k.length>1&&E(k.filter(function(r,t){return t!==e}).map(function(e,r){return e8(e8({},e),{},{numero:r+1})}))},R=function(e,r,t){var n=(0,u.A)(k);n[e]=e8(e8({},n[e]),{},(0,m.A)({},r,t)),E(n)},z=(r=(0,s.A)(o().mark(function e(){var r,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(D(!0),e.prev=1,!("predefinido"===x&&S)){e.next=6;break}r={titulo:S.nombre,descripcion:"".concat(S.descripcion,"\n\nCuerpo: ").concat(S.cuerpo),tipo:"completo",temas:S.temas.map(function(e,r){return{numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,orden:r+1}})},e.next=9;break;case 6:if(!(!(b.trim()?!k.some(function(e){return!e.titulo.trim()})||(p.oR.error("Todos los temas deben tener un t\xedtulo"),!1):(p.oR.error("El t\xedtulo del temario es obligatorio"),!1))||!x)){e.next=8;break}return e.abrupt("return");case 8:r={titulo:b,descripcion:y,tipo:x,temas:k.map(function(e,r){return{numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,orden:r+1}})};case 9:return e.next=11,(0,eX.r5)(r.titulo,r.descripcion,r.tipo);case 11:if(n=e.sent){e.next=15;break}return p.oR.error("Error al crear el temario"),e.abrupt("return");case 15:return e.next=17,(0,eX.sW)(n,r.temas);case 17:if(e.sent){e.next=21;break}return p.oR.error("Error al crear los temas"),e.abrupt("return");case 21:p.oR.success("\xa1Temario configurado exitosamente!"),t(),e.next=29;break;case 25:e.prev=25,e.t0=e.catch(1),console.error("Error al guardar temario:",e.t0),p.oR.error("Error al configurar el temario");case 29:return e.prev=29,D(!1),e.finish(29);case 32:case"end":return e.stop()}},e,null,[[1,25,29,32]])})),function(){return r.apply(this,arguments)});return"seleccion"===a?(0,h.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,h.jsxs)("div",{className:"max-w-4xl w-full",children:[(0,h.jsxs)("div",{className:"text-center mb-8",children:[(0,h.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"\xa1Bienvenido a OposiAI! \uD83C\uDF89"}),(0,h.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Para comenzar, necesitamos configurar tu temario de estudio."}),(0,h.jsx)("p",{className:"text-gray-500",children:"Esto nos permitir\xe1 crear una planificaci\xf3n personalizada y hacer un seguimiento de tu progreso. El temario solo es el \xedndice de los temas necesarios para preparar la oposici\xf3n, los textos ser\xe1n a\xf1adidos por el opositor."})]}),(0,h.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,h.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-green-500 cursor-pointer transition-all duration-200 hover:shadow-md",onClick:function(){return T("predefinido")},children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,h.jsx)(l.a4x,{className:"w-8 h-8 text-green-600"})}),(0,h.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Temarios Predefinidos"}),(0,h.jsx)("p",{className:"text-gray-600 mb-4",children:"Selecciona un temario oficial ya configurado y listo para usar."}),(0,h.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,h.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,h.jsx)("span",{children:"Basados en convocatorias oficiales"})]})}),(0,h.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,h.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,h.jsx)("span",{children:"Configuraci\xf3n instant\xe1nea"})]})}),(0,h.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,h.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,h.jsx)("span",{children:"La IA podr\xe1 crear una planificaci\xf3n completa y personalizada"})]})}),(0,h.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,h.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,h.jsx)("span",{children:"Seguimiento detallado del progreso por temas"})]})})]})}),(0,h.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-blue-500 cursor-pointer transition-all duration-200 hover:shadow-md",onClick:function(){return T("completo")},children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,h.jsx)(l.H9b,{className:"w-8 h-8 text-blue-600"})}),(0,h.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Temario Personalizado"}),(0,h.jsx)("p",{className:"text-gray-600 mb-4",children:"Configura todos los temas de tu oposici\xf3n de forma estructurada."}),(0,h.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,h.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,h.jsx)("span",{children:"La IA podr\xe1 crear una planificaci\xf3n completa y personalizada"})]})}),(0,h.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,h.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,h.jsx)("span",{children:"Seguimiento detallado del progreso por temas"})]})})]})})]}),(0,h.jsx)("div",{className:"text-center mt-8",children:(0,h.jsx)("p",{className:"text-sm text-gray-500",children:"Podr\xe1s modificar tu temario m\xe1s adelante desde la configuraci\xf3n"})})]})}):"predefinidos"===a?(0,h.jsx)(e5,{onSeleccionar:function(e){O(e),c("configuracion")},onVolver:function(){c("seleccion"),f(null)}}):(0,h.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,h.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,h.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("button",{onClick:function(){return c("seleccion")},className:"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4",children:"← Volver a la selecci\xf3n"}),(0,h.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"predefinido"===x?"Confirmar Temario Predefinido":"Configurar Temario Completo"})]}),"predefinido"===x&&S?(0,h.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 mb-6",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-green-900 mb-4",children:"Temario Seleccionado"}),(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Nombre:"}),(0,h.jsx)("p",{className:"text-green-700",children:S.nombre})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Cuerpo:"}),(0,h.jsx)("p",{className:"text-green-700",children:S.cuerpo})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Descripci\xf3n:"}),(0,h.jsx)("p",{className:"text-green-700",children:S.descripcion})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Total de temas:"}),(0,h.jsxs)("p",{className:"text-green-700",children:[S.temas.length," temas"]})]})]})]}):(0,h.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo del temario *"}),(0,h.jsx)("input",{type:"text",id:"titulo",value:b,onChange:function(e){return v(e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Oposiciones Auxiliar Administrativo 2024"})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional)"}),(0,h.jsx)("textarea",{id:"descripcion",value:y,onChange:function(e){return N(e.target.value)},rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Describe brevemente tu temario..."})]})]}),"predefinido"!==x&&(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Temas"}),(0,h.jsxs)("button",{onClick:function(){var e=k.length+1;E([].concat((0,u.A)(k),[{numero:e,titulo:"",descripcion:""}]))},className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm flex items-center",children:[(0,h.jsx)(l.GGD,{className:"w-4 h-4 mr-1"}),"A\xf1adir tema"]})]}),(0,h.jsx)("div",{className:"space-y-3",children:k.map(function(e,r){return(0,h.jsxs)("div",{className:"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg",children:[(0,h.jsxs)("div",{className:"w-16",children:[(0,h.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Tema"}),(0,h.jsx)("input",{type:"number",value:e.numero,onChange:function(e){return R(r,"numero",parseInt(e.target.value)||1)},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",min:"1"})]}),(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"T\xedtulo *"}),(0,h.jsx)("input",{type:"text",value:e.titulo,onChange:function(e){return R(r,"titulo",e.target.value)},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"T\xedtulo del tema"})]}),(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Descripci\xf3n"}),(0,h.jsx)("input",{type:"text",value:e.descripcion,onChange:function(e){return R(r,"descripcion",e.target.value)},className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Descripci\xf3n opcional"})]}),k.length>1&&(0,h.jsx)("button",{onClick:function(){return _(r)},className:"text-red-600 hover:text-red-700 p-1",title:"Eliminar tema",children:(0,h.jsx)(l.IXo,{className:"w-4 h-4"})})]},r)})})]}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,h.jsx)("button",{onClick:function(){return c("seleccion")},className:"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors",disabled:P,children:"Cancelar"}),(0,h.jsx)("button",{onClick:z,disabled:P,className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:P?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):"Guardar temario"})]})]})})})},e9=function(e){var r,t,a,c=e.onNavigateToTab,d=(0,C.A)().user,u=(0,i.useState)(null),m=u[0],x=u[1],p=(0,i.useState)([]),f=p[0],g=p[1],b=(0,i.useState)(!0),v=b[0],j=b[1],y=(0,i.useState)(!1),N=y[0],w=y[1];(0,i.useEffect)(function(){k()},[]);var k=(r=(0,s.A)(o().mark(function e(){var r,t,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return j(!0),e.prev=1,e.next=4,Promise.all([(0,eK.x)(),(0,eK.w)(5),(0,eX.yr)()]);case 4:r=e.sent,s=(t=(0,n.A)(r,3))[0],a=t[1],i=t[2],x(s),g(a),i||w(!0),e.next=20;break;case 14:e.prev=14,e.t0=e.catch(1),console.error("Error al cargar datos del dashboard:",e.t0),x({totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}),g([]),w(!0);case 20:return e.prev=20,j(!1),e.finish(20);case 23:case"end":return e.stop()}},e,null,[[1,14,20,23]])})),function(){return r.apply(this,arguments)});return N?(0,h.jsx)(e7,{onComplete:function(){w(!1),k()}}):v?(0,h.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white",children:[(0,h.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:[(t=new Date().getHours())<12?"Buenos d\xedas":t<18?"Buenas tardes":"Buenas noches",", ",(null==d||null==(a=d.email)?void 0:a.split("@")[0])||"Estudiante","! \uD83D\uDC4B"]}),(0,h.jsx)("p",{className:"text-blue-100",children:"\xbfListo para continuar con tu preparaci\xf3n? Aqu\xed tienes un resumen de tu progreso."})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,h.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Documentos"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==m?void 0:m.totalDocumentos)||0})]}),(0,h.jsx)(l.jH2,{className:"h-8 w-8 text-blue-600"})]})}),(0,h.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Colecciones"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==m?void 0:m.totalColeccionesFlashcards)||0})]}),(0,h.jsx)(l.H9b,{className:"h-8 w-8 text-emerald-600"})]})}),(0,h.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tests"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==m?void 0:m.totalTests)||0})]}),(0,h.jsx)(l.NLe,{className:"h-8 w-8 text-pink-600"})]})}),(0,h.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Flashcards"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==m?void 0:m.totalFlashcards)||0})]}),(0,h.jsx)(l.x_j,{className:"h-8 w-8 text-orange-600"})]})})]}),(0,h.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,h.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Estudio de Hoy"}),(0,h.jsx)(l.wIk,{className:"h-6 w-6 text-gray-400"})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,h.jsx)("div",{className:"bg-orange-50 rounded-lg p-4 border border-orange-200",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-orange-800",children:"Para Repasar Hoy"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:(null==m?void 0:m.flashcardsParaHoy)||0})]}),(0,h.jsx)(l.Ohp,{className:"h-6 w-6 text-orange-600"})]})}),(0,h.jsx)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Nuevas"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:(null==m?void 0:m.flashcardsNuevas)||0})]}),(0,h.jsx)(l.D1A,{className:"h-6 w-6 text-blue-600"})]})}),(0,h.jsx)("div",{className:"bg-green-50 rounded-lg p-4 border border-green-200",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-green-800",children:"% Acierto Tests"}),(0,h.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[(null==m?void 0:m.porcentajeAcierto.toFixed(1))||0,"%"]})]}),(0,h.jsx)(l.ARf,{className:"h-6 w-6 text-green-600"})]})})]}),m&&m.flashcardsParaHoy>0&&(0,h.jsx)("div",{className:"mt-4",children:(0,h.jsxs)("button",{onClick:function(){return c("misFlashcards")},className:"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors",children:[(0,h.jsx)(l.aze,{className:"mr-2"}),"Comenzar Estudio"]})})]}),f.length>0&&(0,h.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,h.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Pr\xf3ximas Flashcards"}),(0,h.jsx)("div",{className:"space-y-3",children:f.slice(0,3).map(function(e){return(0,h.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("p",{className:"font-medium text-gray-900 truncate",children:e.pregunta}),(0,h.jsx)("p",{className:"text-sm text-gray-600",children:e.coleccionTitulo})]}),(0,h.jsx)("div",{className:"text-right",children:(0,h.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("nuevo"===e.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:e.estado})})]},e.id)})}),f.length>3&&(0,h.jsx)("button",{onClick:function(){return c("misFlashcards")},className:"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Ver todas las flashcards pendientes"})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,h.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,h.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Colecciones Recientes"}),(0,h.jsx)("div",{className:"space-y-3",children:null==m?void 0:m.coleccionesRecientes.map(function(e){return(0,h.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,h.jsxs)("p",{className:"text-sm text-gray-600",children:["Creada: ",new Date(e.fechaCreacion).toLocaleDateString("es-ES")]})]}),(0,h.jsx)("div",{className:"text-right",children:(0,h.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:[e.paraHoy," para hoy"]})})]},e.id)})}),(0,h.jsx)("button",{onClick:function(){return c("misFlashcards")},className:"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium",children:"Ver todas las colecciones"})]}),(0,h.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,h.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Tests Recientes"}),(0,h.jsx)("div",{className:"space-y-3",children:null==m?void 0:m.testsRecientes.map(function(e){return(0,h.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,h.jsxs)("div",{className:"flex-1",children:[(0,h.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,h.jsxs)("p",{className:"text-sm text-gray-600",children:["Creado: ",new Date(e.fechaCreacion).toLocaleDateString("es-ES")]})]}),(0,h.jsx)("div",{className:"text-right",children:(0,h.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800",children:[e.numeroPreguntas," preguntas"]})})]},e.id)})}),(0,h.jsx)("button",{onClick:function(){return c("misTests")},className:"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium",children:"Ver todos los tests"})]})]}),(0,h.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,h.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Acciones R\xe1pidas"}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,h.jsxs)("button",{onClick:function(){return c("preguntas")},className:"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200",children:[(0,h.jsx)(l.jH2,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,h.jsx)("span",{className:"font-medium text-blue-900",children:"Hacer Preguntas"})]}),(0,h.jsxs)("button",{onClick:function(){return c("flashcards")},className:"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200",children:[(0,h.jsx)(l.GGD,{className:"h-6 w-6 text-orange-600 mr-3"}),(0,h.jsx)("span",{className:"font-medium text-orange-900",children:"Crear Flashcards"})]}),(0,h.jsxs)("button",{onClick:function(){return c("tests")},className:"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200",children:[(0,h.jsx)(l.NLe,{className:"h-6 w-6 text-indigo-600 mr-3"}),(0,h.jsx)("span",{className:"font-medium text-indigo-900",children:"Generar Tests"})]}),(0,h.jsxs)("button",{onClick:function(){return c("mapas")},className:"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200",children:[(0,h.jsx)(l.s_k,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,h.jsx)("span",{className:"font-medium text-purple-900",children:"Mapas Mentales"})]}),(0,h.jsxs)("button",{onClick:function(){return c("planEstudios")},className:"flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200",children:[(0,h.jsx)(l.wIk,{className:"h-6 w-6 text-teal-600 mr-3"}),(0,h.jsx)("span",{className:"font-medium text-teal-900",children:"Plan de Estudios"})]})]})]})]})};var re=t(25519);function rr(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rt(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rr(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rr(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var rn=[{key:"lunes",label:"Lunes"},{key:"martes",label:"Martes"},{key:"miercoles",label:"Mi\xe9rcoles"},{key:"jueves",label:"Jueves"},{key:"viernes",label:"Viernes"},{key:"sabado",label:"S\xe1bado"},{key:"domingo",label:"Domingo"}];let rs=function(e){var r,t,n=e.temario,a=e.onComplete,c=e.onCancel,d=e.isEditing,x=void 0!==d&&d,f=(0,i.useState)(1),g=f[0],b=f[1],v=(0,i.useState)([]),j=(v[0],v[1]),y=(0,i.useState)(!0),N=y[0],w=y[1],k=(0,i.useState)(!1),E=k[0],C=k[1],S=(0,i.useState)({tiempoDiarioPromedio:2,tiempoPorDia:{},fechaExamen:"",fechaExamenAproximada:"",familiaridadGeneral:3,estimacionesTemas:{},preferenciasHorario:[],frecuenciaRepasos:"semanal"}),O=S[0],A=S[1];(0,i.useEffect)(function(){P()},[n.id,x]);var P=(r=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return w(!0),e.prev=1,e.next=4,(0,eX.cm)(n.id);case 4:if(j(e.sent),!x){e.next=11;break}return e.next=9,(0,re.u9)(n.id);case 9:(r=e.sent)&&A({tiempoDiarioPromedio:r.tiempo_diario_promedio||2,tiempoPorDia:r.tiempo_por_dia||{},fechaExamen:r.fecha_examen||"",fechaExamenAproximada:r.fecha_examen_aproximada||"",familiaridadGeneral:r.familiaridad_general||3,estimacionesTemas:{},preferenciasHorario:r.preferencias_horario||[],frecuenciaRepasos:r.frecuencia_repasos||"semanal"});case 11:e.next=17;break;case 13:e.prev=13,e.t0=e.catch(1),console.error("Error al cargar datos:",e.t0),p.oR.error(x?"Error al cargar la planificaci\xf3n existente":"Error al cargar los temas del temario");case 17:return e.prev=17,w(!1),e.finish(17);case 20:case"end":return e.stop()}},e,null,[[1,13,17,20]])})),function(){return r.apply(this,arguments)}),D=function(e,r){A(function(t){return rt(rt({},t),{},(0,m.A)({},e,r))})},T=function(e,r){A(function(t){return rt(rt({},t),{},{tiempoPorDia:rt(rt({},t.tiempoPorDia),{},(0,m.A)({},e,r))})})},_=function(e){A(function(r){return rt(rt({},r),{},{preferenciasHorario:r.preferenciasHorario.includes(e)?r.preferenciasHorario.filter(function(r){return r!==e}):[].concat((0,u.A)(r.preferenciasHorario),[e])})})},R=(t=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return C(!0),e.prev=1,e.next=4,(0,re.Pk)(n.id,{tiempo_diario_promedio:O.tiempoDiarioPromedio,tiempo_por_dia:O.tiempoPorDia,fecha_examen:O.fechaExamen||void 0,fecha_examen_aproximada:O.fechaExamenAproximada||void 0,familiaridad_general:O.familiaridadGeneral,preferencias_horario:O.preferenciasHorario,frecuencia_repasos:O.frecuenciaRepasos});case 4:if(e.sent){e.next=7;break}throw Error("Error al guardar la planificaci\xf3n");case 7:p.oR.success(x?"\xa1Planificaci\xf3n actualizada exitosamente!":"\xa1Planificaci\xf3n configurada exitosamente!"),a(),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(1),console.error("Error al finalizar asistente:",e.t0),p.oR.error(x?"Error al actualizar la planificaci\xf3n":"Error al guardar la planificaci\xf3n");case 15:return e.prev=15,C(!1),e.finish(15);case 18:case"end":return e.stop()}},e,null,[[1,11,15,18]])})),function(){return t.apply(this,arguments)});return N?(0,h.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,h.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,h.jsxs)("div",{className:"mb-8",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,h.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:x?"Modificar Planificaci\xf3n IA":"Asistente de Planificaci\xf3n IA"}),(0,h.jsxs)("span",{className:"text-sm text-gray-500",children:["Paso ",g," de 4"]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,h.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(g/4*100,"%")}})})]}),(0,h.jsx)("div",{className:"bg-white rounded-xl shadow-sm border p-8 mb-6",children:function(){switch(g){case 1:return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)(l.Ohp,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,h.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Disponibilidad de Tiempo"}),(0,h.jsx)("p",{className:"text-gray-600",children:"Para empezar, \xbfcu\xe1nto tiempo REAL estimas que puedes dedicar al estudio cada d\xeda?"}),(0,h.jsx)("p",{className:"text-sm text-blue-600 mt-2",children:"S\xe9 realista. Considera tu trabajo, familia y otros compromisos."})]}),(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Tiempo promedio diario (horas)"}),(0,h.jsx)("input",{type:"number",min:"0.5",max:"12",step:"0.5",value:O.tiempoDiarioPromedio,onChange:function(e){return D("tiempoDiarioPromedio",parseFloat(e.target.value))},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Tiempo espec\xedfico por d\xeda (opcional)"}),(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:rn.map(function(e){return(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("label",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,h.jsx)("input",{type:"number",min:"0",max:"12",step:"0.5",value:O.tiempoPorDia[e.key]||"",onChange:function(r){return T(e.key,parseFloat(r.target.value)||0)},className:"w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0"})]},e.key)})})]})]});case 2:return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)(l.wIk,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,h.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Fecha del Examen"}),(0,h.jsx)("p",{className:"text-gray-600",children:"\xbfCu\xe1l es la fecha (aproximada o exacta) de tu pr\xf3xima convocatoria o examen principal?"}),(0,h.jsx)("p",{className:"text-sm text-blue-600 mt-2",children:"Esto nos ayudar\xe1 a distribuir el temario en el tiempo disponible."})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Fecha exacta del examen"}),(0,h.jsx)("input",{type:"date",value:O.fechaExamen,onChange:function(e){return D("fechaExamen",e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"O fecha aproximada"}),(0,h.jsxs)("select",{value:O.fechaExamenAproximada,onChange:function(e){return D("fechaExamenAproximada",e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,h.jsx)("option",{value:"",children:"Selecciona una opci\xf3n"}),(0,h.jsx)("option",{value:"1-3_meses",children:"En 1-3 meses"}),(0,h.jsx)("option",{value:"3-6_meses",children:"En 3-6 meses"}),(0,h.jsx)("option",{value:"6-12_meses",children:"En 6-12 meses"}),(0,h.jsx)("option",{value:"mas_12_meses",children:"M\xe1s de 12 meses"}),(0,h.jsx)("option",{value:"primavera_2025",children:"Primavera 2025"}),(0,h.jsx)("option",{value:"verano_2025",children:"Verano 2025"}),(0,h.jsx)("option",{value:"otono_2025",children:"Oto\xf1o 2025"}),(0,h.jsx)("option",{value:"invierno_2025",children:"Invierno 2025"})]})]})]})]});case 3:return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)(l.x_j,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,h.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Familiaridad con el Temario"}),(0,h.jsx)("p",{className:"text-gray-600",children:"En una escala del 1 al 5, \xbfc\xf3mo describir\xedas tu familiaridad general actual con el conjunto del temario?"})]}),(0,h.jsx)("div",{className:"bg-gray-50 rounded-lg p-6",children:(0,h.jsx)("div",{className:"grid grid-cols-5 gap-4",children:[1,2,3,4,5].map(function(e){return(0,h.jsxs)("button",{onClick:function(){return D("familiaridadGeneral",e)},className:"p-4 rounded-lg border-2 transition-all ".concat(O.familiaridadGeneral===e?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300"),children:[(0,h.jsx)("div",{className:"text-2xl font-bold mb-2",children:e}),(0,h.jsxs)("div",{className:"text-xs",children:[1===e&&"Muy poco",2===e&&"Poco",3===e&&"Moderado",4===e&&"Bastante",5===e&&"Muy familiarizado"]})]},e)})})})]});case 4:return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)(l.usP,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,h.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Preferencias de Estudio"}),(0,h.jsx)("p",{className:"text-gray-600",children:"Configura tus preferencias de horario y frecuencia de repasos."})]}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Preferencias de Horario"}),(0,h.jsx)("div",{className:"space-y-2",children:["ma\xf1ana","tarde","noche"].map(function(e){return(0,h.jsxs)("label",{className:"flex items-center",children:[(0,h.jsx)("input",{type:"checkbox",checked:O.preferenciasHorario.includes(e),onChange:function(){return _(e)},className:"mr-3"}),(0,h.jsx)("span",{className:"capitalize",children:e})]},e)})})]}),(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Frecuencia de Repasos"}),(0,h.jsxs)("select",{value:O.frecuenciaRepasos,onChange:function(e){return D("frecuenciaRepasos",e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,h.jsx)("option",{value:"semanal",children:"Semanal"}),(0,h.jsx)("option",{value:"quincenal",children:"Quincenal"}),(0,h.jsx)("option",{value:"mensual",children:"Mensual"})]}),(0,h.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Los repasos son fundamentales para consolidar el aprendizaje a largo plazo."})]})]})]});default:return null}}()}),(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("button",{onClick:c,className:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancelar"}),(0,h.jsxs)("div",{className:"flex space-x-3",children:[g>1&&(0,h.jsxs)("button",{onClick:function(){g>1&&b(g-1)},className:"flex items-center px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,h.jsx)(l.kRp,{className:"w-4 h-4 mr-2"}),"Anterior"]}),g<4?(0,h.jsxs)("button",{onClick:function(){g<4&&b(g+1)},className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:["Siguiente",(0,h.jsx)(l.dyV,{className:"w-4 h-4 ml-2"})]}):(0,h.jsxs)("button",{onClick:R,disabled:E,className:"flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:[E?(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-2"}),x?"Actualizar":"Finalizar"]})]})]})]})};function ra(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function ro(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ra(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ra(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let ri=function(e){var r,t=e.isOpen,n=e.onClose,a=e.temario,c=e.onSave,d=(0,i.useState)(""),u=d[0],m=d[1],x=(0,i.useState)(""),f=x[0],g=x[1],b=(0,i.useState)(!1),v=b[0],j=b[1];(0,i.useEffect)(function(){t&&a&&(m(a.titulo),g(a.descripcion||""))},[t,a]);var y=(r=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(u.trim()){e.next=3;break}return p.oR.error("El t\xedtulo del temario es obligatorio"),e.abrupt("return");case 3:return j(!0),e.prev=4,r=p.oR.loading("Actualizando temario..."),e.next=8,(0,eX.Se)(a.id,u.trim(),f.trim());case 8:e.sent?(p.oR.success("Temario actualizado exitosamente",{id:r}),c(ro(ro({},a),{},{titulo:u.trim(),descripcion:f.trim(),actualizado_en:new Date().toISOString()})),n()):p.oR.error("Error al actualizar el temario",{id:r}),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(4),console.error("Error al actualizar temario:",e.t0),p.oR.error("Error al actualizar el temario",{id:r});case 16:return e.prev=16,j(!1),e.finish(16);case 19:case"end":return e.stop()}},e,null,[[4,12,16,19]])})),function(){return r.apply(this,arguments)}),N=function(){m(a.titulo),g(a.descripcion||""),n()};return t?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Editar Temario"}),(0,h.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:v,children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"p-6 space-y-4",onKeyDown:function(e){"Escape"===e.key?N():"Enter"===e.key&&e.ctrlKey&&y()},children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del temario *"}),(0,h.jsx)("input",{type:"text",id:"titulo",value:u,onChange:function(e){return m(e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Oposiciones Auxiliar Administrativo 2024",disabled:v,autoFocus:!0})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,h.jsx)("textarea",{id:"descripcion",value:f,onChange:function(e){return g(e.target.value)},rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"Describe brevemente tu temario...",disabled:v})]}),(0,h.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)("div",{className:"flex-shrink-0",children:(0,h.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"})}),(0,h.jsx)("div",{className:"ml-3",children:(0,h.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,h.jsx)("strong",{children:"Nota:"})," Solo puedes editar el t\xedtulo y la descripci\xf3n del temario. Para modificar los temas, utiliza las opciones individuales de cada tema."]})})]})})]}),(0,h.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,h.jsx)("button",{onClick:N,className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",disabled:v,children:"Cancelar"}),(0,h.jsx)("button",{onClick:y,disabled:v||!u.trim(),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:v?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.Bc_,{className:"w-4 h-4 mr-2"}),"Guardar cambios"]})})]}),(0,h.jsx)("div",{className:"px-6 pb-4",children:(0,h.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,h.jsx)("strong",{children:"Atajos:"})," Esc para cancelar • Ctrl+Enter para guardar"]})})]})}):null};function rc(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rl(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rc(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let rd=function(e){var r,t=e.isOpen,n=e.onClose,a=e.tema,c=e.onSave,d=(0,i.useState)(""),u=d[0],m=d[1],x=(0,i.useState)(""),f=x[0],g=x[1],b=(0,i.useState)(!1),v=b[0],j=b[1];(0,i.useEffect)(function(){t&&a&&(m(a.titulo),g(a.descripcion||""))},[t,a]);var y=(r=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(u.trim()){e.next=3;break}return p.oR.error("El t\xedtulo del tema es obligatorio"),e.abrupt("return");case 3:return j(!0),e.prev=4,r=p.oR.loading("Actualizando tema..."),e.next=8,(0,eX.oS)(a.id,u.trim(),f.trim());case 8:e.sent?(p.oR.success("Tema actualizado exitosamente",{id:r}),c(rl(rl({},a),{},{titulo:u.trim(),descripcion:f.trim(),actualizado_en:new Date().toISOString()})),n()):p.oR.error("Error al actualizar el tema",{id:r}),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(4),console.error("Error al actualizar tema:",e.t0),p.oR.error("Error al actualizar el tema",{id:r});case 16:return e.prev=16,j(!1),e.finish(16);case 19:case"end":return e.stop()}},e,null,[[4,12,16,19]])})),function(){return r.apply(this,arguments)}),N=function(){m(a.titulo),g(a.descripcion||""),n()};return t?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,h.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Editar Tema ",a.numero]}),(0,h.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:v,children:(0,h.jsx)(l.yGN,{size:24})})]}),(0,h.jsxs)("div",{className:"p-6 space-y-4",onKeyDown:function(e){"Escape"===e.key?N():"Enter"===e.key&&e.ctrlKey&&y()},children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del tema *"}),(0,h.jsx)("input",{type:"text",id:"titulo",value:u,onChange:function(e){return m(e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Introducci\xf3n al Derecho Administrativo",disabled:v,autoFocus:!0})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,h.jsx)("textarea",{id:"descripcion",value:f,onChange:function(e){return g(e.target.value)},rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"Describe brevemente el contenido del tema...",disabled:v})]}),(0,h.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"flex items-start",children:[(0,h.jsx)("div",{className:"flex-shrink-0",children:(0,h.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"})}),(0,h.jsx)("div",{className:"ml-3",children:(0,h.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,h.jsx)("strong",{children:"Nota:"})," El n\xfamero del tema y su estado de completado no se pueden modificar desde aqu\xed. Solo puedes editar el t\xedtulo y la descripci\xf3n."]})})]})})]}),(0,h.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,h.jsx)("button",{onClick:N,className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",disabled:v,children:"Cancelar"}),(0,h.jsx)("button",{onClick:y,disabled:v||!u.trim(),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:v?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.Bc_,{className:"w-4 h-4 mr-2"}),"Guardar cambios"]})})]}),(0,h.jsx)("div",{className:"px-6 pb-4",children:(0,h.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,h.jsx)("strong",{children:"Atajos:"})," Esc para cancelar • Ctrl+Enter para guardar"]})})]})}):null},ru=function(e){var r,t=e.tema,n=e.onEdit,a=e.onDelete,c=e.onToggleCompletado,d=e.isUpdating,u=(0,i.useState)(!1),m=u[0],x=u[1],f=(0,i.useState)(!1),g=f[0],b=f[1],v=(r=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return b(!0),e.prev=1,r=p.oR.loading("Eliminando tema..."),e.next=5,(0,eX.B$)(t.id);case 5:e.sent?(p.oR.success("Tema eliminado exitosamente",{id:r}),a(t.id)):p.oR.error("Error al eliminar el tema",{id:r}),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("Error al eliminar tema:",e.t0),p.oR.error("Error al eliminar el tema",{id:r});case 13:return e.prev=13,b(!1),x(!1),e.finish(13);case 17:case"end":return e.stop()}},e,null,[[1,9,13,17]])})),function(){return r.apply(this,arguments)});return m?(0,h.jsxs)("div",{className:"flex items-center space-x-2 bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,h.jsx)(l.eHT,{className:"w-4 h-4 text-red-600 flex-shrink-0"}),(0,h.jsx)("div",{className:"flex-1 min-w-0",children:(0,h.jsxs)("p",{className:"text-sm text-red-800",children:['\xbfEliminar tema "',t.titulo,'"?']})}),(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)("button",{onClick:function(){x(!1)},className:"px-2 py-1 text-xs text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors",disabled:g,children:"Cancelar"}),(0,h.jsx)("button",{onClick:v,disabled:g,className:"px-2 py-1 text-xs text-white bg-red-600 rounded hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center",children:g?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-white mr-1"}),"Eliminando..."]}):"Eliminar"})]})]}):(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)("button",{onClick:function(){n(t)},className:"p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors",title:"Editar tema",disabled:d,children:(0,h.jsx)(l.SG1,{className:"w-4 h-4"})}),(0,h.jsx)("button",{onClick:function(){x(!0)},className:"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors",title:"Eliminar tema",disabled:d,children:(0,h.jsx)(l.IXo,{className:"w-4 h-4"})}),(0,h.jsxs)("button",{onClick:function(){c(t.id,t.completado)},disabled:d,className:"flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ".concat(t.completado?"bg-gray-100 text-gray-700 hover:bg-gray-200":"bg-green-100 text-green-700 hover:bg-green-200"," disabled:opacity-50 disabled:cursor-not-allowed"),children:[d?(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1"}):t.completado?(0,h.jsx)(l.Ohp,{className:"w-4 h-4 mr-1"}):(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-1"}),t.completado?"Marcar pendiente":"Marcar completado"]})]})};function rm(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rx(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rm(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rm(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let rp=function(){var e,r,t,a,c=(0,i.useState)(null),d=c[0],u=c[1],m=(0,i.useState)([]),x=m[0],f=m[1],g=(0,i.useState)(null),b=g[0],v=g[1],j=(0,i.useState)(!0),y=j[0],N=j[1],w=(0,i.useState)(null),k=w[0],E=w[1],C=(0,i.useState)(!1),S=C[0],O=C[1],A=(0,i.useState)(!1),P=A[0],D=A[1],T=(0,i.useState)(!1),_=T[0],R=T[1],z=(0,i.useState)(!1),I=z[0],F=z[1],M=(0,i.useState)(null),L=M[0],G=M[1],q=(0,i.useState)(!1),H=q[0],U=q[1],V=(0,i.useState)(!1),B=V[0],$=V[1];(0,i.useEffect)(function(){W()},[]);var W=(e=(0,s.A)(o().mark(function e(){var r,t,s,a,i,c;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return N(!0),e.prev=1,e.next=4,(0,eX.jg)();case 4:if(!(r=e.sent)){e.next=17;break}return u(r),e.next=9,Promise.all([(0,eX.cm)(r.id),(0,eX.Il)(r.id),(0,re.vD)(r.id)]);case 9:t=e.sent,a=(s=(0,n.A)(t,3))[0],i=s[1],c=s[2],f(a),v(i),O(c);case 17:e.next=23;break;case 19:e.prev=19,e.t0=e.catch(1),console.error("Error al cargar datos del temario:",e.t0),p.oR.error("Error al cargar el temario");case 23:return e.prev=23,N(!1),e.finish(23);case 26:case"end":return e.stop()}},e,null,[[1,19,23,26]])})),function(){return e.apply(this,arguments)}),Y=(r=(0,s.A)(o().mark(function e(r,t){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return E(r),e.prev=1,e.next=4,(0,eX.cN)(r,!t);case 4:if(!e.sent){e.next=15;break}if(f(x.map(function(e){return e.id===r?rx(rx({},e),{},{completado:!t,fecha_completado:t?void 0:new Date().toISOString()}):e})),!d){e.next=12;break}return e.next=10,(0,eX.Il)(d.id);case 10:v(e.sent);case 12:p.oR.success(t?"Tema marcado como pendiente":"Tema marcado como completado"),e.next=16;break;case 15:p.oR.error("Error al actualizar el estado del tema");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(1),console.error("Error al actualizar tema:",e.t0),p.oR.error("Error al actualizar el tema");case 22:return e.prev=22,E(null),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[1,18,22,25]])})),function(e,t){return r.apply(this,arguments)}),K=function(e){return new Date(e).toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric"})},X=function(e){G(e),F(!0)},J=(t=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(f(x.filter(function(e){return e.id!==r})),!d){e.next=6;break}return e.next=4,(0,eX.Il)(d.id);case 4:v(e.sent);case 6:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)}),Z=(a=(0,s.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(d){e.next=2;break}return e.abrupt("return");case 2:return $(!0),e.prev=3,r=p.oR.loading("Eliminando temario y desactivando plan de estudios..."),e.next=7,(0,eR.iF)();case 7:if(!(t=e.sent.user)){e.next=12;break}return e.next=12,e_.N.from("planificacion_usuario").delete().eq("user_id",t.id).eq("temario_id",d.id);case 12:return e.next=14,e_.N.from("planes_estudios").update({activo:!1}).eq("temario_id",d.id);case 14:return e.next=16,(0,eX.xv)(d.id);case 16:e.sent?(p.oR.success("Temario eliminado y plan de estudios desactivado exitosamente",{id:r}),u(null),f([]),v(null),O(!1)):p.oR.error("Error al eliminar el temario",{id:r}),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(3),console.error("Error al eliminar temario:",e.t0),p.oR.error("Error al eliminar el temario",{id:r});case 24:return e.prev=24,$(!1),U(!1),e.finish(24);case 28:case"end":return e.stop()}},e,null,[[3,20,24,28]])})),function(){return a.apply(this,arguments)});return y?(0,h.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):d?P?(0,h.jsx)(rs,{temario:d,onComplete:function(){D(!1),O(!0),W()},onCancel:function(){D(!1)},isEditing:S}):(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,h.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:d.titulo}),d.descripcion&&(0,h.jsx)("p",{className:"text-gray-600",children:d.descripcion}),(0,h.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,h.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("completo"===d.tipo?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:"completo"===d.tipo?"Temario Completo":"Temas Sueltos"}),(0,h.jsxs)("span",{className:"text-sm text-gray-500",children:["Creado el ",K(d.creado_en)]})]})]}),(0,h.jsxs)("div",{className:"flex space-x-2",children:[(0,h.jsx)("button",{onClick:function(){R(!0)},className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors",title:"Editar temario",children:(0,h.jsx)(l.SG1,{className:"w-5 h-5"})}),(0,h.jsx)("button",{onClick:function(){U(!0)},className:"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors",title:"Eliminar temario",children:(0,h.jsx)(l.IXo,{className:"w-5 h-5"})})]})]}),b&&(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,h.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)(l.H9b,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Total Temas"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:b.totalTemas})]})]})}),(0,h.jsx)("div",{className:"bg-green-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)(l.YrT,{className:"w-5 h-5 text-green-600 mr-2"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Completados"}),(0,h.jsx)("p",{className:"text-2xl font-bold text-green-600",children:b.temasCompletados})]})]})}),(0,h.jsx)("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)(l.ARf,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Progreso"}),(0,h.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[b.porcentajeCompletado.toFixed(1),"%"]})]})]})})]}),b&&(0,h.jsxs)("div",{className:"mt-4",children:[(0,h.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[(0,h.jsx)("span",{children:"Progreso del temario"}),(0,h.jsxs)("span",{children:[b.porcentajeCompletado.toFixed(1),"%"]})]}),(0,h.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,h.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(b.porcentajeCompletado,"%")}})})]})]}),"completo"===d.tipo&&(0,h.jsx)("div",{className:"border rounded-xl p-6 ".concat(S?"bg-green-50 border-green-200":"bg-blue-50 border-blue-200"),children:(0,h.jsxs)("div",{className:"flex items-start justify-between",children:[(0,h.jsxs)("div",{className:"flex items-start",children:[S?(0,h.jsx)(l.YrT,{className:"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5"}):(0,h.jsx)(l.FrA,{className:"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-2 ".concat(S?"text-green-900":"text-blue-900"),children:S?"Planificaci\xf3n Configurada":"Planificaci\xf3n Inteligente con IA"}),(0,h.jsx)("p",{className:"text-sm mb-3 ".concat(S?"text-green-800":"text-blue-800"),children:S?"Ya tienes configurada tu planificaci\xf3n de estudio personalizada. Pronto podr\xe1s ver tu calendario y seguimiento.":"Configura tu planificaci\xf3n personalizada con nuestro asistente inteligente:"}),!S&&(0,h.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,h.jsx)("li",{children:"• Planificaci\xf3n autom\xe1tica de estudio con IA"}),(0,h.jsx)("li",{children:"• Seguimiento de progreso personalizado"}),(0,h.jsx)("li",{children:"• Recomendaciones de orden de estudio"}),(0,h.jsx)("li",{children:"• Estimaci\xf3n de tiempos por tema"})]})]})]}),(0,h.jsx)("div",{className:"flex gap-2",children:S?(0,h.jsxs)("button",{onClick:function(){D(!0)},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,h.jsx)(l.VSk,{className:"w-4 h-4 mr-2"}),"Modificar Planificaci\xf3n"]}):(0,h.jsxs)("button",{onClick:function(){D(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,h.jsx)(l.FrA,{className:"w-4 h-4 mr-2"}),"Configurar Planificaci\xf3n"]})})]})}),(0,h.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,h.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,h.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Temas del Temario"}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Marca los temas como completados seg\xfan vayas estudi\xe1ndolos"})]}),(0,h.jsx)("div",{className:"divide-y divide-gray-200",children:x.map(function(e){return(0,h.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,h.jsx)("div",{className:"flex-shrink-0",children:(0,h.jsx)("span",{className:"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium",children:e.numero})}),(0,h.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,h.jsx)("h3",{className:"text-lg font-medium ".concat(e.completado?"text-gray-500 line-through":"text-gray-900"),children:e.titulo}),e.descripcion&&(0,h.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.descripcion}),e.fecha_completado&&(0,h.jsxs)("div",{className:"flex items-center mt-2 text-sm text-green-600",children:[(0,h.jsx)(l.YrT,{className:"w-4 h-4 mr-1"}),"Completado el ",K(e.fecha_completado)]})]})]}),(0,h.jsx)("div",{className:"flex items-center space-x-3",children:(0,h.jsx)(ru,{tema:e,onEdit:X,onDelete:J,onToggleCompletado:Y,isUpdating:k===e.id})})]})},e.id)})})]}),d&&(0,h.jsx)(ri,{isOpen:_,onClose:function(){R(!1)},temario:d,onSave:function(e){u(e),R(!1)}}),L&&(0,h.jsx)(rd,{isOpen:I,onClose:function(){F(!1),G(null)},tema:L,onSave:function(e){f(x.map(function(r){return r.id===e.id?e:r})),F(!1),G(null)}}),H&&(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,h.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-4",children:[(0,h.jsx)("div",{className:"flex-shrink-0",children:(0,h.jsx)(l.IXo,{className:"w-6 h-6 text-red-600"})}),(0,h.jsx)("div",{className:"ml-3",children:(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Eliminar Temario"})})]}),(0,h.jsxs)("div",{className:"mb-6",children:[(0,h.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"\xbfEst\xe1s seguro de que quieres eliminar este temario? Esta acci\xf3n:"}),(0,h.jsxs)("ul",{className:"text-sm text-red-600 space-y-1 ml-4",children:[(0,h.jsxs)("li",{children:['• Eliminar\xe1 permanentemente el temario "',(0,h.jsx)("strong",{children:null==d?void 0:d.titulo}),'"']}),(0,h.jsx)("li",{children:"• Eliminar\xe1 todos los temas asociados"}),(0,h.jsx)("li",{children:"• Eliminar\xe1 la planificaci\xf3n de estudios configurada"}),(0,h.jsx)("li",{children:"• Desactivar\xe1 los planes de estudios generados (se conservan en el historial)"})]}),(0,h.jsx)("p",{className:"text-sm text-gray-600 mt-3 font-medium",children:"Esta acci\xf3n no se puede deshacer."})]}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,h.jsx)("button",{onClick:function(){U(!1)},disabled:B,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50",children:"Cancelar"}),(0,h.jsx)("button",{onClick:Z,disabled:B,className:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center",children:B?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.IXo,{className:"w-4 h-4 mr-2"}),"Eliminar Temario"]})})]})]})})]}):(0,h.jsxs)("div",{className:"text-center py-12",children:[(0,h.jsx)(l.H9b,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No hay temario configurado"}),(0,h.jsx)("p",{className:"text-gray-500",children:"Configura tu temario desde el dashboard para comenzar."})]})};function rh(){var e=(0,i.useState)(null),r=e[0],t=e[1],n=(0,i.useState)(!1),a=n[0],c=n[1];return((0,i.useEffect)(function(){var e,r=(e=(0,s.A)(o().mark(function e(){var r,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r={userAgent:navigator.userAgent,isMobile:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),hasLocalStorage:"undefined"!=typeof Storage,hasCookies:navigator.cookieEnabled,supabaseSession:!1,localStorageToken:!1,cookieToken:!1,timestamp:new Date().toISOString()},e.prev=1,e.next=4,X.N.auth.getSession();case 4:n=e.sent.data.session,r.supabaseSession=!!n,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),console.warn("Error al verificar sesi\xf3n de Supabase:",e.t0);case 12:try{s=localStorage.getItem("supabase.auth.token"),r.localStorageToken=!!s}catch(e){console.warn("Error al verificar localStorage:",e)}try{r.cookieToken=document.cookie.includes("supabase.auth.token")}catch(e){console.warn("Error al verificar cookies:",e)}t(r);case 15:case"end":return e.stop()}},e,null,[[1,9]])})),function(){return e.apply(this,arguments)});r();var n=setInterval(r,5e3);return function(){return clearInterval(n)}},[]),r)?(0,h.jsxs)("div",{className:"fixed bottom-4 right-4 z-50",children:[(0,h.jsx)("button",{onClick:function(){return c(!a)},className:"bg-blue-500 text-white px-3 py-2 rounded-full text-sm shadow-lg hover:bg-blue-600 transition-colors",children:"\uD83D\uDC1B Debug"}),a&&(0,h.jsxs)("div",{className:"absolute bottom-12 right-0 bg-black bg-opacity-90 text-white p-4 rounded-lg shadow-xl max-w-sm text-xs",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,h.jsx)("h3",{className:"font-bold text-sm",children:"Debug Info"}),(0,h.jsx)("button",{onClick:function(){return c(!1)},className:"text-gray-300 hover:text-white",children:"✕"})]}),(0,h.jsxs)("div",{className:"space-y-1",children:[(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("span",{children:"M\xf3vil:"}),(0,h.jsx)("span",{className:r.isMobile?"text-green-400":"text-red-400",children:r.isMobile?"S\xed":"No"})]}),(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("span",{children:"LocalStorage:"}),(0,h.jsx)("span",{className:r.hasLocalStorage?"text-green-400":"text-red-400",children:r.hasLocalStorage?"S\xed":"No"})]}),(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("span",{children:"Cookies:"}),(0,h.jsx)("span",{className:r.hasCookies?"text-green-400":"text-red-400",children:r.hasCookies?"S\xed":"No"})]}),(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("span",{children:"Sesi\xf3n Supabase:"}),(0,h.jsx)("span",{className:r.supabaseSession?"text-green-400":"text-red-400",children:r.supabaseSession?"S\xed":"No"})]}),(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("span",{children:"Token localStorage:"}),(0,h.jsx)("span",{className:r.localStorageToken?"text-green-400":"text-red-400",children:r.localStorageToken?"S\xed":"No"})]}),(0,h.jsxs)("div",{className:"flex justify-between",children:[(0,h.jsx)("span",{children:"Token Cookie:"}),(0,h.jsx)("span",{className:r.cookieToken?"text-green-400":"text-red-400",children:r.cookieToken?"S\xed":"No"})]}),(0,h.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-600",children:(0,h.jsxs)("div",{className:"text-gray-300 break-all",children:[(0,h.jsx)("strong",{children:"User Agent:"}),(0,h.jsx)("br",{}),r.userAgent.substring(0,100),"..."]})}),(0,h.jsxs)("div",{className:"text-gray-400 text-xs mt-1",children:["Actualizado: ",new Date(r.timestamp).toLocaleTimeString()]})]})]})]}):null}var rf=t(74100),rg=t(38310);function rb(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rv(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rb(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rb(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function rj(){var e,r,t=(0,i.useState)(!1),n=t[0],a=t[1],c=(0,i.useState)({supabaseConnection:!1,userAuthenticated:!1,geminiApiKey:!1,conversationsCount:0,documentsCount:0,lastError:null}),l=c[0],d=c[1],u=(0,i.useState)(!1),m=u[0],x=u[1],p=(0,C.A)(),f=p.user,g=p.session,b=(e=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,x(!0),e.next=4,(0,rf.Yp)("Conversaci\xf3n de prueba",!1);case 4:(r=e.sent)?(d(function(e){return rv(rv({},e),{},{lastError:"✅ Conversaci\xf3n de prueba creada exitosamente: ".concat(r)})}),v()):d(function(e){return rv(rv({},e),{},{lastError:"❌ No se pudo crear la conversaci\xf3n de prueba"})}),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),d(function(r){return rv(rv({},r),{},{lastError:"❌ Error al crear conversaci\xf3n de prueba: ".concat(e.t0 instanceof Error?e.t0.message:"Unknown error")})});case 11:return e.prev=11,x(!1),e.finish(11);case 14:case"end":return e.stop()}},e,null,[[0,8,11,14]])})),function(){return e.apply(this,arguments)}),v=(r=(0,s.A)(o().mark(function e(){var r,t,n,s,a,i,c,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return x(!0),r={supabaseConnection:!1,userAuthenticated:!1,geminiApiKey:!1,conversationsCount:0,documentsCount:0,lastError:null},e.prev=2,r.userAuthenticated=!!f&&!!g,e.prev=4,e.next=7,X.N.from("conversaciones").select("id").limit(1);case 7:if((t=e.sent).data,n=t.error){e.next=20;break}return r.supabaseConnection=!0,e.next=14,X.N.from("conversaciones").select("*",{count:"exact",head:!0}).eq("user_id",f&&f.id?f.id:"");case 14:a=(s=e.sent).count,(i=s.error)?(r.conversationsCount=0,"406"===i.code?r.lastError="Error 406 al contar conversaciones - esto es normal para usuarios nuevos":r.lastError="Error al contar conversaciones: ".concat(i.message)):r.conversationsCount=a||0,e.next=21;break;case 20:r.lastError="Supabase error: ".concat(n.message);case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(4),r.lastError="Supabase connection error: ".concat(e.t0 instanceof Error?e.t0.message:"Unknown error");case 26:return e.prev=26,e.next=29,X.N.from("documentos").select("count",{count:"exact",head:!0});case 29:l=(c=e.sent).data,c.error||(r.documentsCount=(null==l?void 0:l.length)||0),e.next=38;break;case 35:e.prev=35,e.t1=e.catch(26),console.error("Error checking documents:",e.t1);case 38:r.geminiApiKey=!!rg.env.OPENAI_API_KEY,e.next=44;break;case 41:e.prev=41,e.t2=e.catch(2),r.lastError="General error: ".concat(e.t2 instanceof Error?e.t2.message:"Unknown error");case 44:d(r),x(!1);case 46:case"end":return e.stop()}},e,null,[[2,41],[4,23],[26,35]])})),function(){return r.apply(this,arguments)});return((0,i.useEffect)(function(){n&&v()},[n]),n)?(0,h.jsxs)("div",{className:"fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-80 z-50",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold",children:"Diagn\xf3stico del Sistema"}),(0,h.jsx)("button",{onClick:function(){return a(!1)},className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),m?(0,h.jsxs)("div",{className:"text-center py-4",children:[(0,h.jsx)("div",{className:"animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto"}),(0,h.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Ejecutando diagn\xf3sticos..."})]}):(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("span",{className:"text-sm",children:"Usuario autenticado:"}),(0,h.jsx)("span",{className:"text-sm font-semibold ".concat(l.userAuthenticated?"text-green-600":"text-red-600"),children:l.userAuthenticated?"✓":"✗"})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("span",{className:"text-sm",children:"Conexi\xf3n Supabase:"}),(0,h.jsx)("span",{className:"text-sm font-semibold ".concat(l.supabaseConnection?"text-green-600":"text-red-600"),children:l.supabaseConnection?"✓":"✗"})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("span",{className:"text-sm",children:"API Key Gemini:"}),(0,h.jsx)("span",{className:"text-sm font-semibold ".concat(l.geminiApiKey?"text-green-600":"text-red-600"),children:l.geminiApiKey?"✓":"✗"})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("span",{className:"text-sm",children:"Conversaciones:"}),(0,h.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:l.conversationsCount})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("span",{className:"text-sm",children:"Documentos:"}),(0,h.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:l.documentsCount})]}),l.lastError&&(0,h.jsxs)("div",{className:"mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,h.jsx)("p",{className:"text-xs text-red-700 font-semibold",children:"\xdaltimo error:"}),(0,h.jsx)("p",{className:"text-xs text-red-600 mt-1",children:l.lastError})]}),(0,h.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,h.jsxs)("div",{className:"flex space-x-2",children:[(0,h.jsx)("button",{onClick:v,className:"flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-3 rounded",disabled:m,children:"Actualizar"}),(0,h.jsx)("button",{onClick:function(){console.log("Diagn\xf3sticos completos:",l),console.log("Usuario:",f),console.log("Sesi\xf3n:",g)},className:"flex-1 bg-gray-500 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded",children:"Log Info"})]}),(0,h.jsx)("button",{onClick:b,className:"w-full bg-green-500 hover:bg-green-600 text-white text-xs py-2 px-3 rounded",disabled:m||!l.userAuthenticated,children:"Probar Crear Conversaci\xf3n"})]})]})]}):(0,h.jsx)("button",{onClick:function(){return a(!0)},className:"fixed bottom-4 right-4 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full shadow-lg z-50",title:"Abrir panel de diagn\xf3stico",children:"\uD83D\uDD27"})}let ry=function(e){var r=e.activeTab,t=e.onTabChange,n=e.children,s=(0,i.useState)([]),a=s[0],o=s[1],c=(0,i.useState)(!1),d=c[0],m=c[1];(0,i.useEffect)(function(){var e=localStorage.getItem("sidebarCollapsed");e&&m(JSON.parse(e))},[]),(0,i.useEffect)(function(){localStorage.setItem("sidebarCollapsed",JSON.stringify(d))},[d]);var x=[{id:"dashboard",label:"Principal",icon:(0,h.jsx)(l.jTZ,{}),color:"bg-gradient-to-r from-blue-600 to-purple-600"},{id:"mi-temario-group",label:"Mi Temario",icon:(0,h.jsx)(l.H9b,{}),color:"bg-green-600",isGroup:!0,children:[{id:"temario",label:"Mi Temario",icon:(0,h.jsx)(l.H9b,{}),color:"bg-green-600"},{id:"gestionar",label:"Gestionar Documentos",icon:(0,h.jsx)(l.VSk,{}),color:"bg-gray-600"}]},{id:"planEstudios",label:"Mi Plan de Estudios",icon:(0,h.jsx)(l.wIk,{}),color:"bg-teal-600"},{id:"preguntas",label:"Habla con tu preparador",icon:(0,h.jsx)(l.mEP,{}),color:"bg-blue-600"},{id:"herramientas-group",label:"Herramientas de estudio",icon:(0,h.jsx)(l.x_j,{}),color:"bg-purple-600",isGroup:!0,children:[{id:"flashcards-group",label:"Flashcards",icon:(0,h.jsx)(l.lZI,{}),color:"bg-orange-500",isGroup:!0,children:[{id:"flashcards",label:"Generador de Flashcards",icon:(0,h.jsx)(l.GGD,{}),color:"bg-orange-500"},{id:"misFlashcards",label:"Mis Flashcards",icon:(0,h.jsx)(l.lZI,{}),color:"bg-emerald-600"}]},{id:"tests-group",label:"Tests",icon:(0,h.jsx)(l.NLe,{}),color:"bg-indigo-600",isGroup:!0,children:[{id:"tests",label:"Generador de Tests",icon:(0,h.jsx)(l.GGD,{}),color:"bg-indigo-600"},{id:"misTests",label:"Mis Tests",icon:(0,h.jsx)(l.NLe,{}),color:"bg-pink-600"}]}]},{id:"resumenes",label:"Res\xfamenes",icon:(0,h.jsx)(l.D1A,{}),color:"bg-green-600"},{id:"mapas",label:"Mapas Mentales",icon:(0,h.jsx)(l.s_k,{}),color:"bg-purple-600"}],p=function(e){o(function(r){return r.includes(e)?r.filter(function(r){return r!==e}):[].concat((0,u.A)(r),[e])})},f=function(){o([])},g=function e(n){var s,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=n.children&&n.children.length>0,c=r===n.id&&!n.isGroup,u=(s=n.id,a.includes(s));return d&&o>0?null:(0,h.jsxs)("div",{children:[(0,h.jsxs)("div",{className:"flex items-center ".concat(d?"justify-center tooltip-hover":"justify-between"," px-").concat(d?"2":2+2*o," py-2 rounded-lg transition-all duration-300 cursor-pointer ").concat(c&&!i?"text-white ".concat(n.color," shadow-md"):"text-gray-600 hover:bg-gray-100 hover:text-gray-800"),onClick:function(){d&&i?(m(!1),p(n.id)):i||n.isGroup?p(n.id):(f(),t(n.id))},title:d?n.label:void 0,"data-tooltip":d?n.label:void 0,"aria-label":n.label,children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"".concat(d?"":"mr-2"," text-sm"),children:n.icon}),!d&&(0,h.jsx)("span",{className:"text-sm font-medium",children:n.label})]}),i&&!d&&(0,h.jsx)("span",{className:"text-xs",children:u?(0,h.jsx)(l.fK4,{}):(0,h.jsx)(l.fOo,{})})]}),i&&u&&!d&&(0,h.jsx)("div",{className:"ml-2 mt-1 space-y-1",children:n.children.map(function(r){return e(r,o+1)})})]},"".concat(n.id,"-").concat(o))};return(0,h.jsxs)("div",{className:"".concat(d?"w-16":"w-80"," flex-shrink-0 space-y-4 sidebar-transition"),children:[(0,h.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-4 sticky top-6",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[!d&&(0,h.jsx)("h2",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider px-2",children:"Men\xfa de Estudio"}),(0,h.jsx)("button",{onClick:function(){m(function(e){var r=!e;return r&&o([]),r})},className:"p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",title:d?"Expandir men\xfa":"Colapsar men\xfa","aria-label":d?"Expandir men\xfa de navegaci\xf3n":"Colapsar men\xfa de navegaci\xf3n","aria-expanded":!d,children:d?(0,h.jsx)(l.ND1,{className:"w-4 h-4 text-gray-600"}):(0,h.jsx)(l.pM3,{className:"w-4 h-4 text-gray-600"})})]}),(0,h.jsx)("nav",{className:"space-y-1",children:x.map(function(e){return g(e)})})]}),!d&&n]})};var rN=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=(0,I.M)().tasks,t=(0,i.useState)(null),n=t[0],s=t[1],a=(0,i.useState)(!1),o=a[0],c=a[1];return(0,i.useEffect)(function(){var t=r.filter(function(e){return"plan-estudios"===e.type});c(t.some(function(e){return"pending"===e.status||"processing"===e.status}));var a=t.filter(function(e){return"completed"===e.status&&e.result}).sort(function(e,r){return new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime()});if(a.length>0){var o=a[0];s(o.result),e.onResult&&o.result!==n&&e.onResult(o.result)}var i=t.filter(function(e){return"error"===e.status}).sort(function(e,r){return new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime()});if(i.length>0&&e.onError){var l=i[0];e.onError(l.error||"Error desconocido")}},[r,e,n]),{latestResult:n,isLoading:o,hasResults:!!n}},rw=t(17863),rk=t(74318),rE=t(11157),rC=t(59539),rS=t(38310);function rO(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rA(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rO(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rO(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var rP=function(){var e,r,t,n,a,i,c;function l(){(0,rE.A)(this,l)}return(0,rC.A)(l,null,[{key:"logWebhookEvent",value:(e=(0,s.A)(o().mark(function e(r){var t,n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=r.success?"✅":"❌",n=new Date().toISOString(),console.log("".concat(t," [WEBHOOK] ").concat(n),rA(rA({eventType:r.eventType,eventId:r.eventId,success:r.success,processingTime:"".concat(r.processingTime,"ms"),message:r.message},r.error&&{error:r.error}),r.data&&{data:r.data})),e.next=7,this.logToExternalService(r);case 7:e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Error logging webhook event:",e.t0);case 12:case"end":return e.stop()}},e,this,[[0,9]])})),function(r){return e.apply(this,arguments)})},{key:"logFeatureAccess",value:(r=(0,s.A)(o().mark(function e(r,t,n,s){var a,i,c,l,d=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:a=d.length>4&&void 0!==d[4]?d[4]:0,i=d.length>5?d[5]:void 0,e.prev=2,e.next=10;break;case 6:return c=e.sent.SupabaseAdminService,e.next=10,c.logFeatureAccess({user_id:r,feature_name:t,access_granted:n,plan_at_time:s,tokens_used:a,denial_reason:i});case 10:l=n?"✅":"❌",console.log("".concat(l," [FEATURE_ACCESS]"),rA({userId:r,feature:t,granted:n,plan:s,tokens:a},i&&{reason:i})),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(2),console.error("Error logging feature access:",e.t0);case 17:case"end":return e.stop()}},e,null,[[2,14]])})),function(e,t,n,s){return r.apply(this,arguments)})},{key:"logPlanChange",value:(t=(0,s.A)(o().mark(function e(r,t,n,s,a,i){var c;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,e.next=8;break;case 4:return c=e.sent.SupabaseAdminService,e.next=8,c.logPlanChange({user_id:r,old_plan:t||void 0,new_plan:n,changed_by:s,reason:a,transaction_id:i});case 8:console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:r,oldPlan:t,newPlan:n,changedBy:s,reason:a,transactionId:i}),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error logging plan change:",e.t0);case 14:case"end":return e.stop()}},e,null,[[0,11]])})),function(e,r,n,s,a,o){return t.apply(this,arguments)})},{key:"logCriticalError",value:(n=(0,s.A)(o().mark(function e(r,t,n){var s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",s={context:r,message:t.message,stack:t.stack,timestamp:new Date().toISOString(),additionalData:n}),e.next=6,this.sendCriticalAlert(s);case 6:e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error logging critical error:",e.t0);case 11:case"end":return e.stop()}},e,this,[[0,8]])})),function(e,r,t){return n.apply(this,arguments)})},{key:"logPerformanceMetrics",value:function(e,r,t,n){var s=rA({operation:e,duration:"".concat(r,"ms"),success:t,timestamp:new Date().toISOString()},n);console.log("\uD83D\uDCCA [PERFORMANCE]",s),this.sendMetrics(s)}},{key:"logToExternalService",value:(a=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}},e)})),function(e){return a.apply(this,arguments)})},{key:"sendCriticalAlert",value:(i=(0,s.A)(o().mark(function e(r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}},e)})),function(e){return i.apply(this,arguments)})},{key:"sendMetrics",value:function(e){"true"===rS.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}},{key:"getWebhookStats",value:(c=(0,s.A)(o().mark(function e(){var r=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r.length>0&&void 0!==r[0]&&r[0],e.abrupt("return",{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}});case 2:case"end":return e.stop()}},e)})),function(){return c.apply(this,arguments)})}]),l}();function rD(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rT(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rD(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rD(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var r_=function(){var e,r,n,a,i,c,l,d,u;function m(){(0,rE.A)(this,m)}return(0,rC.A)(m,null,[{key:"checkUserLimits",value:(e=(0,s.A)(o().mark(function e(r){var n,s,a,i,c;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.e(2024).then(t.bind(t,72024));case 3:return n=e.sent.SupabaseAdminService,e.next=7,n.getUserProfile(r);case 7:if(s=e.sent){e.next=10;break}return e.abrupt("return",[]);case 10:return a=[],e.next=13,this.checkTokenLimits(s);case 13:return(i=e.sent)&&a.push(i),e.next=17,this.checkPlanLimits(s);case 17:return(c=e.sent)&&a.push(c),e.abrupt("return",a);case 22:return e.prev=22,e.t0=e.catch(0),console.error("Error checking user limits:",e.t0),e.abrupt("return",[]);case 26:case"end":return e.stop()}},e,this,[[0,22]])})),function(r){return e.apply(this,arguments)})},{key:"checkClientUserLimits",value:(r=(0,s.A)(o().mark(function e(){var r,t,n,s,a,i,c,l,d;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,r=(0,X.U)(),e.next=4,r.auth.getUser();case 4:if(n=(t=e.sent).data.user,!(t.error||!n)){e.next=9;break}return e.abrupt("return",[]);case 9:return e.next=11,r.from("user_profiles").select("subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month, plan_expires_at").eq("user_id",n.id).single();case 11:if(a=(s=e.sent).data,!((i=s.error)&&"PGRST116"!==i.code)){e.next=17;break}return console.error("Error fetching profile for limits check:",i),e.abrupt("return",[]);case 17:if(a){e.next=19;break}return e.abrupt("return",[]);case 19:return c=[],e.next=22,this.checkTokenLimits(a);case 22:return(l=e.sent)&&c.push(l),e.next=26,this.checkPlanLimits(a);case 26:return(d=e.sent)&&c.push(d),e.abrupt("return",c);case 31:return e.prev=31,e.t0=e.catch(0),console.error("Error checking client user limits:",e.t0),e.abrupt("return",[]);case 35:case"end":return e.stop()}},e,this,[[0,31]])})),function(){return r.apply(this,arguments)})},{key:"checkTokenLimits",value:(n=(0,s.A)(o().mark(function e(r){var t,n,s,a,i,c,l,d;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=new Date().toISOString().slice(0,7)+"-01",s=(n=r.current_month===t?r.current_month_tokens:0)/r.monthly_token_limit*100,a="warning",i="",c=!1,l="",!(s>=100)){e.next=14;break}a="exceeded",i="Has excedido tu l\xedmite mensual de tokens (".concat(n.toLocaleString(),"/").concat(r.monthly_token_limit.toLocaleString(),")"),c=!0,l="Actualiza tu plan para obtener m\xe1s tokens",e.next=29;break;case 14:if(!(s>=90)){e.next=21;break}a="limit_reached",i="Est\xe1s cerca de tu l\xedmite mensual de tokens (".concat(Math.round(s),"% usado)"),c=!0,l="Considera actualizar tu plan antes de alcanzar el l\xedmite",e.next=29;break;case 21:if(!(s>=75)){e.next=28;break}a="warning",i="Has usado ".concat(Math.round(s),"% de tus tokens mensuales"),c=!1,l="Monitorea tu uso para evitar alcanzar el l\xedmite",e.next=29;break;case 28:return e.abrupt("return",null);case 29:return d=this.getUpgradeOptions(r.subscription_plan),e.abrupt("return",{type:"tokens",severity:a,current:n,limit:r.monthly_token_limit,percentage:Math.round(s),message:i,actionRequired:c,suggestedAction:l,upgradeOptions:d});case 31:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)})},{key:"checkPlanLimits",value:(a=(0,s.A)(o().mark(function e(r){var t,n,s;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!("free"!==r.subscription_plan&&!r.payment_verified)){e.next=2;break}return e.abrupt("return",{type:"plan",severity:"exceeded",current:0,limit:1,percentage:0,message:"Tu pago est\xe1 pendiente de verificaci\xf3n",actionRequired:!0,suggestedAction:"Completa el proceso de pago para activar tu plan",upgradeOptions:[]});case 2:if(!r.plan_expires_at){e.next=12;break}if(t=new Date(r.plan_expires_at),n=new Date,!((s=Math.ceil((t.getTime()-n.getTime())/864e5))<=0)){e.next=10;break}return e.abrupt("return",{type:"plan",severity:"exceeded",current:0,limit:1,percentage:0,message:"Tu plan ha expirado",actionRequired:!0,suggestedAction:"Renueva tu suscripci\xf3n para continuar usando las funciones premium",upgradeOptions:this.getUpgradeOptions(r.subscription_plan)});case 10:if(!(s<=7)){e.next=12;break}return e.abrupt("return",{type:"plan",severity:"warning",current:s,limit:30,percentage:Math.round((30-s)/30*100),message:"Tu plan expira en ".concat(s," d\xeda").concat(1!==s?"s":""),actionRequired:!1,suggestedAction:"Renueva tu suscripci\xf3n para evitar la interrupci\xf3n del servicio",upgradeOptions:this.getUpgradeOptions(r.subscription_plan)});case 12:return e.abrupt("return",null);case 13:case"end":return e.stop()}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"getUpgradeOptions",value:function(e){var r=[];if("free"===e){var t=(0,S.IE)("usuario"),n=(0,S.IE)("pro");if(t){var s=t.limits.monthlyTokens||1e6;r.push({plan:"usuario",benefits:["Chat con preparador IA","".concat(s.toLocaleString()," tokens mensuales"),"Tests y flashcards ilimitados"],newLimit:s})}if(n){var a=n.limits.monthlyTokens||1e6;r.push({plan:"pro",benefits:["Todas las funciones del plan Usuario","Planificaci\xf3n de estudios con IA","Res\xfamenes A1 y A2","".concat(a.toLocaleString()," tokens mensuales")],newLimit:a})}}else if("usuario"===e){var o=(0,S.IE)("pro");if(o){var i=o.limits.monthlyTokens||1e6;r.push({plan:"pro",benefits:["Planificaci\xf3n de estudios con IA","Res\xfamenes A1 y A2","Funciones avanzadas","".concat(i.toLocaleString()," tokens mensuales")],newLimit:i})}}return r}},{key:"createLimitNotification",value:(i=(0,s.A)(o().mark(function e(r,t){var n;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={userId:r,type:"limit_".concat(t.type),severity:"exceeded"===t.severity?"error":"limit_reached"===t.severity?"warning":"info",title:this.getNotificationTitle(t),message:t.message,metadata:{limitType:t.type,current:t.current,limit:t.limit,percentage:t.percentage}},t.actionRequired&&t.upgradeOptions&&t.upgradeOptions.length>0&&(n.actionUrl="/payment",n.actionText="Actualizar Plan"),e.next=4,rP.logFeatureAccess(r,"limit_notification_".concat(t.type),!1,"system",0,"Limit notification: ".concat(t.severity));case 4:return e.abrupt("return",n);case 5:case"end":return e.stop()}},e,this)})),function(e,r){return i.apply(this,arguments)})},{key:"getNotificationTitle",value:function(e){switch(e.type){case"tokens":if("exceeded"===e.severity)return"L\xedmite de tokens excedido";if("limit_reached"===e.severity)return"L\xedmite de tokens casi alcanzado";return"Uso elevado de tokens";case"plan":if("exceeded"===e.severity)return"Plan expirado o pago pendiente";return"Plan pr\xf3ximo a expirar";default:return"L\xedmite alcanzado"}}},{key:"isActionBlocked",value:(c=(0,s.A)(o().mark(function e(r,t){var n,s,a,i,c=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=c.length>2&&void 0!==c[2]?c[2]:0,e.prev=1,e.next=4,this.checkUserLimits(r);case 4:if(s=e.sent,!(n>0)){e.next=11;break}if(!((a=s.find(function(e){return"tokens"===e.type}))&&"exceeded"===a.severity)){e.next=9;break}return e.abrupt("return",{blocked:!0,reason:"L\xedmite mensual de tokens excedido",limitStatus:a});case 9:if(!(a&&a.current+n>a.limit)){e.next=11;break}return e.abrupt("return",{blocked:!0,reason:"Esta acci\xf3n requiere ".concat(n," tokens pero solo tienes ").concat(a.limit-a.current," disponibles"),limitStatus:a});case 11:if(!(i=s.find(function(e){return"plan"===e.type&&"exceeded"===e.severity}))){e.next=14;break}return e.abrupt("return",{blocked:!0,reason:i.message,limitStatus:i});case 14:return e.abrupt("return",{blocked:!1});case 17:return e.prev=17,e.t0=e.catch(1),console.error("Error checking if action is blocked:",e.t0),e.abrupt("return",{blocked:!0,reason:"Error verificando l\xedmites"});case 21:case"end":return e.stop()}},e,this,[[1,17]])})),function(e,r){return c.apply(this,arguments)})},{key:"isClientActionBlocked",value:(l=(0,s.A)(o().mark(function e(r){var t,n,s,a,i=arguments;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=i.length>1&&void 0!==i[1]?i[1]:0,e.prev=1,e.next=4,this.checkClientUserLimits();case 4:if(n=e.sent,!(t>0)){e.next=11;break}if(!((s=n.find(function(e){return"tokens"===e.type}))&&"exceeded"===s.severity)){e.next=9;break}return e.abrupt("return",{blocked:!0,reason:"L\xedmite mensual de tokens excedido",limitStatus:s});case 9:if(!(s&&s.current+t>s.limit)){e.next=11;break}return e.abrupt("return",{blocked:!0,reason:"Esta acci\xf3n requiere ".concat(t," tokens pero solo tienes ").concat(s.limit-s.current," disponibles"),limitStatus:s});case 11:if(!(a=n.find(function(e){return"plan"===e.type&&"exceeded"===e.severity}))){e.next=14;break}return e.abrupt("return",{blocked:!0,reason:a.message,limitStatus:a});case 14:return e.abrupt("return",{blocked:!1});case 17:return e.prev=17,e.t0=e.catch(1),console.error("Error checking if client action is blocked:",e.t0),e.abrupt("return",{blocked:!0,reason:"Error verificando l\xedmites"});case 21:case"end":return e.stop()}},e,this,[[1,17]])})),function(e){return l.apply(this,arguments)})},{key:"recordUsage",value:(d=(0,s.A)(o().mark(function e(r,n,s){var a,i,c,l;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!(s>0)){e.next=15;break}return e.next=4,t.e(2024).then(t.bind(t,72024));case 4:return a=e.sent.SupabaseAdminService,e.next=8,a.getUserProfile(r);case 8:if(!(i=e.sent)){e.next=15;break}return c=new Date().toISOString().slice(0,7)+"-01",l=i.current_month===c?i.current_month_tokens:0,e.next=14,a.upsertUserProfile(rT(rT({},i),{},{current_month_tokens:l+s,current_month:c,updated_at:new Date().toISOString()}));case 14:console.log("✅ Tokens actualizados: +".concat(s," para usuario ").concat(r));case 15:return e.next=17,rP.logFeatureAccess(r,n,!0,"system",s,"Action completed successfully");case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(0),console.error("Error recording usage:",e.t0);case 22:case"end":return e.stop()}},e,null,[[0,19]])})),function(e,r,t){return d.apply(this,arguments)})},{key:"recordClientUsage",value:(u=(0,s.A)(o().mark(function e(r,t){var n,s,a,i,c,l,d,u,m;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n=(0,X.U)(),e.next=4,n.auth.getUser();case 4:if(a=(s=e.sent).data.user,!(s.error||!a)){e.next=10;break}return console.warn("Cannot record usage: user not authenticated"),e.abrupt("return");case 10:if(!(t>0)){e.next=27;break}return e.next=13,n.from("user_profiles").select("subscription_plan, monthly_token_limit, current_month_tokens, current_month").eq("user_id",a.id).single();case 13:if(c=(i=e.sent).data,!(l=i.error)){e.next=19;break}return console.error("Error fetching profile for usage recording:",l),e.abrupt("return");case 19:if(!c){e.next=27;break}return d=new Date().toISOString().slice(0,7)+"-01",u=c.current_month===d?c.current_month_tokens:0,e.next=24,n.from("user_profiles").update({current_month_tokens:u+t,current_month:d,updated_at:new Date().toISOString()}).eq("user_id",a.id);case 24:(m=e.sent.error)?console.error("Error updating token usage:",m):console.log("✅ Tokens actualizados: +".concat(t," para usuario ").concat(a.id));case 27:return e.next=29,rP.logFeatureAccess(a.id,r,!0,"system",t,"Action completed successfully");case 29:e.next=34;break;case 31:e.prev=31,e.t0=e.catch(0),console.error("Error recording client usage:",e.t0);case 34:case"end":return e.stop()}},e,null,[[0,31]])})),function(e,r){return u.apply(this,arguments)})}]),m}();function rR(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function rz(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?rR(Object(t),!0).forEach(function(r){(0,m.A)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rR(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function rI(e){var r=e.used,t=e.limit,n=e.percentage,s=e.remaining,a=n||0,o=function(e){var r=e||0;return r>=1e6?"".concat((r/1e6).toFixed(1),"M"):r>=1e3?"".concat((r/1e3).toFixed(1),"K"):r.toLocaleString()};return(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Uso de Tokens"}),(0,h.jsxs)("span",{className:"text-sm font-semibold ".concat(a<50?"text-green-600":a<80?"text-yellow-600":"text-red-600"),children:[a,"%"]})]}),(0,h.jsx)("div",{className:"w-full ".concat(a<50?"bg-green-100":a<80?"bg-yellow-100":"bg-red-100"," rounded-full h-3"),children:(0,h.jsx)("div",{className:"h-3 rounded-full transition-all duration-300 ".concat(a<50?"bg-green-500":a<80?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(Math.min(a,100),"%")}})}),(0,h.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,h.jsxs)("span",{children:[(0,h.jsx)("strong",{children:o(r||0)})," usados"]}),(0,h.jsxs)("span",{children:[(0,h.jsx)("strong",{children:o(s||0)})," restantes"]})]}),(0,h.jsx)("div",{className:"text-center",children:(0,h.jsxs)("span",{className:"text-xs text-gray-500",children:["L\xedmite mensual: ",(0,h.jsx)("strong",{children:o(t||0)})," tokens"]})}),a>=80&&(0,h.jsx)("div",{className:"p-2 rounded-lg text-xs ".concat(a>=95?"bg-red-50 text-red-700 border border-red-200":"bg-yellow-50 text-yellow-700 border border-yellow-200"),children:a>=95?(0,h.jsx)("span",{children:"⚠️ L\xedmite casi alcanzado. Considera comprar m\xe1s tokens."}):(0,h.jsx)("span",{children:"⚠️ Te est\xe1s acercando al l\xedmite mensual de tokens."})})]})}function rF(e){var r,t,n,a,c,d=e.isOpen,u=e.onClose,m=(e.shouldRefreshOnOpen,t=(r=(0,i.useState)({loading:!0,userPlan:null,tokenUsage:null,limits:[],paymentVerified:!1,error:null}))[0],n=r[1],a=(0,i.useCallback)((0,s.A)(o().mark(function e(){var r,t,s,a,i,c,l,d,u,m;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n(function(e){return rz(rz({},e),{},{loading:!0,error:null})}),r=(0,X.U)(),e.next=5,r.auth.getUser();case 5:if(s=(t=e.sent).data.user,!(t.error||!s)){e.next=11;break}return n(function(e){return rz(rz({},e),{},{loading:!1,error:"Usuario no autenticado"})}),e.abrupt("return");case 11:return e.next=13,r.from("user_profiles").select("*").eq("user_id",s.id).single();case 13:if(i=(a=e.sent).data,!(a.error||!i)){e.next=19;break}return n(function(e){return rz(rz({},e),{},{loading:!1,error:"Perfil no encontrado"})}),e.abrupt("return");case 19:return c=new Date().toISOString().slice(0,7)+"-01",u={current:l=i.current_month===c&&i.current_month_tokens||0,limit:d=i.monthly_token_limit||0,percentage:d>0?Math.round(l/d*100):0,remaining:Math.max(0,d-l)},e.next=25,r_.checkClientUserLimits();case 25:m=e.sent,n({loading:!1,userPlan:i.subscription_plan,tokenUsage:u,limits:m,paymentVerified:i.payment_verified||"free"===i.subscription_plan,error:null}),e.next=33;break;case 29:e.prev=29,e.t0=e.catch(0),console.error("Error loading plan limits:",e.t0),n(function(e){return rz(rz({},e),{},{loading:!1,error:"Error cargando l\xedmites"})});case 33:case"end":return e.stop()}},e,null,[[0,29]])})),[]),(0,i.useEffect)(function(){a()},[a]),c=(0,i.useCallback)(function(){a()},[a]),rz(rz({},t),{},{refresh:c}));return d?(0,h.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,h.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,h.jsx)(l.hht,{className:"w-6 h-6 text-blue-600"}),(0,h.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Estad\xedsticas de Uso de IA"})]}),(0,h.jsx)("button",{onClick:u,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,h.jsx)(l.yGN,{className:"w-5 h-5 text-gray-500"})})]}),(0,h.jsx)("div",{className:"p-6 space-y-6",children:m.loading?(0,h.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,h.jsx)("span",{className:"ml-2 text-gray-600",children:"Verificando plan..."})]}):"free"===m.userPlan?(0,h.jsxs)("div",{className:"text-center py-12",children:[(0,h.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Estad\xedsticas de Tokens no disponibles"}),(0,h.jsx)("p",{className:"text-gray-600 mb-6",children:"Las estad\xedsticas detalladas est\xe1n disponibles para planes de pago."}),(0,h.jsx)("div",{children:(0,h.jsx)(_(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"Ver Planes Disponibles"})})]}):m.tokenUsage&&m.tokenUsage.limit>0?(0,h.jsx)("div",{className:"mb-6",children:(0,h.jsx)(rI,{used:m.tokenUsage.current||0,limit:m.tokenUsage.limit||0,percentage:m.tokenUsage.percentage||0,remaining:m.tokenUsage.remaining||0})}):(0,h.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,h.jsx)("p",{className:"text-gray-600",children:"No hay datos de uso disponibles."})})}),(0,h.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,h.jsx)("button",{onClick:u,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cerrar"})})]})}):null}function rM(){var e,r,t,a,u,m=(0,i.useState)([]),x=m[0],f=m[1],g=(0,i.useState)(!1),b=g[0],v=g[1],j=(0,i.useState)("dashboard"),y=j[0],N=j[1],w=(0,i.useState)(!1),k=w[0],E=w[1],A=(0,i.useState)(!1),P=A[0],T=A[1],_=(0,i.useState)(null),R=_[0],I=_[1],M=(0,i.useState)(null),L=M[0],G=M[1],q=(0,i.useState)(!1),H=q[0],U=q[1],V=(0,i.useState)(!1),B=V[0],W=V[1],Y=(0,i.useState)(0),X=Y[0],J=Y[1],Z=(0,i.useState)(!1),Q=Z[0],ee=Z[1],er=(0,i.useState)(!1),et=er[0],en=er[1],es=(0,i.useState)(!1),ea=es[0],eo=es[1],ei=(0,i.useState)(!1),ec=ei[0],ed=ei[1],ex=(0,C.A)(),ep=ex.cerrarSesion,eh=ex.user,ef=ex.isLoading,eg=(0,c.useRouter)(),eb=(0,i.useRef)(null),ev=F(),ej=ev.generatePlanEstudios,ey=ev.isGenerating,eN=rN({onResult:function(e){I(e),p.oR.success("\xa1Plan de estudios generado exitosamente!")},onError:function(e){p.oR.error("Error al generar plan: ".concat(e))}}),ew=eN.latestResult,ek=eN.isLoading;if((0,i.useEffect)(function(){ef||eh||eg.push("/login")},[eh,ef,eg]),(0,i.useEffect)(function(){var e;(e=(0,s.A)(o().mark(function e(){var r,t;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(eh){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,(0,eX.jg)();case 5:if(!(r=e.sent)){e.next=18;break}return G(r.id),e.next=10,(0,re.vD)(r.id);case 10:return U(e.sent),e.next=14,(0,rw.fF)(r.id);case 14:(t=e.sent)&&t.plan_data?I(t.plan_data):I(null),e.next=21;break;case 18:G(null),U(!1),I(null);case 21:e.next=29;break;case 23:e.prev=23,e.t0=e.catch(2),console.error("Error al cargar datos del temario:",e.t0),G(null),U(!1),I(null);case 29:case"end":return e.stop()}},e,null,[[2,23]])})),function(){return e.apply(this,arguments)})()},[eh]),(0,i.useEffect)(function(){ew&&I(ew)},[ew]),(0,i.useEffect)(function(){var e;(e=(0,s.A)(o().mark(function e(){var r,t,s,a,i;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!eh){e.next=11;break}return e.next=3,Promise.all([(0,S.qk)("study_planning"),(0,S.qk)("ai_tutor_chat"),(0,S.qk)("summary_a1_a2")]);case 3:r=e.sent,s=(t=(0,n.A)(r,3))[0],a=t[1],i=t[2],ee(s),en(a),eo(i);case 11:case"end":return e.stop()}},e)})),function(){return e.apply(this,arguments)})()},[eh]),ef||!eh)return(0,h.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,h.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})});var eE=(e=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return E(!0),T(!0),e.prev=2,e.next=5,null==(r=eb.current)?void 0:r.recargarDocumentos();case 5:e.next=10;break;case 7:e.prev=7,e.t0=e.catch(2),console.error("Error al recargar documentos:",e.t0);case 10:return e.prev=10,T(!1),e.finish(10);case 13:setTimeout(function(){return E(!1)},5e3);case 14:case"end":return e.stop()}},e,null,[[2,7,10,13]])})),function(){return e.apply(this,arguments)}),eS=(r=(0,s.A)(o().mark(function e(){var r;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,null==(r=eb.current)?void 0:r.recargarDocumentos();case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),console.error("Error al recargar documentos despu\xe9s de eliminar:",e.t0);case 8:case"end":return e.stop()}},e,null,[[0,5]])})),function(){return r.apply(this,arguments)}),eO=(t=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,ep();case 2:case"end":return e.stop()}},e)})),function(){return t.apply(this,arguments)}),eA=(a=(0,s.A)(o().mark(function e(){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(L){e.next=3;break}return p.oR.error("No se encontr\xf3 un temario configurado"),e.abrupt("return");case 3:if(H){e.next=6;break}return p.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'),e.abrupt("return");case 6:return e.prev=6,e.next=9,ej({temarioId:L,onComplete:function(e){I(e)},onError:function(e){e.includes("planificaci\xf3n configurada")?p.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'):p.oR.error("Error al generar el plan de estudios. Int\xe9ntalo de nuevo.")}});case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(6),console.error("Error al iniciar generaci\xf3n del plan:",e.t0);case 14:case"end":return e.stop()}},e,null,[[6,11]])})),function(){return a.apply(this,arguments)}),eD=function(e){var r="# Plan de Estudios Personalizado\n\n";return r+="".concat(e.introduccion,"\n\n"),r+="## Resumen del Plan\n\n",r+="- **Tiempo total de estudio:** ".concat(e.resumen.tiempoTotalEstudio,"\n"),r+="- **N\xfamero de temas:** ".concat(e.resumen.numeroTemas,"\n"),r+="- **Duraci\xf3n estudio nuevo:** ".concat(e.resumen.duracionEstudioNuevo,"\n"),r+="- **Duraci\xf3n repaso final:** ".concat(e.resumen.duracionRepasoFinal,"\n\n"),r+="## Cronograma Semanal\n\n",e.semanas.forEach(function(e){r+="### Semana ".concat(e.numero," (").concat(e.fechaInicio," - ").concat(e.fechaFin,")\n\n"),r+="**Objetivo:** ".concat(e.objetivoPrincipal,"\n\n"),e.dias.forEach(function(e){r+="**".concat(e.dia," (").concat(e.horas,"h):**\n"),e.tareas.forEach(function(e){r+="- ".concat(e.titulo," (").concat(e.duracionEstimada,")\n"),e.descripcion&&(r+="  ".concat(e.descripcion,"\n"))}),r+="\n"})}),r+="## Estrategia de Repasos\n\n".concat(e.estrategiaRepasos,"\n\n"),r+="## Pr\xf3ximos Pasos\n\n".concat(e.proximosPasos,"\n")};return(0,h.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,h.jsx)("header",{className:"bg-white shadow-sm",children:(0,h.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2",children:(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsx)("div",{children:(0,h.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"OposiAI"})}),(0,h.jsxs)("div",{className:"flex items-center space-x-4",children:[eh&&(0,h.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,h.jsxs)("div",{className:"text-sm text-gray-600",children:["Hola, ",null==(u=eh.email)?void 0:u.split("@")[0]]}),(0,h.jsx)("button",{onClick:function(){return eg.push("/profile")},className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",title:"Ver perfil",children:(0,h.jsx)(l.JXP,{className:"w-4 h-4"})})]}),(0,h.jsx)("button",{onClick:function(){return W(!0)},className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",title:"Ver estad\xedsticas de uso de IA",children:(0,h.jsx)(l.hht,{className:"w-4 h-4"})}),(0,h.jsxs)("button",{onClick:function(){return v(!b)},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",children:[(0,h.jsx)(l.B88,{className:"mr-2"}),b?"Ocultar formulario":"Nuevo documento"]}),(0,h.jsxs)("button",{onClick:eO,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,h.jsx)(l.QeK,{className:"mr-2"}),"Cerrar sesi\xf3n"]})]})]})})}),(0,h.jsxs)("main",{className:"px-4 sm:px-6 lg:px-8 py-8",children:[b&&(0,h.jsx)("div",{className:"mb-8 transition-all duration-300 ease-in-out",children:(0,h.jsx)(z,{onSuccess:eE})}),k&&(0,h.jsx)("div",{className:"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200",children:(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)(l.YrT,{className:"text-green-500 mr-2 flex-shrink-0"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"font-medium",children:"\xa1Documento subido exitosamente!"}),(0,h.jsx)("p",{className:"text-sm text-green-700 mt-1",children:P?(0,h.jsxs)("span",{className:"flex items-center",children:[(0,h.jsx)(l.jTZ,{className:"animate-spin mr-1"}),"Actualizando lista de documentos..."]}):"El documento ya est\xe1 disponible en los desplegables de selecci\xf3n."})]})]})}),(0,h.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4",children:[(0,h.jsxs)("div",{className:"flex items-center mb-2",children:[(0,h.jsx)(l.jH2,{className:"w-4 h-4 text-blue-600 mr-2"}),(0,h.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Documentos Seleccionados"})]}),(0,h.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:"Selecciona los documentos que quieres usar para generar contenido con IA."}),(0,h.jsx)(d.A,{ref:eb,onSelectionChange:f}),x.length>0&&(0,h.jsxs)("div",{className:"mt-2 p-2 bg-blue-50 rounded-lg",children:[(0,h.jsxs)("p",{className:"text-xs text-blue-800 font-medium",children:[(0,h.jsx)("strong",{children:x.length})," documento",1!==x.length?"s":""," seleccionado",1!==x.length?"s":"","."]}),(0,h.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:x.map(function(e){return(0,h.jsxs)("span",{className:"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:[e.numero_tema&&"Tema ".concat(e.numero_tema,": "),e.titulo]},e.id)})})]})]}),(0,h.jsxs)("div",{className:"flex gap-6 mb-8",children:[(0,h.jsx)(ry,{activeTab:y,onTabChange:N}),(0,h.jsx)("div",{className:"flex-1",children:"dashboard"===y?(0,h.jsx)(e9,{onNavigateToTab:function(e){N(e)}}):(0,h.jsx)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:(0,h.jsxs)("div",{className:"p-6",children:["temario"===y&&(0,h.jsx)(rp,{}),"planEstudios"===y&&(0,h.jsx)("div",{children:Q?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Mi Plan de Estudios"}),(0,h.jsx)("div",{className:"flex gap-2",children:R&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("button",{onClick:eA,disabled:ek||ey("plan-estudios"),className:"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,h.jsx)(l.jTZ,{className:"w-4 h-4 ".concat(ek||ey("plan-estudios")?"animate-spin":"")}),"Regenerar Plan"]}),(0,h.jsxs)("button",{onClick:function(){if(R){var e=new Blob([eD(R)],{type:"text/markdown"}),r=URL.createObjectURL(e),t=document.createElement("a");t.href=r,t.download="plan-estudios-".concat(new Date().toISOString().split("T")[0],".md"),document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(r),p.oR.success("Plan descargado exitosamente")}},className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,h.jsx)(l.a4x,{className:"w-4 h-4"}),"Descargar"]}),(0,h.jsxs)("button",{onClick:function(){if(R){var e=eD(R),r=window.open("","_blank");r&&(r.document.write('\n        <html>\n          <head>\n            <title>Plan de Estudios Personalizado</title>\n            <style>\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n              h1, h2, h3 { color: #333; }\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\n              ul, ol { margin-left: 20px; }\n              strong { color: #2563eb; }\n              @media print { body { margin: 0; } }\n            </style>\n          </head>\n          <body>\n            <div id="content"></div>\n            <script>\n              // Convertir markdown a HTML b\xe1sico para impresi\xf3n\n              const markdown = '.concat(JSON.stringify(e),";\n              const content = markdown\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\n                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n                .replace(/\\n/g, '<br>');\n              document.getElementById('content').innerHTML = content;\n              window.print();\n            <\/script>\n          </body>\n        </html>\n      ")),r.document.close())}},className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,h.jsx)(l.Mvz,{className:"w-4 h-4"}),"Imprimir"]})]})})]}),ek||ey("plan-estudios")?(0,h.jsxs)("div",{className:"text-center py-12",children:[(0,h.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Generando tu plan personalizado"}),(0,h.jsx)("p",{className:"text-gray-600",children:"La IA est\xe1 analizando tu temario y configuraci\xf3n..."})]}):R&&L?(0,h.jsx)(rk.A,{plan:R,temarioId:L}):(0,h.jsxs)("div",{className:"text-center py-12",children:[(0,h.jsx)("div",{className:"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,h.jsx)(l.wIk,{className:"w-10 h-10 text-teal-600"})}),(0,h.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Genera tu Plan de Estudios Personalizado"}),(0,h.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Crea un plan de estudios personalizado basado en tu temario y configuraci\xf3n de planificaci\xf3n"}),(0,h.jsxs)("button",{onClick:eA,disabled:!H,className:"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,h.jsx)(l.wIk,{className:"w-5 h-5 mr-3"}),"Generar Plan de Estudios"]}),!H&&(0,h.jsx)("p",{className:"text-sm text-red-600 mt-4",children:'Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'})]})]}):(0,h.jsx)(O.A,{feature:"study_planning",benefits:["Planes de estudio personalizados con IA","Cronogramas adaptativos a tu ritmo","Seguimiento autom\xe1tico de progreso","Recomendaciones inteligentes de repaso"],className:"min-h-[600px]"})}),"preguntas"===y&&(et?(0,h.jsx)(D,{documentosSeleccionados:x}):(0,h.jsx)(O.A,{feature:"ai_tutor_chat",benefits:["Chat ilimitado con IA especializada","Respuestas personalizadas a tus documentos","Historial completo de conversaciones","Explicaciones detalladas y ejemplos"],className:"min-h-[600px]"})),"mapas"===y&&(0,h.jsx)($,{documentosSeleccionados:x}),"flashcards"===y&&(0,h.jsx)(K,{documentosSeleccionados:x}),"tests"===y&&(0,h.jsx)(eP,{documentosSeleccionados:x}),"misTests"===y&&(0,h.jsx)(eY,{}),"misFlashcards"===y&&(0,h.jsx)(eC,{}),"resumenes"===y&&(ea?(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)(el,{documentosSeleccionados:x,onSummaryGenerated:function(e){J(function(e){return e+1}),p.oR.success("Resumen generado exitosamente")}}),(0,h.jsx)("hr",{className:"border-gray-200"}),(0,h.jsx)(eu,{refreshTrigger:X})]}):(0,h.jsx)(O.A,{feature:"summary_a1_a2",benefits:["Res\xfamenes inteligentes con IA","Formato A1 y A2 optimizado","Edici\xf3n autom\xe1tica de contenido","Exportaci\xf3n a PDF de alta calidad"],className:"min-h-[600px]"})),"gestionar"===y&&(0,h.jsx)(em,{onDocumentDeleted:eS})]})})})]})]}),(0,h.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-12",children:(0,h.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,h.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,h.jsx)("div",{className:"flex items-center",children:(0,h.jsxs)("span",{className:"text-gray-500 text-sm",children:["\xa9 ",new Date().getFullYear()," OposiAI - Asistente para Oposiciones"]})}),(0,h.jsx)("div",{className:"mt-4 md:mt-0",children:(0,h.jsxs)("nav",{className:"flex space-x-6",children:[(0,h.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"T\xe9rminos"}),(0,h.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"Privacidad"}),(0,h.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"Contacto"})]})})]})})}),(0,h.jsx)(rh,{}),(0,h.jsx)(rj,{}),(0,h.jsx)(rF,{isOpen:B,onClose:function(){W(!1),ed(!1)},shouldRefreshOnOpen:ec})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[5730,844,2331,512,2390,1448,3329,104,4659,4001,2490,8441,6891,7358],()=>r(25410)),_N_E=e.O()}]);
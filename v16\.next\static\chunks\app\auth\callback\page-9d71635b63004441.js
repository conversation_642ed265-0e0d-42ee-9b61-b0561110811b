(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6769],{55564:(e,r,a)=>{"use strict";a.d(r,{N:()=>t.N,U:()=>t.U});var t=a(66618)},58430:(e,r,a)=>{Promise.resolve().then(a.bind(a,99390))},66618:(e,r,a)=>{"use strict";a.d(r,{N:()=>n,U:()=>s});var t=a(73728);function s(){return(0,t.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}var n=s()},99390:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>p});var t=a(33311),s=a(10631),n=a(28295),i=a.n(n),o=a(12115),c=a(6066),u=a(55564),d=a(95155);function l(){var e=(0,c.useRouter)(),r=(0,c.useSearchParams)(),a=(0,o.useState)("loading"),n=a[0],l=a[1],p=(0,o.useState)("Procesando autenticaci\xf3n, por favor espera..."),m=p[0],h=p[1],x=(0,o.useState)([]),g=x[0],v=x[1],f=(0,o.useRef)(null),b=function(e,r){var a=new Date().toISOString(),t="[".concat(a,"] ").concat(e);console.log(t,r||""),v(function(e){return[].concat((0,s.A)(e),[t])})};return(0,o.useEffect)(function(){b("--- AuthCallbackContent useEffect INICIADO ---"),b("URL Completa en el cliente:",window.location.href),b("Query Params (searchParams):",Object.fromEntries(r.entries()));var a,s=r.get("error_description"),o=r.get("error_code"),c=r.get("error");if(s||o||c){var d=decodeURIComponent(s||c||"Error desconocido");"user_already_invited"===o||s&&s.includes("User already invited")?d="Ya se ha enviado una invitaci\xf3n a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contrase\xf1a desde la p\xe1gina de login.":("token_expired_or_invalid"===o||s&&(s.includes("invalid token")||s.includes("expired")))&&(d="El enlace de autenticaci\xf3n es inv\xe1lido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesi\xf3n de nuevo."),b("❌ Error expl\xedcito detectado en la URL.",{error:d,code:o,error_param:c}),l("error"),h(d),f.current&&clearTimeout(f.current);return}var p=(0,u.U)();b("Cliente Supabase del navegador inicializado.");var m=p.auth.onAuthStateChange((a=(0,t.A)(i().mark(function r(a,t){var s,n,o,c,u;return i().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:b("EVENTO onAuthStateChange RECIBIDO: ".concat(a),{hasSession:!!t,userId:null==t||null==(s=t.user)?void 0:s.id,userMetadata:null==t||null==(n=t.user)?void 0:n.user_metadata}),f.current&&(clearTimeout(f.current),f.current=null),"PASSWORD_RECOVERY"===a?(b("✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password..."),l("success"),h("Sesi\xf3n de recuperaci\xf3n lista. Redirigiendo para establecer contrase\xf1a..."),e.push("/auth/reset-password")):"SIGNED_IN"===a?(b("✅ Evento SIGNED_IN detectado."),null!=t&&t.user?(null==(o=t.user.user_metadata)?void 0:o.requires_password_setup)===!0?(b("\uD83D\uDC64 Usuario necesita establecer contrase\xf1a inicial. Redirigiendo a /auth/reset-password..."),l("success"),h("\xa1Cuenta activada! Ahora, crea tu contrase\xf1a..."),e.push("/auth/reset-password")):(b("\uD83D\uDE80 Usuario ya tiene contrase\xf1a o no requiere setup. Redirigiendo a /app..."),l("success"),h("\xa1Autenticaci\xf3n exitosa! Redirigiendo..."),e.push("/app")):(b("⚠️ Evento SIGNED_IN sin sesi\xf3n completa. Comportamiento inesperado."),l("error"),h("Error inesperado durante el inicio de sesi\xf3n. Por favor, int\xe9ntalo de nuevo."))):"USER_UPDATED"===a&&null!=t&&t.user?(b("✅ Evento USER_UPDATED con sesi\xf3n."),(null==(c=t.user.user_metadata)?void 0:c.requires_password_setup)===!0?(b("\uD83D\uDC64 Usuario actualizado necesita establecer contrase\xf1a. Redirigiendo a /auth/reset-password..."),l("success"),h("Actualizaci\xf3n de cuenta completada. Crea tu contrase\xf1a..."),e.push("/auth/reset-password")):(b("\uD83D\uDE80 Cuenta actualizada. Redirigiendo a /app..."),l("success"),h("Cuenta actualizada. Redirigiendo..."),e.push("/app"))):"INITIAL_SESSION"!==a||t?"INITIAL_SESSION"===a&&null!=t&&t.user&&(b("✅ Evento INITIAL_SESSION con sesi\xf3n. Procediendo como SIGNED_IN."),(null==(u=t.user.user_metadata)?void 0:u.requires_password_setup)===!0?e.push("/auth/reset-password"):e.push("/app")):b("\uD83D\uDD39 Evento INITIAL_SESSION sin sesi\xf3n. Esperando m\xe1s eventos o timeout.");case 3:case"end":return r.stop()}},r)})),function(e,r){return a.apply(this,arguments)})).data;return b("Listener onAuthStateChange configurado."),f.current=setTimeout((0,t.A)(i().mark(function r(){var a,t,s,o;return i().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if("loading"!==n){r.next=7;break}return r.next=3,p.auth.getSession();case 3:t=(a=r.sent).data.session,s=a.error,null!=t&&t.user?(b("✅ Sesi\xf3n encontrada en la verificaci\xf3n final del timeout."),(null==(o=t.user.user_metadata)?void 0:o.requires_password_setup)===!0?(b("\uD83D\uDC64 Redirigiendo a /auth/reset-password desde timeout."),e.push("/auth/reset-password")):(b("\uD83D\uDE80 Redirigiendo a /app desde timeout."),e.push("/app")),l("success"),h("\xa1Autenticaci\xf3n completada! Redirigiendo...")):(b("❌ TIMEOUT (10s): No se recibi\xf3 un evento de autenticaci\xf3n v\xe1lido y no hay sesi\xf3n activa."),b("Detalles del error en la verificaci\xf3n final del timeout:",null==s?void 0:s.message),l("error"),h("El enlace de autenticaci\xf3n es inv\xe1lido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte."));case 7:case"end":return r.stop()}},r)})),1e4),function(){b("--- AuthCallbackContent useEffect LIMPIEZA ---"),null==m||m.subscription.unsubscribe(),b("Suscripci\xf3n a onAuthStateChange eliminada."),f.current&&(clearTimeout(f.current),b("Temporizador de timeout limpiado."))}},[e,r]),(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center",children:["loading"===n&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Procesando..."}),(0,d.jsx)("p",{className:"text-gray-600",children:m})]}),"success"===n&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"\xa1\xc9xito!"}),(0,d.jsx)("p",{className:"text-gray-600",children:m})]}),"error"===n&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Error de Autenticaci\xf3n"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:m}),(0,d.jsx)("div",{className:"space-y-2",children:(0,d.jsx)("button",{onClick:function(){return e.push("/login")},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Login"})})]}),(0,d.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200 text-left bg-gray-50 p-3 rounded max-h-48 overflow-y-auto",children:[(0,d.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Registro de Eventos:"}),(0,d.jsx)("div",{className:"text-xs text-gray-500 space-y-1",children:g.map(function(e,r){return(0,d.jsx)("div",{children:e},r)})})]})]})})}function p(){return(0,d.jsx)(o.Suspense,{fallback:(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Cargando..."}),(0,d.jsx)("p",{className:"text-gray-600",children:"Procesando autenticaci\xf3n..."})]})}),children:(0,d.jsx)(l,{})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[5730,2390,8441,6891,7358],()=>r(58430)),_N_E=e.O()}]);
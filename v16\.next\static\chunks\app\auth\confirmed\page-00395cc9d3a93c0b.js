(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5223],{40380:(e,r,t)=>{Promise.resolve().then(t.bind(t,65142))},65142:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var n=t(33311),o=t(28295),a=t.n(o),i=t(12115),c=t(6066),s=t(66618),u=t(75780),l=t(95155);function f(){var e=(0,c.useRouter)(),r=(0,s.U)(),t=(0,i.useState)("loading"),o=t[0],f=t[1],p=(0,i.useState)("Verificando tu confirmaci\xf3n de email..."),m=p[0],d=p[1];return(0,i.useEffect)(function(){var t;(t=(0,n.A)(a().mark(function t(){var o;return a().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,o=r.auth.onAuthStateChange(function(){var t=(0,n.A)(a().mark(function t(n,o){var i,c,s;return a().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(console.log("Auth event:",n,"Session:",o),!("SIGNED_IN"===n&&o)){t.next=28;break}return d("Email confirmado exitosamente. Verificando tu perfil..."),t.prev=3,t.next=6,r.from("user_profiles").select("*").eq("user_id",o.user.id).single();case 6:if(c=(i=t.sent).data,!(s=i.error)){t.next=14;break}return console.error("Error obteniendo perfil:",s),f("error"),d("Error al verificar tu perfil. Por favor, contacta con soporte."),t.abrupt("return");case 14:if(c){t.next=18;break}return f("error"),d("No se encontr\xf3 tu perfil. Por favor, contacta con soporte."),t.abrupt("return");case 18:!1===c.payment_verified&&"free"!==c.subscription_plan?(d("Redirigiendo a completar tu pago..."),f("redirecting"),setTimeout(function(){e.push("/payment?plan="+c.subscription_plan)},2e3)):!0===c.payment_verified||"free"===c.subscription_plan?(d("\xa1Cuenta activada! Redirigiendo a tu dashboard..."),f("success"),setTimeout(function(){e.push("/app")},2e3)):(f("error"),d("Estado de cuenta inesperado. Por favor, contacta con soporte.")),t.next=26;break;case 21:t.prev=21,t.t0=t.catch(3),console.error("Error procesando confirmaci\xf3n:",t.t0),f("error"),d("Error al procesar la confirmaci\xf3n. Por favor, intenta de nuevo.");case 26:t.next=29;break;case 28:"SIGNED_OUT"===n&&(f("error"),d("Error en la confirmaci\xf3n. Por favor, intenta de nuevo."));case 29:case"end":return t.stop()}},t,null,[[3,21]])}));return function(e,r){return t.apply(this,arguments)}}()).data.subscription,t.abrupt("return",function(){o.unsubscribe()});case 5:t.prev=5,t.t0=t.catch(0),console.error("Error en handleEmailConfirmation:",t.t0),f("error"),d("Error al procesar la confirmaci\xf3n. Por favor, intenta de nuevo.");case 10:case"end":return t.stop()}},t,null,[[0,5]])})),function(){return t.apply(this,arguments)})()},[r,e]),(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:(0,l.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8 max-w-md w-full text-center",children:[function(){switch(o){case"loading":default:return(0,l.jsx)(u.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"});case"success":case"redirecting":return(0,l.jsx)(u.A3x,{className:"w-12 h-12 text-green-600 mx-auto mb-4"});case"error":return(0,l.jsx)(u.eHT,{className:"w-12 h-12 text-red-600 mx-auto mb-4"})}}(),(0,l.jsx)("h1",{className:"text-2xl font-semibold text-gray-800 mb-4",children:function(){switch(o){case"loading":return"Confirmando tu email...";case"success":return"\xa1Email confirmado!";case"redirecting":return"\xa1Confirmaci\xf3n exitosa!";case"error":return"Error en la confirmaci\xf3n";default:return"Procesando..."}}()}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:m}),"error"===o&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("button",{onClick:function(){return e.push("/auth/login")},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"}),(0,l.jsx)("button",{onClick:function(){return e.push("/")},className:"w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors",children:"Volver al Inicio"})]})]})})}},66618:(e,r,t)=>{"use strict";t.d(r,{N:()=>a,U:()=>o});var n=t(73728);function o(){return(0,n.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}var a=o()},77906:(e,r,t)=>{"use strict";t.d(r,{k5:()=>l});var n=t(12115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(o),i=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function s(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function u(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?s(Object(t),!0).forEach(function(r){var n,o,a;n=e,o=r,a=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function l(e){return function(r){return n.createElement(f,c({attr:u({},e.attr)},r),function e(r){return r&&r.map(function(r,t){return n.createElement(r.tag,u({key:t},r.attr),e(r.child))})}(e.child))}}function f(e){var r=function(r){var t,o=e.attr,a=e.size,s=e.title,l=function(e,r){if(null==e)return{};var t,n,o=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(o[t]=e[t])}return o}(e,i),f=a||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,o,l,{className:t,style:u(u({color:e.color||r.color},r.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),s&&n.createElement("title",null,s),e.children)};return void 0!==a?n.createElement(a.Consumer,null,function(e){return r(e)}):r(o)}}},e=>{var r=r=>e(e.s=r);e.O(0,[5730,844,2390,8441,6891,7358],()=>r(40380)),_N_E=e.O()}]);
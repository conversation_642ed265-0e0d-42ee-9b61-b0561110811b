(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{66618:(e,r,t)=>{"use strict";t.d(r,{N:()=>n,U:()=>a});var s=t(73728);function a(){return(0,s.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}var n=a()},77906:(e,r,t)=>{"use strict";t.d(r,{k5:()=>u});var s=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(a),o=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var s,a,n;s=e,a=r,n=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in s?Object.defineProperty(s,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return function(r){return s.createElement(d,i({attr:c({},e.attr)},r),function e(r){return r&&r.map(function(r,t){return s.createElement(r.tag,c({key:t},r.attr),e(r.child))})}(e.child))}}function d(e){var r=function(r){var t,a=e.attr,n=e.size,l=e.title,u=function(e,r){if(null==e)return{};var t,s,a=function(e,r){if(null==e)return{};var t={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(r.indexOf(s)>=0)continue;t[s]=e[s]}return t}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)t=n[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,o),d=n||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),s.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,u,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&s.createElement("title",null,l),e.children)};return void 0!==n?s.createElement(n.Consumer,null,function(e){return r(e)}):r(a)}},86310:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(33311),a=t(28295),n=t.n(a),o=t(12115),i=t(6066),l=t(66618),c=t(1448),u=t(75780),d=t(95155);function m(){var e,r=(0,i.useRouter)(),t=(0,i.useSearchParams)(),a=(0,o.useState)(""),m=a[0],p=a[1],x=(0,o.useState)(""),f=x[0],b=x[1],h=(0,o.useState)(!1),g=h[0],v=h[1],w=(0,o.useState)(""),y=w[0],j=w[1],N=(0,o.useState)(!0),E=N[0],O=N[1],S=(0,o.useState)(!1),_=S[0],I=S[1],P=(0,o.useState)(null),C=P[0],R=P[1];(0,o.useEffect)(function(){console.log("ResetPasswordForm: useEffect iniciado. URL:",window.location.href),O(!0);var e=t.get("error"),r=t.get("error_code"),s=t.get("error_description");if(e||r||s){var a=decodeURIComponent(s||e||"Error desconocido en el enlace.");(a.toLowerCase().includes("link is invalid or has expired")||a.toLowerCase().includes("token has expired")||"token_expired_or_invalid"===r)&&(a="El enlace de recuperaci\xf3n ha expirado o ya fue utilizado. Por favor, solicita un nuevo enlace."),console.error("ResetPasswordForm: Error expl\xedcito en URL:",{urlErrorParam:e,errorCodeParam:r,errorDescriptionParam:s,userFriendlyMessage:a}),R(a),I(!1),O(!1);return}var n=(0,l.U)().auth.onAuthStateChange(function(e,r){var t;console.log("ResetPasswordForm: Evento onAuthStateChange:",e,"Session:",!!r,"User:",null==r||null==(t=r.user)?void 0:t.id),"PASSWORD_RECOVERY"===e?r?(console.log("ResetPasswordForm: Sesi\xf3n establecida por evento PASSWORD_RECOVERY."),I(!0),R(null)):(console.warn("ResetPasswordForm: Evento PASSWORD_RECOVERY recibido pero sin sesi\xf3n. El token puede ser inv\xe1lido o expirado."),C||R("El enlace de recuperaci\xf3n parece ser inv\xe1lido o ha expirado.")):"INITIAL_SESSION"===e&&r&&(console.log("ResetPasswordForm: Evento INITIAL_SESSION con sesi\xf3n. Verificando si es de recuperaci\xf3n..."),window.location.hash.includes("type=recovery")&&I(!0)),(_||"INITIAL_SESSION"===e||"SIGNED_IN"===e)&&O(!1)}).data,o=setTimeout(function(){E&&!_&&(console.warn("ResetPasswordForm: Timeout (5s) esperando evento de sesi\xf3n. isSessionReady:",_),O(!1),C||_||R("No se pudo establecer una sesi\xf3n para cambiar la contrase\xf1a. El enlace podr\xeda ser inv\xe1lido o haber expirado."))},5e3);return function(){null==n||n.subscription.unsubscribe(),clearTimeout(o),console.log("ResetPasswordForm: Listener de autenticaci\xf3n y timeout limpiados.")}},[t]);var k=(e=(0,s.A)(n().mark(function e(t){var s,a,o,i,u,d,p,x;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),j(""),_){e.next=6;break}return j("No se ha establecido una sesi\xf3n v\xe1lida para cambiar la contrase\xf1a. Por favor, aseg\xfarate de usar el enlace de tu email."),c.oR.error("Error de sesi\xf3n. Intenta usar el enlace de tu email de nuevo."),e.abrupt("return");case 6:if(!(m.length<6)){e.next=9;break}return j("La nueva contrase\xf1a debe tener al menos 6 caracteres."),e.abrupt("return");case 9:if(m===f){e.next=12;break}return j("Las contrase\xf1as no coinciden."),e.abrupt("return");case 12:return v(!0),o=(0,l.U)(),e.next=16,o.auth.getUser();case 16:if(u=(i=e.sent).data,!(i.error||!u.user)){e.next=23;break}return j("No se pudo verificar la sesi\xf3n actual antes de actualizar. Intenta de nuevo."),v(!1),e.abrupt("return");case 23:return d=(null==(s=u.user.user_metadata)?void 0:s.requires_terms_acceptance_and_final_password_setup)===!0||(null==(a=u.user.user_metadata)?void 0:a.requires_initial_password_change)===!0,p={},d&&(p.requires_terms_acceptance_and_final_password_setup=!1,p.temporary_password_set=!1,p.requires_initial_password_change=!1),e.next=28,o.auth.updateUser({password:m,data:p});case 28:x=e.sent.error,v(!1),x?(console.error("ResetPasswordForm: Error al actualizar contrase\xf1a:",x),j("Auth session missing!"===x.message?"Error de sesi\xf3n: Tu sesi\xf3n ha expirado o es inv\xe1lida. Por favor, usa el enlace de tu email de nuevo.":x.message),c.oR.error("Auth session missing!"===x.message?"Error de sesi\xf3n. Usa el enlace de tu email.":"Error al actualizar la contrase\xf1a.")):(c.oR.success("\xa1Contrase\xf1a establecida/actualizada exitosamente!"),console.log("ResetPasswordForm: Contrase\xf1a actualizada. Redirigiendo a /app"),r.push("/app"));case 32:case"end":return e.stop()}},e)})),function(r){return e.apply(this,arguments)});return E?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,d.jsx)(u.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Verificando enlace..."}),(0,d.jsx)("p",{className:"text-gray-600 mt-2",children:"Esto puede tardar unos segundos."})]}):C?(0,d.jsx)("div",{className:"min-h-screen bg-red-50 flex flex-col justify-center items-center p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,d.jsx)(u.Ohp,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Enlace Inv\xe1lido o Expirado"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:C}),(0,d.jsx)("button",{onClick:function(){return r.push("/login")},className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Volver a Inicio de Sesi\xf3n"})]})}):_||E?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)(u.F5$,{className:"w-12 h-12 text-blue-600"})}),(0,d.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Crea tu Nueva Contrase\xf1a"})]}),(0,d.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,d.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,d.jsxs)("form",{className:"space-y-6",onSubmit:k,children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Nueva Contrase\xf1a"}),(0,d.jsx)("div",{className:"mt-1",children:(0,d.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:m,onChange:function(e){return p(e.target.value)},className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"M\xednimo 6 caracteres"})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar Nueva Contrase\xf1a"}),(0,d.jsx)("div",{className:"mt-1",children:(0,d.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:f,onChange:function(e){return b(e.target.value)},className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Repite la contrase\xf1a"})})]}),y&&(0,d.jsx)("div",{className:"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200",children:y}),(0,d.jsx)("div",{children:(0,d.jsx)("button",{type:"submit",disabled:g||!_,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(u.TwU,{className:"animate-spin h-4 w-4 mr-2"})," Actualizando..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(u.YrT,{className:"h-4 w-4 mr-2"})," Establecer Contrase\xf1a"]})})})]})})})]}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,d.jsx)(u.F5$,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-700",children:"Acceso no Autorizado"}),(0,d.jsx)("p",{className:"text-gray-500 mt-2 mb-6 max-w-md text-center",children:"Esta p\xe1gina es para establecer o restablecer tu contrase\xf1a usando un enlace seguro enviado a tu email. Si necesitas restablecer tu contrase\xf1a, solic\xedtalo desde la p\xe1gina de inicio de sesi\xf3n."}),(0,d.jsx)("button",{onClick:function(){return r.push("/login")},className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Inicio de Sesi\xf3n"})]})}function p(){return(0,d.jsx)(o.Suspense,{fallback:(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:[(0,d.jsx)(u.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Cargando..."})]}),children:(0,d.jsx)(m,{})})}},88870:(e,r,t)=>{Promise.resolve().then(t.bind(t,86310))}},e=>{var r=r=>e(e.s=r);e.O(0,[5730,844,2390,1448,8441,6891,7358],()=>r(88870)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7954],{4247:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>d});var t=a(33311),n=a(28295),r=a.n(n),i=a(12115),l=a(4001),c=a(95155);function d(){var s=(0,l.A)().user,e=(0,i.useState)(null),a=e[0],n=e[1],d=(0,i.useState)(null),o=d[0],x=d[1],u=(0,i.useState)(!0),m=u[0],h=u[1];return((0,i.useEffect)(function(){var e,a=(e=(0,t.A)(r().mark(function s(){var e,a;return r().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,h(!0),s.next=4,fetch("/api/user/plan");case 4:if(!(e=s.sent).ok){s.next=10;break}return s.next=8,e.json();case 8:n(s.sent);case 10:return s.next=12,fetch("/api/auth/free-account-status");case 12:if(!(a=s.sent).ok){s.next=18;break}return s.next=16,a.json();case 16:x(s.sent);case 18:s.next=23;break;case 20:s.prev=20,s.t0=s.catch(0),console.error("Error cargando informaci\xf3n del usuario:",s.t0);case 23:return s.prev=23,h(!1),s.finish(23);case 26:case"end":return s.stop()}},s,null,[[0,20,23,26]])})),function(){return e.apply(this,arguments)});s&&a()},[s]),m)?(0,c.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,c.jsx)("p",{children:"Cargando informaci\xf3n del usuario..."})]})}):s?(0,c.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,c.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Debug: Informaci\xf3n del Usuario"}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Usuario Autenticado"}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"ID:"})," ",s.id]}),(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"Email:"})," ",s.email]}),(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"Creado:"})," ",new Date(s.created_at).toLocaleString()]}),(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"\xdaltima conexi\xf3n:"})," ",s.last_sign_in_at?new Date(s.last_sign_in_at).toLocaleString():"N/A"]})]})]}),(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Plan Actual"}),a?(0,c.jsx)("div",{className:"space-y-2",children:(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"Plan:"})," ",(0,c.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat("free"===a.plan?"bg-yellow-100 text-yellow-800":"usuario"===a.plan?"bg-blue-100 text-blue-800":"pro"===a.plan?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:a.plan||"No definido"})]})}):(0,c.jsx)("p",{className:"text-gray-500",children:"No se pudo cargar informaci\xf3n del plan"})]}),(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 md:col-span-2",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Estado de Cuenta Gratuita"}),o?(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"Es cuenta gratuita:"})," ",o.isFreeAccount?"S\xed":"No"]}),o.isFreeAccount&&o.status&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"Estado:"})," ",o.status.isActive?"Activa":"Inactiva"]}),(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"Expira:"})," ",new Date(o.status.expiresAt).toLocaleString()]}),(0,c.jsxs)("p",{children:[(0,c.jsx)("strong",{children:"D\xedas restantes:"})," ",o.status.daysRemaining]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Uso Actual:"}),(0,c.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,c.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,c.jsx)("p",{className:"text-sm text-gray-600",children:"Documentos"}),(0,c.jsxs)("p",{className:"text-lg font-semibold",children:[o.status.usageCount.documents,"/",o.status.limits.documents]})]}),(0,c.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,c.jsx)("p",{className:"text-sm text-gray-600",children:"Tests"}),(0,c.jsxs)("p",{className:"text-lg font-semibold",children:[o.status.usageCount.tests,"/",o.status.limits.tests]})]}),(0,c.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,c.jsx)("p",{className:"text-sm text-gray-600",children:"Flashcards"}),(0,c.jsxs)("p",{className:"text-lg font-semibold",children:[o.status.usageCount.flashcards,"/",o.status.limits.flashcards]})]}),(0,c.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,c.jsx)("p",{className:"text-sm text-gray-600",children:"Mapas Mentales"}),(0,c.jsxs)("p",{className:"text-lg font-semibold",children:[o.status.usageCount.mindMaps,"/",o.status.limits.mindMaps]})]})]})]})]})]}):(0,c.jsx)("p",{className:"text-gray-500",children:"No se pudo cargar informaci\xf3n de cuenta gratuita"})]})]}),(0,c.jsx)("div",{className:"mt-8 text-center",children:(0,c.jsx)("button",{onClick:function(){return window.location.href="/app"},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Volver a la aplicaci\xf3n"})})]})}):(0,c.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"No hay usuario autenticado"}),(0,c.jsx)("p",{className:"text-gray-600",children:"Debes iniciar sesi\xf3n para ver esta informaci\xf3n."})]})})}},22840:(s,e,a)=>{Promise.resolve().then(a.bind(a,4247))}},s=>{var e=e=>s(s.s=e);s.O(0,[5730,2390,1448,4001,8441,6891,7358],()=>e(22840)),_N_E=s.O()}]);
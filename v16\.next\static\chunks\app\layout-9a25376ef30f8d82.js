(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{30347:()=>{},39337:(e,t,r)=>{"use strict";r.d(t,{default:()=>M});var a=r(12115),n=r(4001),s=r(57644),l=r(33311),i=r(28295),c=r.n(i),o=r(55564);function d(){return(0,a.useEffect)(function(){(e=(0,l.A)(c().mark(function e(){var t,r,a,n,s;return c().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,t="https://fxnhpxjijinfuxxxplzj.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4";e.next=6;break;case 6:return e.next=8,fetch("".concat(t,"/rest/v1/"),{method:"GET",headers:{"Content-Type":"application/json",apikey:r}});case 8:a=new Date(e.sent.headers.get("date")||""),n=new Date,(s=Math.abs((a.getTime()-n.getTime())/1e3))>60&&console.warn("Posible problema de sincronizaci\xf3n de tiempo detectado. "+"La diferencia entre tu hora local y el servidor es de ".concat(Math.round(s)," segundos. ")+"Esto puede causar problemas de autenticaci\xf3n."),e.next=18;break;case 15:e.prev=15,e.t0=e.catch(0),console.error("Error al verificar sincronizaci\xf3n de tiempo:",e.t0);case 18:case"end":return e.stop()}},e,null,[[0,15]])})),function(){return e.apply(this,arguments)})();var e,t=o.N.auth.onAuthStateChange(function(e,t){}).data;return function(){t.subscription.unsubscribe()}},[]),null}var u=r(61810),m=["title","titleId"],h=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,m);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),x=["title","titleId"],f=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,x);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),p=["title","titleId"],g=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,p);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),w=["title","titleId"],v=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,w);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}),b=["title","titleId"],j=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,b);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}),k=["title","titleId"],N=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,k);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}),y=["title","titleId"],E=a.forwardRef(function(e,t){var r=e.title,n=e.titleId,s=(0,u.A)(e,y);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),I=r(95155);let O=function(){var e=(0,s.M)(),t=e.activeTasks,r=e.completedTasks,n=e.removeTask,l=e.clearCompletedTasks,i=(0,a.useState)(!1),c=i[0],o=i[1],d=(0,a.useState)(!1),u=d[0],m=d[1];if(0===t.length+r.length)return null;var x=function(e){switch(e.status){case"pending":return(0,I.jsx)(h,{className:"h-4 w-4 text-yellow-500"});case"processing":return(0,I.jsx)(f,{className:"h-4 w-4 text-blue-500 animate-spin"});case"completed":return(0,I.jsx)(g,{className:"h-4 w-4 text-green-500"});case"error":return(0,I.jsx)(v,{className:"h-4 w-4 text-red-500"});default:return(0,I.jsx)(h,{className:"h-4 w-4 text-gray-500"})}},p=function(e){switch(e){case"mapa-mental":return"Mapa Mental";case"test":return"Test";case"flashcards":return"Flashcards";default:return"Tarea"}},w=function(e){return e.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"})};return(0,I.jsx)("div",{className:"fixed bottom-4 right-4 z-50 max-w-sm",children:(0,I.jsxs)("div",{className:"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden",children:[(0,I.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between",onClick:function(){return o(!c)},children:[(0,I.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,I.jsx)(f,{className:"h-5 w-5"}),(0,I.jsxs)("span",{className:"font-medium",children:["Tareas (",t.length," activas)"]})]}),c?(0,I.jsx)(j,{className:"h-5 w-5"}):(0,I.jsx)(N,{className:"h-5 w-5"})]}),c&&(0,I.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[t.length>0&&(0,I.jsxs)("div",{className:"p-3 border-b border-gray-100",children:[(0,I.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Tareas Activas"}),(0,I.jsx)("div",{className:"space-y-2",children:t.map(function(e){return(0,I.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[x(e),(0,I.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,I.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:p(e.type)}),(0,I.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),void 0!==e.progress&&(0,I.jsx)("div",{className:"mt-1",children:(0,I.jsx)("div",{className:"bg-gray-200 rounded-full h-1.5",children:(0,I.jsx)("div",{className:"bg-blue-500 h-1.5 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})})})]}),(0,I.jsx)("span",{className:"text-xs text-gray-400",children:w(e.createdAt)})]},e.id)})})]}),r.length>0&&(0,I.jsxs)("div",{className:"p-3",children:[(0,I.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,I.jsxs)("button",{onClick:function(){return m(!u)},className:"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1",children:[(0,I.jsxs)("span",{children:["Completadas (",r.length,")"]}),u?(0,I.jsx)(N,{className:"h-4 w-4"}):(0,I.jsx)(j,{className:"h-4 w-4"})]}),r.length>0&&(0,I.jsx)("button",{onClick:l,className:"text-xs text-gray-500 hover:text-red-600 transition-colors",children:"Limpiar"})]}),u&&(0,I.jsx)("div",{className:"space-y-2",children:r.map(function(e){return(0,I.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[x(e),(0,I.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,I.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:p(e.type)}),(0,I.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),e.error&&(0,I.jsx)("p",{className:"text-xs text-red-500 truncate",children:e.error})]}),(0,I.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,I.jsx)("span",{className:"text-xs text-gray-400",children:e.completedAt?w(e.completedAt):w(e.createdAt)}),(0,I.jsx)("button",{onClick:function(){return n(e.id)},className:"text-gray-400 hover:text-red-500 transition-colors",children:(0,I.jsx)(E,{className:"h-4 w-4"})})]})]},e.id)})})]})]})]})})};var C=r(1448);function M(e){var t=e.children;return(0,I.jsx)(s.W,{children:(0,I.jsxs)(n.O,{children:[(0,I.jsx)(d,{}),(0,I.jsx)(C.l$,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,style:{background:"#10b981",color:"#fff"}},error:{duration:5e3,style:{background:"#ef4444",color:"#fff"}}}}),(0,I.jsx)(O,{}),t]})})}},61810:(e,t,r)=>{"use strict";function a(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r,a,n={},s=Object.keys(e);for(a=0;a<s.length;a++)r=s[a],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}r.d(t,{A:()=>a})},75790:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,39337))}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,5730,2390,1448,4001,8441,6891,7358],()=>t(75790)),_N_E=e.O()}]);
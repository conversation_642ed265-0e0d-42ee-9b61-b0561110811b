(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8407],{2427:(e,r,t)=>{Promise.resolve().then(t.bind(t,72728))},3243:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(69940);function n(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var a,n,s,o,i=[],u=!0,l=!1;try{if(s=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;u=!1}else for(;!(u=(a=s.call(t)).done)&&(i.push(a.value),i.length!==r);u=!0);}catch(e){l=!0,n=e}finally{try{if(!u&&null!=t.return&&(o=t.return(),Object(o)!==o))return}finally{if(l)throw n}}return i}}(e,r)||(0,a.A)(e,r)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},6066:(e,r,t)=>{"use strict";var a=t(98790);t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},10631:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var a=t(78321),n=t(69940);function s(e){return function(e){if(Array.isArray(e))return(0,a.A)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,n.A)(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},11555:(e,r,t)=>{"use strict";t.d(r,{IE:()=>i,qk:()=>l,qo:()=>o});var a=t(33311),n=t(28295),s=t.n(n),o={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function i(e){return o[e]||null}function u(e,r){var t=i(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}function l(e){return c.apply(this,arguments)}function c(){return(c=(0,a.A)(s().mark(function e(r){var t,a;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/api/user/plan");case 3:if((t=e.sent).ok){e.next=7;break}return console.error("Error obteniendo plan del usuario"),e.abrupt("return",u("free",r));case 7:return e.next=9,t.json();case 9:return a=e.sent.plan||"free",e.abrupt("return",u(a,r));case 15:return e.prev=15,e.t0=e.catch(0),console.error("Error verificando acceso a caracter\xedstica:",e.t0),e.abrupt("return",u("free",r));case 19:case"end":return e.stop()}},e,null,[[0,15]])}))).apply(this,arguments)}},29889:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(94348);function n(e){var r=function(e,r){if("object"!==(0,a.A)(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!==(0,a.A)(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===(0,a.A)(r)?r:String(r)}},33311:(e,r,t)=>{"use strict";function a(e,r,t,a,n,s,o){try{var i=e[s](o),u=i.value}catch(e){t(e);return}i.done?r(u):Promise.resolve(u).then(a,n)}function n(e){return function(){var r=this,t=arguments;return new Promise(function(n,s){var o=e.apply(r,t);function i(e){a(o,n,s,i,u,"next",e)}function u(e){a(o,n,s,i,u,"throw",e)}i(void 0)})}}t.d(r,{A:()=>n})},37711:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(29889);function n(e,r,t){return(r=(0,a.A)(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}},42520:(e,r,t)=>{"use strict";t.d(r,{Md:()=>s,NB:()=>n});var a=t(11555),n={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:a.qo.free.limits,planConfig:a.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:a.qo.usuario.limits,planConfig:a.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:a.qo.pro.limits,planConfig:a.qo.pro}};function s(e){return n[e]||null}},69940:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(78321);function n(e,r){if(e){if("string"==typeof e)return(0,a.A)(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return(0,a.A)(e,r)}}},72728:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var a=t(33311),n=t(28295),s=t.n(n),o=t(12115),i=t(6066),u=t(42520),l=t(1448),c=t(95155);function d(){var e,r=(0,i.useRouter)(),t=(0,i.useSearchParams)().get("plan")||"free",n=(0,o.useState)(""),d=n[0],m=n[1],p=(0,o.useState)(""),f=p[0],h=p[1],b=(0,o.useState)(""),y=b[0],g=b[1],x=(0,o.useState)(""),v=x[0],_=x[1],j=(0,o.useState)(!1),A=j[0],P=j[1],k=(0,u.Md)(t);(0,o.useEffect)(function(){k||r.push("/")},[k,r]);var N=(e=(0,a.A)(s().mark(function e(a){var n,o,i,u,c,m;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a.preventDefault(),d.trim()){e.next=4;break}return l.Ay.error("Por favor, ingresa tu email"),e.abrupt("return");case 4:if(f.trim()){e.next=7;break}return l.Ay.error("Por favor, ingresa una contrase\xf1a"),e.abrupt("return");case 7:if(!(f.length<6)){e.next=10;break}return l.Ay.error("La contrase\xf1a debe tener al menos 6 caracteres"),e.abrupt("return");case 10:if(f===y){e.next=13;break}return l.Ay.error("Las contrase\xf1as no coinciden"),e.abrupt("return");case 13:if(P(!0),e.prev=14,"free"!==t){e.next=25;break}return e.next=18,fetch("/api/auth/register-free",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d,password:f,customerName:v})});case 18:return n=e.sent,e.next=21,n.json();case 21:o=e.sent,n.ok&&o.success?(l.Ay.success("Registro exitoso. Revisa tu email para confirmar tu cuenta."),r.push("/thank-you?plan=".concat(t,"&email_sent=true"))):429===n.status?l.Ay.error("Demasiados intentos. Int\xe9ntalo en 15 minutos."):l.Ay.error(o.error||"Error al crear la cuenta gratuita"),e.next=43;break;case 25:return console.log("\uD83D\uDD04 Iniciando nuevo flujo de pre-registro para plan de pago"),e.next=28,fetch("/api/auth/pre-register-paid",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d,password:f,customerName:v||d.split("@")[0],planId:t})});case 28:return i=e.sent,e.next=31,i.json();case 31:if(u=e.sent,i.ok){e.next=35;break}return l.Ay.error(u.error||"Error al crear la cuenta"),e.abrupt("return");case 35:return console.log("✅ Usuario pre-registrado exitosamente:",u.userId),e.next=38,fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:t,email:d,customerName:v,userId:u.userId})});case 38:return c=e.sent,e.next=41,c.json();case 41:m=e.sent,c.ok&&m.url?(console.log("\uD83D\uDD04 Redirigiendo a Stripe Checkout..."),window.location.href=m.url):l.Ay.error(m.error||"Error al crear la sesi\xf3n de pago");case 43:e.next=49;break;case 45:e.prev=45,e.t0=e.catch(14),console.error("Error en handleSubmit:",e.t0),l.Ay.error("Error al procesar la solicitud. Por favor, intenta de nuevo.");case 49:return e.prev=49,P(!1),e.finish(49);case 52:case"end":return e.stop()}},e,null,[[14,45,49,52]])})),function(r){return e.apply(this,arguments)});return k?(0,c.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,c.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,c.jsxs)("div",{className:"text-center mb-8",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:k.name}),(0,c.jsxs)("p",{className:"text-2xl font-semibold text-blue-600 mt-2",children:[0===k.price?"Gratis":"€".concat((k.price/100).toFixed(2)),("pro"===t||"usuario"===t)&&k.price>0&&(0,c.jsx)("span",{className:"text-sm text-gray-500",children:"/mes"})]})]}),(0,c.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,c.jsxs)("div",{className:"mb-6",children:[(0,c.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Caracter\xedsticas incluidas:"}),(0,c.jsx)("ul",{className:"space-y-2",children:k.features.map(function(e,r){return(0,c.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,c.jsx)("svg",{className:"h-4 w-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,c.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},r)})})]}),(0,c.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),(0,c.jsx)("input",{type:"email",id:"email",required:!0,value:d,onChange:function(e){return m(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:A})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Contrase\xf1a *"}),(0,c.jsx)("input",{type:"password",id:"password",required:!0,value:f,onChange:function(e){return h(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"M\xednimo 6 caracteres",disabled:A,minLength:6})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar Contrase\xf1a *"}),(0,c.jsx)("input",{type:"password",id:"confirmPassword",required:!0,value:y,onChange:function(e){return g(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Repite tu contrase\xf1a",disabled:A,minLength:6})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"customerName",className:"block text-sm font-medium text-gray-700",children:"Nombre (opcional)"}),(0,c.jsx)("input",{type:"text",id:"customerName",value:v,onChange:function(e){return _(e.target.value)},className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Tu nombre",disabled:A})]}),(0,c.jsx)("button",{type:"submit",disabled:A,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:A?"Procesando...":"free"===t?"Solicitar Acceso Gratuito":"Proceder al Pago"})]})]})]})}):(0,c.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Cargando detalles del plan o redirigiendo..."})}function m(){return(0,c.jsx)(o.Suspense,{fallback:(0,c.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,c.jsx)("div",{className:"max-w-md mx-auto",children:(0,c.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,c.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})})})}),children:(0,c.jsx)(d,{})})}},78321:(e,r,t)=>{"use strict";function a(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,a=Array(r);t<r;t++)a[t]=e[t];return a}t.d(r,{A:()=>a})},94348:(e,r,t)=>{"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.d(r,{A:()=>a})}},e=>{var r=r=>e(e.s=r);e.O(0,[1448,8441,6891,7358],()=>r(2427)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[525],{62477:(e,n,a)=>{Promise.resolve().then(a.bind(a,95694))},95694:(e,n,a)=>{"use strict";a.r(n),a.d(n,{default:()=>f});var r=a(33311),t=a(28295),s=a.n(t),o=a(12115),i=a(75780),c=a(33821),l=a(25519),d=a(17863),u=a(74318),m=a(86520),x=a(1448),p=a(4001),h=a(11555),g=a(52750),b=a(95155);let f=function(){var e,n,a=(0,o.useState)(null),t=a[0],f=a[1],j=(0,o.useState)(null),v=j[0],y=j[1],N=(0,o.useState)(!0),w=N[0],k=N[1],E=(0,o.useState)(!1),P=E[0],R=E[1],S=(0,o.useState)(!1),C=S[0],A=S[1],_=(0,o.useState)([]),I=_[0],T=_[1],$=(0,o.useRef)(null),z=(0,p.A)().user,O=(0,o.useState)(null),L=O[0],F=O[1];(0,o.useEffect)(function(){M()},[]),(0,o.useEffect)(function(){var e;(e=(0,r.A)(s().mark(function e(){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!z){e.next=5;break}return e.next=3,(0,h.qk)("study_planning");case 3:F(e.sent?"paid":"free");case 5:case"end":return e.stop()}},e)})),function(){return e.apply(this,arguments)})()},[z]);var M=(e=(0,r.A)(s().mark(function e(){var n,a,r;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return k(!0),e.prev=1,e.next=4,(0,c.jg)();case 4:if(n=e.sent){e.next=11;break}return x.oR.error("No se encontr\xf3 un temario configurado"),f(null),A(!1),y(null),e.abrupt("return");case 11:return f(n),e.next=14,(0,l.vD)(n.id);case 14:if(A(a=e.sent),a){e.next=20;break}return x.oR.error("Necesitas configurar tu planificaci\xf3n antes de generar el plan de estudios"),y(null),e.abrupt("return");case 20:return e.next=22,(0,d.fF)(n.id);case 22:(r=e.sent)&&r.plan_data?(console.log("✅ Plan de estudios existente encontrado"),y(r.plan_data),x.oR.success("Plan de estudios cargado desde la base de datos")):y(null),e.next=33;break;case 26:e.prev=26,e.t0=e.catch(1),console.error("Error al cargar datos:",e.t0),x.oR.error("Error al cargar los datos"),f(null),A(!1),y(null);case 33:return e.prev=33,k(!1),e.finish(33);case 36:case"end":return e.stop()}},e,null,[[1,26,33,36]])})),function(){return e.apply(this,arguments)}),D=(n=(0,r.A)(s().mark(function e(){var n,a;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:if(C){e.next=5;break}return x.oR.error("Necesitas configurar tu planificaci\xf3n antes de generar el plan de estudios"),e.abrupt("return");case 5:return R(!0),e.prev=6,n=x.oR.loading("La generaci\xf3n del plan de estudios puede tardar unos minutos. Si encuentra alg\xfan fallo una vez finalizado, vuelve a generar. OposiAI puede cometer errores de configuraci\xf3n.",{duration:0}),e.next=10,fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generarPlanEstudios",peticion:t.id,contextos:[]})});case 10:if((a=e.sent).ok){e.next=17;break}return e.next=14,a.json().catch(function(){return{}});case 14:throw Error(e.sent.error||"Error en la API: ".concat(a.status));case 17:return e.next=19,a.json();case 19:y(e.sent.result),x.oR.success("\xa1Plan de estudios generado exitosamente!",{id:n}),e.next=30;break;case 25:e.prev=25,e.t0=e.catch(6),console.error("Error al generar plan:",e.t0),(e.t0 instanceof Error?e.t0.message:"Error desconocido").includes("planificaci\xf3n configurada")?x.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"',{id:n}):x.oR.error("Error al generar el plan de estudios. Int\xe9ntalo de nuevo.",{id:n});case 30:return e.prev=30,R(!1),e.finish(30);case 33:case"end":return e.stop()}},e,null,[[6,25,30,33]])})),function(){return n.apply(this,arguments)}),q=function(e){var n="# Plan de Estudios - ".concat(null==t?void 0:t.titulo,"\n\n");return n+="".concat(e.introduccion,"\n\n"),n+="## Resumen del Plan\n\n",n+="- **Tiempo total de estudio:** ".concat(e.resumen.tiempoTotalEstudio,"\n"),n+="- **N\xfamero de temas:** ".concat(e.resumen.numeroTemas,"\n"),n+="- **Duraci\xf3n estudio nuevo:** ".concat(e.resumen.duracionEstudioNuevo,"\n"),n+="- **Duraci\xf3n repaso final:** ".concat(e.resumen.duracionRepasoFinal,"\n\n"),n+="## Cronograma Semanal\n\n",e.semanas.forEach(function(e){n+="### Semana ".concat(e.numero," (").concat(e.fechaInicio," - ").concat(e.fechaFin,")\n\n"),n+="**Objetivo:** ".concat(e.objetivoPrincipal,"\n\n"),e.dias.forEach(function(e){n+="**".concat(e.dia," (").concat(e.horas,"h):**\n"),e.tareas.forEach(function(e){n+="- ".concat(e.titulo," (").concat(e.duracionEstimada,")\n"),e.descripcion&&(n+="  ".concat(e.descripcion,"\n"))}),n+="\n"})}),n+="## Estrategia de Repasos\n\n".concat(e.estrategiaRepasos,"\n\n"),n+="## Pr\xf3ximos Pasos\n\n".concat(e.proximosPasos,"\n")};return w?(0,b.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Cargando datos..."})]})}):"free"===L?(0,b.jsx)(g.A,{feature:"study_planning",benefits:["Planes de estudio personalizados con IA","Cronogramas adaptativos a tu ritmo","Seguimiento autom\xe1tico de progreso","Recomendaciones inteligentes de repaso"]}):t&&C?(0,b.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,b.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:[(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-1",children:"Mi Plan de Estudios"}),(0,b.jsxs)("p",{className:"text-sm text-gray-600",children:["Plan personalizado generado con IA para: ",(0,b.jsx)("strong",{children:t.titulo})]})]}),(0,b.jsx)("div",{className:"flex items-center space-x-3",children:v&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("button",{onClick:function(){y(null),D()},disabled:P,className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 text-sm",children:[(0,b.jsx)(i.jTZ,{className:"w-4 h-4 mr-2"}),"Regenerar"]}),(0,b.jsxs)("button",{onClick:function(){if(v){var e=new Blob([q(v)],{type:"text/markdown"}),n=URL.createObjectURL(e),a=document.createElement("a");a.href=n,a.download="plan-estudios-".concat((null==t?void 0:t.titulo)||"temario",".md"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(n),x.oR.success("Plan descargado exitosamente")}},className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[(0,b.jsx)(i.a4x,{className:"w-4 h-4 mr-2"}),"Descargar"]}),(0,b.jsxs)("button",{onClick:function(){if(v){var e=q(v),n=window.open("","_blank");n&&(n.document.write("\n        <html>\n          <head>\n            <title>Plan de Estudios - ".concat(null==t?void 0:t.titulo,'</title>\n            <style>\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n              h1, h2, h3 { color: #333; }\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\n              ul, ol { margin-left: 20px; }\n              strong { color: #2563eb; }\n              @media print { body { margin: 0; } }\n            </style>\n          </head>\n          <body>\n            <div id="content"></div>\n            <script>\n              // Convertir markdown a HTML b\xe1sico para impresi\xf3n\n              const markdown = ').concat(JSON.stringify(e),";\n              const content = markdown\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\n                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n                .replace(/\\n/g, '<br>');\n              document.getElementById('content').innerHTML = content;\n              window.print();\n            <\/script>\n          </body>\n        </html>\n      ")),n.document.close())}},className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[(0,b.jsx)(i.Mvz,{className:"w-4 h-4 mr-2"}),"Imprimir"]})]})})]})}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6",children:[(0,b.jsxs)("div",{className:"flex items-center mb-3",children:[(0,b.jsx)(i.jH2,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Documentos Seleccionados"})]}),(0,b.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Selecciona los documentos que quieres consultar durante el estudio de tu plan personalizado."}),(0,b.jsx)(m.A,{ref:$,onSelectionChange:T}),I.length>0&&(0,b.jsx)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,b.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,b.jsx)("strong",{children:I.length})," documento",1!==I.length?"s":""," seleccionado",1!==I.length?"s":""," para consulta."]})})]}),v?(0,b.jsx)(u.A,{plan:v,temarioId:t.id}):(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,b.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,b.jsx)(i.wIk,{className:"w-10 h-10 text-blue-600"})}),(0,b.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Genera tu Plan de Estudios Personalizado"}),(0,b.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Nuestro asistente de IA analizar\xe1 tu planificaci\xf3n, disponibilidad de tiempo, y las caracter\xedsticas de cada tema para crear un plan de estudios completamente personalizado y realista."}),(0,b.jsx)("button",{onClick:D,disabled:P,className:"flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto",children:P?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"}),"Generando plan con IA..."]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(i.x_j,{className:"w-5 h-5 mr-3"}),"Generar Plan de Estudios"]})})]})]})}):(0,b.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,b.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,b.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,b.jsx)(i.x_j,{className:"w-8 h-8 text-yellow-600"})}),(0,b.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Configuraci\xf3n Requerida"}),(0,b.jsx)("p",{className:"text-gray-600 mb-4",children:"Para generar tu plan de estudios personalizado, necesitas:"}),(0,b.jsxs)("ul",{className:"text-left text-sm text-gray-600 mb-6 space-y-2",children:[(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),"Tener un temario configurado"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),"Completar la planificaci\xf3n inteligente"]})]}),(0,b.jsxs)("a",{href:"/temario",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,b.jsx)(i.x_j,{className:"w-4 h-4 mr-2"}),"Ir a Mi Temario"]})]})})}}},e=>{var n=n=>e(e.s=n);e.O(0,[5730,844,2390,1448,3329,104,4001,2490,8441,6891,7358],()=>n(62477)),_N_E=e.O()}]);
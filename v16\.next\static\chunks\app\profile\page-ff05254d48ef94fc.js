(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{21149:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(33311),a=r(28295),n=r.n(a),i=r(12115),l=r(6066),c=r(75780),o=r(4001),d=r(95155);function m(e){var s,r=e.userProfile,a=e.onUpdate,l=(0,i.useState)(!1),o=l[0],m=l[1],u=(0,i.useState)(r.user.name),x=u[0],p=u[1],h=(0,i.useState)(!1),f=h[0],g=h[1],b=(0,i.useState)(null),j=b[0],N=b[1],v=(0,i.useState)(!1),y=v[0],w=v[1],k=(s=(0,t.A)(n().mark(function e(){return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,g(!0),N(null),e.next=5,fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:x})});case 5:if(e.sent.ok){e.next=8;break}throw Error("Error actualizando perfil");case 8:w(!0),m(!1),a(),setTimeout(function(){return w(!1)},3e3),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("Error updating profile:",e.t0),N(e.t0 instanceof Error?e.t0.message:"Error desconocido");case 18:return e.prev=18,g(!1),e.finish(18);case 21:case"end":return e.stop()}},e,null,[[0,14,18,21]])})),function(){return s.apply(this,arguments)}),E=function(e){return new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})};return(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Informaci\xf3n de la Cuenta"}),!o&&(0,d.jsxs)("button",{onClick:function(){return m(!0)},className:"flex items-center text-blue-600 hover:text-blue-700 transition-colors",children:[(0,d.jsx)(c.WXf,{className:"w-4 h-4 mr-1"}),"Editar"]})]}),j&&(0,d.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[(0,d.jsx)(c.y3G,{className:"w-5 h-5 text-red-500 mr-2"}),(0,d.jsx)("span",{className:"text-red-700",children:j})]}),y&&(0,d.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[(0,d.jsx)(c.YrT,{className:"w-5 h-5 text-green-500 mr-2"}),(0,d.jsx)("span",{className:"text-green-700",children:"Perfil actualizado correctamente"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informaci\xf3n Personal"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,d.jsx)(c.JXP,{className:"w-4 h-4 inline mr-1"}),"Nombre"]}),o?(0,d.jsx)("input",{type:"text",value:x,onChange:function(e){return p(e.target.value)},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Tu nombre"}):(0,d.jsx)("p",{className:"text-gray-900",children:r.user.name})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,d.jsx)(c.pHD,{className:"w-4 h-4 inline mr-1"}),"Email"]}),(0,d.jsx)("p",{className:"text-gray-900",children:r.user.email}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"El email no se puede modificar"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,d.jsx)(c.wIk,{className:"w-4 h-4 inline mr-1"}),"Miembro desde"]}),(0,d.jsx)("p",{className:"text-gray-900",children:E(r.user.created_at)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ID de Usuario"}),(0,d.jsx)("p",{className:"text-gray-600 font-mono text-sm",children:r.user.id})]})]}),o&&(0,d.jsxs)("div",{className:"flex items-center gap-3 mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("button",{onClick:k,disabled:f,className:"flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50",children:[f?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,d.jsx)(c.Bc_,{className:"w-4 h-4 mr-2"}),f?"Guardando...":"Guardar"]}),(0,d.jsxs)("button",{onClick:function(){p(r.user.name),m(!1),N(null)},disabled:f,className:"flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors disabled:opacity-50",children:[(0,d.jsx)(c.yGN,{className:"w-4 h-4 mr-2"}),"Cancelar"]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informaci\xf3n de Suscripci\xf3n"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,d.jsx)(c.lZI,{className:"w-4 h-4 inline mr-1"}),"Plan Actual"]}),(0,d.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(function(e){switch(e){case"free":default:return"bg-gray-100 text-gray-800";case"usuario":return"bg-blue-100 text-blue-800";case"pro":return"bg-purple-100 text-purple-800"}}(r.profile.subscription_plan)),children:function(e){switch(e){case"free":return"Plan Gratuito";case"usuario":return"Plan Usuario";case"pro":return"Plan Pro";default:return e}}(r.profile.subscription_plan)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Estado de Pago"}),(0,d.jsx)("div",{className:"flex items-center",children:r.profile.payment_verified?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.YrT,{className:"w-4 h-4 text-green-500 mr-1"}),(0,d.jsx)("span",{className:"text-green-700",children:"Verificado"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.Ohp,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,d.jsx)("span",{className:"text-yellow-700",children:"Pendiente"})]})})]}),"free"!==r.profile.subscription_plan&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Renovaci\xf3n Autom\xe1tica"}),(0,d.jsx)("div",{className:"flex items-center",children:r.profile.auto_renew?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.YrT,{className:"w-4 h-4 text-green-500 mr-1"}),(0,d.jsx)("span",{className:"text-green-700",children:"Activa"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.yGN,{className:"w-4 h-4 text-red-500 mr-1"}),(0,d.jsx)("span",{className:"text-red-700",children:"Inactiva"})]})})]}),r.profile.plan_expires_at&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:r.profile.auto_renew?"Pr\xf3xima Renovaci\xf3n":"Expira el"}),(0,d.jsx)("p",{className:"text-gray-900",children:E(r.profile.plan_expires_at)})]}),r.profile.last_payment_date&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdaltimo Pago"}),(0,d.jsx)("p",{className:"text-gray-900",children:E(r.profile.last_payment_date)})]})]}),"free"===r.profile.subscription_plan&&(0,d.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,d.jsxs)("a",{href:"/upgrade-plan",className:"inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(c.lZI,{className:"w-4 h-4 mr-2"}),"Actualizar Plan"]})})]})]})]})}function u(e){var s,r,a=e.userId,l=(0,i.useState)([]),o=l[0],m=l[1],u=(0,i.useState)(!0),x=u[0],p=u[1],h=(0,i.useState)(null),f=h[0],g=h[1],b=(0,i.useState)("all"),j=b[0],N=b[1],v=(0,i.useState)(0),y=v[0],w=v[1];(0,i.useEffect)(function(){k()},[a,j]);var k=(s=(0,t.A)(n().mark(function e(){var s,r,t;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,p(!0),g(null),s=new URLSearchParams({limit:"20"}),"all"!==j&&s.append("type",j),e.next=7,fetch("/api/user/notifications?".concat(s));case 7:if((r=e.sent).ok){e.next=10;break}throw Error("Error cargando notificaciones");case 10:return e.next=12,r.json();case 12:if(!(t=e.sent).success){e.next=18;break}m(t.data.notifications),w(t.data.total),e.next=19;break;case 18:throw Error(t.error||"Error desconocido");case 19:e.next=25;break;case 21:e.prev=21,e.t0=e.catch(0),console.error("Error loading notifications:",e.t0),g(e.t0 instanceof Error?e.t0.message:"Error desconocido");case 25:return e.prev=25,p(!1),e.finish(25);case 28:case"end":return e.stop()}},e,null,[[0,21,25,28]])})),function(){return s.apply(this,arguments)}),E=function(e){switch(e){case"subscription_cancelled":case"payment_failed":return(0,d.jsx)(c.yGN,{className:"w-5 h-5 text-red-500"});case"grace_period_ending":return(0,d.jsx)(c.Ohp,{className:"w-5 h-5 text-yellow-500"});case"plan_expired":return(0,d.jsx)(c.y3G,{className:"w-5 h-5 text-red-500"});case"welcome":return(0,d.jsx)(c.YrT,{className:"w-5 h-5 text-green-500"});default:return(0,d.jsx)(c.zd,{className:"w-5 h-5 text-blue-500"})}},_=function(e){switch(e){case"subscription_cancelled":return"Suscripci\xf3n Cancelada";case"grace_period_ending":return"Per\xedodo de Gracia";case"plan_expired":return"Plan Expirado";case"payment_failed":return"Pago Fallido";case"welcome":return"Bienvenida";default:return"Notificaci\xf3n"}},P=function(e){switch(e){case"sent":return(0,d.jsx)(c.YrT,{className:"w-4 h-4 text-green-500"});case"failed":return(0,d.jsx)(c.yGN,{className:"w-4 h-4 text-red-500"});case"pending":return(0,d.jsx)(c.Ohp,{className:"w-4 h-4 text-yellow-500"});default:return(0,d.jsx)(c.pHD,{className:"w-4 h-4 text-gray-500"})}},C=function(e){var s=new Date(e),r=Math.floor((new Date().getTime()-s.getTime())/36e5);return r<1?"Hace unos minutos":r<24?"Hace ".concat(r," hora").concat(r>1?"s":""):s.toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},S=[{value:"all",label:"Todas las notificaciones"},{value:"subscription_cancelled",label:"Suscripciones canceladas"},{value:"grace_period_ending",label:"Per\xedodos de gracia"},{value:"plan_expired",label:"Planes expirados"},{value:"payment_failed",label:"Pagos fallidos"},{value:"welcome",label:"Bienvenida"}];return(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Historial de Notificaciones"}),(0,d.jsxs)("button",{onClick:k,disabled:x,className:"flex items-center text-blue-600 hover:text-blue-700 transition-colors disabled:opacity-50",children:[(0,d.jsx)(c.jTZ,{className:"w-4 h-4 mr-1 ".concat(x?"animate-spin":"")}),"Actualizar"]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(c.K7R,{className:"w-4 h-4 text-gray-500"}),(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Filtrar por tipo:"})]}),(0,d.jsx)("select",{value:j,onChange:function(e){return N(e.target.value)},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:S.map(function(e){return(0,d.jsx)("option",{value:e.value,children:e.label},e.value)})})]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:y}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Total de notificaciones"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.filter(function(e){return"sent"===e.status}).length}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Enviadas exitosamente"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:o.filter(function(e){return"failed"===e.status}).length}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Fallos de env\xedo"})]})]})}),x?(0,d.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):f?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(c.y3G,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error cargando notificaciones"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:f}),(0,d.jsx)("button",{onClick:k,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Reintentar"})]}):0===o.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(c.zd,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No hay notificaciones"}),(0,d.jsx)("p",{className:"text-gray-600",children:"all"===j?"A\xfan no has recibido ninguna notificaci\xf3n por email.":'No hay notificaciones del tipo "'.concat(null==(r=S.find(function(e){return e.value===j}))?void 0:r.label,'".')})]}):(0,d.jsx)("div",{className:"space-y-4",children:o.map(function(e){return(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:"flex-shrink-0 mt-1",children:E(e.type)}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-blue-600",children:_(e.type)}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[P(e.status),(0,d.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.status})]})]}),(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-1",children:e.subject}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:C(e.sentAt)}),e.metadata&&(0,d.jsxs)("div",{className:"mt-2 text-xs text-gray-600",children:[e.metadata.planName&&(0,d.jsxs)("span",{className:"inline-block bg-gray-100 px-2 py-1 rounded mr-2",children:["Plan: ",e.metadata.planName]}),void 0!==e.metadata.daysRemaining&&(0,d.jsxs)("span",{className:"inline-block bg-yellow-100 px-2 py-1 rounded mr-2",children:[e.metadata.daysRemaining," d\xedas restantes"]}),void 0!==e.metadata.hoursRemaining&&(0,d.jsxs)("span",{className:"inline-block bg-red-100 px-2 py-1 rounded mr-2",children:[e.metadata.hoursRemaining," horas restantes"]})]})]})]})})},e.id)})})]})}function x(e){var s,r,t,a,n,i,l,o,m,u,x,p,h,f,g,b,j,N=e.userProfile,v=N.access,y=N.tokenUsage,w=N.profile;if(!v||!y||!w)return(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Cargando informaci\xf3n del plan..."})})});var k=function(e){return e>=90?"bg-red-500":e>=75?"bg-yellow-500":e>=50?"bg-blue-500":"bg-green-500"},E=function(e){return -1===e||"Ilimitado"===e||"string"==typeof e},_=function(e,s){return s?e.replace("este mes","(sin l\xedmites)"):e},P=[{icon:c.jH2,label:"Documentos",current:(null==(r=v.currentUsage)?void 0:r.documents)||0,limit:E(null==(t=v.limits)?void 0:t.documents)?"Ilimitado":(null==(a=v.limits)?void 0:a.documents)||0,color:"blue",description:_("Documentos PDF/TXT subidos",E(null==(n=v.limits)?void 0:n.documents))},{icon:c.NLe,label:"Tests",current:(null==(i=v.currentUsage)?void 0:i.tests)||0,limit:E(null==(l=v.limits)?void 0:l.tests)?"Ilimitado":(null==(o=v.limits)?void 0:o.tests)||0,color:"green",description:_("Tests generados este mes",E(null==(m=v.limits)?void 0:m.tests))},{icon:c.s_k,label:"Flashcards",current:(null==(u=v.currentUsage)?void 0:u.flashcards)||0,limit:E(null==(x=v.limits)?void 0:x.flashcards)?"Ilimitado":(null==(p=v.limits)?void 0:p.flashcards)||0,color:"purple",description:_("Flashcards creadas este mes",E(null==(h=v.limits)?void 0:h.flashcards))},{icon:c.x_j,label:"Mapas Mentales",current:(null==(f=v.currentUsage)?void 0:f.mindMaps)||0,limit:E(null==(g=v.limits)?void 0:g.mindMaps)?"Ilimitado":(null==(b=v.limits)?void 0:b.mindMaps)||0,color:"indigo",description:_("Mapas mentales generados este mes",E(null==(j=v.limits)?void 0:j.mindMaps))}],C=function(e){return e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString()};return(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Uso y L\xedmites del Plan"}),(0,d.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat("free"===w.subscription_plan?"bg-gray-100 text-gray-800":"usuario"===w.subscription_plan?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:function(e){switch(e){case"free":return"Plan Gratuito";case"usuario":return"Plan Usuario";case"pro":return"Plan Pro";default:return e}}(w.subscription_plan)})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(c.Ojn,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tokens de IA"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Uso mensual de procesamiento de IA"})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:C(y.current||0)}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["de ",C(y.limit||0)]})]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,d.jsx)("div",{className:"h-3 rounded-full transition-all duration-300 ".concat(k(y.percentage||0)),style:{width:"".concat(y.percentage||0,"%")}})}),(0,d.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,d.jsxs)("span",{className:(s=y.percentage||0)>=90?"text-red-600":s>=75?"text-yellow-600":"text-gray-600",children:[(y.percentage||0).toFixed(1),"% utilizado"]}),(0,d.jsxs)("span",{className:"text-gray-600",children:[C(y.remaining||0)," restantes"]})]}),(y.percentage||0)>=90&&(0,d.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[(0,d.jsx)(c.eHT,{className:"w-5 h-5 text-red-500 mr-2"}),(0,d.jsx)("span",{className:"text-red-700 text-sm",children:"Te est\xe1s acercando al l\xedmite de tokens. Considera actualizar tu plan."})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:P.map(function(e){var s,r,t=e.icon,a=(s=e.current,"string"==typeof(r=e.limit)||0===r?0:Math.min(s/r*100,100)),n="string"==typeof e.limit;return(0,d.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(t,{className:"w-5 h-5 text-".concat(e.color,"-600 mr-2")}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:e.label})]}),(0,d.jsx)("div",{className:"text-right",children:n?(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,d.jsx)(c.YrT,{className:"w-3 h-3 mr-1"}),"Ilimitado"]})}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"font-semibold text-gray-900",children:e.current}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["de ",e.limit]})]})})]}),!n&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(k(a)),style:{width:"".concat(a,"%")}})}),(0,d.jsxs)("div",{className:"text-xs text-gray-600",children:[a.toFixed(1),"% utilizado"]})]}),n&&(0,d.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"✓ Acceso completo sin restricciones"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:e.description})]},e.label)})}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Funciones Disponibles"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:(v.features||[]).map(function(e){return(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(c.YrT,{className:"w-4 h-4 text-green-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:{document_upload:"Subida de documentos",test_generation:"Generaci\xf3n de tests",flashcard_generation:"Creaci\xf3n de flashcards",mind_map_generation:"Mapas mentales",study_planning:"Planificaci\xf3n de estudios",ai_tutor:"Tutor de IA",summaries:"Res\xfamenes autom\xe1ticos",advanced_analytics:"An\xe1lisis avanzado"}[e]||e})]},e)})}),"free"===w.subscription_plan&&(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"\xbfNecesitas m\xe1s funciones y l\xedmites m\xe1s altos?"}),(0,d.jsxs)("a",{href:"/upgrade-plan",className:"inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm",children:[(0,d.jsx)(c.ARf,{className:"w-4 h-4 mr-2"}),"Actualizar Plan"]})]})]})]})}var p=r(55564),h=r(1448);function f(e){var s,r,a,o,m=e.userProfile,u=e.onUpdate,x=(0,l.useRouter)(),f=(0,i.useState)(!1),g=f[0],b=f[1],j=(0,i.useState)(null),N=j[0],v=j[1],y=(0,i.useState)(!1),w=y[0],k=y[1],E=(0,i.useState)(!1),_=E[0],P=E[1],C=(0,i.useState)(!0),S=C[0],O=C[1],T=(0,i.useState)(!1),I=T[0],A=T[1],z=(0,i.useState)(!1),D=z[0],R=z[1],U=(0,i.useState)(!1),F=U[0],M=U[1];(0,i.useEffect)(function(){var e;if(null!=m&&null!=(e=m.profile)&&e.security_flags){var s,r,t=m.profile.security_flags;O(null==(s=t.email_notifications)||s),A(null!=(r=t.marketing_emails)&&r)}},[m]);var G=(s=(0,t.A)(n().mark(function e(){var s,r,t;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return M(!0),e.prev=1,s=h.oR.loading("Enviando enlace de recuperaci\xf3n..."),r=(0,p.U)(),e.next=6,r.auth.resetPasswordForEmail(m.user.email,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});case 6:if(!(t=e.sent.error)){e.next=10;break}throw t;case 10:h.oR.success("Se ha enviado un enlace a tu email para restablecer la contrase\xf1a.",{id:s,duration:6e3}),R(!1),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(1),console.error("Error al solicitar reseteo de contrase\xf1a:",e.t0),h.oR.error("No se pudo enviar el enlace de recuperaci\xf3n. Int\xe9ntalo de nuevo m\xe1s tarde.",{id:s});case 18:return e.prev=18,M(!1),e.finish(18);case 21:case"end":return e.stop()}},e,null,[[1,14,18,21]])})),function(){return s.apply(this,arguments)}),H=(r=(0,t.A)(n().mark(function e(){return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,b(!0),v(null),e.next=5,fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({preferences:{email_notifications:S,marketing_emails:I}})});case 5:if(e.sent.ok){e.next=8;break}throw Error("Error actualizando preferencias");case 8:k(!0),u(),setTimeout(function(){return k(!1)},3e3),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("Error updating preferences:",e.t0),v(e.t0 instanceof Error?e.t0.message:"Error desconocido");case 17:return e.prev=17,b(!1),e.finish(17);case 20:case"end":return e.stop()}},e,null,[[0,13,17,20]])})),function(){return r.apply(this,arguments)}),Y=(a=(0,t.A)(n().mark(function e(){var s;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,s=(0,p.U)(),e.next=4,s.auth.signOut();case 4:x.push("/"),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("Error logging out:",e.t0);case 10:case"end":return e.stop()}},e,null,[[0,7]])})),function(){return a.apply(this,arguments)}),L=(o=(0,t.A)(n().mark(function e(){var s,r;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,b(!0),v(null),e.next=5,fetch("/api/user/cancel-subscription",{method:"POST",headers:{"Content-Type":"application/json"}});case 5:return s=e.sent,e.next=8,s.json();case 8:if(r=e.sent,s.ok){e.next=11;break}throw Error(r.error||"Error cancelando suscripci\xf3n");case 11:k(!0),P(!1),u(),h.oR.success("Suscripci\xf3n cancelada exitosamente. Mantendr\xe1s acceso hasta: ".concat(new Date(r.details.periodEnd).toLocaleDateString("es-ES")),{duration:6e3}),setTimeout(function(){return k(!1)},5e3),e.next=23;break;case 18:e.prev=18,e.t0=e.catch(0),console.error("Error canceling subscription:",e.t0),v(e.t0 instanceof Error?e.t0.message:"Error desconocido"),P(!1);case 23:return e.prev=23,b(!1),e.finish(23);case 26:case"end":return e.stop()}},e,null,[[0,18,23,26]])})),function(){return o.apply(this,arguments)});return(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Configuraci\xf3n de la Cuenta"})}),N&&(0,d.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,d.jsx)("span",{className:"text-red-700",children:N})}),w&&(0,d.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[(0,d.jsx)(c.YrT,{className:"w-5 h-5 text-green-500 mr-2"}),(0,d.jsx)("span",{className:"text-green-700",children:"Configuraci\xf3n actualizada correctamente"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(c.zd,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Preferencias de Notificaciones"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Notificaciones por Email"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir notificaciones importantes sobre tu cuenta y suscripci\xf3n"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:S,onChange:function(e){return O(e.target.checked)},className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Emails de Marketing"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir informaci\xf3n sobre nuevas funciones y ofertas especiales"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:I,onChange:function(e){return A(e.target.checked)},className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,d.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,d.jsxs)("button",{onClick:H,disabled:g,className:"flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50",children:[g?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,d.jsx)(c.Bc_,{className:"w-4 h-4 mr-2"}),g?"Guardando...":"Guardar Preferencias"]})})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(c.pcC,{className:"w-5 h-5 text-green-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Seguridad"})]}),(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cambiar Contrase\xf1a"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir\xe1s un enlace en tu email para establecer una nueva contrase\xf1a."})]}),(0,d.jsx)("button",{onClick:function(){return R(!0)},className:"bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm",children:"Cambiar"})]})})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(c.VSk,{className:"w-5 h-5 text-gray-600 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Acciones de Cuenta"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cerrar Sesi\xf3n"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Cerrar sesi\xf3n en este dispositivo"})]}),(0,d.jsxs)("button",{onClick:Y,className:"flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm",children:[(0,d.jsx)(c.QeK,{className:"w-4 h-4 mr-2"}),"Cerrar Sesi\xf3n"]})]}),"free"!==m.profile.subscription_plan&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cancelar Suscripci\xf3n"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Cancelar tu suscripci\xf3n actual (mantendr\xe1s acceso hasta el final del per\xedodo)"})]}),(0,d.jsxs)("button",{onClick:function(){return P(!0)},className:"flex items-center bg-red-100 text-red-700 px-4 py-2 rounded-md hover:bg-red-200 transition-colors text-sm",children:[(0,d.jsx)(c.lZI,{className:"w-4 h-4 mr-2"}),"Cancelar"]})]})]})]})]}),D&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(c.pHD,{className:"w-6 h-6 text-blue-500 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Restablecer Contrase\xf1a"})]}),(0,d.jsxs)("p",{className:"text-gray-600 mb-6",children:["Se enviar\xe1 un enlace seguro a tu direcci\xf3n de correo electr\xf3nico ",(0,d.jsxs)("strong",{children:["(",m.user.email,")"]})," para que puedas establecer una nueva contrase\xf1a."]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)("button",{onClick:function(){return R(!1)},disabled:F,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"}),(0,d.jsxs)("button",{onClick:G,disabled:F,className:"px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 flex items-center",children:[F?(0,d.jsx)(c.TwU,{className:"animate-spin mr-2"}):(0,d.jsx)(c.pHD,{className:"mr-2"}),F?"Enviando...":"Enviar Enlace"]})]})]})}),_&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(c.lZI,{className:"w-6 h-6 text-red-500 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Confirmar Cancelaci\xf3n"})]}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"\xbfEst\xe1s seguro de que quieres cancelar tu suscripci\xf3n? Mantendr\xe1s acceso a las funciones premium hasta el final de tu per\xedodo de facturaci\xf3n actual."}),(0,d.jsxs)("div",{className:"flex gap-3",children:[(0,d.jsx)("button",{onClick:L,disabled:g,className:"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center justify-center",children:g?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Cancelando..."]}):"S\xed, cancelar suscripci\xf3n"}),(0,d.jsx)("button",{onClick:function(){return P(!1)},className:"flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Mantener suscripci\xf3n"})]})]})})]})}function g(){var e,s=(0,o.A)().user,r=(0,l.useRouter)(),a=(0,i.useState)("account"),p=a[0],h=a[1],g=(0,i.useState)(null),b=g[0],j=g[1],N=(0,i.useState)(!0),v=N[0],y=N[1],w=(0,i.useState)(null),k=w[0],E=w[1];(0,i.useEffect)(function(){if(!s)return void r.push("/login");_()},[s,r]);var _=(e=(0,t.A)(n().mark(function e(){var s;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,y(!0),E(null),e.next=5,fetch("/api/user/profile");case 5:if((s=e.sent).ok){e.next=8;break}throw Error("Error cargando perfil de usuario");case 8:return e.next=10,s.json();case 10:j(e.sent),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("Error loading user profile:",e.t0),E(e.t0 instanceof Error?e.t0.message:"Error desconocido");case 18:return e.prev=18,y(!1),e.finish(18);case 21:case"end":return e.stop()}},e,null,[[0,14,18,21]])})),function(){return e.apply(this,arguments)}),P=[{id:"account",label:"Informaci\xf3n de Cuenta",icon:c.JXP},{id:"notifications",label:"Notificaciones",icon:c.zd},{id:"usage",label:"Uso y L\xedmites",icon:c.lZI},{id:"settings",label:"Configuraci\xf3n",icon:c.VSk}];return v?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):k||!b?(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error cargando perfil"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:k||"No se pudo cargar la informaci\xf3n del perfil"}),(0,d.jsx)("button",{onClick:function(){return r.push("/app")},className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Volver al Dashboard"})]})}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("button",{onClick:function(){return r.push("/app")},className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,d.jsx)(c.kRp,{className:"w-5 h-5 mr-2"}),"Volver al Dashboard"]})}),(0,d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Mi Perfil"}),(0,d.jsx)("div",{className:"w-24"})," "]})})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-8",children:[(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(c.JXP,{className:"w-10 h-10 text-blue-600"})}),(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:b.user.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b.user.email}),(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("free"===b.profile.subscription_plan?"bg-gray-100 text-gray-800":"usuario"===b.profile.subscription_plan?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:["Plan ","free"===b.profile.subscription_plan?"Gratuito":"usuario"===b.profile.subscription_plan?"Usuario":"Pro"]})})]}),(0,d.jsx)("nav",{className:"space-y-1",children:P.map(function(e){var s=e.icon;return(0,d.jsxs)("button",{onClick:function(){return h(e.id)},className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(p===e.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:[(0,d.jsx)(s,{className:"w-4 h-4 mr-3"}),e.label]},e.id)})})]})}),(0,d.jsx)("div",{className:"lg:col-span-9",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:["account"===p&&(0,d.jsx)(m,{userProfile:b,onUpdate:_}),"notifications"===p&&(0,d.jsx)(u,{userId:b.user.id}),"usage"===p&&(0,d.jsx)(x,{userProfile:b}),"settings"===p&&(0,d.jsx)(f,{userProfile:b,onUpdate:_})]})})]})})]})}},77906:(e,s,r)=>{"use strict";r.d(s,{k5:()=>d});var t=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=t.createContext&&t.createContext(a),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e}).apply(this,arguments)}function c(e,s){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);s&&(t=t.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),r.push.apply(r,t)}return r}function o(e){for(var s=1;s<arguments.length;s++){var r=null!=arguments[s]?arguments[s]:{};s%2?c(Object(r),!0).forEach(function(s){var t,a,n;t=e,a=s,n=r[s],(a=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var t=r.call(e,s||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(a))in t?Object.defineProperty(t,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(r,s))})}return e}function d(e){return function(s){return t.createElement(m,l({attr:o({},e.attr)},s),function e(s){return s&&s.map(function(s,r){return t.createElement(s.tag,o({key:r},s.attr),e(s.child))})}(e.child))}}function m(e){var s=function(s){var r,a=e.attr,n=e.size,c=e.title,d=function(e,s){if(null==e)return{};var r,t,a=function(e,s){if(null==e)return{};var r={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){if(s.indexOf(t)>=0)continue;r[t]=e[t]}return r}(e,s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(t=0;t<n.length;t++)r=n[t],!(s.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,i),m=n||s.size||"1em";return s.className&&(r=s.className),e.className&&(r=(r?r+" ":"")+e.className),t.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,a,d,{className:r,style:o(o({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&t.createElement("title",null,c),e.children)};return void 0!==n?t.createElement(n.Consumer,null,function(e){return s(e)}):s(a)}},84828:(e,s,r)=>{Promise.resolve().then(r.bind(r,21149))}},e=>{var s=s=>e(e.s=s);e.O(0,[5730,844,2390,1448,4001,8441,6891,7358],()=>s(84828)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6593],{11555:(e,r,a)=>{"use strict";a.d(r,{IE:()=>l,qk:()=>c,qo:()=>i});var s=a(33311),t=a(28295),n=a.n(t),i={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function l(e){return i[e]||null}function o(e,r){var a=l(e);return!(!a||a.restrictedFeatures.includes(r))&&a.features.includes(r)}function c(e){return d.apply(this,arguments)}function d(){return(d=(0,s.A)(n().mark(function e(r){var a,s;return n().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/api/user/plan");case 3:if((a=e.sent).ok){e.next=7;break}return console.error("Error obteniendo plan del usuario"),e.abrupt("return",o("free",r));case 7:return e.next=9,a.json();case 9:return s=e.sent.plan||"free",e.abrupt("return",o(s,r));case 15:return e.prev=15,e.t0=e.catch(0),console.error("Error verificando acceso a caracter\xedstica:",e.t0),e.abrupt("return",o("free",r));case 19:case"end":return e.stop()}},e,null,[[0,15]])}))).apply(this,arguments)}},21038:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var s=a(33311),t=a(28295),n=a.n(t),i=a(12115),l=a(6066),o=a(55564),c=a(11555),d=a(95155);function u(){var e=(0,l.useRouter)();(0,l.useSearchParams)();var r=(0,i.useState)(null),a=r[0],t=r[1],u=(0,i.useState)(!0),m=u[0],p=u[1];return((0,i.useEffect)(function(){var r;(r=(0,s.A)(n().mark(function r(){var a,s,i,l,d,u,m,h;return n().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,i=(0,o.U)(),r.next=4,i.auth.getUser();case 4:if(d=(l=r.sent).data.user,!(l.error||!d)){r.next=10;break}return e.push("/login"),r.abrupt("return");case 10:return r.next=12,i.from("user_profiles").select("*").eq("user_id",d.id).single();case 12:if(m=(u=r.sent).data,!(u.error||!m)){r.next=18;break}return e.push("/payment"),r.abrupt("return");case 18:if(h=(0,c.IE)(m.subscription_plan)){r.next=22;break}return e.push("/payment"),r.abrupt("return");case 22:t({name:(null==(a=d.user_metadata)?void 0:a.name)||(null==(s=d.email)?void 0:s.split("@")[0])||"Usuario",email:d.email||"",plan:m.subscription_plan,planName:h.name,features:h.features,tokenLimit:m.monthly_token_limit}),p(!1),r.next=30;break;case 26:r.prev=26,r.t0=r.catch(0),console.error("Error loading user info:",r.t0),e.push("/login");case 30:case"end":return r.stop()}},r,null,[[0,26]])})),function(){return r.apply(this,arguments)})()},[e]),m)?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):a?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-2xl w-full",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,d.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:["\xa1Bienvenido a OposiAI, ",a.name,"!"]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Tu cuenta ha sido activada exitosamente"})]}),(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-6",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-blue-800 mb-3",children:["Tu Plan: ",a.planName]}),"free"!==a.plan&&(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("p",{className:"text-blue-700 mb-2",children:[(0,d.jsx)("strong",{children:"L\xedmite mensual de tokens:"})," ",a.tokenLimit.toLocaleString()]}),(0,d.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"0%"}})}),(0,d.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"0 tokens utilizados este mes"})]}),(0,d.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Caracter\xedsticas incluidas:"}),(0,d.jsx)("ul",{className:"space-y-1",children:a.features.map(function(e,r){var a;return(0,d.jsxs)("li",{className:"flex items-center text-blue-700",children:[(0,d.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),{document_upload:"Subida de documentos",test_generation:"Generaci\xf3n de tests",flashcard_generation:"Generaci\xf3n de flashcards",mind_map_generation:"Generaci\xf3n de mapas mentales",ai_tutor_chat:"Chat con preparador IA",study_planning:"Planificaci\xf3n de estudios",summary_a1_a2:"Res\xfamenes A1 y A2"}[a=e]||a]},r)})})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Primeros pasos"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"1. Sube tus documentos"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Comienza subiendo tus materiales de estudio en formato PDF o TXT"})]}),(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"2. Genera contenido"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Crea tests, flashcards y mapas mentales basados en tus documentos"})]}),"free"!==a.plan&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"3. Chatea con la IA"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Haz preguntas espec\xedficas sobre tu temario a tu preparador IA"})]}),"pro"===a.plan&&(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"4. Planifica tu estudio"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Crea un plan de estudios personalizado con IA"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,d.jsx)("button",{onClick:function(){e.push("/app")},className:"flex-1 bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold",children:"Comenzar a usar OposiAI"}),(0,d.jsx)("button",{onClick:function(){return e.push("/app/profile")},className:"flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors",children:"Ver mi perfil"})]}),(0,d.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200 text-center",children:(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfNecesitas ayuda? ",(0,d.jsx)("a",{href:"/contact",className:"text-blue-600 hover:underline",children:"Contacta nuestro soporte"})]})})]})}):null}function m(){return(0,d.jsx)(i.Suspense,{fallback:(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}),children:(0,d.jsx)(u,{})})}},38345:(e,r,a)=>{Promise.resolve().then(a.bind(a,21038))},55564:(e,r,a)=>{"use strict";a.d(r,{N:()=>s.N,U:()=>s.U});var s=a(66618)},66618:(e,r,a)=>{"use strict";a.d(r,{N:()=>n,U:()=>t});var s=a(73728);function t(){return(0,s.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}var n=t()}},e=>{var r=r=>e(e.s=r);e.O(0,[5730,2390,8441,6891,7358],()=>r(38345)),_N_E=e.O()}]);
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx":
/*!**********************************************************************!*\
  !*** ./src/features/planificacion/components/PlanEstudiosViewer.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiClock,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _PlanCalendario__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PlanCalendario */ \"(app-pages-browser)/./src/features/planificacion/components/PlanCalendario.tsx\");\n/* harmony import */ var _TareasDelDia__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TareasDelDia */ \"(app-pages-browser)/./src/features/planificacion/components/TareasDelDia.tsx\");\n/* harmony import */ var _lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/dateUtils */ \"(app-pages-browser)/./src/lib/utils/dateUtils.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\", _this = undefined, _s1 = $RefreshSig$();\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n    if (!it) {\n        if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n            if (it) o = it;\n            var i = 0;\n            var F = function F() {};\n            return {\n                s: F,\n                n: function n() {\n                    if (i >= o.length) return {\n                        done: true\n                    };\n                    return {\n                        done: false,\n                        value: o[i++]\n                    };\n                },\n                e: function e(_e) {\n                    throw _e;\n                },\n                f: F\n            };\n        }\n        throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true, didErr = false, err;\n    return {\n        s: function s() {\n            it = it.call(o);\n        },\n        n: function n() {\n            var step = it.next();\n            normalCompletion = step.done;\n            return step;\n        },\n        e: function e(_e2) {\n            didErr = true;\n            err = _e2;\n        },\n        f: function f() {\n            try {\n                if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n            } finally{\n                if (didErr) throw err;\n            }\n        }\n    };\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\nvar PlanEstudiosViewer = function PlanEstudiosViewer(_ref) {\n    _s();\n    _s1();\n    var plan = _ref.plan, temarioId = _ref.temarioId;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]), progresoPlan = _useState[0], setProgresoPlan = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null), planId = _useState2[0], setPlanId = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true), isLoading = _useState3[0], setIsLoading = _useState3[1];\n    // Estados para el calendario\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null), fechaSeleccionada = _useState4[0], setFechaSeleccionada = _useState4[1];\n    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), calendarioModalAbierto = _useState5[0], setCalendarioModalAbierto = _useState5[1];\n    // Referencias para scroll automático\n    var semanaRefs = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"PlanEstudiosViewer.useEffect\": function() {\n            cargarProgreso();\n        }\n    }[\"PlanEstudiosViewer.useEffect\"], [\n        temarioId\n    ]);\n    var cargarProgreso = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {\n            var planActivo, _progreso;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        _context.next = 3;\n                        return (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__.obtenerPlanEstudiosActivoCliente)(temarioId);\n                    case 3:\n                        planActivo = _context.sent;\n                        if (planActivo) {\n                            _context.next = 10;\n                            break;\n                        }\n                        console.warn('No se encontró plan activo para el temario:', temarioId);\n                        setPlanId(null);\n                        setProgresoPlan([]);\n                        setIsLoading(false);\n                        return _context.abrupt(\"return\");\n                    case 10:\n                        setPlanId(planActivo.id);\n                        _context.next = 13;\n                        return (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__.obtenerProgresoPlaneCliente)(planActivo.id);\n                    case 13:\n                        _progreso = _context.sent;\n                        setProgresoPlan(_progreso);\n                        _context.next = 22;\n                        break;\n                    case 17:\n                        _context.prev = 17;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error al cargar progreso:', _context.t0);\n                        setPlanId(null);\n                        setProgresoPlan([]);\n                    case 22:\n                        _context.prev = 22;\n                        setIsLoading(false);\n                        return _context.finish(22);\n                    case 25:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    17,\n                    22,\n                    25\n                ]\n            ]);\n        }));\n        return function cargarProgreso() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var toggleTareaCompletada = /*#__PURE__*/ function() {\n        var _ref3 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(tarea, semanaNum, dia) {\n            var tareaExistente, nuevoEstado, exito;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee2$(_context2) {\n                while(1)switch(_context2.prev = _context2.next){\n                    case 0:\n                        if (planId) {\n                            _context2.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se pudo identificar el plan de estudios');\n                        return _context2.abrupt(\"return\");\n                    case 3:\n                        _context2.prev = 3;\n                        tareaExistente = progresoPlan.find(function(p) {\n                            return p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo;\n                        });\n                        nuevoEstado = !(tareaExistente !== null && tareaExistente !== void 0 && tareaExistente.completado);\n                        _context2.next = 8;\n                        return (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__.guardarProgresoTareaCliente)(planId, semanaNum, dia, tarea.titulo, tarea.tipo, nuevoEstado);\n                    case 8:\n                        exito = _context2.sent;\n                        if (exito) {\n                            setProgresoPlan(function(prev) {\n                                var index = prev.findIndex(function(p) {\n                                    return p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo;\n                                });\n                                if (index >= 0) {\n                                    var updated = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev);\n                                    updated[index] = _objectSpread(_objectSpread({}, updated[index]), {}, {\n                                        completado: nuevoEstado,\n                                        fecha_completado: nuevoEstado ? new Date().toISOString() : undefined\n                                    });\n                                    return updated;\n                                } else {\n                                    return [].concat((0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev), [\n                                        {\n                                            id: \"temp-\".concat(Date.now()),\n                                            plan_id: planId,\n                                            user_id: '',\n                                            semana_numero: semanaNum,\n                                            dia_nombre: dia,\n                                            tarea_titulo: tarea.titulo,\n                                            tarea_tipo: tarea.tipo,\n                                            completado: nuevoEstado,\n                                            fecha_completado: nuevoEstado ? new Date().toISOString() : undefined,\n                                            creado_en: new Date().toISOString(),\n                                            actualizado_en: new Date().toISOString()\n                                        }\n                                    ]);\n                                }\n                            });\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(nuevoEstado ? 'Tarea completada' : 'Tarea marcada como pendiente');\n                        } else {\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al actualizar el progreso');\n                        }\n                        _context2.next = 16;\n                        break;\n                    case 12:\n                        _context2.prev = 12;\n                        _context2.t0 = _context2[\"catch\"](3);\n                        console.error('Error al actualizar tarea:', _context2.t0);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al actualizar el progreso');\n                    case 16:\n                    case \"end\":\n                        return _context2.stop();\n                }\n            }, _callee2, null, [\n                [\n                    3,\n                    12\n                ]\n            ]);\n        }));\n        return function toggleTareaCompletada(_x, _x2, _x3) {\n            return _ref3.apply(this, arguments);\n        };\n    }();\n    var estaCompletada = function estaCompletada(tarea, semanaNum, dia) {\n        return progresoPlan.some(function(p) {\n            return p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo && p.completado;\n        });\n    };\n    // Manejar selección de fecha en el calendario\n    var handleFechaSeleccionada = function handleFechaSeleccionada(fecha) {\n        setFechaSeleccionada(fecha);\n        // Buscar la semana y día correspondiente para hacer scroll\n        if (plan && plan.semanas) {\n            var _iterator = _createForOfIteratorHelper(plan.semanas), _step;\n            try {\n                for(_iterator.s(); !(_step = _iterator.n()).done;){\n                    var semana = _step.value;\n                    var _iterator2 = _createForOfIteratorHelper(semana.dias || []), _step2;\n                    try {\n                        for(_iterator2.s(); !(_step2 = _iterator2.n()).done;){\n                            var dia = _step2.value;\n                            var fechaDia = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.calcularFechaDia)(semana.fechaInicio, dia.dia);\n                            if (fechaDia && (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fechaDia) === (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fecha)) {\n                                // Hacer scroll a la semana correspondiente\n                                var semanaRef = semanaRefs.current[semana.numero];\n                                if (semanaRef) {\n                                    semanaRef.scrollIntoView({\n                                        behavior: 'smooth',\n                                        block: 'start',\n                                        inline: 'nearest'\n                                    });\n                                }\n                                return;\n                            }\n                        }\n                    } catch (err) {\n                        _iterator2.e(err);\n                    } finally{\n                        _iterator2.f();\n                    }\n                }\n            } catch (err) {\n                _iterator.e(err);\n            } finally{\n                _iterator.f();\n            }\n        }\n    };\n    // Obtener tareas del día seleccionado\n    var obtenerTareasDelDiaSeleccionado = function obtenerTareasDelDiaSeleccionado() {\n        if (!fechaSeleccionada || !plan || !plan.semanas) return [];\n        var tareas = [];\n        var _iterator3 = _createForOfIteratorHelper(plan.semanas), _step3;\n        try {\n            var _loop = function _loop() {\n                var semana = _step3.value;\n                var _iterator4 = _createForOfIteratorHelper(semana.dias || []), _step4;\n                try {\n                    var _loop2 = function _loop2() {\n                        var dia = _step4.value;\n                        var fechaDia = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.calcularFechaDia)(semana.fechaInicio, dia.dia);\n                        if (fechaDia && (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fechaDia) === (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fechaSeleccionada)) {\n                            var _iterator5 = _createForOfIteratorHelper(dia.tareas || []), _step5;\n                            try {\n                                var _loop3 = function _loop3() {\n                                    var _progresoPlan$find;\n                                    var tarea = _step5.value;\n                                    var completada = estaCompletada(tarea, semana.numero, dia.dia);\n                                    tareas.push({\n                                        tarea: tarea,\n                                        semanaNumero: semana.numero,\n                                        diaNombre: dia.dia,\n                                        completada: completada,\n                                        fechaCompletado: (_progresoPlan$find = progresoPlan.find(function(p) {\n                                            return p.semana_numero === semana.numero && p.dia_nombre === dia.dia && p.tarea_titulo === tarea.titulo;\n                                        })) === null || _progresoPlan$find === void 0 ? void 0 : _progresoPlan$find.fecha_completado\n                                    });\n                                };\n                                for(_iterator5.s(); !(_step5 = _iterator5.n()).done;){\n                                    _loop3();\n                                }\n                            } catch (err) {\n                                _iterator5.e(err);\n                            } finally{\n                                _iterator5.f();\n                            }\n                        }\n                    };\n                    for(_iterator4.s(); !(_step4 = _iterator4.n()).done;){\n                        _loop2();\n                    }\n                } catch (err) {\n                    _iterator4.e(err);\n                } finally{\n                    _iterator4.f();\n                }\n            };\n            for(_iterator3.s(); !(_step3 = _iterator3.n()).done;){\n                _loop();\n            }\n        } catch (err) {\n            _iterator3.e(err);\n        } finally{\n            _iterator3.f();\n        }\n        return tareas;\n    };\n    // Manejar clic en tarea desde el panel del día\n    var handleTareaDelDiaClick = function handleTareaDelDiaClick(tareaDelDia) {\n        // Hacer scroll a la tarea específica en la lista principal\n        var semanaRef = semanaRefs.current[tareaDelDia.semanaNumero];\n        if (semanaRef) {\n            semanaRef.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n            });\n        }\n    };\n    var calcularProgreso = function calcularProgreso() {\n        if (!plan || !plan.semanas || !Array.isArray(plan.semanas)) {\n            return {\n                completadas: 0,\n                total: 0,\n                porcentaje: 0\n            };\n        }\n        var totalTareas = plan.semanas.reduce(function(acc, semana) {\n            if (!semana || !semana.dias || !Array.isArray(semana.dias)) {\n                return acc;\n            }\n            return acc + semana.dias.reduce(function(dayAcc, dia) {\n                if (!dia || !dia.tareas || !Array.isArray(dia.tareas)) {\n                    return dayAcc;\n                }\n                return dayAcc + dia.tareas.length;\n            }, 0);\n        }, 0);\n        var tareasCompletadasCount = progresoPlan.filter(function(p) {\n            return p.completado;\n        }).length;\n        return {\n            completadas: tareasCompletadasCount,\n            total: totalTareas,\n            porcentaje: totalTareas > 0 ? Math.round(tareasCompletadasCount / totalTareas * 100) : 0\n        };\n    };\n    var progreso = calcularProgreso();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                    className: \"ml-3 text-gray-600\",\n                    children: \"Cargando progreso...\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 7\n        }, _this);\n    }\n    if (!plan) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"No se pudo cargar el plan de estudios\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 7\n        }, _this);\n    }\n    if (!temarioId || temarioId.trim() === '') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"ID de temario no v\\xE1lido\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 7\n        }, _this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"Introducci\\xF3n\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: plan.introduccion || 'Introducción no disponible'\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Progreso General\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    progreso.porcentaje,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(progreso.porcentaje, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            progreso.completadas,\n                            \" de \",\n                            progreso.total,\n                            \" tareas completadas\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 7\n            }, _this),\n            plan.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiClock, {\n                                    className: \"w-5 h-5 text-blue-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Tiempo Total\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.tiempoTotalEstudio || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiBook, {\n                                    className: \"w-5 h-5 text-green-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Temas\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.numeroTemas || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTarget, {\n                                    className: \"w-5 h-5 text-purple-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Estudio Nuevo\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.duracionEstudioNuevo || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiRefreshCw, {\n                                    className: \"w-5 h-5 text-orange-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Repaso Final\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.duracionRepasoFinal || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 9\n            }, _this),\n            plan.semanas && plan.semanas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCalendar, {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, _this),\n                                    \"Cronograma Semanal\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"button\", {\n                                onClick: function onClick() {\n                                    return setCalendarioModalAbierto(true);\n                                },\n                                className: \"lg:hidden flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCalendar, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, _this),\n                                    \"Ver Calendario\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-12 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-8 space-y-6\",\n                                children: plan.semanas.map(function(semana, semanaIndex) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                        ref: function ref(el) {\n                                            semanaRefs.current[semana.numero] = el;\n                                        },\n                                        className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-6 py-4 border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: [\n                                                                    \"Semana \",\n                                                                    (semana === null || semana === void 0 ? void 0 : semana.numero) || 'N/A'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 359,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    (semana === null || semana === void 0 ? void 0 : semana.fechaInicio) || 'N/A',\n                                                                    \" - \",\n                                                                    (semana === null || semana === void 0 ? void 0 : semana.fechaFin) || 'N/A'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 362,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 mt-2\",\n                                                        children: (semana === null || semana === void 0 ? void 0 : semana.objetivoPrincipal) || 'Objetivo no especificado'\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                className: \"p-6 space-y-4\",\n                                                children: semana.dias && Array.isArray(semana.dias) ? semana.dias.map(function(dia, diaIndex) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-semibold text-gray-900\",\n                                                                        children: (dia === null || dia === void 0 ? void 0 : dia.dia) || 'Día no especificado'\n                                                                    }, void 0, false, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                                                        children: [\n                                                                            (dia === null || dia === void 0 ? void 0 : dia.horas) || 0,\n                                                                            \"h\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: dia.tareas && Array.isArray(dia.tareas) ? dia.tareas.map(function(tarea, tareaIndex) {\n                                                                    var completada = estaCompletada(tarea, semana.numero, dia.dia);\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start p-3 rounded-lg border transition-all cursor-pointer \".concat(completada ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200 hover:border-blue-300'),\n                                                                        onClick: function onClick() {\n                                                                            return toggleTareaCompletada(tarea, semana.numero, dia.dia);\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center \".concat(completada ? 'bg-green-500 border-green-500' : 'border-gray-300 hover:border-blue-400'),\n                                                                                children: completada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCheck, {\n                                                                                    className: \"w-3 h-3 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: _jsxFileName,\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 46\n                                                                                }, _this)\n                                                                            }, void 0, false, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 29\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h6\", {\n                                                                                        className: \"font-medium \".concat(completada ? 'text-green-800 line-through' : 'text-gray-900'),\n                                                                                        children: (tarea === null || tarea === void 0 ? void 0 : tarea.titulo) || 'Tarea sin título'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 401,\n                                                                                        columnNumber: 31\n                                                                                    }, _this),\n                                                                                    (tarea === null || tarea === void 0 ? void 0 : tarea.descripcion) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm mt-1 \".concat(completada ? 'text-green-700' : 'text-gray-600'),\n                                                                                        children: tarea.descripcion\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 405,\n                                                                                        columnNumber: 33\n                                                                                    }, _this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center mt-2 space-x-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs px-2 py-1 rounded \".concat((tarea === null || tarea === void 0 ? void 0 : tarea.tipo) === 'estudio' ? 'bg-blue-100 text-blue-800' : (tarea === null || tarea === void 0 ? void 0 : tarea.tipo) === 'repaso' ? 'bg-yellow-100 text-yellow-800' : (tarea === null || tarea === void 0 ? void 0 : tarea.tipo) === 'practica' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'),\n                                                                                                children: (tarea === null || tarea === void 0 ? void 0 : tarea.tipo) || 'general'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: _jsxFileName,\n                                                                                                lineNumber: 410,\n                                                                                                columnNumber: 33\n                                                                                            }, _this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs text-gray-500\",\n                                                                                                children: (tarea === null || tarea === void 0 ? void 0 : tarea.duracionEstimada) || 'No especificado'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: _jsxFileName,\n                                                                                                lineNumber: 418,\n                                                                                                columnNumber: 33\n                                                                                            }, _this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 409,\n                                                                                        columnNumber: 31\n                                                                                    }, _this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 29\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, tareaIndex, true, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"No hay tareas disponibles\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, diaIndex, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, _this);\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"No hay d\\xEDas disponibles\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, semanaIndex, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block lg:col-span-4 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_PlanCalendario__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        plan: plan,\n                                        progresoPlan: progresoPlan,\n                                        fechaSeleccionada: fechaSeleccionada,\n                                        onFechaSeleccionada: handleFechaSeleccionada,\n                                        className: \"sticky top-4\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_TareasDelDia__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        fecha: fechaSeleccionada,\n                                        tareas: obtenerTareasDelDiaSeleccionado(),\n                                        onTareaClick: handleTareaDelDiaClick,\n                                        className: \"sticky top-4\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 9\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-yellow-900 mb-2\",\n                        children: \"Estrategia de Repasos\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-800\",\n                        children: typeof plan.estrategiaRepasos === 'string' ? plan.estrategiaRepasos : plan.estrategiaRepasos && typeof plan.estrategiaRepasos === 'object' ? plan.estrategiaRepasos.descripcion || 'Estrategia de repasos no disponible' : 'Estrategia de repasos no disponible'\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-purple-900 mb-2\",\n                        children: \"Pr\\xF3ximos Pasos y Consejos\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-purple-800\",\n                        children: typeof plan.proximosPasos === 'string' ? plan.proximosPasos : plan.proximosPasos && typeof plan.proximosPasos === 'object' ? plan.proximosPasos.descripcion || 'Próximos pasos no disponibles' : 'Próximos pasos no disponibles'\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 5\n    }, _this);\n};\n_s(PlanEstudiosViewer, \"hDR33EA8x7HC3NNL9BCYsY06iXc=\");\n_c1 = PlanEstudiosViewer;\n_s1(PlanEstudiosViewer, \"95gGK/pdZBPcFFqIqOf8nwwScKA=\");\n_c = PlanEstudiosViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanEstudiosViewer);\nvar _c;\n$RefreshReg$(_c, \"PlanEstudiosViewer\");\nvar _c1;\n$RefreshReg$(_c1, \"PlanEstudiosViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9wbGFuaWZpY2FjaW9uL2NvbXBvbmVudHMvUGxhbkVzdHVkaW9zVmlld2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNrQztBQU05QztBQUVQO0FBQ007QUFDSjtBQUUyQjtBQUFDO0FBT3JFLHlCQUE4RCxTQUF4RG9CLGtCQUFxREEsQ0FBQUMsSUFBQSxFQUE0Qjs7SUFBQUMsRUFBQTtJQUFBLElBQXRCQyxJQUFJLEdBQUFGLElBQUEsQ0FBSkUsSUFBSSxFQUFFQyxTQUFTLEdBQUFILElBQUEsQ0FBVEcsU0FBUztJQUM5RSxJQUFBQyxTQUFBLEdBQXdDeEIsK0NBQVEsQ0FBeUIsRUFBRSxDQUFDLEVBQXJFeUIsWUFBWSxHQUFBRCxTQUFBLEtBQUVFLGVBQWUsR0FBQUYsU0FBQTtJQUNwQyxJQUFBRyxVQUFBLEdBQTRCM0IsK0NBQVEsQ0FBZ0IsSUFBSSxDQUFDLEVBQWxENEIsTUFBTSxHQUFBRCxVQUFBLEtBQUVFLFNBQVMsR0FBQUYsVUFBQTtJQUN4QixJQUFBRyxVQUFBLEdBQWtDOUIsK0NBQVEsQ0FBQyxJQUFJLENBQUMsRUFBekMrQixTQUFTLEdBQUFELFVBQUEsS0FBRUUsWUFBWSxHQUFBRixVQUFBO0lBRTlCO0lBQ0EsSUFBQUcsVUFBQSxHQUFrRGpDLCtDQUFRLENBQWMsSUFBSSxDQUFDLEVBQXRFa0MsaUJBQWlCLEdBQUFELFVBQUEsS0FBRUUsb0JBQW9CLEdBQUFGLFVBQUE7SUFDOUMsSUFBQUcsVUFBQSxHQUE0RHBDLCtDQUFRLENBQUMsS0FBSyxDQUFDLEVBQXBFcUMsc0JBQXNCLEdBQUFELFVBQUEsS0FBRUUseUJBQXlCLEdBQUFGLFVBQUE7SUFFeEQ7SUFDQSxJQUFNRyxVQUFVLEdBQUdyQyw2Q0FBTSxDQUEyQyxDQUFDLENBQUMsQ0FBQztJQUV2RUQsZ0RBQVM7d0NBQUMsWUFBTTtZQUNkdUMsY0FBYyxDQUFDLENBQUM7UUFDbEIsQ0FBQzt1Q0FBRTtRQUFDakIsU0FBUztLQUFDLENBQUM7SUFFZixJQUFNaUIsY0FBYztRQUFBLElBQUFDLEtBQUEsR0FBQUMsNEtBQUEsZUFBQUMsOElBQUEsQ0FBRyxTQUFBRSxRQUFBO1lBQUEsSUFBQUMsVUFBQSxFQUFBQyxTQUFBO1lBQUEsT0FBQUosOElBQUEsVUFBQU0sU0FBQUMsUUFBQTtnQkFBQSxlQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO29CQUFBO3dCQUFBRixRQUFBLENBQUFDLElBQUE7d0JBQUFELFFBQUEsQ0FBQUUsSUFBQTt3QkFBQSxPQUVNM0MscUdBQWdDLENBQUNjLFNBQVMsQ0FBQztvQkFBQTt3QkFBOUR1QixVQUFVLEdBQUFJLFFBQUEsQ0FBQUcsSUFBQTt3QkFBQSxJQUNYUCxVQUFVOzRCQUFBSSxRQUFBLENBQUFFLElBQUE7NEJBQUE7d0JBQUE7d0JBQ2JFLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLDZDQUE2QyxFQUFFaEMsU0FBUyxDQUFDO3dCQUN0RU0sU0FBUyxDQUFDLElBQUksQ0FBQzt3QkFDZkgsZUFBZSxDQUFDLEVBQUUsQ0FBQzt3QkFDbkJNLFlBQVksQ0FBQyxLQUFLLENBQUM7d0JBQUMsT0FBQWtCLFFBQUEsQ0FBQU0sTUFBQTtvQkFBQTt3QkFJdEIzQixTQUFTLENBQUNpQixVQUFVLENBQUNXLEVBQUUsQ0FBQzt3QkFBQ1AsUUFBQSxDQUFBRSxJQUFBO3dCQUFBLE9BQ0YxQyxnR0FBMkIsQ0FBQ29DLFVBQVUsQ0FBQ1csRUFBRSxDQUFDO29CQUFBO3dCQUEzREMsU0FBUSxHQUFBUixRQUFBLENBQUFHLElBQUE7d0JBQ2QzQixlQUFlLENBQUNnQyxTQUFRLENBQUM7d0JBQUNSLFFBQUEsQ0FBQUUsSUFBQTt3QkFBQTtvQkFBQTt3QkFBQUYsUUFBQSxDQUFBQyxJQUFBO3dCQUFBRCxRQUFBLENBQUFTLEVBQUEsR0FBQVQsUUFBQTt3QkFFMUJJLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLDJCQUEyQixFQUFBVixRQUFBLENBQUFTLEVBQU8sQ0FBQzt3QkFDakQ5QixTQUFTLENBQUMsSUFBSSxDQUFDO3dCQUNmSCxlQUFlLENBQUMsRUFBRSxDQUFDO29CQUFDO3dCQUFBd0IsUUFBQSxDQUFBQyxJQUFBO3dCQUVwQm5CLFlBQVksQ0FBQyxLQUFLLENBQUM7d0JBQUMsT0FBQWtCLFFBQUEsQ0FBQVcsTUFBQTtvQkFBQTtvQkFBQTt3QkFBQSxPQUFBWCxRQUFBLENBQUFZLElBQUE7Z0JBQUE7WUFBQSxHQUFBakIsT0FBQTtnQkFBQTtvQkFBQTtvQkFBQTtvQkFBQTtvQkFBQTtpQkFBQTthQUFBO1FBQUEsQ0FFdkI7UUFBQSxnQkFyQktMLGNBQWNBLENBQUE7WUFBQSxPQUFBQyxLQUFBLENBQUFzQixLQUFBLE9BQUFDLFNBQUE7UUFBQTtJQUFBLEdBcUJuQjtJQUVELElBQU1DLHFCQUFxQjtRQUFBLElBQUFDLEtBQUEsR0FBQXhCLDRLQUFBLGVBQUFDLDhJQUFBLENBQUcsU0FBQXdCLFNBQU9DLEtBQWdCLEVBQUVDLFNBQWlCLEVBQUVDLEdBQVc7WUFBQSxJQUFBQyxjQUFBLEVBQUFDLFdBQUEsRUFBQUMsS0FBQTtZQUFBLE9BQUE5Qiw4SUFBQSxVQUFBK0IsVUFBQUMsU0FBQTtnQkFBQSxlQUFBQSxTQUFBLENBQUF4QixJQUFBLEdBQUF3QixTQUFBLENBQUF2QixJQUFBO29CQUFBO3dCQUFBLElBQzlFeEIsTUFBTTs0QkFBQStDLFNBQUEsQ0FBQXZCLElBQUE7NEJBQUE7d0JBQUE7d0JBQ1R4QyxrREFBSyxDQUFDZ0QsS0FBSyxDQUFDLDRDQUE0QyxDQUFDO3dCQUFDLE9BQUFlLFNBQUEsQ0FBQW5CLE1BQUE7b0JBQUE7d0JBQUFtQixTQUFBLENBQUF4QixJQUFBO3dCQUtwRG9CLGNBQWMsR0FBRzlDLFlBQVksQ0FBQ21ELElBQUksQ0FBQyxTQUFBQyxDQUFDOzRCQUFBLE9BQ3hDQSxDQUFDLENBQUNDLGFBQWEsS0FBS1QsU0FBUyxJQUM3QlEsQ0FBQyxDQUFDRSxVQUFVLEtBQUtULEdBQUcsSUFDcEJPLENBQUMsQ0FBQ0csWUFBWSxLQUFLWixLQUFLLENBQUNhLE1BQU07d0JBQUEsQ0FDakMsQ0FBQzt3QkFFS1QsV0FBVyxHQUFHLEVBQUNELGNBQWMsYUFBZEEsY0FBYyxlQUFkQSxjQUFjLENBQUVXLFVBQUFBO3dCQUFVUCxTQUFBLENBQUF2QixJQUFBO3dCQUFBLE9BRTNCekMsZ0dBQTJCLENBQzdDaUIsTUFBTSxFQUNOeUMsU0FBUyxFQUNUQyxHQUFHLEVBQ0hGLEtBQUssQ0FBQ2EsTUFBTSxFQUNaYixLQUFLLENBQUNlLElBQUksRUFDVlgsV0FDRixDQUFDO29CQUFBO3dCQVBLQyxLQUFLLEdBQUFFLFNBQUEsQ0FBQXRCLElBQUE7d0JBU1gsSUFBSW9CLEtBQUssRUFBRTs0QkFDVC9DLGVBQWUsQ0FBQyxTQUFBeUIsSUFBSSxFQUFJO2dDQUN0QixJQUFNaUMsS0FBSyxHQUFHakMsSUFBSSxDQUFDa0MsU0FBUyxDQUFDLFNBQUFSLENBQUM7b0NBQUEsT0FDNUJBLENBQUMsQ0FBQ0MsYUFBYSxLQUFLVCxTQUFTLElBQzdCUSxDQUFDLENBQUNFLFVBQVUsS0FBS1QsR0FBRyxJQUNwQk8sQ0FBQyxDQUFDRyxZQUFZLEtBQUtaLEtBQUssQ0FBQ2EsTUFBTTtnQ0FBQSxDQUNqQyxDQUFDO2dDQUVELElBQUlHLEtBQUssSUFBSSxDQUFDLEVBQUU7b0NBQ2QsSUFBTUUsT0FBTyxHQUFBQyw2S0FBQSxDQUFPcEMsSUFBSSxDQUFDO29DQUN6Qm1DLE9BQU8sQ0FBQ0YsS0FBSyxDQUFDLEdBQUFJLGFBQUEsQ0FBQUEsYUFBQSxLQUNURixPQUFPLENBQUNGLEtBQUssQ0FBQzt3Q0FDakJGLFVBQVUsRUFBRVYsV0FBVzt3Q0FDdkJpQixnQkFBZ0IsRUFBRWpCLFdBQVcsR0FBRyxJQUFJa0IsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsR0FBR0M7b0NBQVMsRUFDckU7b0NBQ0QsT0FBT04sT0FBTztnQ0FDaEIsQ0FBQyxNQUFNO29DQUNMLFVBQUFPLE1BQUEsQ0FBQU4sNktBQUEsQ0FBV3BDLElBQUk7d0NBQUU7NENBQ2ZNLEVBQUUsVUFBQW9DLE1BQUEsQ0FBVUgsSUFBSSxDQUFDSSxHQUFHLENBQUMsQ0FBQyxDQUFFOzRDQUN4QkMsT0FBTyxFQUFFbkUsTUFBTTs0Q0FDZm9FLE9BQU8sRUFBRSxFQUFFOzRDQUNYbEIsYUFBYSxFQUFFVCxTQUFTOzRDQUN4QlUsVUFBVSxFQUFFVCxHQUFHOzRDQUNmVSxZQUFZLEVBQUVaLEtBQUssQ0FBQ2EsTUFBTTs0Q0FDMUJnQixVQUFVLEVBQUU3QixLQUFLLENBQUNlLElBQUk7NENBQ3RCRCxVQUFVLEVBQUVWLFdBQVc7NENBQ3ZCaUIsZ0JBQWdCLEVBQUVqQixXQUFXLEdBQUcsSUFBSWtCLElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQyxDQUFDLEdBQUdDLFNBQVM7NENBQ3BFTSxTQUFTLEVBQUUsSUFBSVIsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUM7NENBQ25DUSxjQUFjLEVBQUUsSUFBSVQsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDO3dDQUN6QyxDQUFDO3FDQUFBO2dDQUNIOzRCQUNGLENBQUMsQ0FBQzs0QkFFRi9FLGtEQUFLLENBQUN3RixPQUFPLENBQUM1QixXQUFXLEdBQUcsa0JBQWtCLEdBQUcsOEJBQThCLENBQUM7d0JBQ2xGLENBQUMsTUFBTTs0QkFDTDVELGtEQUFLLENBQUNnRCxLQUFLLENBQUMsaUNBQWlDLENBQUM7d0JBQ2hEO3dCQUFDZSxTQUFBLENBQUF2QixJQUFBO3dCQUFBO29CQUFBO3dCQUFBdUIsU0FBQSxDQUFBeEIsSUFBQTt3QkFBQXdCLFNBQUEsQ0FBQWhCLEVBQUEsR0FBQWdCLFNBQUE7d0JBRURyQixPQUFPLENBQUNNLEtBQUssQ0FBQyw0QkFBNEIsRUFBQWUsU0FBQSxDQUFBaEIsRUFBTyxDQUFDO3dCQUNsRC9DLGtEQUFLLENBQUNnRCxLQUFLLENBQUMsaUNBQWlDLENBQUM7b0JBQUM7b0JBQUE7d0JBQUEsT0FBQWUsU0FBQSxDQUFBYixJQUFBO2dCQUFBO1lBQUEsR0FBQUssUUFBQTtnQkFBQTtvQkFBQTtvQkFBQTtpQkFBQTthQUFBO1FBQUEsQ0FFbEQ7UUFBQSxnQkFqRUtGLHFCQUFxQkEsQ0FBQW9DLEVBQUEsRUFBQUMsR0FBQSxFQUFBQyxHQUFBO1lBQUEsT0FBQXJDLEtBQUEsQ0FBQUgsS0FBQSxPQUFBQyxTQUFBO1FBQUE7SUFBQSxHQWlFMUI7SUFFRCxJQUFNd0MsY0FBYyxHQUFHLFNBQWpCQSxjQUFjQSxDQUFJcEMsS0FBZ0IsRUFBRUMsU0FBaUIsRUFBRUMsR0FBVyxFQUFjO1FBQ3BGLE9BQU83QyxZQUFZLENBQUNnRixJQUFJLENBQUMsU0FBQTVCLENBQUM7WUFBQSxPQUN4QkEsQ0FBQyxDQUFDQyxhQUFhLEtBQUtULFNBQVMsSUFDN0JRLENBQUMsQ0FBQ0UsVUFBVSxLQUFLVCxHQUFHLElBQ3BCTyxDQUFDLENBQUNHLFlBQVksS0FBS1osS0FBSyxDQUFDYSxNQUFNLElBQy9CSixDQUFDLENBQUNLLFVBQVU7UUFBQSxDQUNkLENBQUM7SUFDSCxDQUFDO0lBRUQ7SUFDQSxJQUFNd0IsdUJBQXVCLEdBQUcsU0FBMUJBLHVCQUF1QkEsQ0FBSUMsS0FBVyxFQUFLO1FBQy9DeEUsb0JBQW9CLENBQUN3RSxLQUFLLENBQUM7UUFFM0I7UUFDQSxJQUFJckYsSUFBSSxJQUFJQSxJQUFJLENBQUNzRixPQUFPLEVBQUU7WUFBQSxJQUFBQyxTQUFBLEdBQUFDLDBCQUFBLENBQ0h4RixJQUFJLENBQUNzRixPQUFPLEdBQUFHLEtBQUE7WUFBQTtnQkFBakMsSUFBQUYsU0FBQSxDQUFBRyxDQUFBLE1BQUFELEtBQUEsR0FBQUYsU0FBQSxDQUFBSSxDQUFBLElBQUFDLElBQUEsRUFBbUM7b0JBQUEsSUFBeEJDLE1BQU0sR0FBQUosS0FBQSxDQUFBSyxLQUFBO29CQUFBLElBQUFDLFVBQUEsR0FBQVAsMEJBQUEsQ0FDR0ssTUFBTSxDQUFDRyxJQUFJLElBQUksRUFBRSxHQUFBQyxNQUFBO29CQUFBO3dCQUFuQyxJQUFBRixVQUFBLENBQUFMLENBQUEsTUFBQU8sTUFBQSxHQUFBRixVQUFBLENBQUFKLENBQUEsSUFBQUMsSUFBQSxFQUFxQzs0QkFBQSxJQUExQjVDLEdBQUcsR0FBQWlELE1BQUEsQ0FBQUgsS0FBQTs0QkFDWixJQUFNSSxRQUFRLEdBQUd6RyxzRUFBZ0IsQ0FBQ29HLE1BQU0sQ0FBQ00sV0FBVyxFQUFFbkQsR0FBRyxDQUFDQSxHQUFHLENBQUM7NEJBQzlELElBQUlrRCxRQUFRLElBQUl4RyxnRUFBVSxDQUFDd0csUUFBUSxDQUFDLEtBQUt4RyxnRUFBVSxDQUFDMkYsS0FBSyxDQUFDLEVBQUU7Z0NBQzFEO2dDQUNBLElBQU1lLFNBQVMsR0FBR25GLFVBQVUsQ0FBQ29GLE9BQU8sQ0FBQ1IsTUFBTSxDQUFDUyxNQUFNLENBQUM7Z0NBQ25ELElBQUlGLFNBQVMsRUFBRTtvQ0FDYkEsU0FBUyxDQUFDRyxjQUFjLENBQUM7d0NBQ3ZCQyxRQUFRLEVBQUUsUUFBUTt3Q0FDbEJDLEtBQUssRUFBRSxPQUFPO3dDQUNkQyxNQUFNLEVBQUU7b0NBQ1YsQ0FBQyxDQUFDO2dDQUNKO2dDQUNBOzRCQUNGO3dCQUNGO29CQUFDLFNBQUFDLEdBQUE7d0JBQUFaLFVBQUEsQ0FBQWEsQ0FBQSxDQUFBRCxHQUFBO29CQUFBO3dCQUFBWixVQUFBLENBQUFjLENBQUE7b0JBQUE7Z0JBQ0g7WUFBQyxTQUFBRixHQUFBO2dCQUFBcEIsU0FBQSxDQUFBcUIsQ0FBQSxDQUFBRCxHQUFBO1lBQUE7Z0JBQUFwQixTQUFBLENBQUFzQixDQUFBO1lBQUE7UUFDSDtJQUNGLENBQUM7SUFFRDtJQUNBLElBQU1DLCtCQUErQixHQUFHLFNBQWxDQSwrQkFBK0JBLENBQUEsRUFBUztRQUM1QyxJQUFJLENBQUNsRyxpQkFBaUIsSUFBSSxDQUFDWixJQUFJLElBQUksQ0FBQ0EsSUFBSSxDQUFDc0YsT0FBTyxFQUFFLE9BQU8sRUFBRTtRQUUzRCxJQUFNeUIsTUFBTSxHQUFHLEVBQUU7UUFBQyxJQUFBQyxVQUFBLEdBQUF4QiwwQkFBQSxDQUNHeEYsSUFBSSxDQUFDc0YsT0FBTyxHQUFBMkIsTUFBQTtRQUFBO1lBQUEsSUFBQUMsS0FBQSxZQUFBQSxNQUFBLEVBQUU7Z0JBQUEsSUFBeEJyQixNQUFNLEdBQUFvQixNQUFBLENBQUFuQixLQUFBO2dCQUFBLElBQUFxQixVQUFBLEdBQUEzQiwwQkFBQSxDQUNHSyxNQUFNLENBQUNHLElBQUksSUFBSSxFQUFFLEdBQUFvQixNQUFBO2dCQUFBO29CQUFBLElBQUFDLE1BQUEsWUFBQUEsT0FBQSxFQUFFO3dCQUFBLElBQTFCckUsR0FBRyxHQUFBb0UsTUFBQSxDQUFBdEIsS0FBQTt3QkFDWixJQUFNSSxRQUFRLEdBQUd6RyxzRUFBZ0IsQ0FBQ29HLE1BQU0sQ0FBQ00sV0FBVyxFQUFFbkQsR0FBRyxDQUFDQSxHQUFHLENBQUM7d0JBQzlELElBQUlrRCxRQUFRLElBQUl4RyxnRUFBVSxDQUFDd0csUUFBUSxDQUFDLEtBQUt4RyxnRUFBVSxDQUFDa0IsaUJBQWlCLENBQUMsRUFBRTs0QkFBQSxJQUFBMEcsVUFBQSxHQUFBOUIsMEJBQUEsQ0FDbER4QyxHQUFHLENBQUMrRCxNQUFNLElBQUksRUFBRSxHQUFBUSxNQUFBOzRCQUFBO2dDQUFBLElBQUFDLE1BQUEsWUFBQUEsT0FBQSxFQUFFO29DQUFBLElBQUFDLGtCQUFBO29DQUFBLElBQTNCM0UsS0FBSyxHQUFBeUUsTUFBQSxDQUFBekIsS0FBQTtvQ0FDZCxJQUFNNEIsVUFBVSxHQUFHeEMsY0FBYyxDQUFDcEMsS0FBSyxFQUFFK0MsTUFBTSxDQUFDUyxNQUFNLEVBQUV0RCxHQUFHLENBQUNBLEdBQUcsQ0FBQztvQ0FDaEUrRCxNQUFNLENBQUNZLElBQUksQ0FBQzt3Q0FDVjdFLEtBQUssRUFBTEEsS0FBSzt3Q0FDTDhFLFlBQVksRUFBRS9CLE1BQU0sQ0FBQ1MsTUFBTTt3Q0FDM0J1QixTQUFTLEVBQUU3RSxHQUFHLENBQUNBLEdBQUc7d0NBQ2xCMEUsVUFBVSxFQUFWQSxVQUFVO3dDQUNWSSxlQUFlLHdCQUFFM0gsWUFBWSxDQUFDbUQsSUFBSSxDQUFDLFNBQUFDLENBQUM7NENBQUEsT0FDbENBLENBQUMsQ0FBQ0MsYUFBYSxLQUFLcUMsTUFBTSxDQUFDUyxNQUFNLElBQ2pDL0MsQ0FBQyxDQUFDRSxVQUFVLEtBQUtULEdBQUcsQ0FBQ0EsR0FBRyxJQUN4Qk8sQ0FBQyxDQUFDRyxZQUFZLEtBQUtaLEtBQUssQ0FBQ2EsTUFBTTt3Q0FBQSxFQUNqQyxDQUFDLGFBQUE4RCxrQkFBQSx1QkFKZ0JBLGtCQUFBLENBSWR0RCxnQkFBQUE7b0NBQ0wsQ0FBQyxDQUFDO2dDQUNKLENBQUM7Z0NBYkQsSUFBQW1ELFVBQUEsQ0FBQTVCLENBQUEsTUFBQTZCLE1BQUEsR0FBQUQsVUFBQSxDQUFBM0IsQ0FBQSxJQUFBQyxJQUFBO29DQUFBNEIsTUFBQTtnQ0FBQTs0QkFhQyxTQUFBYixHQUFBO2dDQUFBVyxVQUFBLENBQUFWLENBQUEsQ0FBQUQsR0FBQTs0QkFBQTtnQ0FBQVcsVUFBQSxDQUFBVCxDQUFBOzRCQUFBO3dCQUNIO29CQUNGLENBQUM7b0JBbEJELElBQUFNLFVBQUEsQ0FBQXpCLENBQUEsZUFBQXlCLFVBQUEsQ0FBQXhCLENBQUEsSUFBQUMsSUFBQTt3QkFBQXlCLE1BQUE7b0JBQUE7Z0JBa0JDLFNBQUFWLEdBQUE7b0JBQUFRLFVBQUEsQ0FBQVAsQ0FBQSxDQUFBRCxHQUFBO2dCQUFBO29CQUFBUSxVQUFBLENBQUFOLENBQUE7Z0JBQUE7WUFDSCxDQUFDO1lBcEJELElBQUFHLFVBQUEsQ0FBQXRCLENBQUEsTUFBQXVCLE1BQUEsR0FBQUQsVUFBQSxDQUFBckIsQ0FBQSxJQUFBQyxJQUFBO2dCQUFBc0IsS0FBQTtZQUFBO1FBb0JDLFNBQUFQLEdBQUE7WUFBQUssVUFBQSxDQUFBSixDQUFBLENBQUFELEdBQUE7UUFBQTtZQUFBSyxVQUFBLENBQUFILENBQUE7UUFBQTtRQUNELE9BQU9FLE1BQU07SUFDZixDQUFDO0lBRUQ7SUFDQSxJQUFNZ0Isc0JBQXNCLEdBQUcsU0FBekJBLHNCQUFzQkEsQ0FBSUMsV0FBZ0IsRUFBSztRQUNuRDtRQUNBLElBQU01QixTQUFTLEdBQUduRixVQUFVLENBQUNvRixPQUFPLENBQUMyQixXQUFXLENBQUNKLFlBQVksQ0FBQztRQUM5RCxJQUFJeEIsU0FBUyxFQUFFO1lBQ2JBLFNBQVMsQ0FBQ0csY0FBYyxDQUFDO2dCQUN2QkMsUUFBUSxFQUFFLFFBQVE7Z0JBQ2xCQyxLQUFLLEVBQUUsUUFBUTtnQkFDZkMsTUFBTSxFQUFFO1lBQ1YsQ0FBQyxDQUFDO1FBQ0o7SUFDRixDQUFDO0lBRUQsSUFBTXVCLGdCQUFnQixHQUFHLFNBQW5CQSxnQkFBZ0JBLENBQUEsRUFBcUU7UUFDekYsSUFBSSxDQUFDakksSUFBSSxJQUFJLENBQUNBLElBQUksQ0FBQ3NGLE9BQU8sSUFBSSxDQUFDNEMsS0FBSyxDQUFDQyxPQUFPLENBQUNuSSxJQUFJLENBQUNzRixPQUFPLENBQUMsRUFBRTtZQUMxRCxPQUFPO2dCQUFFOEMsV0FBVyxFQUFFLENBQUM7Z0JBQUVDLEtBQUssRUFBRSxDQUFDO2dCQUFFQyxVQUFVLEVBQUU7WUFBRSxDQUFDO1FBQ3BEO1FBRUEsSUFBTUMsV0FBVyxHQUFHdkksSUFBSSxDQUFDc0YsT0FBTyxDQUFDa0QsTUFBTSxDQUFDLFNBQUNDLEdBQUcsRUFBRTVDLE1BQU0sRUFBSztZQUN2RCxJQUFJLENBQUNBLE1BQU0sSUFBSSxDQUFDQSxNQUFNLENBQUNHLElBQUksSUFBSSxDQUFDa0MsS0FBSyxDQUFDQyxPQUFPLENBQUN0QyxNQUFNLENBQUNHLElBQUksQ0FBQyxFQUFFO2dCQUMxRCxPQUFPeUMsR0FBRztZQUNaO1lBQ0EsT0FBT0EsR0FBRyxHQUFHNUMsTUFBTSxDQUFDRyxJQUFJLENBQUN3QyxNQUFNLENBQUMsU0FBQ0UsTUFBTSxFQUFFMUYsR0FBRyxFQUFLO2dCQUMvQyxJQUFJLENBQUNBLEdBQUcsSUFBSSxDQUFDQSxHQUFHLENBQUMrRCxNQUFNLElBQUksQ0FBQ21CLEtBQUssQ0FBQ0MsT0FBTyxDQUFDbkYsR0FBRyxDQUFDK0QsTUFBTSxDQUFDLEVBQUU7b0JBQ3JELE9BQU8yQixNQUFNO2dCQUNmO2dCQUNBLE9BQU9BLE1BQU0sR0FBRzFGLEdBQUcsQ0FBQytELE1BQU0sQ0FBQzRCLE1BQU07WUFDbkMsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUNQLENBQUMsRUFBRSxDQUFDLENBQUM7UUFFTCxJQUFNQyxzQkFBc0IsR0FBR3pJLFlBQVksQ0FBQzBJLE1BQU0sQ0FBQyxTQUFBdEYsQ0FBQztZQUFBLE9BQUlBLENBQUMsQ0FBQ0ssVUFBVTtRQUFBLEVBQUMsQ0FBQytFLE1BQU07UUFFNUUsT0FBTztZQUNMUCxXQUFXLEVBQUVRLHNCQUFzQjtZQUNuQ1AsS0FBSyxFQUFFRSxXQUFXO1lBQ2xCRCxVQUFVLEVBQUVDLFdBQVcsR0FBRyxDQUFDLEdBQUdPLElBQUksQ0FBQ0MsS0FBSyxDQUFFSCxzQkFBc0IsR0FBR0wsV0FBVyxHQUFJLEdBQUcsQ0FBQyxHQUFHO1FBQzNGLENBQUM7SUFDSCxDQUFDO0lBRUQsSUFBTW5HLFFBQVEsR0FBRzZGLGdCQUFnQixDQUFDLENBQUM7SUFFbkMsSUFBSXhILFNBQVMsRUFBRTtRQUNiLHFCQUNFYiw4REFBQTtZQUFLb0osU0FBUyxFQUFDLHVDQUF1QztZQUFBQyxRQUFBO2dCQUFBLGNBQ3BEckosOERBQUE7b0JBQUtvSixTQUFTLEVBQUM7Z0JBQThEO29CQUFBRSxRQUFBLEVBQUFDLFlBQUE7b0JBQUFDLFVBQUE7b0JBQUFDLFlBQUE7Z0JBQUEsR0FBQUMsS0FBTSxDQUFDO2dCQUFBLGNBQ3BGMUosOERBQUE7b0JBQU1vSixTQUFTLEVBQUMsb0JBQW9CO29CQUFBQyxRQUFBLEVBQUM7Z0JBQW9CO29CQUFBQyxRQUFBLEVBQUFDLFlBQUE7b0JBQUFDLFVBQUE7b0JBQUFDLFlBQUE7Z0JBQUEsR0FBQUMsS0FBTSxDQUFDO2FBQUE7UUFBQTtZQUFBSixRQUFBLEVBQUFDLFlBQUE7WUFBQUMsVUFBQTtZQUFBQyxZQUFBO1FBQUEsR0FBQUMsS0FDN0QsQ0FBQztJQUVWO0lBRUEsSUFBSSxDQUFDdEosSUFBSSxFQUFFO1FBQ1QscUJBQ0VKLDhEQUFBO1lBQUtvSixTQUFTLEVBQUMsdUNBQXVDO1lBQUFDLFFBQUEsZ0JBQ3BEckosOERBQUE7Z0JBQUtvSixTQUFTLEVBQUMsYUFBYTtnQkFBQUMsUUFBQSxnQkFDMUJySiw4REFBQTtvQkFBR29KLFNBQVMsRUFBQyxlQUFlO29CQUFBQyxRQUFBLEVBQUM7Z0JBQXFDO29CQUFBQyxRQUFBLEVBQUFDLFlBQUE7b0JBQUFDLFVBQUE7b0JBQUFDLFlBQUE7Z0JBQUEsR0FBQUMsS0FBRztZQUFDO2dCQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0JBQUFDLFVBQUE7Z0JBQUFDLFlBQUE7WUFBQSxHQUFBQyxLQUNuRTtRQUFDO1lBQUFKLFFBQUEsRUFBQUMsWUFBQTtZQUFBQyxVQUFBO1lBQUFDLFlBQUE7UUFBQSxHQUFBQyxLQUNILENBQUM7SUFFVjtJQUVBLElBQUksQ0FBQ3JKLFNBQVMsSUFBSUEsU0FBUyxDQUFDc0osSUFBSSxDQUFDLENBQUMsS0FBSyxFQUFFLEVBQUU7UUFDekMscUJBQ0UzSiw4REFBQTtZQUFLb0osU0FBUyxFQUFDLHVDQUF1QztZQUFBQyxRQUFBLGdCQUNwRHJKLDhEQUFBO2dCQUFLb0osU0FBUyxFQUFDLGFBQWE7Z0JBQUFDLFFBQUEsZ0JBQzFCckosOERBQUE7b0JBQUdvSixTQUFTLEVBQUMsZUFBZTtvQkFBQUMsUUFBQSxFQUFDO2dCQUF1QjtvQkFBQUMsUUFBQSxFQUFBQyxZQUFBO29CQUFBQyxVQUFBO29CQUFBQyxZQUFBO2dCQUFBLEdBQUFDLEtBQUc7WUFBQztnQkFBQUosUUFBQSxFQUFBQyxZQUFBO2dCQUFBQyxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsR0FBQUMsS0FDckQ7UUFBQztZQUFBSixRQUFBLEVBQUFDLFlBQUE7WUFBQUMsVUFBQTtZQUFBQyxZQUFBO1FBQUEsR0FBQUMsS0FDSCxDQUFDO0lBRVY7SUFFQSxxQkFDRTFKLDhEQUFBO1FBQUtvSixTQUFTLEVBQUMsV0FBVztRQUFBQyxRQUFBO1lBQUEsY0FFeEJySiw4REFBQTtnQkFBS29KLFNBQVMsRUFBQyxrREFBa0Q7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDL0RySiw4REFBQTt3QkFBSW9KLFNBQVMsRUFBQyxrQ0FBa0M7d0JBQUFDLFFBQUEsRUFBQztvQkFBWTt3QkFBQUMsUUFBQSxFQUFBQyxZQUFBO3dCQUFBQyxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLEdBQUFDLEtBQUksQ0FBQztvQkFBQSxjQUNsRTFKLDhEQUFBO3dCQUFHb0osU0FBUyxFQUFDLGVBQWU7d0JBQUFDLFFBQUEsRUFBRWpKLElBQUksQ0FBQ3dKLFlBQVksSUFBSTtvQkFBNEI7d0JBQUFOLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUFJLENBQUM7aUJBQUE7WUFBQTtnQkFBQUosUUFBQSxFQUFBQyxZQUFBO2dCQUFBQyxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsR0FBQUMsS0FDakYsQ0FBQztZQUFBLGNBR04xSiw4REFBQTtnQkFBS29KLFNBQVMsRUFBQyxrRkFBa0Y7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDL0ZySiw4REFBQTt3QkFBS29KLFNBQVMsRUFBQyx3Q0FBd0M7d0JBQUFDLFFBQUE7NEJBQUEsY0FDckRySiw4REFBQTtnQ0FBSW9KLFNBQVMsRUFBQyw2QkFBNkI7Z0NBQUFDLFFBQUEsRUFBQzs0QkFBZ0I7Z0NBQUFDLFFBQUEsRUFBQUMsWUFBQTtnQ0FBQUMsVUFBQTtnQ0FBQUMsWUFBQTs0QkFBQSxHQUFBQyxLQUFJLENBQUM7NEJBQUEsY0FDakUxSiw4REFBQTtnQ0FBTW9KLFNBQVMsRUFBQyxtQ0FBbUM7Z0NBQUFDLFFBQUE7b0NBQUU3RyxRQUFRLENBQUNrRyxVQUFVO29DQUFDLEdBQUM7aUNBQUE7NEJBQUE7Z0NBQUFZLFFBQUEsRUFBQUMsWUFBQTtnQ0FBQUMsVUFBQTtnQ0FBQUMsWUFBQTs0QkFBQSxHQUFBQyxLQUFNLENBQUM7eUJBQUE7b0JBQUE7d0JBQUFKLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUM5RSxDQUFDO29CQUFBLGNBQ04xSiw4REFBQTt3QkFBS29KLFNBQVMsRUFBQywwQ0FBMEM7d0JBQUFDLFFBQUEsZ0JBQ3ZEckosOERBQUE7NEJBQ0VvSixTQUFTLEVBQUMsMEZBQTBGOzRCQUNwR1MsS0FBSyxFQUFFO2dDQUFFQyxLQUFLLEtBQUFuRixNQUFBLENBQUtuQyxRQUFRLENBQUNrRyxVQUFVOzRCQUFJO3dCQUFFOzRCQUFBWSxRQUFBLEVBQUFDLFlBQUE7NEJBQUFDLFVBQUE7NEJBQUFDLFlBQUE7d0JBQUEsR0FBQUMsS0FDeEM7b0JBQUM7d0JBQUFKLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUNKLENBQUM7b0JBQUEsY0FDTjFKLDhEQUFBO3dCQUFHb0osU0FBUyxFQUFDLHVCQUF1Qjt3QkFBQUMsUUFBQTs0QkFDakM3RyxRQUFRLENBQUNnRyxXQUFXOzRCQUFDLE1BQUk7NEJBQUNoRyxRQUFRLENBQUNpRyxLQUFLOzRCQUFDLHFCQUM1Qzt5QkFBQTtvQkFBQTt3QkFBQWEsUUFBQSxFQUFBQyxZQUFBO3dCQUFBQyxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLEdBQUFDLEtBQUcsQ0FBQztpQkFBQTtZQUFBO2dCQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0JBQUFDLFVBQUE7Z0JBQUFDLFlBQUE7WUFBQSxHQUFBQyxLQUNELENBQUM7WUFHTHRKLElBQUksQ0FBQzJKLE9BQU8sa0JBQ1gvSiw4REFBQTtnQkFBS29KLFNBQVMsRUFBQyxzREFBc0Q7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDbkVySiw4REFBQTt3QkFBS29KLFNBQVMsRUFBQyxnREFBZ0Q7d0JBQUFDLFFBQUEsZ0JBQzdEckosOERBQUE7NEJBQUtvSixTQUFTLEVBQUMsbUJBQW1COzRCQUFBQyxRQUFBO2dDQUFBLGNBQ2hDckosOERBQUEsQ0FBQ2Qsa0lBQU87b0NBQUNrSyxTQUFTLEVBQUM7Z0NBQTRCO29DQUFBRSxRQUFBLEVBQUFDLFlBQUE7b0NBQUFDLFVBQUE7b0NBQUFDLFlBQUE7Z0NBQUEsR0FBQUMsS0FBRSxDQUFDO2dDQUFBLGNBQ2xEMUosOERBQUE7b0NBQUFxSixRQUFBO3dDQUFBLGNBQ0VySiw4REFBQTs0Q0FBR29KLFNBQVMsRUFBQyx1QkFBdUI7NENBQUFDLFFBQUEsRUFBQzt3Q0FBWTs0Q0FBQUMsUUFBQSxFQUFBQyxZQUFBOzRDQUFBQyxVQUFBOzRDQUFBQyxZQUFBO3dDQUFBLEdBQUFDLEtBQUcsQ0FBQzt3Q0FBQSxjQUNyRDFKLDhEQUFBOzRDQUFHb0osU0FBUyxFQUFDLGVBQWU7NENBQUFDLFFBQUEsRUFBRWpKLElBQUksQ0FBQzJKLE9BQU8sQ0FBQ0Msa0JBQWtCLElBQUk7d0NBQWU7NENBQUFWLFFBQUEsRUFBQUMsWUFBQTs0Q0FBQUMsVUFBQTs0Q0FBQUMsWUFBQTt3Q0FBQSxHQUFBQyxLQUFJLENBQUM7cUNBQUE7Z0NBQUE7b0NBQUFKLFFBQUEsRUFBQUMsWUFBQTtvQ0FBQUMsVUFBQTtvQ0FBQUMsWUFBQTtnQ0FBQSxHQUFBQyxLQUNsRixDQUFDOzZCQUFBO3dCQUFBOzRCQUFBSixRQUFBLEVBQUFDLFlBQUE7NEJBQUFDLFVBQUE7NEJBQUFDLFlBQUE7d0JBQUEsR0FBQUMsS0FDSDtvQkFBQzt3QkFBQUosUUFBQSxFQUFBQyxZQUFBO3dCQUFBQyxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLEdBQUFDLEtBQ0gsQ0FBQztvQkFBQSxjQUNOMUosOERBQUE7d0JBQUtvSixTQUFTLEVBQUMsZ0RBQWdEO3dCQUFBQyxRQUFBLGdCQUM3RHJKLDhEQUFBOzRCQUFLb0osU0FBUyxFQUFDLG1CQUFtQjs0QkFBQUMsUUFBQTtnQ0FBQSxjQUNoQ3JKLDhEQUFBLENBQUNYLGlJQUFNO29DQUFDK0osU0FBUyxFQUFDO2dDQUE2QjtvQ0FBQUUsUUFBQSxFQUFBQyxZQUFBO29DQUFBQyxVQUFBO29DQUFBQyxZQUFBO2dDQUFBLEdBQUFDLEtBQUUsQ0FBQztnQ0FBQSxjQUNsRDFKLDhEQUFBO29DQUFBcUosUUFBQTt3Q0FBQSxjQUNFckosOERBQUE7NENBQUdvSixTQUFTLEVBQUMsdUJBQXVCOzRDQUFBQyxRQUFBLEVBQUM7d0NBQUs7NENBQUFDLFFBQUEsRUFBQUMsWUFBQTs0Q0FBQUMsVUFBQTs0Q0FBQUMsWUFBQTt3Q0FBQSxHQUFBQyxLQUFHLENBQUM7d0NBQUEsY0FDOUMxSiw4REFBQTs0Q0FBR29KLFNBQVMsRUFBQyxlQUFlOzRDQUFBQyxRQUFBLEVBQUVqSixJQUFJLENBQUMySixPQUFPLENBQUNFLFdBQVcsSUFBSTt3Q0FBZTs0Q0FBQVgsUUFBQSxFQUFBQyxZQUFBOzRDQUFBQyxVQUFBOzRDQUFBQyxZQUFBO3dDQUFBLEdBQUFDLEtBQUksQ0FBQztxQ0FBQTtnQ0FBQTtvQ0FBQUosUUFBQSxFQUFBQyxZQUFBO29DQUFBQyxVQUFBO29DQUFBQyxZQUFBO2dDQUFBLEdBQUFDLEtBQzNFLENBQUM7NkJBQUE7d0JBQUE7NEJBQUFKLFFBQUEsRUFBQUMsWUFBQTs0QkFBQUMsVUFBQTs0QkFBQUMsWUFBQTt3QkFBQSxHQUFBQyxLQUNIO29CQUFDO3dCQUFBSixRQUFBLEVBQUFDLFlBQUE7d0JBQUFDLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsR0FBQUMsS0FDSCxDQUFDO29CQUFBLGNBQ04xSiw4REFBQTt3QkFBS29KLFNBQVMsRUFBQyxnREFBZ0Q7d0JBQUFDLFFBQUEsZ0JBQzdEckosOERBQUE7NEJBQUtvSixTQUFTLEVBQUMsbUJBQW1COzRCQUFBQyxRQUFBO2dDQUFBLGNBQ2hDckosOERBQUEsQ0FBQ1osbUlBQVE7b0NBQUNnSyxTQUFTLEVBQUM7Z0NBQThCO29DQUFBRSxRQUFBLEVBQUFDLFlBQUE7b0NBQUFDLFVBQUE7b0NBQUFDLFlBQUE7Z0NBQUEsR0FBQUMsS0FBRSxDQUFDO2dDQUFBLGNBQ3JEMUosOERBQUE7b0NBQUFxSixRQUFBO3dDQUFBLGNBQ0VySiw4REFBQTs0Q0FBR29KLFNBQVMsRUFBQyx1QkFBdUI7NENBQUFDLFFBQUEsRUFBQzt3Q0FBYTs0Q0FBQUMsUUFBQSxFQUFBQyxZQUFBOzRDQUFBQyxVQUFBOzRDQUFBQyxZQUFBO3dDQUFBLEdBQUFDLEtBQUcsQ0FBQzt3Q0FBQSxjQUN0RDFKLDhEQUFBOzRDQUFHb0osU0FBUyxFQUFDLGVBQWU7NENBQUFDLFFBQUEsRUFBRWpKLElBQUksQ0FBQzJKLE9BQU8sQ0FBQ0csb0JBQW9CLElBQUk7d0NBQWU7NENBQUFaLFFBQUEsRUFBQUMsWUFBQTs0Q0FBQUMsVUFBQTs0Q0FBQUMsWUFBQTt3Q0FBQSxHQUFBQyxLQUFJLENBQUM7cUNBQUE7Z0NBQUE7b0NBQUFKLFFBQUEsRUFBQUMsWUFBQTtvQ0FBQUMsVUFBQTtvQ0FBQUMsWUFBQTtnQ0FBQSxHQUFBQyxLQUNwRixDQUFDOzZCQUFBO3dCQUFBOzRCQUFBSixRQUFBLEVBQUFDLFlBQUE7NEJBQUFDLFVBQUE7NEJBQUFDLFlBQUE7d0JBQUEsR0FBQUMsS0FDSDtvQkFBQzt3QkFBQUosUUFBQSxFQUFBQyxZQUFBO3dCQUFBQyxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLEdBQUFDLEtBQ0gsQ0FBQztvQkFBQSxjQUNOMUosOERBQUE7d0JBQUtvSixTQUFTLEVBQUMsZ0RBQWdEO3dCQUFBQyxRQUFBLGdCQUM3RHJKLDhEQUFBOzRCQUFLb0osU0FBUyxFQUFDLG1CQUFtQjs0QkFBQUMsUUFBQTtnQ0FBQSxjQUNoQ3JKLDhEQUFBLENBQUNWLHNJQUFXO29DQUFDOEosU0FBUyxFQUFDO2dDQUE4QjtvQ0FBQUUsUUFBQSxFQUFBQyxZQUFBO29DQUFBQyxVQUFBO29DQUFBQyxZQUFBO2dDQUFBLEdBQUFDLEtBQUUsQ0FBQztnQ0FBQSxjQUN4RDFKLDhEQUFBO29DQUFBcUosUUFBQTt3Q0FBQSxjQUNFckosOERBQUE7NENBQUdvSixTQUFTLEVBQUMsdUJBQXVCOzRDQUFBQyxRQUFBLEVBQUM7d0NBQVk7NENBQUFDLFFBQUEsRUFBQUMsWUFBQTs0Q0FBQUMsVUFBQTs0Q0FBQUMsWUFBQTt3Q0FBQSxHQUFBQyxLQUFHLENBQUM7d0NBQUEsY0FDckQxSiw4REFBQTs0Q0FBR29KLFNBQVMsRUFBQyxlQUFlOzRDQUFBQyxRQUFBLEVBQUVqSixJQUFJLENBQUMySixPQUFPLENBQUNJLG1CQUFtQixJQUFJO3dDQUFlOzRDQUFBYixRQUFBLEVBQUFDLFlBQUE7NENBQUFDLFVBQUE7NENBQUFDLFlBQUE7d0NBQUEsR0FBQUMsS0FBSSxDQUFDO3FDQUFBO2dDQUFBO29DQUFBSixRQUFBLEVBQUFDLFlBQUE7b0NBQUFDLFVBQUE7b0NBQUFDLFlBQUE7Z0NBQUEsR0FBQUMsS0FDbkYsQ0FBQzs2QkFBQTt3QkFBQTs0QkFBQUosUUFBQSxFQUFBQyxZQUFBOzRCQUFBQyxVQUFBOzRCQUFBQyxZQUFBO3dCQUFBLEdBQUFDLEtBQ0g7b0JBQUM7d0JBQUFKLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUNILENBQUM7aUJBQUE7WUFBQTtnQkFBQUosUUFBQSxFQUFBQyxZQUFBO2dCQUFBQyxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsR0FBQUMsS0FDSCxDQUNOO1lBR0F0SixJQUFJLENBQUNzRixPQUFPLElBQUl0RixJQUFJLENBQUNzRixPQUFPLENBQUNxRCxNQUFNLEdBQUcsQ0FBQyxrQkFDdEMvSSw4REFBQTtnQkFBS29KLFNBQVMsRUFBQyxXQUFXO2dCQUFBQyxRQUFBO29CQUFBLGNBQ3hCckosOERBQUE7d0JBQUtvSixTQUFTLEVBQUMsbUNBQW1DO3dCQUFBQyxRQUFBOzRCQUFBLGNBQ2hEckosOERBQUE7Z0NBQUlvSixTQUFTLEVBQUMsdURBQXVEO2dDQUFBQyxRQUFBO29DQUFBLGNBQ25FckosOERBQUEsQ0FBQ2IscUlBQVU7d0NBQUNpSyxTQUFTLEVBQUM7b0NBQWM7d0NBQUFFLFFBQUEsRUFBQUMsWUFBQTt3Q0FBQUMsVUFBQTt3Q0FBQUMsWUFBQTtvQ0FBQSxHQUFBQyxLQUFFLENBQUM7b0NBQUEsb0JBRXpDO2lDQUFBOzRCQUFBO2dDQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0NBQUFDLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsR0FBQUMsS0FBSSxDQUFDOzRCQUFBLGNBR0wxSiw4REFBQTtnQ0FDRW9LLE9BQU8sRUFBRSxTQUFBQSxRQUFBO29DQUFBLE9BQU1oSix5QkFBeUIsQ0FBQyxJQUFJLENBQUM7Z0NBQUEsQ0FBQztnQ0FDL0NnSSxTQUFTLEVBQUMsaU9BQWlPO2dDQUFBQyxRQUFBO29DQUFBLGNBRTNPckosOERBQUEsQ0FBQ2IscUlBQVU7d0NBQUNpSyxTQUFTLEVBQUM7b0NBQWM7d0NBQUFFLFFBQUEsRUFBQUMsWUFBQTt3Q0FBQUMsVUFBQTt3Q0FBQUMsWUFBQTtvQ0FBQSxHQUFBQyxLQUFFLENBQUM7b0NBQUEsZ0JBRXpDO2lDQUFBOzRCQUFBO2dDQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0NBQUFDLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsR0FBQUMsS0FBUSxDQUFDO3lCQUFBO29CQUFBO3dCQUFBSixRQUFBLEVBQUFDLFlBQUE7d0JBQUFDLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsR0FBQUMsS0FDTixDQUFDO29CQUFBLGNBR04xSiw4REFBQTt3QkFBS29KLFNBQVMsRUFBQyx3Q0FBd0M7d0JBQUFDLFFBQUE7NEJBQUEsY0FFckRySiw4REFBQTtnQ0FBS29KLFNBQVMsRUFBQyx5QkFBeUI7Z0NBQUFDLFFBQUEsRUFDckNqSixJQUFJLENBQUNzRixPQUFPLENBQUMyRSxHQUFHLENBQUMsU0FBQ3BFLE1BQU0sRUFBRXFFLFdBQVc7b0NBQUEscUJBQ3BDdEssOERBQUE7d0NBRUV1SyxHQUFHLEVBQUUsU0FBQUEsSUFBQ0MsRUFBRSxFQUFLOzRDQUFFbkosVUFBVSxDQUFDb0YsT0FBTyxDQUFDUixNQUFNLENBQUNTLE1BQU0sQ0FBQyxHQUFHOEQsRUFBRTt3Q0FBRSxDQUFFO3dDQUN6RHBCLFNBQVMsRUFBQyxtREFBbUQ7d0NBQUFDLFFBQUE7NENBQUEsY0FFakVySiw4REFBQTtnREFBS29KLFNBQVMsRUFBQywrQ0FBK0M7Z0RBQUFDLFFBQUE7b0RBQUEsY0FDNURySiw4REFBQTt3REFBS29KLFNBQVMsRUFBQyxtQ0FBbUM7d0RBQUFDLFFBQUE7NERBQUEsY0FDaERySiw4REFBQTtnRUFBSW9KLFNBQVMsRUFBQyxxQ0FBcUM7Z0VBQUFDLFFBQUE7b0VBQUMsU0FDM0M7cUVBQUNwRCxNQUFNLGFBQU5BLE1BQU0sdUJBQU5BLE1BQU0sQ0FBRVMsTUFBQUEsS0FBVSxLQUFLO2lFQUFBOzREQUFBO2dFQUFBNEMsUUFBQSxFQUFBQyxZQUFBO2dFQUFBQyxVQUFBO2dFQUFBQyxZQUFBOzREQUFBLEdBQUFDLEtBQzdCLENBQUM7NERBQUEsY0FDTDFKLDhEQUFBO2dFQUFNb0osU0FBUyxFQUFDLHVCQUF1QjtnRUFBQUMsUUFBQSxHQUNwQztxRUFBQXBELE1BQU0sYUFBTkEsTUFBTSx1QkFBTkEsTUFBTSxDQUFFTSxXQUFBQSxLQUFlLEtBQUs7b0VBQUMsS0FBRztxRUFBQ04sTUFBTSxhQUFOQSxNQUFNLHVCQUFOQSxNQUFNLENBQUV3RSxRQUFBQSxLQUFZLEtBQUs7aUVBQUE7NERBQUE7Z0VBQUFuQixRQUFBLEVBQUFDLFlBQUE7Z0VBQUFDLFVBQUE7Z0VBQUFDLFlBQUE7NERBQUEsR0FBQUMsS0FDdkQsQ0FBQzt5REFBQTtvREFBQTt3REFBQUosUUFBQSxFQUFBQyxZQUFBO3dEQUFBQyxVQUFBO3dEQUFBQyxZQUFBO29EQUFBLEdBQUFDLEtBQ0osQ0FBQztvREFBQSxjQUNOMUosOERBQUE7d0RBQUdvSixTQUFTLEVBQUMsb0JBQW9CO3dEQUFBQyxRQUFBLEVBQUUsQ0FBQXBELE1BQU0sYUFBTkEsTUFBTSx1QkFBTkEsTUFBTSxDQUFFeUUsaUJBQUFBLEtBQXFCO29EQUEwQjt3REFBQXBCLFFBQUEsRUFBQUMsWUFBQTt3REFBQUMsVUFBQTt3REFBQUMsWUFBQTtvREFBQSxHQUFBQyxLQUFJLENBQUM7aURBQUE7NENBQUE7Z0RBQUFKLFFBQUEsRUFBQUMsWUFBQTtnREFBQUMsVUFBQTtnREFBQUMsWUFBQTs0Q0FBQSxHQUFBQyxLQUM1RixDQUFDOzRDQUFBLGNBRU4xSiw4REFBQTtnREFBS29KLFNBQVMsRUFBQyxlQUFlO2dEQUFBQyxRQUFBLEVBQzNCcEQsTUFBTSxDQUFDRyxJQUFJLElBQUlrQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ3RDLE1BQU0sQ0FBQ0csSUFBSSxDQUFDLEdBQUdILE1BQU0sQ0FBQ0csSUFBSSxDQUFDaUUsR0FBRyxDQUFDLFNBQUNqSCxHQUFHLEVBQUV1SCxRQUFRO29EQUFBLHFCQUN6RTNLLDhEQUFBO3dEQUFvQm9KLFNBQVMsRUFBQyx1Q0FBdUM7d0RBQUFDLFFBQUE7NERBQUEsY0FDbkVySiw4REFBQTtnRUFBS29KLFNBQVMsRUFBQyx3Q0FBd0M7Z0VBQUFDLFFBQUE7b0VBQUEsY0FDckRySiw4REFBQTt3RUFBSW9KLFNBQVMsRUFBQyw2QkFBNkI7d0VBQUFDLFFBQUEsRUFBRSxDQUFBakcsR0FBRyxhQUFIQSxHQUFHLHVCQUFIQSxHQUFHLENBQUVBLEdBQUFBLEtBQU87b0VBQXFCO3dFQUFBa0csUUFBQSxFQUFBQyxZQUFBO3dFQUFBQyxVQUFBO3dFQUFBQyxZQUFBO29FQUFBLEdBQUFDLEtBQUssQ0FBQztvRUFBQSxjQUNwRjFKLDhEQUFBO3dFQUFNb0osU0FBUyxFQUFDLHFEQUFxRDt3RUFBQUMsUUFBQSxHQUNsRTs0RUFBQWpHLEdBQUcsY0FBSEEsR0FBRyx1QkFBSEEsR0FBRyxDQUFFd0gsS0FBSyxLQUFJLENBQUM7NEVBQUMsR0FDbkI7eUVBQUE7b0VBQUE7d0VBQUF0QixRQUFBLEVBQUFDLFlBQUE7d0VBQUFDLFVBQUE7d0VBQUFDLFlBQUE7b0VBQUEsR0FBQUMsS0FBTSxDQUFDO2lFQUFBOzREQUFBO2dFQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0VBQUFDLFVBQUE7Z0VBQUFDLFlBQUE7NERBQUEsR0FBQUMsS0FDSixDQUFDOzREQUFBLGNBRU4xSiw4REFBQTtnRUFBS29KLFNBQVMsRUFBQyxXQUFXO2dFQUFBQyxRQUFBLEVBQ3ZCakcsR0FBRyxDQUFDK0QsTUFBTSxJQUFJbUIsS0FBSyxDQUFDQyxPQUFPLENBQUNuRixHQUFHLENBQUMrRCxNQUFNLENBQUMsR0FBRy9ELEdBQUcsQ0FBQytELE1BQU0sQ0FBQ2tELEdBQUcsQ0FBQyxTQUFDbkgsS0FBSyxFQUFFMkgsVUFBVSxFQUFLO29FQUMvRSxJQUFNL0MsVUFBVSxHQUFHeEMsY0FBYyxDQUFDcEMsS0FBSyxFQUFFK0MsTUFBTSxDQUFDUyxNQUFNLEVBQUV0RCxHQUFHLENBQUNBLEdBQUcsQ0FBQztvRUFDaEUscUJBQ0VwRCw4REFBQTt3RUFFRW9KLFNBQVMsMEVBQUF6RSxNQUFBLENBQ1BtRCxVQUFVLEdBQ04sOEJBQThCLEdBQzlCLGdEQUFnRCxDQUNuRDt3RUFDSHNDLE9BQU8sRUFBRSxTQUFBQSxRQUFBOzRFQUFBLE9BQU1ySCxxQkFBcUIsQ0FBQ0csS0FBSyxFQUFFK0MsTUFBTSxDQUFDUyxNQUFNLEVBQUV0RCxHQUFHLENBQUNBLEdBQUcsQ0FBQzt3RUFBQSxDQUFDO3dFQUFBaUcsUUFBQTs0RUFBQSxjQUVwRXJKLDhEQUFBO2dGQUFLb0osU0FBUyx5RkFBQXpFLE1BQUEsQ0FDWm1ELFVBQVUsR0FDTiwrQkFBK0IsR0FDL0IsdUNBQXVDLENBQzFDO2dGQUFBdUIsUUFBQSxFQUNBdkIsVUFBVSxrQkFBSTlILDhEQUFBLENBQUNmLGtJQUFPO29GQUFDbUssU0FBUyxFQUFDO2dGQUFvQjtvRkFBQUUsUUFBQSxFQUFBQyxZQUFBO29GQUFBQyxVQUFBO29GQUFBQyxZQUFBO2dGQUFBLEdBQUFDLEtBQUU7NEVBQUM7Z0ZBQUFKLFFBQUEsRUFBQUMsWUFBQTtnRkFBQUMsVUFBQTtnRkFBQUMsWUFBQTs0RUFBQSxHQUFBQyxLQUN0RCxDQUFDOzRFQUFBLGNBRU4xSiw4REFBQTtnRkFBS29KLFNBQVMsRUFBQyxRQUFRO2dGQUFBQyxRQUFBO29GQUFBLGNBQ3JCckosOERBQUE7d0ZBQUlvSixTQUFTLGlCQUFBekUsTUFBQSxDQUFpQm1ELFVBQVUsR0FBRyw2QkFBNkIsR0FBRyxlQUFlLENBQUc7d0ZBQUF1QixRQUFBLEVBQzFGLENBQUFuRyxLQUFLLGFBQUxBLEtBQUssdUJBQUxBLEtBQUssQ0FBRWEsTUFBQUEsS0FBVTtvRkFBa0I7d0ZBQUF1RixRQUFBLEVBQUFDLFlBQUE7d0ZBQUFDLFVBQUE7d0ZBQUFDLFlBQUE7b0ZBQUEsR0FBQUMsS0FDbEMsQ0FBQztxRkFDSnhHLEtBQUssYUFBTEEsS0FBSyx1QkFBTEEsS0FBSyxDQUFFNEgsV0FBQUEsS0FBVyxjQUNqQjlLLDhEQUFBO3dGQUFHb0osU0FBUyxrQkFBQXpFLE1BQUEsQ0FBa0JtRCxVQUFVLEdBQUcsZ0JBQWdCLEdBQUcsZUFBZSxDQUFHO3dGQUFBdUIsUUFBQSxFQUM3RW5HLEtBQUssQ0FBQzRILFdBQUFBO29GQUFXO3dGQUFBeEIsUUFBQSxFQUFBQyxZQUFBO3dGQUFBQyxVQUFBO3dGQUFBQyxZQUFBO29GQUFBLEdBQUFDLEtBQ2pCLENBQ0o7b0ZBQUEsY0FDRDFKLDhEQUFBO3dGQUFLb0osU0FBUyxFQUFDLGtDQUFrQzt3RkFBQUMsUUFBQTs0RkFBQSxjQUMvQ3JKLDhEQUFBO2dHQUFNb0osU0FBUywrQkFBQXpFLE1BQUEsQ0FDYixDQUFBekIsS0FBSyxhQUFMQSxLQUFLLHVCQUFMQSxLQUFLLENBQUVlLElBQUFBLE1BQVMsU0FBUyxHQUFHLDJCQUEyQixHQUN2RCxDQUFBZixLQUFLLGFBQUxBLEtBQUssdUJBQUxBLEtBQUssQ0FBRWUsSUFBQUEsTUFBUyxRQUFRLEdBQUcsK0JBQStCLEdBQzFELENBQUFmLEtBQUssYUFBTEEsS0FBSyx1QkFBTEEsS0FBSyxDQUFFZSxJQUFBQSxNQUFTLFVBQVUsR0FBRywrQkFBK0IsR0FDNUQsMkJBQTJCLENBQzFCO2dHQUFBb0YsUUFBQSxFQUNBLENBQUFuRyxLQUFLLGFBQUxBLEtBQUssdUJBQUxBLEtBQUssQ0FBRWUsSUFBQUEsS0FBUTs0RkFBUztnR0FBQXFGLFFBQUEsRUFBQUMsWUFBQTtnR0FBQUMsVUFBQTtnR0FBQUMsWUFBQTs0RkFBQSxHQUFBQyxLQUNyQixDQUFDOzRGQUFBLGNBQ1AxSiw4REFBQTtnR0FBTW9KLFNBQVMsRUFBQyx1QkFBdUI7Z0dBQUFDLFFBQUEsRUFDcEMsQ0FBQW5HLEtBQUssYUFBTEEsS0FBSyx1QkFBTEEsS0FBSyxDQUFFNkgsZ0JBQUFBLEtBQW9COzRGQUFpQjtnR0FBQXpCLFFBQUEsRUFBQUMsWUFBQTtnR0FBQUMsVUFBQTtnR0FBQUMsWUFBQTs0RkFBQSxHQUFBQyxLQUN6QyxDQUFDO3lGQUFBO29GQUFBO3dGQUFBSixRQUFBLEVBQUFDLFlBQUE7d0ZBQUFDLFVBQUE7d0ZBQUFDLFlBQUE7b0ZBQUEsR0FBQUMsS0FDSixDQUFDO2lGQUFBOzRFQUFBO2dGQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0ZBQUFDLFVBQUE7Z0ZBQUFDLFlBQUE7NEVBQUEsR0FBQUMsS0FDSCxDQUFDO3lFQUFBO29FQUFBLEdBdENEbUIsVUFBVTt3RUFBQXZCLFFBQUEsRUFBQUMsWUFBQTt3RUFBQUMsVUFBQTt3RUFBQUMsWUFBQTtvRUFBQSxHQUFBQyxLQXVDWixDQUFDO2dFQUVWLENBQUMsQ0FBQyxpQkFDQTFKLDhEQUFBO29FQUFHb0osU0FBUyxFQUFDLHVCQUF1QjtvRUFBQUMsUUFBQSxFQUFDO2dFQUF5QjtvRUFBQUMsUUFBQSxFQUFBQyxZQUFBO29FQUFBQyxVQUFBO29FQUFBQyxZQUFBO2dFQUFBLEdBQUFDLEtBQUc7NERBQ2xFO2dFQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0VBQUFDLFVBQUE7Z0VBQUFDLFlBQUE7NERBQUEsR0FBQUMsS0FDRSxDQUFDO3lEQUFBO29EQUFBLEdBekRFaUIsUUFBUTt3REFBQXJCLFFBQUEsRUFBQUMsWUFBQTt3REFBQUMsVUFBQTt3REFBQUMsWUFBQTtvREFBQSxHQUFBQyxLQTBEYixDQUFDO2dEQUFBLENBQ1AsQ0FBQyxpQkFDQTFKLDhEQUFBO29EQUFHb0osU0FBUyxFQUFDLHVCQUF1QjtvREFBQUMsUUFBQSxFQUFDO2dEQUF1QjtvREFBQUMsUUFBQSxFQUFBQyxZQUFBO29EQUFBQyxVQUFBO29EQUFBQyxZQUFBO2dEQUFBLEdBQUFDLEtBQUc7NENBQ2hFO2dEQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0RBQUFDLFVBQUE7Z0RBQUFDLFlBQUE7NENBQUEsR0FBQUMsS0FDRSxDQUFDO3lDQUFBO29DQUFBLEdBaEZHWSxXQUFXO3dDQUFBaEIsUUFBQSxFQUFBQyxZQUFBO3dDQUFBQyxVQUFBO3dDQUFBQyxZQUFBO29DQUFBLEdBQUFDLEtBaUZqQixDQUFDO2dDQUFBLENBQ1A7NEJBQUM7Z0NBQUFKLFFBQUEsRUFBQUMsWUFBQTtnQ0FBQUMsVUFBQTtnQ0FBQUMsWUFBQTs0QkFBQSxHQUFBQyxLQUNLLENBQUM7NEJBQUEsY0FHTjFKLDhEQUFBO2dDQUFLb0osU0FBUyxFQUFDLHlDQUF5QztnQ0FBQUMsUUFBQTtvQ0FBQSxjQUV0RHJKLDhEQUFBLENBQUNMLHVEQUFjO3dDQUNiUyxJQUFJLEVBQUVBLElBQUs7d0NBQ1hHLFlBQVksRUFBRUEsWUFBYTt3Q0FDM0JTLGlCQUFpQixFQUFFQSxpQkFBa0I7d0NBQ3JDZ0ssbUJBQW1CLEVBQUV4Rix1QkFBd0I7d0NBQzdDNEQsU0FBUyxFQUFDO29DQUFjO3dDQUFBRSxRQUFBLEVBQUFDLFlBQUE7d0NBQUFDLFVBQUE7d0NBQUFDLFlBQUE7b0NBQUEsR0FBQUMsS0FDekIsQ0FBQztvQ0FBQSxjQUdGMUosOERBQUEsQ0FBQ0oscURBQVk7d0NBQ1g2RixLQUFLLEVBQUV6RSxpQkFBa0I7d0NBQ3pCbUcsTUFBTSxFQUFFRCwrQkFBK0IsQ0FBQyxDQUFFO3dDQUMxQytELFlBQVksRUFBRTlDLHNCQUF1Qjt3Q0FDckNpQixTQUFTLEVBQUM7b0NBQWM7d0NBQUFFLFFBQUEsRUFBQUMsWUFBQTt3Q0FBQUMsVUFBQTt3Q0FBQUMsWUFBQTtvQ0FBQSxHQUFBQyxLQUN6QixDQUFDO2lDQUFBOzRCQUFBO2dDQUFBSixRQUFBLEVBQUFDLFlBQUE7Z0NBQUFDLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsR0FBQUMsS0FDQyxDQUFDO3lCQUFBO29CQUFBO3dCQUFBSixRQUFBLEVBQUFDLFlBQUE7d0JBQUFDLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsR0FBQUMsS0FDSCxDQUFDO2lCQUFBO1lBQUE7Z0JBQUFKLFFBQUEsRUFBQUMsWUFBQTtnQkFBQUMsVUFBQTtnQkFBQUMsWUFBQTtZQUFBLEdBQUFDLEtBQ0gsQ0FDTjtZQUFBLGNBR0QxSiw4REFBQTtnQkFBS29KLFNBQVMsRUFBQyxzREFBc0Q7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDbkVySiw4REFBQTt3QkFBSW9KLFNBQVMsRUFBQyxvQ0FBb0M7d0JBQUFDLFFBQUEsRUFBQztvQkFBcUI7d0JBQUFDLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUFJLENBQUM7b0JBQUEsY0FDN0UxSiw4REFBQTt3QkFBR29KLFNBQVMsRUFBQyxpQkFBaUI7d0JBQUFDLFFBQUEsRUFDM0IsT0FBT2pKLElBQUksQ0FBQzhLLGlCQUFpQixLQUFLLFFBQVEsR0FDdkM5SyxJQUFJLENBQUM4SyxpQkFBaUIsR0FDckI5SyxJQUFJLENBQUM4SyxpQkFBaUIsSUFBSSxPQUFPOUssSUFBSSxDQUFDOEssaUJBQWlCLEtBQUssUUFBUSxHQUNuRTlLLElBQUksQ0FBQzhLLGlCQUFpQixDQUFDSixXQUFXLElBQUkscUNBQXFDLEdBQzNFO29CQUFxQzt3QkFBQXhCLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUUxQyxDQUFDO2lCQUFBO1lBQUE7Z0JBQUFKLFFBQUEsRUFBQUMsWUFBQTtnQkFBQUMsVUFBQTtnQkFBQUMsWUFBQTtZQUFBLEdBQUFDLEtBQ0QsQ0FBQztZQUFBLGNBR04xSiw4REFBQTtnQkFBS29KLFNBQVMsRUFBQyxzREFBc0Q7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDbkVySiw4REFBQTt3QkFBSW9KLFNBQVMsRUFBQyxvQ0FBb0M7d0JBQUFDLFFBQUEsRUFBQztvQkFBeUI7d0JBQUFDLFFBQUEsRUFBQUMsWUFBQTt3QkFBQUMsVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxHQUFBQyxLQUFJLENBQUM7b0JBQUEsY0FDakYxSiw4REFBQTt3QkFBR29KLFNBQVMsRUFBQyxpQkFBaUI7d0JBQUFDLFFBQUEsRUFDM0IsT0FBT2pKLElBQUksQ0FBQytLLGFBQWEsS0FBSyxRQUFRLEdBQ25DL0ssSUFBSSxDQUFDK0ssYUFBYSxHQUNqQi9LLElBQUksQ0FBQytLLGFBQWEsSUFBSSxPQUFPL0ssSUFBSSxDQUFDK0ssYUFBYSxLQUFLLFFBQVEsR0FDM0QvSyxJQUFJLENBQUMrSyxhQUFhLENBQUNMLFdBQVcsSUFBSSwrQkFBK0IsR0FDakU7b0JBQStCO3dCQUFBeEIsUUFBQSxFQUFBQyxZQUFBO3dCQUFBQyxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLEdBQUFDLEtBRXBDLENBQUM7aUJBQUE7WUFBQTtnQkFBQUosUUFBQSxFQUFBQyxZQUFBO2dCQUFBQyxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsR0FBQUMsS0FDRCxDQUFDO1NBQUE7SUFBQTtRQUFBSixRQUFBLEVBQUFDLFlBQUE7UUFBQUMsVUFBQTtRQUFBQyxZQUFBO0lBQUEsR0FBQUMsS0FDSCxDQUFDO0FBRVYsQ0FBQzs7TUFuZEt6SixrQkFBcUQ7QUFtZHpERSxFQUFBLEVBbmRJRixrQkFBcUQ7QUFBQW1MLEVBQUEsR0FBckRuTCxrQkFBcUQ7QUFxZDNELGlFQUFlQSxrQkFBa0I7QUFBQyxJQUFBbUwsRUFBQTtBQUFBQyxZQUFBLENBQUFELEVBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcc3JjXFxmZWF0dXJlc1xccGxhbmlmaWNhY2lvblxcY29tcG9uZW50c1xcUGxhbkVzdHVkaW9zVmlld2VyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRmlDaGVjaywgRmlDbG9jaywgRmlDYWxlbmRhciwgRmlUYXJnZXQsIEZpQm9vaywgRmlSZWZyZXNoQ3cgfSBmcm9tICdyZWFjdC1pY29ucy9maSc7XG5pbXBvcnQgeyBQbGFuRXN0dWRpb3NFc3RydWN0dXJhZG8sIFRhcmVhUGxhbiB9IGZyb20gJy4uL3NlcnZpY2VzL3BsYW5HZW5lcmF0b3JTZXJ2aWNlJztcbmltcG9ydCB7XG4gIG9idGVuZXJQbGFuRXN0dWRpb3NBY3Rpdm9DbGllbnRlLFxuICBvYnRlbmVyUHJvZ3Jlc29QbGFuZUNsaWVudGUsXG4gIGd1YXJkYXJQcm9ncmVzb1RhcmVhQ2xpZW50ZVxufSBmcm9tICcuLi9zZXJ2aWNlcy9wbGFuRXN0dWRpb3NDbGllbnRTZXJ2aWNlJztcbmltcG9ydCB7IFByb2dyZXNvUGxhbkVzdHVkaW9zIH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc3VwYWJhc2VDbGllbnQnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuaW1wb3J0IFBsYW5DYWxlbmRhcmlvIGZyb20gJy4vUGxhbkNhbGVuZGFyaW8nO1xuaW1wb3J0IFRhcmVhc0RlbERpYSBmcm9tICcuL1RhcmVhc0RlbERpYSc7XG5pbXBvcnQgQ2FsZW5kYXJpb01vZGFsIGZyb20gJy4vQ2FsZW5kYXJpb01vZGFsJztcbmltcG9ydCB7IGNhbGN1bGFyRmVjaGFEaWEsIGZvcm1hdERhdGUgfSBmcm9tICdAL2xpYi91dGlscy9kYXRlVXRpbHMnO1xuXG5pbnRlcmZhY2UgUGxhbkVzdHVkaW9zVmlld2VyUHJvcHMge1xuICBwbGFuOiBQbGFuRXN0dWRpb3NFc3RydWN0dXJhZG87XG4gIHRlbWFyaW9JZDogc3RyaW5nO1xufVxuXG5jb25zdCBQbGFuRXN0dWRpb3NWaWV3ZXI6IFJlYWN0LkZDPFBsYW5Fc3R1ZGlvc1ZpZXdlclByb3BzPiA9ICh7IHBsYW4sIHRlbWFyaW9JZCB9KSA9PiB7XG4gIGNvbnN0IFtwcm9ncmVzb1BsYW4sIHNldFByb2dyZXNvUGxhbl0gPSB1c2VTdGF0ZTxQcm9ncmVzb1BsYW5Fc3R1ZGlvc1tdPihbXSk7XG4gIGNvbnN0IFtwbGFuSWQsIHNldFBsYW5JZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vIEVzdGFkb3MgcGFyYSBlbCBjYWxlbmRhcmlvXG4gIGNvbnN0IFtmZWNoYVNlbGVjY2lvbmFkYSwgc2V0RmVjaGFTZWxlY2Npb25hZGFdID0gdXNlU3RhdGU8RGF0ZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY2FsZW5kYXJpb01vZGFsQWJpZXJ0bywgc2V0Q2FsZW5kYXJpb01vZGFsQWJpZXJ0b10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gUmVmZXJlbmNpYXMgcGFyYSBzY3JvbGwgYXV0b23DoXRpY29cbiAgY29uc3Qgc2VtYW5hUmVmcyA9IHVzZVJlZjx7IFtrZXk6IG51bWJlcl06IEhUTUxEaXZFbGVtZW50IHwgbnVsbCB9Pih7fSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjYXJnYXJQcm9ncmVzbygpO1xuICB9LCBbdGVtYXJpb0lkXSk7XG5cbiAgY29uc3QgY2FyZ2FyUHJvZ3Jlc28gPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBsYW5BY3Rpdm8gPSBhd2FpdCBvYnRlbmVyUGxhbkVzdHVkaW9zQWN0aXZvQ2xpZW50ZSh0ZW1hcmlvSWQpO1xuICAgICAgaWYgKCFwbGFuQWN0aXZvKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignTm8gc2UgZW5jb250csOzIHBsYW4gYWN0aXZvIHBhcmEgZWwgdGVtYXJpbzonLCB0ZW1hcmlvSWQpO1xuICAgICAgICBzZXRQbGFuSWQobnVsbCk7XG4gICAgICAgIHNldFByb2dyZXNvUGxhbihbXSk7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgc2V0UGxhbklkKHBsYW5BY3Rpdm8uaWQpO1xuICAgICAgY29uc3QgcHJvZ3Jlc28gPSBhd2FpdCBvYnRlbmVyUHJvZ3Jlc29QbGFuZUNsaWVudGUocGxhbkFjdGl2by5pZCk7XG4gICAgICBzZXRQcm9ncmVzb1BsYW4ocHJvZ3Jlc28pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBjYXJnYXIgcHJvZ3Jlc286JywgZXJyb3IpO1xuICAgICAgc2V0UGxhbklkKG51bGwpO1xuICAgICAgc2V0UHJvZ3Jlc29QbGFuKFtdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlVGFyZWFDb21wbGV0YWRhID0gYXN5bmMgKHRhcmVhOiBUYXJlYVBsYW4sIHNlbWFuYU51bTogbnVtYmVyLCBkaWE6IHN0cmluZykgPT4ge1xuICAgIGlmICghcGxhbklkKSB7XG4gICAgICB0b2FzdC5lcnJvcignTm8gc2UgcHVkbyBpZGVudGlmaWNhciBlbCBwbGFuIGRlIGVzdHVkaW9zJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRhcmVhRXhpc3RlbnRlID0gcHJvZ3Jlc29QbGFuLmZpbmQocCA9PlxuICAgICAgICBwLnNlbWFuYV9udW1lcm8gPT09IHNlbWFuYU51bSAmJlxuICAgICAgICBwLmRpYV9ub21icmUgPT09IGRpYSAmJlxuICAgICAgICBwLnRhcmVhX3RpdHVsbyA9PT0gdGFyZWEudGl0dWxvXG4gICAgICApO1xuXG4gICAgICBjb25zdCBudWV2b0VzdGFkbyA9ICF0YXJlYUV4aXN0ZW50ZT8uY29tcGxldGFkbztcblxuICAgICAgY29uc3QgZXhpdG8gPSBhd2FpdCBndWFyZGFyUHJvZ3Jlc29UYXJlYUNsaWVudGUoXG4gICAgICAgIHBsYW5JZCxcbiAgICAgICAgc2VtYW5hTnVtLFxuICAgICAgICBkaWEsXG4gICAgICAgIHRhcmVhLnRpdHVsbyxcbiAgICAgICAgdGFyZWEudGlwbyxcbiAgICAgICAgbnVldm9Fc3RhZG9cbiAgICAgICk7XG5cbiAgICAgIGlmIChleGl0bykge1xuICAgICAgICBzZXRQcm9ncmVzb1BsYW4ocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgaW5kZXggPSBwcmV2LmZpbmRJbmRleChwID0+XG4gICAgICAgICAgICBwLnNlbWFuYV9udW1lcm8gPT09IHNlbWFuYU51bSAmJlxuICAgICAgICAgICAgcC5kaWFfbm9tYnJlID09PSBkaWEgJiZcbiAgICAgICAgICAgIHAudGFyZWFfdGl0dWxvID09PSB0YXJlYS50aXR1bG9cbiAgICAgICAgICApO1xuXG4gICAgICAgICAgaWYgKGluZGV4ID49IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSBbLi4ucHJldl07XG4gICAgICAgICAgICB1cGRhdGVkW2luZGV4XSA9IHtcbiAgICAgICAgICAgICAgLi4udXBkYXRlZFtpbmRleF0sXG4gICAgICAgICAgICAgIGNvbXBsZXRhZG86IG51ZXZvRXN0YWRvLFxuICAgICAgICAgICAgICBmZWNoYV9jb21wbGV0YWRvOiBudWV2b0VzdGFkbyA/IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSA6IHVuZGVmaW5lZFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiB1cGRhdGVkO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gWy4uLnByZXYsIHtcbiAgICAgICAgICAgICAgaWQ6IGB0ZW1wLSR7RGF0ZS5ub3coKX1gLFxuICAgICAgICAgICAgICBwbGFuX2lkOiBwbGFuSWQsXG4gICAgICAgICAgICAgIHVzZXJfaWQ6ICcnLFxuICAgICAgICAgICAgICBzZW1hbmFfbnVtZXJvOiBzZW1hbmFOdW0sXG4gICAgICAgICAgICAgIGRpYV9ub21icmU6IGRpYSxcbiAgICAgICAgICAgICAgdGFyZWFfdGl0dWxvOiB0YXJlYS50aXR1bG8sXG4gICAgICAgICAgICAgIHRhcmVhX3RpcG86IHRhcmVhLnRpcG8sXG4gICAgICAgICAgICAgIGNvbXBsZXRhZG86IG51ZXZvRXN0YWRvLFxuICAgICAgICAgICAgICBmZWNoYV9jb21wbGV0YWRvOiBudWV2b0VzdGFkbyA/IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgY3JlYWRvX2VuOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgIGFjdHVhbGl6YWRvX2VuOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH1dO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgdG9hc3Quc3VjY2VzcyhudWV2b0VzdGFkbyA/ICdUYXJlYSBjb21wbGV0YWRhJyA6ICdUYXJlYSBtYXJjYWRhIGNvbW8gcGVuZGllbnRlJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0b2FzdC5lcnJvcignRXJyb3IgYWwgYWN0dWFsaXphciBlbCBwcm9ncmVzbycpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBhY3R1YWxpemFyIHRhcmVhOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdFcnJvciBhbCBhY3R1YWxpemFyIGVsIHByb2dyZXNvJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGVzdGFDb21wbGV0YWRhID0gKHRhcmVhOiBUYXJlYVBsYW4sIHNlbWFuYU51bTogbnVtYmVyLCBkaWE6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiBwcm9ncmVzb1BsYW4uc29tZShwID0+XG4gICAgICBwLnNlbWFuYV9udW1lcm8gPT09IHNlbWFuYU51bSAmJlxuICAgICAgcC5kaWFfbm9tYnJlID09PSBkaWEgJiZcbiAgICAgIHAudGFyZWFfdGl0dWxvID09PSB0YXJlYS50aXR1bG8gJiZcbiAgICAgIHAuY29tcGxldGFkb1xuICAgICk7XG4gIH07XG5cbiAgLy8gTWFuZWphciBzZWxlY2Npw7NuIGRlIGZlY2hhIGVuIGVsIGNhbGVuZGFyaW9cbiAgY29uc3QgaGFuZGxlRmVjaGFTZWxlY2Npb25hZGEgPSAoZmVjaGE6IERhdGUpID0+IHtcbiAgICBzZXRGZWNoYVNlbGVjY2lvbmFkYShmZWNoYSk7XG5cbiAgICAvLyBCdXNjYXIgbGEgc2VtYW5hIHkgZMOtYSBjb3JyZXNwb25kaWVudGUgcGFyYSBoYWNlciBzY3JvbGxcbiAgICBpZiAocGxhbiAmJiBwbGFuLnNlbWFuYXMpIHtcbiAgICAgIGZvciAoY29uc3Qgc2VtYW5hIG9mIHBsYW4uc2VtYW5hcykge1xuICAgICAgICBmb3IgKGNvbnN0IGRpYSBvZiBzZW1hbmEuZGlhcyB8fCBbXSkge1xuICAgICAgICAgIGNvbnN0IGZlY2hhRGlhID0gY2FsY3VsYXJGZWNoYURpYShzZW1hbmEuZmVjaGFJbmljaW8sIGRpYS5kaWEpO1xuICAgICAgICAgIGlmIChmZWNoYURpYSAmJiBmb3JtYXREYXRlKGZlY2hhRGlhKSA9PT0gZm9ybWF0RGF0ZShmZWNoYSkpIHtcbiAgICAgICAgICAgIC8vIEhhY2VyIHNjcm9sbCBhIGxhIHNlbWFuYSBjb3JyZXNwb25kaWVudGVcbiAgICAgICAgICAgIGNvbnN0IHNlbWFuYVJlZiA9IHNlbWFuYVJlZnMuY3VycmVudFtzZW1hbmEubnVtZXJvXTtcbiAgICAgICAgICAgIGlmIChzZW1hbmFSZWYpIHtcbiAgICAgICAgICAgICAgc2VtYW5hUmVmLnNjcm9sbEludG9WaWV3KHtcbiAgICAgICAgICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCcsXG4gICAgICAgICAgICAgICAgYmxvY2s6ICdzdGFydCcsXG4gICAgICAgICAgICAgICAgaW5saW5lOiAnbmVhcmVzdCdcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIE9idGVuZXIgdGFyZWFzIGRlbCBkw61hIHNlbGVjY2lvbmFkb1xuICBjb25zdCBvYnRlbmVyVGFyZWFzRGVsRGlhU2VsZWNjaW9uYWRvID0gKCkgPT4ge1xuICAgIGlmICghZmVjaGFTZWxlY2Npb25hZGEgfHwgIXBsYW4gfHwgIXBsYW4uc2VtYW5hcykgcmV0dXJuIFtdO1xuXG4gICAgY29uc3QgdGFyZWFzID0gW107XG4gICAgZm9yIChjb25zdCBzZW1hbmEgb2YgcGxhbi5zZW1hbmFzKSB7XG4gICAgICBmb3IgKGNvbnN0IGRpYSBvZiBzZW1hbmEuZGlhcyB8fCBbXSkge1xuICAgICAgICBjb25zdCBmZWNoYURpYSA9IGNhbGN1bGFyRmVjaGFEaWEoc2VtYW5hLmZlY2hhSW5pY2lvLCBkaWEuZGlhKTtcbiAgICAgICAgaWYgKGZlY2hhRGlhICYmIGZvcm1hdERhdGUoZmVjaGFEaWEpID09PSBmb3JtYXREYXRlKGZlY2hhU2VsZWNjaW9uYWRhKSkge1xuICAgICAgICAgIGZvciAoY29uc3QgdGFyZWEgb2YgZGlhLnRhcmVhcyB8fCBbXSkge1xuICAgICAgICAgICAgY29uc3QgY29tcGxldGFkYSA9IGVzdGFDb21wbGV0YWRhKHRhcmVhLCBzZW1hbmEubnVtZXJvLCBkaWEuZGlhKTtcbiAgICAgICAgICAgIHRhcmVhcy5wdXNoKHtcbiAgICAgICAgICAgICAgdGFyZWEsXG4gICAgICAgICAgICAgIHNlbWFuYU51bWVybzogc2VtYW5hLm51bWVybyxcbiAgICAgICAgICAgICAgZGlhTm9tYnJlOiBkaWEuZGlhLFxuICAgICAgICAgICAgICBjb21wbGV0YWRhLFxuICAgICAgICAgICAgICBmZWNoYUNvbXBsZXRhZG86IHByb2dyZXNvUGxhbi5maW5kKHAgPT5cbiAgICAgICAgICAgICAgICBwLnNlbWFuYV9udW1lcm8gPT09IHNlbWFuYS5udW1lcm8gJiZcbiAgICAgICAgICAgICAgICBwLmRpYV9ub21icmUgPT09IGRpYS5kaWEgJiZcbiAgICAgICAgICAgICAgICBwLnRhcmVhX3RpdHVsbyA9PT0gdGFyZWEudGl0dWxvXG4gICAgICAgICAgICAgICk/LmZlY2hhX2NvbXBsZXRhZG9cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdGFyZWFzO1xuICB9O1xuXG4gIC8vIE1hbmVqYXIgY2xpYyBlbiB0YXJlYSBkZXNkZSBlbCBwYW5lbCBkZWwgZMOtYVxuICBjb25zdCBoYW5kbGVUYXJlYURlbERpYUNsaWNrID0gKHRhcmVhRGVsRGlhOiBhbnkpID0+IHtcbiAgICAvLyBIYWNlciBzY3JvbGwgYSBsYSB0YXJlYSBlc3BlY8OtZmljYSBlbiBsYSBsaXN0YSBwcmluY2lwYWxcbiAgICBjb25zdCBzZW1hbmFSZWYgPSBzZW1hbmFSZWZzLmN1cnJlbnRbdGFyZWFEZWxEaWEuc2VtYW5hTnVtZXJvXTtcbiAgICBpZiAoc2VtYW5hUmVmKSB7XG4gICAgICBzZW1hbmFSZWYuc2Nyb2xsSW50b1ZpZXcoe1xuICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCcsXG4gICAgICAgIGJsb2NrOiAnY2VudGVyJyxcbiAgICAgICAgaW5saW5lOiAnbmVhcmVzdCdcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjYWxjdWxhclByb2dyZXNvID0gKCk6IHsgY29tcGxldGFkYXM6IG51bWJlcjsgdG90YWw6IG51bWJlcjsgcG9yY2VudGFqZTogbnVtYmVyIH0gPT4ge1xuICAgIGlmICghcGxhbiB8fCAhcGxhbi5zZW1hbmFzIHx8ICFBcnJheS5pc0FycmF5KHBsYW4uc2VtYW5hcykpIHtcbiAgICAgIHJldHVybiB7IGNvbXBsZXRhZGFzOiAwLCB0b3RhbDogMCwgcG9yY2VudGFqZTogMCB9O1xuICAgIH1cblxuICAgIGNvbnN0IHRvdGFsVGFyZWFzID0gcGxhbi5zZW1hbmFzLnJlZHVjZSgoYWNjLCBzZW1hbmEpID0+IHtcbiAgICAgIGlmICghc2VtYW5hIHx8ICFzZW1hbmEuZGlhcyB8fCAhQXJyYXkuaXNBcnJheShzZW1hbmEuZGlhcykpIHtcbiAgICAgICAgcmV0dXJuIGFjYztcbiAgICAgIH1cbiAgICAgIHJldHVybiBhY2MgKyBzZW1hbmEuZGlhcy5yZWR1Y2UoKGRheUFjYywgZGlhKSA9PiB7XG4gICAgICAgIGlmICghZGlhIHx8ICFkaWEudGFyZWFzIHx8ICFBcnJheS5pc0FycmF5KGRpYS50YXJlYXMpKSB7XG4gICAgICAgICAgcmV0dXJuIGRheUFjYztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZGF5QWNjICsgZGlhLnRhcmVhcy5sZW5ndGg7XG4gICAgICB9LCAwKTtcbiAgICB9LCAwKTtcblxuICAgIGNvbnN0IHRhcmVhc0NvbXBsZXRhZGFzQ291bnQgPSBwcm9ncmVzb1BsYW4uZmlsdGVyKHAgPT4gcC5jb21wbGV0YWRvKS5sZW5ndGg7XG5cbiAgICByZXR1cm4ge1xuICAgICAgY29tcGxldGFkYXM6IHRhcmVhc0NvbXBsZXRhZGFzQ291bnQsXG4gICAgICB0b3RhbDogdG90YWxUYXJlYXMsXG4gICAgICBwb3JjZW50YWplOiB0b3RhbFRhcmVhcyA+IDAgPyBNYXRoLnJvdW5kKCh0YXJlYXNDb21wbGV0YWRhc0NvdW50IC8gdG90YWxUYXJlYXMpICogMTAwKSA6IDBcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IHByb2dyZXNvID0gY2FsY3VsYXJQcm9ncmVzbygpO1xuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTMgdGV4dC1ncmF5LTYwMFwiPkNhcmdhbmRvIHByb2dyZXNvLi4uPC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmICghcGxhbikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5ObyBzZSBwdWRvIGNhcmdhciBlbCBwbGFuIGRlIGVzdHVkaW9zPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoIXRlbWFyaW9JZCB8fCB0ZW1hcmlvSWQudHJpbSgpID09PSAnJykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5JRCBkZSB0ZW1hcmlvIG5vIHbDoWxpZG88L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBJbnRyb2R1Y2Npw7NuICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTkwMCBtYi0yXCI+SW50cm9kdWNjacOzbjwvaDM+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS04MDBcIj57cGxhbi5pbnRyb2R1Y2Npb24gfHwgJ0ludHJvZHVjY2nDs24gbm8gZGlzcG9uaWJsZSd9PC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcm9ncmVzbyBnZW5lcmFsICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAgdG8tYmx1ZS01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlByb2dyZXNvIEdlbmVyYWw8L2gzPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPntwcm9ncmVzby5wb3JjZW50YWplfSU8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0zIG1iLTJcIj5cbiAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTUwMCB0by1ibHVlLTUwMCBoLTMgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7cHJvZ3Jlc28ucG9yY2VudGFqZX0lYCB9fVxuICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgIHtwcm9ncmVzby5jb21wbGV0YWRhc30gZGUge3Byb2dyZXNvLnRvdGFsfSB0YXJlYXMgY29tcGxldGFkYXNcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBSZXN1bWVuICovfVxuICAgICAge3BsYW4ucmVzdW1lbiAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8RmlDbG9jayBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS02MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+VGllbXBvIFRvdGFsPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGRcIj57cGxhbi5yZXN1bWVuLnRpZW1wb1RvdGFsRXN0dWRpbyB8fCAnTm8gZGlzcG9uaWJsZSd9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8RmlCb29rIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi02MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+VGVtYXM8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPntwbGFuLnJlc3VtZW4ubnVtZXJvVGVtYXMgfHwgJ05vIGRpc3BvbmlibGUnfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEZpVGFyZ2V0IGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1wdXJwbGUtNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkVzdHVkaW8gTnVldm88L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPntwbGFuLnJlc3VtZW4uZHVyYWNpb25Fc3R1ZGlvTnVldm8gfHwgJ05vIGRpc3BvbmlibGUnfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEZpUmVmcmVzaEN3IGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1vcmFuZ2UtNjAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlJlcGFzbyBGaW5hbDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e3BsYW4ucmVzdW1lbi5kdXJhY2lvblJlcGFzb0ZpbmFsIHx8ICdObyBkaXNwb25pYmxlJ308L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIENyb25vZ3JhbWEgc2VtYW5hbCBjb24gY2FsZW5kYXJpbyAqL31cbiAgICAgIHtwbGFuLnNlbWFuYXMgJiYgcGxhbi5zZW1hbmFzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEZpQ2FsZW5kYXIgY2xhc3NOYW1lPVwidy01IGgtNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQ3Jvbm9ncmFtYSBTZW1hbmFsXG4gICAgICAgICAgICA8L2gzPlxuXG4gICAgICAgICAgICB7LyogVG9nZ2xlIGRlbCBjYWxlbmRhcmlvIGVuIG3Ds3ZpbC90YWJsZXQgKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENhbGVuZGFyaW9Nb2RhbEFiaWVydG8odHJ1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxnOmhpZGRlbiBmbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLW9wYWNpdHktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8RmlDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBWZXIgQ2FsZW5kYXJpb1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTGF5b3V0IGRlIGRvcyBjb2x1bW5hcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTEyIGdhcC02XCI+XG4gICAgICAgICAgICB7LyogQ29sdW1uYSBwcmluY2lwYWwgLSBMaXN0YSBkZSB0YXJlYXMgKDcwJSkgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTggc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIHtwbGFuLnNlbWFuYXMubWFwKChzZW1hbmEsIHNlbWFuYUluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtzZW1hbmFJbmRleH1cbiAgICAgICAgICAgICAgICAgIHJlZj17KGVsKSA9PiB7IHNlbWFuYVJlZnMuY3VycmVudFtzZW1hbmEubnVtZXJvXSA9IGVsOyB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgU2VtYW5hIHtzZW1hbmE/Lm51bWVybyB8fCAnTi9BJ31cbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3NlbWFuYT8uZmVjaGFJbmljaW8gfHwgJ04vQSd9IC0ge3NlbWFuYT8uZmVjaGFGaW4gfHwgJ04vQSd9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBtdC0yXCI+e3NlbWFuYT8ub2JqZXRpdm9QcmluY2lwYWwgfHwgJ09iamV0aXZvIG5vIGVzcGVjaWZpY2Fkbyd9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHtzZW1hbmEuZGlhcyAmJiBBcnJheS5pc0FycmF5KHNlbWFuYS5kaWFzKSA/IHNlbWFuYS5kaWFzLm1hcCgoZGlhLCBkaWFJbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2RpYUluZGV4fSBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMTAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+e2RpYT8uZGlhIHx8ICdEw61hIG5vIGVzcGVjaWZpY2Fkbyd9PC9oNT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgYmctZ3JheS0xMDAgcHgtMiBweS0xIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtkaWE/LmhvcmFzIHx8IDB9aFxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtkaWEudGFyZWFzICYmIEFycmF5LmlzQXJyYXkoZGlhLnRhcmVhcykgPyBkaWEudGFyZWFzLm1hcCgodGFyZWEsIHRhcmVhSW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRhZGEgPSBlc3RhQ29tcGxldGFkYSh0YXJlYSwgc2VtYW5hLm51bWVybywgZGlhLmRpYSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt0YXJlYUluZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtc3RhcnQgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIHRyYW5zaXRpb24tYWxsIGN1cnNvci1wb2ludGVyICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wbGV0YWRhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLXdoaXRlIGJvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItYmx1ZS0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlVGFyZWFDb21wbGV0YWRhKHRhcmVhLCBzZW1hbmEubnVtZXJvLCBkaWEuZGlhKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC1zaHJpbmstMCB3LTUgaC01IHJvdW5kZWQgYm9yZGVyLTIgbXItMyBtdC0wLjUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBsZXRhZGEgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwMCBib3JkZXItZ3JlZW4tNTAwJyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMzAwIGhvdmVyOmJvcmRlci1ibHVlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29tcGxldGFkYSAmJiA8RmlDaGVjayBjbGFzc05hbWU9XCJ3LTMgaC0zIHRleHQtd2hpdGVcIiAvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg2IGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7Y29tcGxldGFkYSA/ICd0ZXh0LWdyZWVuLTgwMCBsaW5lLXRocm91Z2gnIDogJ3RleHQtZ3JheS05MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFyZWE/LnRpdHVsbyB8fCAnVGFyZWEgc2luIHTDrXR1bG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oNj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YXJlYT8uZGVzY3JpcGNpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXNtIG10LTEgJHtjb21wbGV0YWRhID8gJ3RleHQtZ3JlZW4tNzAwJyA6ICd0ZXh0LWdyYXktNjAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFyZWEuZGVzY3JpcGNpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJlYT8udGlwbyA9PT0gJ2VzdHVkaW8nID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJlYT8udGlwbyA9PT0gJ3JlcGFzbycgPyAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJlYT8udGlwbyA9PT0gJ3ByYWN0aWNhJyA/ICdiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RhcmVhPy50aXBvIHx8ICdnZW5lcmFsJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFyZWE/LmR1cmFjaW9uRXN0aW1hZGEgfHwgJ05vIGVzcGVjaWZpY2Fkbyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgfSkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5ObyBoYXkgdGFyZWFzIGRpc3BvbmlibGVzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSkgOiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5ObyBoYXkgZMOtYXMgZGlzcG9uaWJsZXM8L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ29sdW1uYSBkZWwgY2FsZW5kYXJpbyAoMzAlKSAtIFNvbG8gdmlzaWJsZSBlbiBkZXNrdG9wICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6YmxvY2sgbGc6Y29sLXNwYW4tNCBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgey8qIENhbGVuZGFyaW8gKi99XG4gICAgICAgICAgICAgIDxQbGFuQ2FsZW5kYXJpb1xuICAgICAgICAgICAgICAgIHBsYW49e3BsYW59XG4gICAgICAgICAgICAgICAgcHJvZ3Jlc29QbGFuPXtwcm9ncmVzb1BsYW59XG4gICAgICAgICAgICAgICAgZmVjaGFTZWxlY2Npb25hZGE9e2ZlY2hhU2VsZWNjaW9uYWRhfVxuICAgICAgICAgICAgICAgIG9uRmVjaGFTZWxlY2Npb25hZGE9e2hhbmRsZUZlY2hhU2VsZWNjaW9uYWRhfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtNFwiXG4gICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgey8qIFBhbmVsIGRlIHRhcmVhcyBkZWwgZMOtYSBzZWxlY2Npb25hZG8gKi99XG4gICAgICAgICAgICAgIDxUYXJlYXNEZWxEaWFcbiAgICAgICAgICAgICAgICBmZWNoYT17ZmVjaGFTZWxlY2Npb25hZGF9XG4gICAgICAgICAgICAgICAgdGFyZWFzPXtvYnRlbmVyVGFyZWFzRGVsRGlhU2VsZWNjaW9uYWRvKCl9XG4gICAgICAgICAgICAgICAgb25UYXJlYUNsaWNrPXtoYW5kbGVUYXJlYURlbERpYUNsaWNrfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtNFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogRXN0cmF0ZWdpYSBkZSByZXBhc29zICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQteWVsbG93LTkwMCBtYi0yXCI+RXN0cmF0ZWdpYSBkZSBSZXBhc29zPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctODAwXCI+XG4gICAgICAgICAge3R5cGVvZiBwbGFuLmVzdHJhdGVnaWFSZXBhc29zID09PSAnc3RyaW5nJ1xuICAgICAgICAgICAgPyBwbGFuLmVzdHJhdGVnaWFSZXBhc29zXG4gICAgICAgICAgICA6IChwbGFuLmVzdHJhdGVnaWFSZXBhc29zICYmIHR5cGVvZiBwbGFuLmVzdHJhdGVnaWFSZXBhc29zID09PSAnb2JqZWN0JylcbiAgICAgICAgICAgICAgPyBwbGFuLmVzdHJhdGVnaWFSZXBhc29zLmRlc2NyaXBjaW9uIHx8ICdFc3RyYXRlZ2lhIGRlIHJlcGFzb3Mgbm8gZGlzcG9uaWJsZSdcbiAgICAgICAgICAgICAgOiAnRXN0cmF0ZWdpYSBkZSByZXBhc29zIG5vIGRpc3BvbmlibGUnXG4gICAgICAgICAgfVxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFByw7N4aW1vcyBwYXNvcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHVycGxlLTUwIGJvcmRlciBib3JkZXItcHVycGxlLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXB1cnBsZS05MDAgbWItMlwiPlByw7N4aW1vcyBQYXNvcyB5IENvbnNlam9zPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtODAwXCI+XG4gICAgICAgICAge3R5cGVvZiBwbGFuLnByb3hpbW9zUGFzb3MgPT09ICdzdHJpbmcnXG4gICAgICAgICAgICA/IHBsYW4ucHJveGltb3NQYXNvc1xuICAgICAgICAgICAgOiAocGxhbi5wcm94aW1vc1Bhc29zICYmIHR5cGVvZiBwbGFuLnByb3hpbW9zUGFzb3MgPT09ICdvYmplY3QnKVxuICAgICAgICAgICAgICA/IHBsYW4ucHJveGltb3NQYXNvcy5kZXNjcmlwY2lvbiB8fCAnUHLDs3hpbW9zIHBhc29zIG5vIGRpc3BvbmlibGVzJ1xuICAgICAgICAgICAgICA6ICdQcsOzeGltb3MgcGFzb3Mgbm8gZGlzcG9uaWJsZXMnXG4gICAgICAgICAgfVxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFBsYW5Fc3R1ZGlvc1ZpZXdlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwiRmlDaGVjayIsIkZpQ2xvY2siLCJGaUNhbGVuZGFyIiwiRmlUYXJnZXQiLCJGaUJvb2siLCJGaVJlZnJlc2hDdyIsIm9idGVuZXJQbGFuRXN0dWRpb3NBY3Rpdm9DbGllbnRlIiwib2J0ZW5lclByb2dyZXNvUGxhbmVDbGllbnRlIiwiZ3VhcmRhclByb2dyZXNvVGFyZWFDbGllbnRlIiwidG9hc3QiLCJQbGFuQ2FsZW5kYXJpbyIsIlRhcmVhc0RlbERpYSIsImNhbGN1bGFyRmVjaGFEaWEiLCJmb3JtYXREYXRlIiwianN4REVWIiwiX2pzeERFViIsIlBsYW5Fc3R1ZGlvc1ZpZXdlciIsIl9yZWYiLCJfcyIsInBsYW4iLCJ0ZW1hcmlvSWQiLCJfdXNlU3RhdGUiLCJwcm9ncmVzb1BsYW4iLCJzZXRQcm9ncmVzb1BsYW4iLCJfdXNlU3RhdGUyIiwicGxhbklkIiwic2V0UGxhbklkIiwiX3VzZVN0YXRlMyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsIl91c2VTdGF0ZTQiLCJmZWNoYVNlbGVjY2lvbmFkYSIsInNldEZlY2hhU2VsZWNjaW9uYWRhIiwiX3VzZVN0YXRlNSIsImNhbGVuZGFyaW9Nb2RhbEFiaWVydG8iLCJzZXRDYWxlbmRhcmlvTW9kYWxBYmllcnRvIiwic2VtYW5hUmVmcyIsImNhcmdhclByb2dyZXNvIiwiX3JlZjIiLCJfYXN5bmNUb0dlbmVyYXRvciIsIl9yZWdlbmVyYXRvclJ1bnRpbWUiLCJtYXJrIiwiX2NhbGxlZSIsInBsYW5BY3Rpdm8iLCJfcHJvZ3Jlc28iLCJ3cmFwIiwiX2NhbGxlZSQiLCJfY29udGV4dCIsInByZXYiLCJuZXh0Iiwic2VudCIsImNvbnNvbGUiLCJ3YXJuIiwiYWJydXB0IiwiaWQiLCJwcm9ncmVzbyIsInQwIiwiZXJyb3IiLCJmaW5pc2giLCJzdG9wIiwiYXBwbHkiLCJhcmd1bWVudHMiLCJ0b2dnbGVUYXJlYUNvbXBsZXRhZGEiLCJfcmVmMyIsIl9jYWxsZWUyIiwidGFyZWEiLCJzZW1hbmFOdW0iLCJkaWEiLCJ0YXJlYUV4aXN0ZW50ZSIsIm51ZXZvRXN0YWRvIiwiZXhpdG8iLCJfY2FsbGVlMiQiLCJfY29udGV4dDIiLCJmaW5kIiwicCIsInNlbWFuYV9udW1lcm8iLCJkaWFfbm9tYnJlIiwidGFyZWFfdGl0dWxvIiwidGl0dWxvIiwiY29tcGxldGFkbyIsInRpcG8iLCJpbmRleCIsImZpbmRJbmRleCIsInVwZGF0ZWQiLCJfdG9Db25zdW1hYmxlQXJyYXkiLCJfb2JqZWN0U3ByZWFkIiwiZmVjaGFfY29tcGxldGFkbyIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVuZGVmaW5lZCIsImNvbmNhdCIsIm5vdyIsInBsYW5faWQiLCJ1c2VyX2lkIiwidGFyZWFfdGlwbyIsImNyZWFkb19lbiIsImFjdHVhbGl6YWRvX2VuIiwic3VjY2VzcyIsIl94IiwiX3gyIiwiX3gzIiwiZXN0YUNvbXBsZXRhZGEiLCJzb21lIiwiaGFuZGxlRmVjaGFTZWxlY2Npb25hZGEiLCJmZWNoYSIsInNlbWFuYXMiLCJfaXRlcmF0b3IiLCJfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlciIsIl9zdGVwIiwicyIsIm4iLCJkb25lIiwic2VtYW5hIiwidmFsdWUiLCJfaXRlcmF0b3IyIiwiZGlhcyIsIl9zdGVwMiIsImZlY2hhRGlhIiwiZmVjaGFJbmljaW8iLCJzZW1hbmFSZWYiLCJjdXJyZW50IiwibnVtZXJvIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImJsb2NrIiwiaW5saW5lIiwiZXJyIiwiZSIsImYiLCJvYnRlbmVyVGFyZWFzRGVsRGlhU2VsZWNjaW9uYWRvIiwidGFyZWFzIiwiX2l0ZXJhdG9yMyIsIl9zdGVwMyIsIl9sb29wIiwiX2l0ZXJhdG9yNCIsIl9zdGVwNCIsIl9sb29wMiIsIl9pdGVyYXRvcjUiLCJfc3RlcDUiLCJfbG9vcDMiLCJfcHJvZ3Jlc29QbGFuJGZpbmQiLCJjb21wbGV0YWRhIiwicHVzaCIsInNlbWFuYU51bWVybyIsImRpYU5vbWJyZSIsImZlY2hhQ29tcGxldGFkbyIsImhhbmRsZVRhcmVhRGVsRGlhQ2xpY2siLCJ0YXJlYURlbERpYSIsImNhbGN1bGFyUHJvZ3Jlc28iLCJBcnJheSIsImlzQXJyYXkiLCJjb21wbGV0YWRhcyIsInRvdGFsIiwicG9yY2VudGFqZSIsInRvdGFsVGFyZWFzIiwicmVkdWNlIiwiYWNjIiwiZGF5QWNjIiwibGVuZ3RoIiwidGFyZWFzQ29tcGxldGFkYXNDb3VudCIsImZpbHRlciIsIk1hdGgiLCJyb3VuZCIsImNsYXNzTmFtZSIsImNoaWxkcmVuIiwiZmlsZU5hbWUiLCJfanN4RmlsZU5hbWUiLCJsaW5lTnVtYmVyIiwiY29sdW1uTnVtYmVyIiwiX3RoaXMiLCJ0cmltIiwiaW50cm9kdWNjaW9uIiwic3R5bGUiLCJ3aWR0aCIsInJlc3VtZW4iLCJ0aWVtcG9Ub3RhbEVzdHVkaW8iLCJudW1lcm9UZW1hcyIsImR1cmFjaW9uRXN0dWRpb051ZXZvIiwiZHVyYWNpb25SZXBhc29GaW5hbCIsIm9uQ2xpY2siLCJtYXAiLCJzZW1hbmFJbmRleCIsInJlZiIsImVsIiwiZmVjaGFGaW4iLCJvYmpldGl2b1ByaW5jaXBhbCIsImRpYUluZGV4IiwiaG9yYXMiLCJ0YXJlYUluZGV4IiwiZGVzY3JpcGNpb24iLCJkdXJhY2lvbkVzdGltYWRhIiwib25GZWNoYVNlbGVjY2lvbmFkYSIsIm9uVGFyZWFDbGljayIsImVzdHJhdGVnaWFSZXBhc29zIiwicHJveGltb3NQYXNvcyIsIl9jIiwiJFJlZnJlc2hSZWckIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/hooks/usePlanCalendario.ts":
/*!***************************************************************!*\
  !*** ./src/features/planificacion/hooks/usePlanCalendario.ts ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePlanCalendario: () => (/* binding */ usePlanCalendario)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/planDateUtils */ \"(app-pages-browser)/./src/features/planificacion/utils/planDateUtils.ts\");\n/* harmony import */ var _lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/dateUtils */ \"(app-pages-browser)/./src/lib/utils/dateUtils.ts\");\n\nvar _s = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n/**\n * Hook personalizado para manejar la lógica del calendario del plan de estudios\n */ \n\n\n/**\n * Hook principal para el manejo del calendario del plan de estudios\n */ function usePlanCalendario(plan, progresoPlan, fechaInicialSeleccionada) {\n    _s();\n    // Estado del calendario\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"usePlanCalendario.useState[_useState]\": function() {\n            var hoy = new Date();\n            return {\n                yearActual: hoy.getFullYear(),\n                mesActual: hoy.getMonth(),\n                fechaSeleccionada: fechaInicialSeleccionada || null,\n                fechasCalendario: [],\n                diasCalendario: []\n            };\n        }\n    }[\"usePlanCalendario.useState[_useState]\"]), estadoCalendario = _useState[0], setEstadoCalendario = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), error = _useState3[0], setError = _useState3[1];\n    // Referencias para evitar recálculos innecesarios\n    var planRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(plan);\n    var progresoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(progresoPlan);\n    // Detectar cambios reales en los datos\n    var planChanged = planRef.current !== plan;\n    var progresoChanged = JSON.stringify(progresoRef.current) !== JSON.stringify(progresoPlan);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePlanCalendario.useEffect\": function() {\n            planRef.current = plan;\n            progresoRef.current = progresoPlan;\n        }\n    }[\"usePlanCalendario.useEffect\"], [\n        plan,\n        progresoPlan\n    ]);\n    // Procesar el plan para obtener datos del calendario (con memoización optimizada)\n    var datosPlan = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[datosPlan]\": function() {\n            if (!plan) return null;\n            // Solo recalcular si realmente hay cambios\n            if (!planChanged && !progresoChanged && planRef.current && progresoRef.current) {\n                return null; // Mantener valor anterior\n            }\n            try {\n                setIsLoading(true);\n                setError(null);\n                var resultado = (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.procesarPlanParaCalendario)(plan, progresoPlan, {\n                    incluirDiasSinTareas: true,\n                    calcularEstadisticas: true,\n                    validarFechas: true,\n                    ordenarTareasPorTipo: true\n                });\n                if (resultado.errores.length > 0) {\n                    console.warn('Errores al procesar el plan:', resultado.errores);\n                    setError(resultado.errores[0]); // Mostrar el primer error\n                }\n                return resultado.datosPlan;\n            } catch (err) {\n                var errorMsg = err instanceof Error ? err.message : 'Error desconocido al procesar el plan';\n                setError(errorMsg);\n                console.error('Error al procesar plan para calendario:', err);\n                return null;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"usePlanCalendario.useMemo[datosPlan]\"], [\n        plan,\n        progresoPlan,\n        planChanged,\n        progresoChanged\n    ]);\n    // Generar fechas del calendario para el mes actual\n    var fechasCalendario = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[fechasCalendario]\": function() {\n            return (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.generarFechasCalendario)(estadoCalendario.yearActual, estadoCalendario.mesActual);\n        }\n    }[\"usePlanCalendario.useMemo[fechasCalendario]\"], [\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    // Generar días del calendario con información del plan\n    var diasCalendario = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[diasCalendario]\": function() {\n            if (!datosPlan) return [];\n            return fechasCalendario.map({\n                \"usePlanCalendario.useMemo[diasCalendario]\": function(fecha) {\n                    var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(fecha);\n                    var diaDelPlan = datosPlan.mapaDias.get(fechaKey);\n                    if (diaDelPlan) {\n                        // Día que existe en el plan\n                        return _objectSpread(_objectSpread({}, diaDelPlan), {}, {\n                            estaEnMesActual: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),\n                            esHoy: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha)\n                        });\n                    } else {\n                        // Día que no está en el plan\n                        return {\n                            fecha: fecha,\n                            dia: fecha.getDate(),\n                            estaEnMesActual: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),\n                            esHoy: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha),\n                            estado: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha) ? 'hoy' : (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual) ? 'normal' : 'fuera-mes',\n                            tareas: [],\n                            totalTareas: 0,\n                            tareasCompletadas: 0,\n                            porcentajeCompletado: 0\n                        };\n                    }\n                }\n            }[\"usePlanCalendario.useMemo[diasCalendario]\"]);\n        }\n    }[\"usePlanCalendario.useMemo[diasCalendario]\"], [\n        fechasCalendario,\n        datosPlan,\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    // Actualizar estado del calendario cuando cambien las fechas o días\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePlanCalendario.useEffect\": function() {\n            setEstadoCalendario({\n                \"usePlanCalendario.useEffect\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        fechasCalendario: fechasCalendario,\n                        diasCalendario: diasCalendario\n                    });\n                }\n            }[\"usePlanCalendario.useEffect\"]);\n        }\n    }[\"usePlanCalendario.useEffect\"], [\n        fechasCalendario,\n        diasCalendario\n    ]);\n    // Navegación del calendario\n    var navegarMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[navegarMes]\": function(direccion) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[navegarMes]\": function(prev) {\n                    var _ref = direccion === 'anterior' ? (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.mesAnterior)(prev.yearActual, prev.mesActual) : (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.mesSiguiente)(prev.yearActual, prev.mesActual), year = _ref.year, month = _ref.month;\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: year,\n                        mesActual: month\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[navegarMes]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[navegarMes]\"], []);\n    var irAMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[irAMes]\": function(year, month) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[irAMes]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: year,\n                        mesActual: month\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[irAMes]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[irAMes]\"], []);\n    var seleccionarFecha = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[seleccionarFecha]\": function(fecha) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[seleccionarFecha]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        fechaSeleccionada: fecha\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[seleccionarFecha]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[seleccionarFecha]\"], []);\n    var irAHoy = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[irAHoy]\": function() {\n            var hoy = new Date();\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[irAHoy]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: hoy.getFullYear(),\n                        mesActual: hoy.getMonth(),\n                        fechaSeleccionada: hoy\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[irAHoy]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[irAHoy]\"], []);\n    // Utilidades\n    var obtenerTareasDelDiaCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[obtenerTareasDelDiaCallback]\": function(fecha) {\n            if (!datosPlan) return [];\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerTareasDelDia)(fecha, datosPlan);\n        }\n    }[\"usePlanCalendario.useCallback[obtenerTareasDelDiaCallback]\"], [\n        datosPlan\n    ]);\n    var obtenerEstadoDiaCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[obtenerEstadoDiaCallback]\": function(fecha) {\n            if (!datosPlan) return 'normal';\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadoDia)(fecha, datosPlan);\n        }\n    }[\"usePlanCalendario.useCallback[obtenerEstadoDiaCallback]\"], [\n        datosPlan\n    ]);\n    var esFechaSeleccionable = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[esFechaSeleccionable]\": function(fecha) {\n            if (!datosPlan) return false;\n            // Verificar si la fecha está dentro del rango del plan\n            var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(fecha);\n            var diaDelPlan = datosPlan.mapaDias.get(fechaKey);\n            // Permitir seleccionar días que tienen tareas o están dentro del rango del plan\n            return diaDelPlan !== undefined || fecha >= datosPlan.fechaInicio && fecha <= datosPlan.fechaFin;\n        }\n    }[\"usePlanCalendario.useCallback[esFechaSeleccionable]\"], [\n        datosPlan\n    ]);\n    // Datos computados\n    var tituloMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[tituloMes]\": function() {\n            var fecha = new Date(estadoCalendario.yearActual, estadoCalendario.mesActual);\n            return \"\".concat((0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.getNombreMes)(fecha), \" \").concat(estadoCalendario.yearActual);\n        }\n    }[\"usePlanCalendario.useMemo[tituloMes]\"], [\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    var tareasDelDiaSeleccionado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[tareasDelDiaSeleccionado]\": function() {\n            if (!estadoCalendario.fechaSeleccionada || !datosPlan) return [];\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerTareasDelDia)(estadoCalendario.fechaSeleccionada, datosPlan);\n        }\n    }[\"usePlanCalendario.useMemo[tareasDelDiaSeleccionado]\"], [\n        estadoCalendario.fechaSeleccionada,\n        datosPlan\n    ]);\n    var estadisticasDelDia = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[estadisticasDelDia]\": function() {\n            if (!estadoCalendario.fechaSeleccionada || !datosPlan) return null;\n            var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(estadoCalendario.fechaSeleccionada);\n            var diaCalendario = datosPlan.mapaDias.get(fechaKey);\n            if (!diaCalendario) return null;\n            return {\n                total: diaCalendario.totalTareas,\n                completadas: diaCalendario.tareasCompletadas,\n                porcentaje: diaCalendario.porcentajeCompletado\n            };\n        }\n    }[\"usePlanCalendario.useMemo[estadisticasDelDia]\"], [\n        estadoCalendario.fechaSeleccionada,\n        datosPlan\n    ]);\n    return {\n        // Estado\n        estadoCalendario: estadoCalendario,\n        datosPlan: datosPlan,\n        isLoading: isLoading,\n        error: error,\n        // Acciones\n        navegarMes: navegarMes,\n        irAMes: irAMes,\n        seleccionarFecha: seleccionarFecha,\n        irAHoy: irAHoy,\n        // Utilidades\n        obtenerTareasDelDia: obtenerTareasDelDiaCallback,\n        obtenerEstadoDia: obtenerEstadoDiaCallback,\n        esFechaSeleccionable: esFechaSeleccionable,\n        // Datos computados\n        tituloMes: tituloMes,\n        tareasDelDiaSeleccionado: tareasDelDiaSeleccionado,\n        estadisticasDelDia: estadisticasDelDia\n    };\n}\n_s(usePlanCalendario, \"KZX+HEGNWu9j49Wyo3btThSYj3Y=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/hooks/usePlanCalendario.ts\n"));

/***/ })

});
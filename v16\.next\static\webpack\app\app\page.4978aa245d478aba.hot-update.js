"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/hooks/usePlanCalendario.ts":
/*!***************************************************************!*\
  !*** ./src/features/planificacion/hooks/usePlanCalendario.ts ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePlanCalendario: () => (/* binding */ usePlanCalendario)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/planDateUtils */ \"(app-pages-browser)/./src/features/planificacion/utils/planDateUtils.ts\");\n/* harmony import */ var _lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/dateUtils */ \"(app-pages-browser)/./src/lib/utils/dateUtils.ts\");\n\nvar _s = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n/**\n * Hook personalizado para manejar la lógica del calendario del plan de estudios\n */ \n\n\n/**\n * Hook principal para el manejo del calendario del plan de estudios\n */ function usePlanCalendario(plan, progresoPlan, fechaInicialSeleccionada) {\n    _s();\n    // Estado del calendario\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"usePlanCalendario.useState[_useState]\": function() {\n            var hoy = new Date();\n            return {\n                yearActual: hoy.getFullYear(),\n                mesActual: hoy.getMonth(),\n                fechaSeleccionada: fechaInicialSeleccionada || null,\n                fechasCalendario: [],\n                diasCalendario: []\n            };\n        }\n    }[\"usePlanCalendario.useState[_useState]\"]), estadoCalendario = _useState[0], setEstadoCalendario = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), error = _useState3[0], setError = _useState3[1];\n    // Procesar el plan para obtener datos del calendario\n    var datosPlan = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[datosPlan]\": function() {\n            if (!plan) return null;\n            try {\n                setIsLoading(true);\n                setError(null);\n                var resultado = (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.procesarPlanParaCalendario)(plan, progresoPlan, {\n                    incluirDiasSinTareas: true,\n                    calcularEstadisticas: true,\n                    validarFechas: true,\n                    ordenarTareasPorTipo: true\n                });\n                if (resultado.errores.length > 0) {\n                    console.warn('Errores al procesar el plan:', resultado.errores);\n                    setError(resultado.errores[0]); // Mostrar el primer error\n                }\n                return resultado.datosPlan;\n            } catch (err) {\n                var errorMsg = err instanceof Error ? err.message : 'Error desconocido al procesar el plan';\n                setError(errorMsg);\n                console.error('Error al procesar plan para calendario:', err);\n                return null;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"usePlanCalendario.useMemo[datosPlan]\"], [\n        plan,\n        progresoPlan\n    ]);\n    // Generar fechas del calendario para el mes actual\n    var fechasCalendario = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[fechasCalendario]\": function() {\n            return (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.generarFechasCalendario)(estadoCalendario.yearActual, estadoCalendario.mesActual);\n        }\n    }[\"usePlanCalendario.useMemo[fechasCalendario]\"], [\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    // Generar días del calendario con información del plan\n    var diasCalendario = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[diasCalendario]\": function() {\n            if (!datosPlan) return [];\n            return fechasCalendario.map({\n                \"usePlanCalendario.useMemo[diasCalendario]\": function(fecha) {\n                    var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(fecha);\n                    var diaDelPlan = datosPlan.mapaDias.get(fechaKey);\n                    if (diaDelPlan) {\n                        // Día que existe en el plan\n                        return _objectSpread(_objectSpread({}, diaDelPlan), {}, {\n                            estaEnMesActual: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),\n                            esHoy: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha)\n                        });\n                    } else {\n                        // Día que no está en el plan\n                        return {\n                            fecha: fecha,\n                            dia: fecha.getDate(),\n                            estaEnMesActual: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),\n                            esHoy: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha),\n                            estado: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha) ? 'hoy' : (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual) ? 'normal' : 'fuera-mes',\n                            tareas: [],\n                            totalTareas: 0,\n                            tareasCompletadas: 0,\n                            porcentajeCompletado: 0\n                        };\n                    }\n                }\n            }[\"usePlanCalendario.useMemo[diasCalendario]\"]);\n        }\n    }[\"usePlanCalendario.useMemo[diasCalendario]\"], [\n        fechasCalendario,\n        datosPlan,\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    // Actualizar estado del calendario cuando cambien las fechas o días\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePlanCalendario.useEffect\": function() {\n            setEstadoCalendario({\n                \"usePlanCalendario.useEffect\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        fechasCalendario: fechasCalendario,\n                        diasCalendario: diasCalendario\n                    });\n                }\n            }[\"usePlanCalendario.useEffect\"]);\n        }\n    }[\"usePlanCalendario.useEffect\"], [\n        fechasCalendario,\n        diasCalendario\n    ]);\n    // Navegación del calendario\n    var navegarMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[navegarMes]\": function(direccion) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[navegarMes]\": function(prev) {\n                    var _ref = direccion === 'anterior' ? (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.mesAnterior)(prev.yearActual, prev.mesActual) : (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.mesSiguiente)(prev.yearActual, prev.mesActual), year = _ref.year, month = _ref.month;\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: year,\n                        mesActual: month\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[navegarMes]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[navegarMes]\"], []);\n    var irAMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[irAMes]\": function(year, month) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[irAMes]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: year,\n                        mesActual: month\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[irAMes]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[irAMes]\"], []);\n    var seleccionarFecha = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[seleccionarFecha]\": function(fecha) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[seleccionarFecha]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        fechaSeleccionada: fecha\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[seleccionarFecha]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[seleccionarFecha]\"], []);\n    var irAHoy = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[irAHoy]\": function() {\n            var hoy = new Date();\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[irAHoy]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: hoy.getFullYear(),\n                        mesActual: hoy.getMonth(),\n                        fechaSeleccionada: hoy\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[irAHoy]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[irAHoy]\"], []);\n    // Utilidades\n    var obtenerTareasDelDiaCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[obtenerTareasDelDiaCallback]\": function(fecha) {\n            if (!datosPlan) return [];\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerTareasDelDia)(fecha, datosPlan);\n        }\n    }[\"usePlanCalendario.useCallback[obtenerTareasDelDiaCallback]\"], [\n        datosPlan\n    ]);\n    var obtenerEstadoDiaCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[obtenerEstadoDiaCallback]\": function(fecha) {\n            if (!datosPlan) return 'normal';\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadoDia)(fecha, datosPlan);\n        }\n    }[\"usePlanCalendario.useCallback[obtenerEstadoDiaCallback]\"], [\n        datosPlan\n    ]);\n    var esFechaSeleccionable = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[esFechaSeleccionable]\": function(fecha) {\n            if (!datosPlan) return false;\n            // Verificar si la fecha está dentro del rango del plan\n            var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(fecha);\n            var diaDelPlan = datosPlan.mapaDias.get(fechaKey);\n            // Permitir seleccionar días que tienen tareas o están dentro del rango del plan\n            return diaDelPlan !== undefined || fecha >= datosPlan.fechaInicio && fecha <= datosPlan.fechaFin;\n        }\n    }[\"usePlanCalendario.useCallback[esFechaSeleccionable]\"], [\n        datosPlan\n    ]);\n    // Datos computados\n    var tituloMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[tituloMes]\": function() {\n            var fecha = new Date(estadoCalendario.yearActual, estadoCalendario.mesActual);\n            return \"\".concat((0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.getNombreMes)(fecha), \" \").concat(estadoCalendario.yearActual);\n        }\n    }[\"usePlanCalendario.useMemo[tituloMes]\"], [\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    var tareasDelDiaSeleccionado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[tareasDelDiaSeleccionado]\": function() {\n            if (!estadoCalendario.fechaSeleccionada || !datosPlan) return [];\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerTareasDelDia)(estadoCalendario.fechaSeleccionada, datosPlan);\n        }\n    }[\"usePlanCalendario.useMemo[tareasDelDiaSeleccionado]\"], [\n        estadoCalendario.fechaSeleccionada,\n        datosPlan\n    ]);\n    var estadisticasDelDia = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[estadisticasDelDia]\": function() {\n            if (!estadoCalendario.fechaSeleccionada || !datosPlan) return null;\n            var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(estadoCalendario.fechaSeleccionada);\n            var diaCalendario = datosPlan.mapaDias.get(fechaKey);\n            if (!diaCalendario) return null;\n            return {\n                total: diaCalendario.totalTareas,\n                completadas: diaCalendario.tareasCompletadas,\n                porcentaje: diaCalendario.porcentajeCompletado\n            };\n        }\n    }[\"usePlanCalendario.useMemo[estadisticasDelDia]\"], [\n        estadoCalendario.fechaSeleccionada,\n        datosPlan\n    ]);\n    return {\n        // Estado\n        estadoCalendario: estadoCalendario,\n        datosPlan: datosPlan,\n        isLoading: isLoading,\n        error: error,\n        // Acciones\n        navegarMes: navegarMes,\n        irAMes: irAMes,\n        seleccionarFecha: seleccionarFecha,\n        irAHoy: irAHoy,\n        // Utilidades\n        obtenerTareasDelDia: obtenerTareasDelDiaCallback,\n        obtenerEstadoDia: obtenerEstadoDiaCallback,\n        esFechaSeleccionable: esFechaSeleccionable,\n        // Datos computados\n        tituloMes: tituloMes,\n        tareasDelDiaSeleccionado: tareasDelDiaSeleccionado,\n        estadisticasDelDia: estadisticasDelDia\n    };\n}\n_s(usePlanCalendario, \"Fvfb/0MiF5+bV69yPQMEEKurwH0=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/hooks/usePlanCalendario.ts\n"));

/***/ })

});
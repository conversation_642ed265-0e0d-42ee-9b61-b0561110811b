"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/hooks/usePlanCalendario.ts":
/*!***************************************************************!*\
  !*** ./src/features/planificacion/hooks/usePlanCalendario.ts ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePlanCalendario: () => (/* binding */ usePlanCalendario)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/planDateUtils */ \"(app-pages-browser)/./src/features/planificacion/utils/planDateUtils.ts\");\n/* harmony import */ var _lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/dateUtils */ \"(app-pages-browser)/./src/lib/utils/dateUtils.ts\");\n\nvar _s = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n/**\n * Hook personalizado para manejar la lógica del calendario del plan de estudios\n */ \n\n\n/**\n * Hook principal para el manejo del calendario del plan de estudios\n */ function usePlanCalendario(plan, progresoPlan, fechaInicialSeleccionada) {\n    _s();\n    // Estado del calendario\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"usePlanCalendario.useState[_useState]\": function() {\n            var hoy = new Date();\n            return {\n                yearActual: hoy.getFullYear(),\n                mesActual: hoy.getMonth(),\n                fechaSeleccionada: fechaInicialSeleccionada || null,\n                fechasCalendario: [],\n                diasCalendario: []\n            };\n        }\n    }[\"usePlanCalendario.useState[_useState]\"]), estadoCalendario = _useState[0], setEstadoCalendario = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), error = _useState3[0], setError = _useState3[1];\n    // Referencias para evitar recálculos innecesarios\n    var planRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(plan);\n    var progresoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(progresoPlan);\n    // Detectar cambios reales en los datos\n    var planChanged = planRef.current !== plan;\n    var progresoChanged = JSON.stringify(progresoRef.current) !== JSON.stringify(progresoPlan);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePlanCalendario.useEffect\": function() {\n            planRef.current = plan;\n            progresoRef.current = progresoPlan;\n        }\n    }[\"usePlanCalendario.useEffect\"], [\n        plan,\n        progresoPlan\n    ]);\n    // Procesar el plan para obtener datos del calendario\n    var datosPlan = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[datosPlan]\": function() {\n            if (!plan) return null;\n            try {\n                setIsLoading(true);\n                setError(null);\n                var resultado = (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.procesarPlanParaCalendario)(plan, progresoPlan, {\n                    incluirDiasSinTareas: true,\n                    calcularEstadisticas: true,\n                    validarFechas: true,\n                    ordenarTareasPorTipo: true\n                });\n                if (resultado.errores.length > 0) {\n                    console.warn('Errores al procesar el plan:', resultado.errores);\n                    setError(resultado.errores[0]); // Mostrar el primer error\n                }\n                return resultado.datosPlan;\n            } catch (err) {\n                var errorMsg = err instanceof Error ? err.message : 'Error desconocido al procesar el plan';\n                setError(errorMsg);\n                console.error('Error al procesar plan para calendario:', err);\n                return null;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"usePlanCalendario.useMemo[datosPlan]\"], [\n        plan,\n        progresoPlan\n    ]);\n    // Generar fechas del calendario para el mes actual\n    var fechasCalendario = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[fechasCalendario]\": function() {\n            return (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.generarFechasCalendario)(estadoCalendario.yearActual, estadoCalendario.mesActual);\n        }\n    }[\"usePlanCalendario.useMemo[fechasCalendario]\"], [\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    // Generar días del calendario con información del plan\n    var diasCalendario = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[diasCalendario]\": function() {\n            if (!datosPlan) return [];\n            return fechasCalendario.map({\n                \"usePlanCalendario.useMemo[diasCalendario]\": function(fecha) {\n                    var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(fecha);\n                    var diaDelPlan = datosPlan.mapaDias.get(fechaKey);\n                    if (diaDelPlan) {\n                        // Día que existe en el plan\n                        return _objectSpread(_objectSpread({}, diaDelPlan), {}, {\n                            estaEnMesActual: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),\n                            esHoy: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha)\n                        });\n                    } else {\n                        // Día que no está en el plan\n                        return {\n                            fecha: fecha,\n                            dia: fecha.getDate(),\n                            estaEnMesActual: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),\n                            esHoy: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha),\n                            estado: (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.esHoy)(fecha) ? 'hoy' : (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.estaEnMesActual)(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual) ? 'normal' : 'fuera-mes',\n                            tareas: [],\n                            totalTareas: 0,\n                            tareasCompletadas: 0,\n                            porcentajeCompletado: 0\n                        };\n                    }\n                }\n            }[\"usePlanCalendario.useMemo[diasCalendario]\"]);\n        }\n    }[\"usePlanCalendario.useMemo[diasCalendario]\"], [\n        fechasCalendario,\n        datosPlan,\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    // Actualizar estado del calendario cuando cambien las fechas o días\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePlanCalendario.useEffect\": function() {\n            setEstadoCalendario({\n                \"usePlanCalendario.useEffect\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        fechasCalendario: fechasCalendario,\n                        diasCalendario: diasCalendario\n                    });\n                }\n            }[\"usePlanCalendario.useEffect\"]);\n        }\n    }[\"usePlanCalendario.useEffect\"], [\n        fechasCalendario,\n        diasCalendario\n    ]);\n    // Navegación del calendario\n    var navegarMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[navegarMes]\": function(direccion) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[navegarMes]\": function(prev) {\n                    var _ref = direccion === 'anterior' ? (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.mesAnterior)(prev.yearActual, prev.mesActual) : (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.mesSiguiente)(prev.yearActual, prev.mesActual), year = _ref.year, month = _ref.month;\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: year,\n                        mesActual: month\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[navegarMes]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[navegarMes]\"], []);\n    var irAMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[irAMes]\": function(year, month) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[irAMes]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: year,\n                        mesActual: month\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[irAMes]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[irAMes]\"], []);\n    var seleccionarFecha = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[seleccionarFecha]\": function(fecha) {\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[seleccionarFecha]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        fechaSeleccionada: fecha\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[seleccionarFecha]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[seleccionarFecha]\"], []);\n    var irAHoy = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[irAHoy]\": function() {\n            var hoy = new Date();\n            setEstadoCalendario({\n                \"usePlanCalendario.useCallback[irAHoy]\": function(prev) {\n                    return _objectSpread(_objectSpread({}, prev), {}, {\n                        yearActual: hoy.getFullYear(),\n                        mesActual: hoy.getMonth(),\n                        fechaSeleccionada: hoy\n                    });\n                }\n            }[\"usePlanCalendario.useCallback[irAHoy]\"]);\n        }\n    }[\"usePlanCalendario.useCallback[irAHoy]\"], []);\n    // Utilidades\n    var obtenerTareasDelDiaCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[obtenerTareasDelDiaCallback]\": function(fecha) {\n            if (!datosPlan) return [];\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerTareasDelDia)(fecha, datosPlan);\n        }\n    }[\"usePlanCalendario.useCallback[obtenerTareasDelDiaCallback]\"], [\n        datosPlan\n    ]);\n    var obtenerEstadoDiaCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[obtenerEstadoDiaCallback]\": function(fecha) {\n            if (!datosPlan) return 'normal';\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadoDia)(fecha, datosPlan);\n        }\n    }[\"usePlanCalendario.useCallback[obtenerEstadoDiaCallback]\"], [\n        datosPlan\n    ]);\n    var esFechaSeleccionable = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"usePlanCalendario.useCallback[esFechaSeleccionable]\": function(fecha) {\n            if (!datosPlan) return false;\n            // Verificar si la fecha está dentro del rango del plan\n            var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(fecha);\n            var diaDelPlan = datosPlan.mapaDias.get(fechaKey);\n            // Permitir seleccionar días que tienen tareas o están dentro del rango del plan\n            return diaDelPlan !== undefined || fecha >= datosPlan.fechaInicio && fecha <= datosPlan.fechaFin;\n        }\n    }[\"usePlanCalendario.useCallback[esFechaSeleccionable]\"], [\n        datosPlan\n    ]);\n    // Datos computados\n    var tituloMes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[tituloMes]\": function() {\n            var fecha = new Date(estadoCalendario.yearActual, estadoCalendario.mesActual);\n            return \"\".concat((0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.getNombreMes)(fecha), \" \").concat(estadoCalendario.yearActual);\n        }\n    }[\"usePlanCalendario.useMemo[tituloMes]\"], [\n        estadoCalendario.yearActual,\n        estadoCalendario.mesActual\n    ]);\n    var tareasDelDiaSeleccionado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[tareasDelDiaSeleccionado]\": function() {\n            if (!estadoCalendario.fechaSeleccionada || !datosPlan) return [];\n            return (0,_utils_planDateUtils__WEBPACK_IMPORTED_MODULE_2__.obtenerTareasDelDia)(estadoCalendario.fechaSeleccionada, datosPlan);\n        }\n    }[\"usePlanCalendario.useMemo[tareasDelDiaSeleccionado]\"], [\n        estadoCalendario.fechaSeleccionada,\n        datosPlan\n    ]);\n    var estadisticasDelDia = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"usePlanCalendario.useMemo[estadisticasDelDia]\": function() {\n            if (!estadoCalendario.fechaSeleccionada || !datosPlan) return null;\n            var fechaKey = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(estadoCalendario.fechaSeleccionada);\n            var diaCalendario = datosPlan.mapaDias.get(fechaKey);\n            if (!diaCalendario) return null;\n            return {\n                total: diaCalendario.totalTareas,\n                completadas: diaCalendario.tareasCompletadas,\n                porcentaje: diaCalendario.porcentajeCompletado\n            };\n        }\n    }[\"usePlanCalendario.useMemo[estadisticasDelDia]\"], [\n        estadoCalendario.fechaSeleccionada,\n        datosPlan\n    ]);\n    return {\n        // Estado\n        estadoCalendario: estadoCalendario,\n        datosPlan: datosPlan,\n        isLoading: isLoading,\n        error: error,\n        // Acciones\n        navegarMes: navegarMes,\n        irAMes: irAMes,\n        seleccionarFecha: seleccionarFecha,\n        irAHoy: irAHoy,\n        // Utilidades\n        obtenerTareasDelDia: obtenerTareasDelDiaCallback,\n        obtenerEstadoDia: obtenerEstadoDiaCallback,\n        esFechaSeleccionable: esFechaSeleccionable,\n        // Datos computados\n        tituloMes: tituloMes,\n        tareasDelDiaSeleccionado: tareasDelDiaSeleccionado,\n        estadisticasDelDia: estadisticasDelDia\n    };\n}\n_s(usePlanCalendario, \"KZX+HEGNWu9j49Wyo3btThSYj3Y=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/hooks/usePlanCalendario.ts\n"));

/***/ })

});
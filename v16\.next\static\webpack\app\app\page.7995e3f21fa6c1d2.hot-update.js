"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx":
/*!**********************************************************************!*\
  !*** ./src/features/planificacion/components/PlanEstudiosViewer.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiClock,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _PlanCalendario__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PlanCalendario */ \"(app-pages-browser)/./src/features/planificacion/components/PlanCalendario.tsx\");\n/* harmony import */ var _TareasDelDia__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TareasDelDia */ \"(app-pages-browser)/./src/features/planificacion/components/TareasDelDia.tsx\");\n/* harmony import */ var _lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/dateUtils */ \"(app-pages-browser)/./src/lib/utils/dateUtils.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\planificacion\\\\components\\\\PlanEstudiosViewer.tsx\", _this = undefined, _s1 = $RefreshSig$();\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n    if (!it) {\n        if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n            if (it) o = it;\n            var i = 0;\n            var F = function F() {};\n            return {\n                s: F,\n                n: function n() {\n                    if (i >= o.length) return {\n                        done: true\n                    };\n                    return {\n                        done: false,\n                        value: o[i++]\n                    };\n                },\n                e: function e(_e) {\n                    throw _e;\n                },\n                f: F\n            };\n        }\n        throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true, didErr = false, err;\n    return {\n        s: function s() {\n            it = it.call(o);\n        },\n        n: function n() {\n            var step = it.next();\n            normalCompletion = step.done;\n            return step;\n        },\n        e: function e(_e2) {\n            didErr = true;\n            err = _e2;\n        },\n        f: function f() {\n            try {\n                if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n            } finally{\n                if (didErr) throw err;\n            }\n        }\n    };\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\nvar PlanEstudiosViewer = function PlanEstudiosViewer(_ref) {\n    _s();\n    _s1();\n    var plan = _ref.plan, temarioId = _ref.temarioId;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]), progresoPlan = _useState[0], setProgresoPlan = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null), planId = _useState2[0], setPlanId = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true), isLoading = _useState3[0], setIsLoading = _useState3[1];\n    // Estados para el calendario\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null), fechaSeleccionada = _useState4[0], setFechaSeleccionada = _useState4[1];\n    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true), mostrarCalendario = _useState5[0], setMostrarCalendario = _useState5[1];\n    var _useState6 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), calendarioModalAbierto = _useState6[0], setCalendarioModalAbierto = _useState6[1];\n    // Referencias para scroll automático\n    var semanaRefs = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"PlanEstudiosViewer.useEffect\": function() {\n            cargarProgreso();\n        }\n    }[\"PlanEstudiosViewer.useEffect\"], [\n        temarioId\n    ]);\n    var cargarProgreso = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {\n            var planActivo, _progreso;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        _context.next = 3;\n                        return (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__.obtenerPlanEstudiosActivoCliente)(temarioId);\n                    case 3:\n                        planActivo = _context.sent;\n                        if (planActivo) {\n                            _context.next = 10;\n                            break;\n                        }\n                        console.warn('No se encontró plan activo para el temario:', temarioId);\n                        setPlanId(null);\n                        setProgresoPlan([]);\n                        setIsLoading(false);\n                        return _context.abrupt(\"return\");\n                    case 10:\n                        setPlanId(planActivo.id);\n                        _context.next = 13;\n                        return (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__.obtenerProgresoPlaneCliente)(planActivo.id);\n                    case 13:\n                        _progreso = _context.sent;\n                        setProgresoPlan(_progreso);\n                        _context.next = 22;\n                        break;\n                    case 17:\n                        _context.prev = 17;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error al cargar progreso:', _context.t0);\n                        setPlanId(null);\n                        setProgresoPlan([]);\n                    case 22:\n                        _context.prev = 22;\n                        setIsLoading(false);\n                        return _context.finish(22);\n                    case 25:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    17,\n                    22,\n                    25\n                ]\n            ]);\n        }));\n        return function cargarProgreso() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var toggleTareaCompletada = /*#__PURE__*/ function() {\n        var _ref3 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(tarea, semanaNum, dia) {\n            var tareaExistente, nuevoEstado, exito;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_3___default().wrap(function _callee2$(_context2) {\n                while(1)switch(_context2.prev = _context2.next){\n                    case 0:\n                        if (planId) {\n                            _context2.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se pudo identificar el plan de estudios');\n                        return _context2.abrupt(\"return\");\n                    case 3:\n                        _context2.prev = 3;\n                        tareaExistente = progresoPlan.find(function(p) {\n                            return p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo;\n                        });\n                        nuevoEstado = !(tareaExistente !== null && tareaExistente !== void 0 && tareaExistente.completado);\n                        _context2.next = 8;\n                        return (0,_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_5__.guardarProgresoTareaCliente)(planId, semanaNum, dia, tarea.titulo, tarea.tipo, nuevoEstado);\n                    case 8:\n                        exito = _context2.sent;\n                        if (exito) {\n                            setProgresoPlan(function(prev) {\n                                var index = prev.findIndex(function(p) {\n                                    return p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo;\n                                });\n                                if (index >= 0) {\n                                    var updated = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev);\n                                    updated[index] = _objectSpread(_objectSpread({}, updated[index]), {}, {\n                                        completado: nuevoEstado,\n                                        fecha_completado: nuevoEstado ? new Date().toISOString() : undefined\n                                    });\n                                    return updated;\n                                } else {\n                                    return [].concat((0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev), [\n                                        {\n                                            id: \"temp-\".concat(Date.now()),\n                                            plan_id: planId,\n                                            user_id: '',\n                                            semana_numero: semanaNum,\n                                            dia_nombre: dia,\n                                            tarea_titulo: tarea.titulo,\n                                            tarea_tipo: tarea.tipo,\n                                            completado: nuevoEstado,\n                                            fecha_completado: nuevoEstado ? new Date().toISOString() : undefined,\n                                            creado_en: new Date().toISOString(),\n                                            actualizado_en: new Date().toISOString()\n                                        }\n                                    ]);\n                                }\n                            });\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(nuevoEstado ? 'Tarea completada' : 'Tarea marcada como pendiente');\n                        } else {\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al actualizar el progreso');\n                        }\n                        _context2.next = 16;\n                        break;\n                    case 12:\n                        _context2.prev = 12;\n                        _context2.t0 = _context2[\"catch\"](3);\n                        console.error('Error al actualizar tarea:', _context2.t0);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al actualizar el progreso');\n                    case 16:\n                    case \"end\":\n                        return _context2.stop();\n                }\n            }, _callee2, null, [\n                [\n                    3,\n                    12\n                ]\n            ]);\n        }));\n        return function toggleTareaCompletada(_x, _x2, _x3) {\n            return _ref3.apply(this, arguments);\n        };\n    }();\n    var estaCompletada = function estaCompletada(tarea, semanaNum, dia) {\n        return progresoPlan.some(function(p) {\n            return p.semana_numero === semanaNum && p.dia_nombre === dia && p.tarea_titulo === tarea.titulo && p.completado;\n        });\n    };\n    // Manejar selección de fecha en el calendario\n    var handleFechaSeleccionada = function handleFechaSeleccionada(fecha) {\n        setFechaSeleccionada(fecha);\n        // Buscar la semana y día correspondiente para hacer scroll\n        if (plan && plan.semanas) {\n            var _iterator = _createForOfIteratorHelper(plan.semanas), _step;\n            try {\n                for(_iterator.s(); !(_step = _iterator.n()).done;){\n                    var semana = _step.value;\n                    var _iterator2 = _createForOfIteratorHelper(semana.dias || []), _step2;\n                    try {\n                        for(_iterator2.s(); !(_step2 = _iterator2.n()).done;){\n                            var dia = _step2.value;\n                            var fechaDia = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.calcularFechaDia)(semana.fechaInicio, dia.dia);\n                            if (fechaDia && (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fechaDia) === (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fecha)) {\n                                // Hacer scroll a la semana correspondiente\n                                var semanaRef = semanaRefs.current[semana.numero];\n                                if (semanaRef) {\n                                    semanaRef.scrollIntoView({\n                                        behavior: 'smooth',\n                                        block: 'start',\n                                        inline: 'nearest'\n                                    });\n                                }\n                                return;\n                            }\n                        }\n                    } catch (err) {\n                        _iterator2.e(err);\n                    } finally{\n                        _iterator2.f();\n                    }\n                }\n            } catch (err) {\n                _iterator.e(err);\n            } finally{\n                _iterator.f();\n            }\n        }\n    };\n    // Obtener tareas del día seleccionado\n    var obtenerTareasDelDiaSeleccionado = function obtenerTareasDelDiaSeleccionado() {\n        if (!fechaSeleccionada || !plan || !plan.semanas) return [];\n        var tareas = [];\n        var _iterator3 = _createForOfIteratorHelper(plan.semanas), _step3;\n        try {\n            var _loop = function _loop() {\n                var semana = _step3.value;\n                var _iterator4 = _createForOfIteratorHelper(semana.dias || []), _step4;\n                try {\n                    var _loop2 = function _loop2() {\n                        var dia = _step4.value;\n                        var fechaDia = (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.calcularFechaDia)(semana.fechaInicio, dia.dia);\n                        if (fechaDia && (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fechaDia) === (0,_lib_utils_dateUtils__WEBPACK_IMPORTED_MODULE_9__.formatDate)(fechaSeleccionada)) {\n                            var _iterator5 = _createForOfIteratorHelper(dia.tareas || []), _step5;\n                            try {\n                                var _loop3 = function _loop3() {\n                                    var _progresoPlan$find;\n                                    var tarea = _step5.value;\n                                    var completada = estaCompletada(tarea, semana.numero, dia.dia);\n                                    tareas.push({\n                                        tarea: tarea,\n                                        semanaNumero: semana.numero,\n                                        diaNombre: dia.dia,\n                                        completada: completada,\n                                        fechaCompletado: (_progresoPlan$find = progresoPlan.find(function(p) {\n                                            return p.semana_numero === semana.numero && p.dia_nombre === dia.dia && p.tarea_titulo === tarea.titulo;\n                                        })) === null || _progresoPlan$find === void 0 ? void 0 : _progresoPlan$find.fecha_completado\n                                    });\n                                };\n                                for(_iterator5.s(); !(_step5 = _iterator5.n()).done;){\n                                    _loop3();\n                                }\n                            } catch (err) {\n                                _iterator5.e(err);\n                            } finally{\n                                _iterator5.f();\n                            }\n                        }\n                    };\n                    for(_iterator4.s(); !(_step4 = _iterator4.n()).done;){\n                        _loop2();\n                    }\n                } catch (err) {\n                    _iterator4.e(err);\n                } finally{\n                    _iterator4.f();\n                }\n            };\n            for(_iterator3.s(); !(_step3 = _iterator3.n()).done;){\n                _loop();\n            }\n        } catch (err) {\n            _iterator3.e(err);\n        } finally{\n            _iterator3.f();\n        }\n        return tareas;\n    };\n    // Manejar clic en tarea desde el panel del día\n    var handleTareaDelDiaClick = function handleTareaDelDiaClick(tareaDelDia) {\n        // Hacer scroll a la tarea específica en la lista principal\n        var semanaRef = semanaRefs.current[tareaDelDia.semanaNumero];\n        if (semanaRef) {\n            semanaRef.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n            });\n        }\n    };\n    var calcularProgreso = function calcularProgreso() {\n        if (!plan || !plan.semanas || !Array.isArray(plan.semanas)) {\n            return {\n                completadas: 0,\n                total: 0,\n                porcentaje: 0\n            };\n        }\n        var totalTareas = plan.semanas.reduce(function(acc, semana) {\n            if (!semana || !semana.dias || !Array.isArray(semana.dias)) {\n                return acc;\n            }\n            return acc + semana.dias.reduce(function(dayAcc, dia) {\n                if (!dia || !dia.tareas || !Array.isArray(dia.tareas)) {\n                    return dayAcc;\n                }\n                return dayAcc + dia.tareas.length;\n            }, 0);\n        }, 0);\n        var tareasCompletadasCount = progresoPlan.filter(function(p) {\n            return p.completado;\n        }).length;\n        return {\n            completadas: tareasCompletadasCount,\n            total: totalTareas,\n            porcentaje: totalTareas > 0 ? Math.round(tareasCompletadasCount / totalTareas * 100) : 0\n        };\n    };\n    var progreso = calcularProgreso();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                    className: \"ml-3 text-gray-600\",\n                    children: \"Cargando progreso...\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 7\n        }, _this);\n    }\n    if (!plan) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"No se pudo cargar el plan de estudios\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 7\n        }, _this);\n    }\n    if (!temarioId || temarioId.trim() === '') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"ID de temario no v\\xE1lido\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 7\n        }, _this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"Introducci\\xF3n\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-blue-800\",\n                        children: plan.introduccion || 'Introducción no disponible'\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"Progreso General\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    progreso.porcentaje,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(progreso.porcentaje, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            progreso.completadas,\n                            \" de \",\n                            progreso.total,\n                            \" tareas completadas\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 7\n            }, _this),\n            plan.resumen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiClock, {\n                                    className: \"w-5 h-5 text-blue-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Tiempo Total\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.tiempoTotalEstudio || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiBook, {\n                                    className: \"w-5 h-5 text-green-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Temas\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.numeroTemas || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiTarget, {\n                                    className: \"w-5 h-5 text-purple-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Estudio Nuevo\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.duracionEstudioNuevo || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiRefreshCw, {\n                                    className: \"w-5 h-5 text-orange-600 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Repaso Final\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: plan.resumen.duracionRepasoFinal || 'No disponible'\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 9\n            }, _this),\n            plan.semanas && plan.semanas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCalendar, {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, _this),\n                                    \"Cronograma Semanal\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"button\", {\n                                onClick: function onClick() {\n                                    return setCalendarioModalAbierto(true);\n                                },\n                                className: \"lg:hidden flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCalendar, {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, _this),\n                                    \"Ver Calendario\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-12 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-8 space-y-6\",\n                                children: plan.semanas.map(function(semana, semanaIndex) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                        ref: function ref(el) {\n                                            semanaRefs.current[semana.numero] = el;\n                                        },\n                                        className: \"border border-gray-200 rounded-lg overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-6 py-4 border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: [\n                                                                    \"Semana \",\n                                                                    (semana === null || semana === void 0 ? void 0 : semana.numero) || 'N/A'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 360,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    (semana === null || semana === void 0 ? void 0 : semana.fechaInicio) || 'N/A',\n                                                                    \" - \",\n                                                                    (semana === null || semana === void 0 ? void 0 : semana.fechaFin) || 'N/A'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 363,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 mt-2\",\n                                                        children: (semana === null || semana === void 0 ? void 0 : semana.objetivoPrincipal) || 'Objetivo no especificado'\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                className: \"p-6 space-y-4\",\n                                                children: semana.dias && Array.isArray(semana.dias) ? semana.dias.map(function(dia, diaIndex) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-100 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-semibold text-gray-900\",\n                                                                        children: (dia === null || dia === void 0 ? void 0 : dia.dia) || 'Día no especificado'\n                                                                    }, void 0, false, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                                                        children: [\n                                                                            (dia === null || dia === void 0 ? void 0 : dia.horas) || 0,\n                                                                            \"h\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 373,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: dia.tareas && Array.isArray(dia.tareas) ? dia.tareas.map(function(tarea, tareaIndex) {\n                                                                    var completada = estaCompletada(tarea, semana.numero, dia.dia);\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start p-3 rounded-lg border transition-all cursor-pointer \".concat(completada ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200 hover:border-blue-300'),\n                                                                        onClick: function onClick() {\n                                                                            return toggleTareaCompletada(tarea, semana.numero, dia.dia);\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center \".concat(completada ? 'bg-green-500 border-green-500' : 'border-gray-300 hover:border-blue-400'),\n                                                                                children: completada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiClock_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiCheck, {\n                                                                                    className: \"w-3 h-3 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: _jsxFileName,\n                                                                                    lineNumber: 398,\n                                                                                    columnNumber: 46\n                                                                                }, _this)\n                                                                            }, void 0, false, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 29\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h6\", {\n                                                                                        className: \"font-medium \".concat(completada ? 'text-green-800 line-through' : 'text-gray-900'),\n                                                                                        children: (tarea === null || tarea === void 0 ? void 0 : tarea.titulo) || 'Tarea sin título'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 402,\n                                                                                        columnNumber: 31\n                                                                                    }, _this),\n                                                                                    (tarea === null || tarea === void 0 ? void 0 : tarea.descripcion) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm mt-1 \".concat(completada ? 'text-green-700' : 'text-gray-600'),\n                                                                                        children: tarea.descripcion\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 406,\n                                                                                        columnNumber: 33\n                                                                                    }, _this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center mt-2 space-x-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs px-2 py-1 rounded \".concat((tarea === null || tarea === void 0 ? void 0 : tarea.tipo) === 'estudio' ? 'bg-blue-100 text-blue-800' : (tarea === null || tarea === void 0 ? void 0 : tarea.tipo) === 'repaso' ? 'bg-yellow-100 text-yellow-800' : (tarea === null || tarea === void 0 ? void 0 : tarea.tipo) === 'practica' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'),\n                                                                                                children: (tarea === null || tarea === void 0 ? void 0 : tarea.tipo) || 'general'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: _jsxFileName,\n                                                                                                lineNumber: 411,\n                                                                                                columnNumber: 33\n                                                                                            }, _this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs text-gray-500\",\n                                                                                                children: (tarea === null || tarea === void 0 ? void 0 : tarea.duracionEstimada) || 'No especificado'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: _jsxFileName,\n                                                                                                lineNumber: 419,\n                                                                                                columnNumber: 33\n                                                                                            }, _this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 410,\n                                                                                        columnNumber: 31\n                                                                                    }, _this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, tareaIndex, true, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"No hay tareas disponibles\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 380,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, diaIndex, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, _this);\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"No hay d\\xEDas disponibles\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, semanaIndex, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-4 space-y-4 \".concat(mostrarCalendario ? 'block' : 'hidden lg:block'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_PlanCalendario__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        plan: plan,\n                                        progresoPlan: progresoPlan,\n                                        fechaSeleccionada: fechaSeleccionada,\n                                        onFechaSeleccionada: handleFechaSeleccionada,\n                                        className: \"sticky top-4\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(_TareasDelDia__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        fecha: fechaSeleccionada,\n                                        tareas: obtenerTareasDelDiaSeleccionado(),\n                                        onTareaClick: handleTareaDelDiaClick,\n                                        className: \"sticky top-4\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 9\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-yellow-900 mb-2\",\n                        children: \"Estrategia de Repasos\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-yellow-800\",\n                        children: typeof plan.estrategiaRepasos === 'string' ? plan.estrategiaRepasos : plan.estrategiaRepasos && typeof plan.estrategiaRepasos === 'object' ? plan.estrategiaRepasos.descripcion || 'Estrategia de repasos no disponible' : 'Estrategia de repasos no disponible'\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"div\", {\n                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-purple-900 mb-2\",\n                        children: \"Pr\\xF3ximos Pasos y Consejos\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxDEV)(\"p\", {\n                        className: \"text-purple-800\",\n                        children: typeof plan.proximosPasos === 'string' ? plan.proximosPasos : plan.proximosPasos && typeof plan.proximosPasos === 'object' ? plan.proximosPasos.descripcion || 'Próximos pasos no disponibles' : 'Próximos pasos no disponibles'\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 478,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 5\n    }, _this);\n};\n_s(PlanEstudiosViewer, \"a91y/yZ7NQLm0iPLYQZTIe9YCQ4=\");\n_c1 = PlanEstudiosViewer;\n_s1(PlanEstudiosViewer, \"9v/jhNGv4/xGyuZkkLzW9sjpjpk=\");\n_c = PlanEstudiosViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanEstudiosViewer);\nvar _c;\n$RefreshReg$(_c, \"PlanEstudiosViewer\");\nvar _c1;\n$RefreshReg$(_c1, \"PlanEstudiosViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\n"));

/***/ })

});
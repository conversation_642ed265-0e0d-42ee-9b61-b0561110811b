/**
 * Utilidades para manejo de fechas en el sistema de planificación
 * Incluye manejo robusto de casos borde y configuración localizada
 */

// Configuración de localización para España (configurable)
export const LOCALE_CONFIG = {
  firstDayOfWeek: 1, // Lunes = 1, Domingo = 0
  locale: 'es-ES',
  dateFormat: 'YYYY-MM-DD',
  displayFormat: 'DD/MM/YYYY'
};

/**
 * Actualiza la configuración del primer día de la semana
 */
export function setFirstDayOfWeek(firstDay: 0 | 1): void {
  LOCALE_CONFIG = {
    ...LOCALE_CONFIG,
    firstDayOfWeek: firstDay
  };
}

// Nombres de días en español (empezando por lunes)
export const DIAS_SEMANA = [
  'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'
] as const;

// Nombres de días abreviados
export const DIAS_SEMANA_CORTOS = [
  'LU', 'MA', 'MI', 'JU', 'VI', 'SA', 'DO'
] as const;

// Nombres de meses en español
export const MESES = [
  'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
  'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
] as const;

export type DiaSemana = typeof DIAS_SEMANA[number];
export type DiaSemanaCorto = typeof DIAS_SEMANA_CORTOS[number];
export type Mes = typeof MESES[number];

/**
 * Convierte una fecha en formato string (YYYY-MM-DD) a objeto Date
 * Maneja casos borde y valida la fecha
 */
export function parseDate(dateString: string): Date | null {
  if (!dateString || typeof dateString !== 'string') {
    return null;
  }

  // Validar formato básico YYYY-MM-DD
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) {
    return null;
  }

  const date = new Date(dateString + 'T00:00:00.000Z');
  
  // Verificar que la fecha es válida
  if (isNaN(date.getTime())) {
    return null;
  }

  // Verificar que la fecha parseada coincide con el string original
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  const reconstructed = `${year}-${month}-${day}`;
  
  if (reconstructed !== dateString) {
    return null; // Fecha inválida (ej: 2023-02-30)
  }

  return date;
}

/**
 * Convierte un objeto Date a string en formato YYYY-MM-DD
 */
export function formatDate(date: Date): string {
  if (!date || isNaN(date.getTime())) {
    return '';
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * Convierte un nombre de día (ej: "Lunes") a su índice (0-6, donde Lunes = 0)
 */
export function getDayIndex(dayName: string): number {
  const index = DIAS_SEMANA.findIndex(dia => 
    dia.toLowerCase() === dayName.toLowerCase()
  );
  return index >= 0 ? index : -1;
}

/**
 * Obtiene el nombre del día para un índice dado (0-6, donde Lunes = 0)
 */
export function getDayName(index: number): DiaSemana | null {
  if (index < 0 || index >= DIAS_SEMANA.length) {
    return null;
  }
  return DIAS_SEMANA[index];
}

/**
 * Calcula la fecha específica de un día dentro de una semana
 * @param fechaInicio Fecha de inicio de la semana (formato YYYY-MM-DD)
 * @param nombreDia Nombre del día (ej: "Martes")
 * @returns Fecha específica del día o null si hay error
 */
export function calcularFechaDia(fechaInicio: string, nombreDia: string): Date | null {
  const fechaInicioDate = parseDate(fechaInicio);
  if (!fechaInicioDate) {
    return null;
  }

  const indiceDia = getDayIndex(nombreDia);
  if (indiceDia === -1) {
    return null;
  }

  // Calcular la fecha del día específico
  const fechaDia = new Date(fechaInicioDate);
  fechaDia.setDate(fechaInicioDate.getDate() + indiceDia);
  
  return fechaDia;
}

/**
 * Obtiene el primer día del mes para un calendario
 * Ajustado para que la semana empiece en lunes
 */
export function getPrimerDiaDelMes(year: number, month: number): Date {
  return new Date(year, month, 1);
}

/**
 * Obtiene el último día del mes
 */
export function getUltimoDiaDelMes(year: number, month: number): Date {
  return new Date(year, month + 1, 0);
}

/**
 * Calcula cuántos días mostrar antes del primer día del mes
 * para completar la primera semana del calendario
 */
export function getDiasAntesDelMes(year: number, month: number): number {
  const primerDia = getPrimerDiaDelMes(year, month);
  const diaSemana = primerDia.getDay(); // 0 = Domingo, 1 = Lunes, etc.

  // Ajustar según la configuración del primer día de la semana
  if (LOCALE_CONFIG.firstDayOfWeek === 1) {
    // Lunes como primer día (España)
    return diaSemana === 0 ? 6 : diaSemana - 1;
  } else {
    // Domingo como primer día
    return diaSemana;
  }
}

/**
 * Genera un array con todas las fechas a mostrar en un calendario mensual
 * Incluye días del mes anterior y siguiente para completar las semanas
 */
export function generarFechasCalendario(year: number, month: number): Date[] {
  const fechas: Date[] = [];
  
  const primerDia = getPrimerDiaDelMes(year, month);
  const ultimoDia = getUltimoDiaDelMes(year, month);
  const diasAntes = getDiasAntesDelMes(year, month);
  
  // Agregar días del mes anterior
  for (let i = diasAntes - 1; i >= 0; i--) {
    const fecha = new Date(primerDia);
    fecha.setDate(fecha.getDate() - (i + 1));
    fechas.push(fecha);
  }
  
  // Agregar días del mes actual
  for (let dia = 1; dia <= ultimoDia.getDate(); dia++) {
    fechas.push(new Date(year, month, dia));
  }
  
  // Agregar días del mes siguiente para completar 42 días (6 semanas)
  const diasRestantes = 42 - fechas.length;
  for (let i = 1; i <= diasRestantes; i++) {
    const fecha = new Date(ultimoDia);
    fecha.setDate(fecha.getDate() + i);
    fechas.push(fecha);
  }
  
  return fechas;
}

/**
 * Verifica si dos fechas son el mismo día
 */
export function esMismoDia(fecha1: Date, fecha2: Date): boolean {
  if (!fecha1 || !fecha2) return false;
  
  return fecha1.getFullYear() === fecha2.getFullYear() &&
         fecha1.getMonth() === fecha2.getMonth() &&
         fecha1.getDate() === fecha2.getDate();
}

/**
 * Verifica si una fecha es hoy
 */
export function esHoy(fecha: Date): boolean {
  return esMismoDia(fecha, new Date());
}

/**
 * Verifica si una fecha está en el mes actual del calendario
 */
export function estaEnMesActual(fecha: Date, year: number, month: number): boolean {
  return fecha.getFullYear() === year && fecha.getMonth() === month;
}

/**
 * Formatea una fecha para mostrar en la interfaz
 */
export function formatearFechaDisplay(fecha: Date): string {
  if (!fecha || isNaN(fecha.getTime())) {
    return '';
  }

  return fecha.toLocaleDateString(LOCALE_CONFIG.locale, {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Obtiene el nombre del mes para una fecha
 */
export function getNombreMes(fecha: Date): Mes {
  return MESES[fecha.getMonth()];
}

/**
 * Navega al mes anterior
 */
export function mesAnterior(year: number, month: number): { year: number; month: number } {
  if (month === 0) {
    return { year: year - 1, month: 11 };
  }
  return { year, month: month - 1 };
}

/**
 * Navega al mes siguiente
 */
export function mesSiguiente(year: number, month: number): { year: number; month: number } {
  if (month === 11) {
    return { year: year + 1, month: 0 };
  }
  return { year, month: month + 1 };
}
